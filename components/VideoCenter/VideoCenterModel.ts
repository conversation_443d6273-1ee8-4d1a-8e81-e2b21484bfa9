import {VideoCenterListObj} from '@common/interface/page';

// 视频中心：排序常量
export const VIDEO_CENTER_SORT_LIST: Array<{
    name?: string;
    value?: string;
}> = [
    {
        value: 'default',
        name: '综合排序',
    },
    {
        value: 'hot',
        name: '最热',
    },
    {
        value: 'fresh',
        name: '最新',
    },
];

// 视频中心：筛选MAP
export const VIDEO_CENTER_SORT_MAP = {
    LEVEL_ONE: 'class',
    LEVEL_TWO: 'tag',
    TYPE: 'type',
    SORT: 'sort',
};

// 视频中心：默认视频列表数据
export const INIT_VIDEO_LIST: VideoCenterListObj = {
    orderBy: '',
    order: '',
    pageNo: 0,
    pageSize: 0,
    totalCount: 0,
    result: [],
};

// 视频详情页：底部链接
export const linksData = {
    title: '更多资源和工具',
    desc: '百度智能云向用户提供丰富全面的产品和业务文档，包含产品介绍、操作指导、最佳实践和常见问题处理方案，用户能够通过阅读文档更加深入地了解云产品，更加轻松地使用云产品，并能够自助解决可能遇到的常见问题。',
    apply: [
        {
            name: '产品文档',
            link: 'https://cloud.baidu.com/doc/index.html',
        },
        {
            name: 'API/SDK',
            link: 'https://cloud.baidu.com/doc/API/index.html',
        },
    ],
};

export const QuestionTypeMap = ['视频过时', '视频错误', '内容不方便检索', '内容不易理解', '相关推荐不恰当'];

// 视频详情页：底部链接
export const linksDataWap = {
    title: '更多资源和工具',
    apply: [
        {
            name: '产品文档',
            link: 'https://cloud.baidu.com/doc/index.html',
            desc: '百度智能云向用户提供丰富全面的产品和业务文档，包含产品介绍、操作指导、最佳实践和常见问题处理方案',
        },
        {
            name: 'API/SDK',
            link: 'https://cloud.baidu.com/doc/API/index.html',
            desc: '用户能够通过阅读文档更加深入地了解云产品，更加轻松地使用云产品，并能够自助解决可能遇到的常见问题',
        },
    ],
};
