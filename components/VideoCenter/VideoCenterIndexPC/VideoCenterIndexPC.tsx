import {useMemo, useState, useRef} from 'react';
import cx from 'classnames';
import {message} from 'antd';
import {VideoCenterTypeObj, VideoCenterTypeItemObj, VideoCenterQueryObj, VideoCenterDetailObj} from '@common/interface/page';
import {netService} from '@baidu/bce-services';
import {getAllParamsFromUrl} from '@baidu/bce-helper';
import {useOnMount} from '@baidu/bce-hooks';
import {urlConst} from '@common/constant/urlConst';
import UPager from '@common/components/UPager/UPager';
import {INIT_VIDEO_QUERY} from 'pages/video-center/index';
import {INIT_VIDEO_LIST, VIDEO_CENTER_SORT_LIST, VIDEO_CENTER_SORT_MAP} from '../VideoCenterModel';
import style from './index.module.less';

const VideoCenterBox = (props: {
    data: VideoCenterTypeItemObj;
    curValue: string;
    handleClick?: (type: string, value: string) => void;
}) => {
    const {data, curValue, handleClick} = props;
    return (
        <div className={cx(style['class-box'], style[`class-${data.value}`], 'clearfix')} data-class={data.value}>
            <h3>{data.name}</h3>
            <div className={style['class-content']}>
                {
                    data && data.content && data.content.map((c, i) => {
                        return (
                            <span
                                // eslint-disable-next-line react/no-array-index-key
                                key={i}
                                data-track-category="视频中心首页"
                                data-track-name="视频筛选"
                                data-track-value={`${data.name}：${c.name}`}
                                className={cx(c.value === curValue && style.active)}
                                data-value={c.value}
                                onClick={() => handleClick(data.value, c.value)}
                            >
                                {c.name}
                            </span>
                        );
                    })
                }
            </div>
        </div>
    );
};

const VideoCenterIndexPC = (props: {
    typeList: VideoCenterTypeObj;
    videoOption: VideoCenterQueryObj;
    isPreview: boolean;
}) => {
    const {typeList, videoOption, isPreview} = props;
    const [videoData, setVideoData] = useState(INIT_VIDEO_LIST); // 视频列表数据

    // 筛选列表
    const [expandBox, setExpandBox] = useState(true);
    const [curExpandHeight, setCurExpandHeight] = useState<number>(null);
    const classBoxRef = useRef<HTMLDivElement>(null);
    const classContentRef = useRef<HTMLDivElement>(null);

    // 查询参数
    const [videoQuery, setVideoQuery] = useState<VideoCenterQueryObj>(videoOption);
    const [curClass, curTag, curType, curSort] = useMemo(() => {
        return [videoQuery.firstClassValue, videoQuery.secondClassValue, videoQuery.type, videoQuery.orderBy];
    }, [videoQuery]);

    // 加载状态
    const [loading, setLoading] = useState<boolean>(false);

    // 搜索值
    const [inputValue, setInputValue] = useState<string>('');
    const [inputFocus, setInputFocus] = useState<boolean>(false);

    // 筛选数据
    const {classification, types: typeData} = typeList;
    const curTagData = useMemo(() => {
        const res = classification?.content?.filter(item => item.value === curClass);
        return {
            'name': '视频标签',
            'value': 'tag',
            'content': res?.length > 0 ? res[0].content : [],
        };
    }, [classification, curClass]);

    const resetBoxExpand = () => {
        setExpandBox(true);
        setCurExpandHeight(null);
    };
    const handleBoxExpand = () => {
        // 首次渲染页面获取盒子高度
        if (!curExpandHeight) {
            classBoxRef.current.style.height = String(classContentRef.current.offsetHeight + 20) + 'px';
        }
        if (expandBox) {
            setExpandBox(pre => !pre);
            const firstEle = classContentRef.current.children[0]?.children[0] as HTMLDivElement;
            setCurExpandHeight(() => firstEle.offsetHeight + 77 + 20);
        } else {
            setExpandBox(pre => !pre);
            setCurExpandHeight(() => classContentRef.current.offsetHeight + 20);
        }
    };

    // 设置url
    const setUrl = (query = videoQuery) => {
        const initUrl = `${location.pathname}?belonging=${query.firstClassValue || ''}&tag=${query.secondClassValue || ''}&type=${query.type || ''}`;
        const newUrl = isPreview ? initUrl + '&status=preview' : initUrl;
        history.pushState({newUrl, title: document.title}, document.title, newUrl);
    };

    // 重新请求数据，刷新数据
    const getVideoData = async (query: VideoCenterQueryObj) => {
        // 分类及每页数量改变时，重置页数为1
        if (query.pageSize && query.pageSize !== videoQuery.pageSize) {
            query.pageNo = 1;
        }
        const oldQuerys = videoQuery;
        const params = {...videoQuery, ...query};
        setVideoQuery(params);
        setLoading(true);
        try {
            const res = await netService.post<VideoCenterQueryObj, VideoCenterDetailObj>(urlConst.GET_VIDEO_CENTER_LIST, params);
            setVideoData(res.page);
            setUrl(params);
        } catch {
            setVideoQuery(oldQuerys);
            setUrl(oldQuerys);
            message.error('网络请求错误，请稍候重试');
        }
        setLoading(false);
    };

    // 筛选点击事件（重置搜索值）
    const handleClick = (type: string, value: string) => {
        let newOption = null;
        switch (type) {
            case VIDEO_CENTER_SORT_MAP.LEVEL_ONE: {
                newOption = {...videoQuery, firstClassValue: value, secondClassValue: 'all', name: ''};
                resetBoxExpand();
                break;
            }
            case VIDEO_CENTER_SORT_MAP.LEVEL_TWO: {
                newOption = {...videoQuery, secondClassValue: value, name: ''};
                break;
            }
            case VIDEO_CENTER_SORT_MAP.TYPE: {
                newOption = {...videoQuery, type: value, name: ''};
                break;
            }
            case VIDEO_CENTER_SORT_MAP.SORT: {
                newOption = {...videoQuery, orderBy: value, name: ''};
                break;
            }
            default:
                break;
        }
        getVideoData(newOption);
        setVideoQuery(newOption);
    };

    const searchSubmit = () => {
        const value = inputValue.trim();
        // 搜索一次性参数
        const searchOption = {
            ...INIT_VIDEO_QUERY,
            name: value,
        };
        getVideoData(searchOption);
    };

    const pagerChange = (page: number) => {
        const newQuery = {
            ...videoQuery,
            pageNo: page,
        };
        getVideoData(newQuery);
        document.documentElement.scrollTop = 0;
    };

    useOnMount(() => {
        getVideoData(videoQuery);
        // 监听回退事件
        const popStateHandler = () => {
            const query = getAllParamsFromUrl();
            const newQuery = {
                ...videoQuery,
                firstClassValue: query['belonging'] || 'all',
                secondClassValue: query['tag'] || 'all',
                type: query['type'] || 'all',
            };
            setVideoQuery(newQuery);
            getVideoData(newQuery);
        };
        window.addEventListener('popstate', popStateHandler); // 监听回退事件
        return () => {
            window.removeEventListener('popstate', popStateHandler);
        };
    });

    return (
        <div className={style.main}>
            <div className={style['video-banner']}>
                <div className={style['content-box']}>
                    <h1>视频中心</h1>
                    <p className={style.desc}>“适合跑AI的云”和“懂场景的AI”，共同构成智能时代基础设施</p>
                </div>
            </div>
            <div className={cx(style['classification-box'], expandBox && style.show)} style={{height: curExpandHeight}} ref={classBoxRef}>
                <div className={style.content} ref={classContentRef}>
                    <div className={style.cover}>
                        <VideoCenterBox data={classification} handleClick={handleClick} curValue={curClass} />
                        {
                            curTagData && curTagData?.content.length > 0 ? (
                                <VideoCenterBox data={curTagData} handleClick={handleClick} curValue={curTag} />
                            ) : null
                        }
                        <VideoCenterBox data={typeData} curValue={curType} handleClick={handleClick} />
                    </div>
                </div>
                <div className={style['tools-bar']}>
                    <span
                        data-track-action="click"
                        data-track-category="视频中心首页"
                        data-track-name="展开收起选项"
                        data-track-value={expandBox ? '收起选项' : '展开选项'}
                        className={style['tool-show']}
                        onClick={handleBoxExpand}
                    >
                        {expandBox ? '收起选项' : '展开选项'}
                    </span>
                </div>
            </div>
            <div className={style['video-content']}>
                <div className={style['tool-sort']}>
                    <ul className="clearfix">
                        {VIDEO_CENTER_SORT_LIST?.map((s, index) => (
                            <li
                                // eslint-disable-next-line react/no-array-index-key
                                key={index}
                                data-track-category="视频中心首页"
                                data-track-name="视频排序"
                                data-track-value={`排序：${s.name}`}
                                className={cx(style['sort-button'], `sort-${s.value}`, s.value === curSort && style.active)}
                                onClick={() => handleClick('sort', s.value)}
                            >
                                {s.name}
                            </li>
                        ))}
                    </ul>
                    <div className={cx(style['search-box'], inputFocus && style.focus)}>
                        <input
                            type="text"
                            placeholder="搜索您感兴趣的课程"
                            onFocus={() => setInputFocus(true)}
                            onBlur={() => setInputFocus(false)}
                            onChange={e => setInputValue(e.target.value)}
                            onKeyUp={e => {
                                if (e.keyCode === 13 || e.code === 'Enter') {
                                    searchSubmit();
                                }
                            }}
                        />
                        <span className={style['search-icon']} onClick={searchSubmit}></span>
                    </div>
                </div>
                <div className={cx(style['video-box'], 'clearfix')}>
                    {
                        videoData?.result.length > 0 ? (
                            loading ? (
                                <div className={style.loading}>
                                    <img src="https://bce.bdstatic.com/portal-cloud-server/images/search/loading_2299ad0b.gif" />
                                </div>
                            ) : videoData?.result.map((video, index) => {
                                return (
                                    // eslint-disable-next-line react/no-array-index-key
                                    <div key={index} className={cx(style.video, style[`video-${video.id}`], style[`video-index-${index}`])}>
                                        <a
                                            href={`/video-center/video/${video.id}`}
                                            data-track-category="视频中心首页"
                                            data-track-name="视频列表"
                                            data-track-value={`视频：${video.name}`}
                                            target="_blank"
                                        >
                                            <img src={video.imgUrl} />
                                            <p className={style['sub-desc']}>{video.name}</p>
                                        </a>
                                    </div>
                                );
                            })
                        ) : (
                            <div className={style['no-result']}>
                                <i></i>
                                <p>抱歉暂无匹配搜索内容，可以修改搜索内容再试试。</p>
                            </div>
                        )
                    }
                </div>
            </div>
            {videoData?.result.length > 0 && (
                <div className={style['pager-box']}>
                    <UPager
                        className={style.pager}
                        pageSize={videoQuery.pageSize}
                        totalCount={videoData?.totalCount}
                        currentPage={videoQuery.pageNo}
                        showTotal={false}
                        ellipsisJump
                        onChange={pagerChange}
                    />
                </div>
            )}
        </div>
    );
};

export default VideoCenterIndexPC;
