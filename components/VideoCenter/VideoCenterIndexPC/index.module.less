@import '@styles/variables.less';

@pageBgColor: #F2F6FA;

.main {
    margin: 0 auto;

    .video-banner {
        box-sizing: border-box;
        width: 100%;
        height: 188px;
        background: #DCE7F3 url('https://bce.bdstatic.com/p3m/common-service/uploads/banner-new_4cefb0a.png') no-repeat center/cover;
        padding-top: 56px;

        .content-box {
            width: 100%;
            margin: 0 auto;
            max-width: @Width1180;
            h1 {
                margin: 0 auto 16px;
                font-family: PingFangSC-Medium;
                font-size: 36px;
                color: #222222;
                line-height: 36px;
                font-weight: 500;
            }
    
            .desc {
                margin: 0 auto;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: rgba(34, 34, 34, 0.9);
                text-align: justify;
                line-height: 24px;
                font-weight: 400;
            }
        }
    }

    .classification-box {
        background: @pageBgColor;
        padding: 0;
        position: relative;
        padding-top: 20px;
        box-sizing: border-box;
        overflow: hidden;
        transition: height 0.3s ease-out;

        &.show {
            .tools-bar .tool-show {
                &::after {
                    transform: rotate(0deg);
                }
            }
        }

        .content {
            width: @Width1180;
            margin: 0 auto;
            background-color: @pageBgColor;
            box-sizing: border-box;
            overflow: hidden;
            transition: all cubic-bezier(0, 0, .26, 1) 0.25s;
            height: auto;
        }
    
        .cover {
            overflow: hidden;
            height: 100%;
            width: 100%;
            max-width: @Width1180;
            padding: 20px 20px 57px;
            box-sizing: border-box;
            background-color: #FFF;
            border-radius: 4px;
        }
    
        .class-box {
            overflow: hidden;
            margin-top: 20px;
    
            &:first-child {
                margin-top: 0;
            }
            
            h3 {
                font-size: 14px;
                margin: 0;
                line-height: 28px;
                height: 28px;
                float: left;
                font-family: PingFangSC-Medium;
                color: rgb(34, 34, 34);
                text-align: justify;
                font-weight: 500;
            }
    
            .class-content {
                margin-left: 76px;
                font-size: 0;
    
                span {
                    box-sizing: border-box;
                    font-size: 14px;
                    color: rgba(33, 33, 33, 0.7);
                    line-height: 28px;
                    height: 28px;
                    padding: 0 8px;
                    margin: 0 8px 8px 0;
                    display: inline-block;
                    font-family: PingFangSC-Regular;
                    text-align: justify;
                    font-weight: 400;
    
                    &:hover {
                        color: #2468F2;
                        cursor: pointer;
                    }
    
                    &.active {
                        background: #2468F2;
                        color: #FFF;
                        border-radius: 4px;
                    }
                }
            }
        }

        .tools-bar {
            box-sizing: border-box;
            margin: 0 auto;
            background: #FFF;
            bottom: 0;
            height: 44px;
            max-width: calc(@Width1180 - 40px);
            position: absolute;
            left: 0;
            right: 0;
            border-top: 1px solid rgba(34, 34, 34, 0.08);
            padding: 10px 0;
            
            .tool-show {
                font-size: 14px;
                color: #2468F2;
                text-align: center;
                height: 24px;
                display: block;
                cursor: pointer;
                background-color: #fff;
                line-height: 24px;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                transition: .3s all cubic-bezier(0, 0, .26, 1) 0.2s;
    
                &::after {
                    content: '';
                    position: absolute;
                    display: inline-block;
                    top: 14px;
                    width: 16px;
                    height: 16px;
                    margin-left: 4px;
                    background: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_arrow_up_26cfc70.png') no-repeat center/cover;
                    transform: rotate(180deg);
                    transition: .3s all cubic-bezier(0, 0, .26, 1) 0.2s;
                }

                &:hover {
                    color: #528EFF;
                    &::after {
                        background-image: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_arrow_up_hover_682a1af.png');
                    }
                }
            }
        }
    }

    .video-content {
        background: @pageBgColor;
        padding: 20px 0 12px;
        
        .tool-sort {
            font-size: 12px;
            position: relative;
            width: @Width1180;
            height: 40px;
            margin: 0 auto;
            
            ul {
                padding: 7px 0;
                    
                .sort-button {
                    float: left;
                    height: 100%;
                    margin-left: 40px;
                    line-height: 26px;
                    font-size: 16px;
                    color: rgba(34, 34, 34, 0.9);
                    font-family: PingFangSC-Semibold;
                    letter-spacing: 0;
                    font-weight: 600;
                    text-align: center;
                    cursor: pointer;
                    box-sizing: border-box; 
            
                    &:first-child {
                        margin-left: 0;
                    }
            
                    &.active, &:hover {
                        color: rgba(36, 104, 242, 0.9);
                    }
                }
            }
        
            .search-box {
                position: absolute;
                top: 0;
                right: 0;
                width: 100%;
                max-width: 280px;
                height: 40px;
                background: #FFFFFF;
                border-radius: 4px;
                overflow: hidden;
                border: 1px solid transparent;
        
                input {
                    width: 100%;
                    height: 100%;
                    box-sizing: border-box;
                    padding: 8px 36px 8px 16px;
                    font-size: 14px;
                    line-height: 24px;
                    font-weight: 400;
                    letter-spacing: 0;
                    font-family: PingFangSC-Regular;
        
                    caret-color: #2468F2;
                    outline: none;
                    border: none;
                    text-overflow: ellipsis;
                    &::placeholder {
                        color: rgba(34, 34, 34, 0.4);
                    }
                }
        
                .search-icon {
                    position: absolute;
                    top: 12px;
                    right: 16px;
                    width: 16px;
                    height: 16px;
                    background: url(https://bce.bdstatic.com/p3m/common-service/uploads/search_6945eb6.png) no-repeat;
                    background-size: auto 16px;
                    background-position: -21px 0;
                    cursor: pointer;
        
                    &:hover {
                        background-position: 0px 0;
                    }
                }
        
                &:hover, &.focus {
                    border-color: rgba(36, 104, 242, 1);
                }
            }
        }

        .video-box {
            width: @Width1180;
            margin: 20px auto 30px;

            .video {
                float: left;
                width: 280px;
                height: 205px;
                box-sizing: border-box;
                margin-top: 0;
                margin-right: 20px;
                background: #FFFFFF;
                border-radius: 4px;
                overflow: hidden;
                transition: transform .3s ease-out;

                &:hover {
                    transform: translateY(-12px);
                    box-shadow: 0 6px 16px 2px rgba(7, 12, 20, 0.08);
                    > a .sub-desc {
                        color: @CB;
                    }
                }

                &:nth-child(4n) {
                    margin-right: 0;
                }

                &:nth-child(n+5) {
                    margin-top: 20px;
                }

                > a {
                    display: block;

                    img {
                        width: 100%;
                        height: 157px;
                    }

                    .sub-desc {
                        line-height: 24px;
                        padding: 12px 20px;
                        font-size: 14px;
                        color: @C0;
                        height: 48px;
                        box-sizing: border-box;
                        background: @CW;
                        font-family: PingFangSC-Medium;
                        text-align: justify;
                        font-weight: 500;
                
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                    }
                }
            }
        }

        .no-result {
            text-align: center;
            display: flex;
            width: 100%;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            > i {
                margin-top: 51px;
                width: 100px;
                height: 100px;
                background: url('https://bce.bdstatic.com/p3m/common-service/uploads/img_none_15e0cb8.png') no-repeat center/cover;
            }
            > p {
                margin-bottom: 22px;
                font-family: PingFangSC-Regular;
                font-size: 13px;
                color: rgba(34,34,34,0.40);
                text-align: center;
                line-height: 21px;
                font-weight: 400;
            }
        }
    }

    .pager-box {
        padding: 0 0 52px;
        position: relative;
        background: @pageBgColor;
        box-sizing: border-box;

        .pager {
            justify-content: center;
            :global {
                .u-pager-list {
                    > li {
                        border-color: transparent;
                        border-radius: 4px;
                        font-size: 14px;
                        margin-right: 0;

                        &.current {
                            border: 1px solid #2468F2;
                            color: #2468F2;
                        }
                        &:hover {
                            color: @CB;
                        }

                        &.left,
                        &.right {
                            span {
                                border-color: #222222;
                            }
                            &.disabled {
                                span {
                                    border-color: rgba(34, 34, 34, 0.4);
                                }
                            }
                            &:hover {
                                span {
                                    border-color: #2468F2;
                                }
                                &.disabled span {
                                    border-color: rgba(34, 34, 34, 0.4);
                                }
                            }
                        }

                        &.ellipsis {
                            position: relative;
                            color: transparent;
                            background: url('https://bce.bdstatic.com/p3m/common-service/uploads/ellipsis_2d2b52c.png') no-repeat center;
                            background-size: 18px 18px;
                            &:hover {
                                color: transparent;
                            }
                        }
                    }
                }
            }
            
        }
    }
}