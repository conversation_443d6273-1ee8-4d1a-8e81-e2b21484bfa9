import {useRef, useState} from 'react';
import cx from 'classnames';
import {message} from 'antd';
import {useOnMount} from '@baidu/bce-hooks';
import {netService} from '@baidu/bce-services';
import {VideoCenterDetailObj} from '@common/interface/page';
import {urlConst} from '@common/constant/urlConst';
import {Cyberplayer} from '@common/components/Cyberplayer/Cyberplayer';
import {getAntiParam} from '@common/helper/page';
import {CyberplayerApi} from '@common/interface/common';
import {linksDataWap, QuestionTypeMap} from '../VideoCenterModel';
import style from './index_m.module.less';

// eslint-disable-next-line complexity
const VideoCenterDetailWap = (props: {
    id: number;
    videoDetail: VideoCenterDetailObj;
    otherVideo: VideoCenterDetailObj[];
}) => {
    const {id, videoDetail, otherVideo} = props;
    const {typeName = '', firstClassName = '', secondClassName = ''} = videoDetail;
    const videoBoxRef = useRef(null);
    const cyberPlayerRef = useRef<CyberplayerApi>(null);
    const [isViewed, setIsViewed] = useState(false);
    const [likeNum, setLikeNum] = useState(videoDetail.likes || 0);
    const [isLiked, setIsLiked] = useState(false);
    const [isDisliked, setIsDisliked] = useState(false);

    const [userId, setUserId] = useState<number>(null);
    // 反馈表单数据
    const formErrorRef = useRef(null);
    const [showFeedback, setShowFeedback] = useState(false);
    const [questionTypeList, setQuestionTypeList] = useState<string[]>([]);
    const [suggestValue, setSuggestValue] = useState<string>('');
    const [validError, setValidError] = useState(false);

    const handleVideoPlay = () => {
        if (!isViewed) {
            netService.post<{id: number}, number>(urlConst.POST_VIDEO_DETAIL_VIEWED, {id: id}).then(() => {
                setIsViewed(true);
            });
        }
    };

    const handleLikeClick = () => {
        if (isLiked) {
            return;
        }
        netService.post<{id: number}, number>(urlConst.POST_VIDEO_DETAIL_LIKES, {id: id}).then(() => {
            setLikeNum(likeNum + 1);
            setIsLiked(true);
        }).catch(() => {
            message.error({
                content: '网络请求失败，请稍后再试',
                style: {marginTop: '60px'},
            });
        });
    };

    const handleDislikeClick = () => {
        setShowFeedback(true);
        if (isDisliked) {
            return;
        }
        netService.post<{id: number}, number>(urlConst.POST_VIDEO_DETAIL_DISLIKES, {id: id}).then(() => {
            setIsDisliked(true);
        });
    };

    // 反馈表单事件
    const closeFeedback = () => {
        setShowFeedback(false);
        setValidError(false);
    };
    const submitHandle = async () => {
        const suggest = suggestValue.trim();
        if (suggest.length === 0) {
            setValidError(true);
            setTimeout(() => {
                formErrorRef.current.scrollIntoView(false);
            }, 50);
            return;
        }
        // 表单数据格式化
        const params = {
            id: userId,
            feedbackType: questionTypeList.join(','),
            feedback: suggest,
        };
        netService.put<{
            id: number;
            feedbackType: string;
            feedback: string;
        }, boolean>(urlConst.POST_VIDEO_DETAIL_USERINFO, params, null as never, {
            headers: typeof window === 'undefined' ? {
                'Content-Type': 'application/json',
            } : {
                'Content-Type': 'application/json',
                'Acs-Token': await getAntiParam(),
            },
        }).then(response => {
            if (response.success && response.result) {
                // 重置表单
                setQuestionTypeList([]);
                setSuggestValue('');
                closeFeedback();
                message.success({
                    content: '感谢您的反馈意见',
                    style: {marginTop: '60px'},
                });
            }
            else {
                message.error({
                    content: '网络请求失败，请稍后再试',
                    style: {marginTop: '60px'},
                });
            }
        }).catch(() => {
            message.error({
                content: '网络请求失败，请稍后再试',
                style: {marginTop: '60px'},
            });
        }).finally(() => {
            // fix：手机浏览器劫持video元素提升层级，若禁止body滚动，会导致video一直遮盖dom，本页面强制提交表单后页面返回顶部
            if (window.scrollTo) {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth',
                });
            } else {
                document.documentElement.scrollTop = 0;
            }
        });
    };
    const toggleCheckedList = (typeC: string) => {
        if (questionTypeList.includes(typeC)) {
            setQuestionTypeList(questionTypeList.filter(q => q !== typeC));
        } else {
            setQuestionTypeList(questionTypeList);
        }
    };
    const updateSuggestText = (value: string) => {
        setSuggestValue(value);
        setValidError(false);
    };

    const handleSnapshot = () => {
        let videoEle: HTMLVideoElement = null;
        let lock = false;
        const cyberTimer = setInterval(() => {
            if (videoEle) {
                clearInterval(cyberTimer);
            }
            if (!lock && !videoEle) {
                videoEle = document.querySelector('#playercontainer video');
                if (videoEle) {
                    const previewEle: HTMLElement = document.querySelector('#playercontainer .jw-preview');
                    videoEle.currentTime = 0.1;
                    const canvas = document.createElement('canvas');
                    canvas.width = cyberPlayerRef.current.getWidth();
                    canvas.height = cyberPlayerRef.current.getHeight();
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(videoEle, 0, 0, canvas.width, canvas.height);
                    try {
                        const dataURL = canvas.toDataURL();
                        videoEle.poster = dataURL;
                        previewEle.style.backgroundImage = `url('${dataURL}')`;
                    // eslint-disable-next-line no-empty
                    } catch {}
                    lock = true;
                }
            }
        }, 100);
    };
    useOnMount(() => {
        if (!userId) {
            // 初始化进入页面，请求获取页面id
            netService.post<{videoName: string}, number>(
                urlConst.POST_VIDEO_DETAIL_USERINFO,
                {videoName: `${firstClassName}-${secondClassName}-${videoDetail.name}`}
            ).then(response => {
                if (response.success && response.result) {
                    // 存储用户id
                    setUserId(response.result);
                }
            });
        }
    });
    return (
        <div className={style.main}>
            <div className={cx(style.crumb, 'clearfix')}>
                <ul>
                    <li
                        className={style['first-crumb']}
                        data-track-category="视频中心详情页"
                        data-track-name="面包屑导航"
                        data-track-value={`视频中心-${typeName}`}
                    >
                        <a href="/video-center/index.html">视频中心</a>
                    </li>
                    <li className={style['title-crumb']}>{videoDetail.name}</li>
                </ul>
            </div>
            <div
                ref={videoBoxRef}
                className={style['video-box']}
                data-track-category="视频中心详情页"
                data-track-name="视频播放"
                data-track-value={`视频：${videoDetail.name}`}
            >
                <div id="playercontainer">
                    <Cyberplayer
                        onload={player => {
                            cyberPlayerRef.current = player;
                            player.onPlay(handleVideoPlay);
                            player.on('ready', handleSnapshot);
                        }}
                        initSdk
                        config={{
                            controlbar: {
                                barLogo: false,
                            },
                            file: videoDetail.src,
                            image: null,
                            controls: true,
                            ak: '468b8de7f7e84fb8ba5aa60c38b9edb0',
                        }}
                    />
                </div>
            </div>
            <div className={style['video-info']}>
                <h1>{videoDetail.name}</h1>
                <p>{videoDetail.videoDesc}</p>
                <div className={style['video-links']}>
                    <div className={style['like-btn']}>
                        <div
                            data-track-category="视频中心详情页"
                            data-track-name="按钮：点赞喜欢"
                            data-track-value={`视频：${videoDetail.name}`}
                            className={cx(style.like, isLiked && style.active)}
                            onClick={handleLikeClick}
                        >
                            <span className={cx(style.iconfont, 'iconfont', 'icon-like')}></span>
                            <span className={style['like-number']}>{likeNum}</span>
                        </div>
                        <div
                            data-track-category="视频中心详情页"
                            data-track-name="按钮：无帮助"
                            data-track-value={`视频：${videoDetail.name}`}
                            className={style.helpless}
                            onClick={handleDislikeClick}
                        >
                            <span className={cx(style.iconfont, 'iconfont', 'icon-like')}></span>
                            <span>无帮助</span>
                        </div>
                    </div>
                    <div className={style.views}>
                        <span className={cx(style.iconfont, 'iconfont', 'icon-view')}></span>
                        <span className={style['views-number']}>播放量：{videoDetail.views}</span>
                    </div>
                </div>
            </div>
            {
                otherVideo && otherVideo.length > 0 && (
                    <div className={style['related-video']}>
                        <div className={style['title-box']}>
                            <h3>相关视频</h3>
                            <a
                                className={style.more}
                                data-track-category="视频中心详情页"
                                data-track-name="链接：查看更多"
                                data-track-value={`视频：${videoDetail.name}-查看更多`}
                                href="/video-center/index.html"
                                target="_blank"
                            >
                                查看更多视频
                            </a>
                        </div>
                        <div className={style['video-other']}>
                            {otherVideo?.map((d, index) => (
                                <a
                                    // eslint-disable-next-line react/no-array-index-key
                                    key={index}
                                    className={cx(style['other-video-item'], 'clearfix')}
                                    data-id={d.id}
                                    data-track-category="视频中心详情页"
                                    data-track-name={`链接：${videoDetail.name}-相关视频`}
                                    data-track-value={`视频：${d.name}`}
                                    href={`/video-center/video/${d.id}`}
                                    target="_blank"
                                >
                                    <div className={style['img-box']}><img src={d.imgUrl} /></div>
                                    <div className={style['other-info']}>
                                        <h2>{d.name}</h2>
                                        <p>{d.videoDesc}</p>
                                    </div>
                                </a>
                            ))}
                        </div>
                    </div>
                )
            }
            {
                linksDataWap && (
                    <div className={style.links}>
                        <h3>{linksDataWap.title}</h3>
                        <div className={cx(style['apply-box'], 'clearfix')}>
                            {linksDataWap.apply.map((apply, index) => {
                                return (
                                    <a
                                        // eslint-disable-next-line react/no-array-index-key
                                        key={index}
                                        className={style['apply-item']}
                                        data-track-category="视频中心详情页"
                                        data-track-name={`链接：${videoDetail.name}-更多资源与工具`}
                                        data-track-value={`${linksDataWap.title}：${apply.name}`}
                                        href={apply.link}
                                        target="_blank"
                                    >
                                        <h5>{apply.name}</h5>
                                        <p>{apply.desc}</p>
                                    </a>
                                );
                            })}
                        </div>
                    </div>
                )
            }
            <div className={cx(style['modal-mask'], showFeedback && style['dialog-show'])}>
                <div className={style['dialog-wrapper']} onClick={closeFeedback}></div>
                <div className={cx(style['feedback-mask'], showFeedback && style['dialog-popup'])}>
                    {showFeedback && (
                        <>
                            <div className={style['form-title']}>
                                视频反馈建议
                                <span className={style['feedback-close']} onClick={closeFeedback}></span>
                            </div>
                            <form className={style['feedback-form']}>
                                <div className={style['form-row']}>
                                    <div className={style['form-name']}>问题类型：</div>
                                    <div className={style['form-detail']}>
                                        {QuestionTypeMap?.map((typeC, index) => (
                                            // eslint-disable-next-line react/no-array-index-key
                                            <label key={index}>
                                                <input
                                                    type="checkbox"
                                                    value={typeC}
                                                    name="questionType"
                                                    onChange={() => toggleCheckedList(typeC)}
                                                />
                                                <span>{typeC}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                <div className={style['form-row']}>
                                    <div className={style['form-name']}>更多建议：</div>
                                    <div className={style['form-detail']} id="feedback-suggest">
                                        <textarea
                                            className={cx(validError && style['show-error'])}
                                            name="suggest"
                                            placeholder="请您输入反馈意见，帮助我们精确定位即解决你的问题"
                                            value={suggestValue}
                                            onChange={e => updateSuggestText(e.target.value)}
                                        >
                                        </textarea>
                                        <div className={style.validError} ref={formErrorRef}>{validError ? '反馈意见不能为空' : ''}</div>
                                    </div>
                                </div>
                            </form>
                            <div className={style['form-submit']} onClick={submitHandle}>确定</div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default VideoCenterDetailWap;
