@import '@styles/mixin.less';
@import '@styles/variables.less';

.main {
    background: #f2f6fa;
    color: #999;
}

.subTitleStyle() {
    font-family: PingFangSC-Medium;
    font-size: 17px;
    color: @C0;
    text-align: justify;
    line-height: 25px;
    font-weight: 500;
    margin-bottom: 16px;
}

.crumb {
    width: 100%;
    height: 48px;
    padding: 14px 12.5px;
    box-sizing: border-box;
    overflow: hidden;

    ul {
        width: 100%;
        height: 100%;
        display: flex;
    }

    li, li a {
        display: inline-block;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: rgba(@C0, 0.7);
        text-align: justify;
        line-height: 20px;
        font-weight: 400;
    }

    .first-crumb {
        position: relative;
        padding-right: 18px;
        flex-shrink: 0;
        &::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0;
            width: 14px;
            height: 14px;
            background: url('https://bce.bdstatic.com/p3m/common-service/uploads/arrow-down_c8dc68d.svg') no-repeat center/cover;

            transform: translateY(-50%) rotate(-90deg);
        }
    }

    .title-crumb {
        position: relative;
        color: rgba(@C0, 0.89);
        padding-left: 4px;
        box-sizing: border-box;
        .ellipsis();
    }
}

.video-box {
    position: relative;
    width: 100%;
    height: 211px;
    box-sizing: border-box;

    #playercontainer {
        height: 100%;
        background: #2A2F33;
    }
    :global {
        .jw-preview {
            background-color: transparent;
            background-size: contain;
        }
        .jw-state-paused, .jw-state-idle {
            .jw-controls .jw-display-icon-container {
                border: none;
                background: #FFFFFF url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_player_b7fa894.png') no-repeat 50%/cover;
                height: 40px;
                width: 40px;
                margin: 0 auto;
                transform: translateY(-50%);

                .jw-icon-display:before {
                    display: none;
                }
            }
        }
        // 显示时间
        .jw-flag-compact-player .jw-text-elapsed,
        .jw-flag-compact-player .jw-text-duration {
            display: inline-block;
        }
    }
}

.video-info {
    width: 100%;
    padding: 16px 12.5px 24px;
    box-sizing: border-box;
    > h1 {
        font-family: PingFangSC-Medium;
        font-size: 20px;
        color: @C0;
        line-height: 28px;
        font-weight: 500;
        margin-bottom: 8px;
    }

    > p {
        font-family: PingFangSC-Regular;
        font-size: 13px;
        color: rgba(@C0, 0.9);
        text-align: justify;
        line-height: 21px;
        font-weight: 400;
        margin-bottom: 16px;
    }

    .video-links {
        display: flex;
        justify-content: space-between;

        .like-btn {
            display: flex;
            div {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 68px;
                height: 28px;
                border: 1px solid rgba(36,104,242,1);
                border-radius: 2px;
                .iconfont {
                    margin-right: 4px;
                    font-size: 14px;
                    line-height: 14px;
                }
                span {
                    font-family: PingFangSC-Regular;
                    font-size: 11px;
                    color: @CB;
                    text-align: justify;
                    line-height: 19px;
                    font-weight: 400;
                }

                &.active {
                    background: @CB;
                    span {
                        color: @CW;
                    }
                }
            }
            .like {
                margin-right: 10px;
            }
            .helpless {
                .iconfont {
                    transform: rotate(180deg);
                }
            }
        }
        .views {
            display: flex;
            align-items: center;
            .iconfont {
                margin-right: 4px;
                font-size: 14px;
                line-height: 14px;
                color: rgba(@C0, 0.2);
            }
            span {
                font-family: PingFangSC-Regular;
                font-size: 11px;
                color: rgba(@C0, 0.4);
                text-align: justify;
                line-height: 19px;
                font-weight: 400;
            }
        }
    }
}

.related-video {
    position: relative;
    width: 100%;
    padding: 24.5px 12.5px 24px;
    box-sizing: border-box;
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 12.5px;
        height: 0.5px;
        width: 35rem;
        background: rgba(34, 34, 34, 0.08);;
    }
    .title-box {
        position: relative;

        > h3 {
            .subTitleStyle();
        }

        .more {
            position: absolute;
            right: 0;
            top: 2px;
            height: 21px;
            display: inline-flex;
            align-items: center;
            font-family: PingFangSC-Regular;
            font-size: 13px;
            color: @CB;
            text-align: center;
            line-height: 21px;
            font-weight: 400;

            &::after {
                content: '';
                display: inline-block;
                width: 1.4rem;
                height: 1.4rem;
                margin-left: 0.4rem;
                background: url('https://bce.bdstatic.com/p3m/common-service/uploads/video-center-icon_9e87a9e.png') no-repeat;
                background-size: auto 1.4rem;
                background-position: -1.9rem 0;
                transform: rotate(90deg);
            }
        }
    }

    .video-other {
        min-height: 10px;
        box-sizing: border-box;
        .other-video-item {
            display: flex;
            width: 350px;
            height: 62px;
            margin: auto;
            background: #FFFFFF;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 10px;
            
            &:last-child {
                margin-bottom: 0;
            }

            .img-box {
                img {
                    width: 100%;
                    max-width: 111px;
                    height: 100%;
                }
            }

            .other-info {
                max-width: 240px;
                padding: 8px 12px;
                box-sizing: border-box;

                h2 {
                    width: 100%;
                    height: 22px;
                    font-family: PingFangSC-Medium;
                    font-size: 14px;
                    color: @C0;
                    line-height: 22px;
                    font-weight: 500;
                    margin-bottom: 4px;
                    .ellipsis();
                }
        
                P {
                    width: 100%;
                    height: 20px;
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: rgba(@C0, 0.9);
                    text-align: justify;
                    line-height: 20px;
                    font-weight: 400;
                    .ellipsis();
                }
            }
        }
    }
}

.links {
    position: relative;
    width: 100%;
    padding: 24.5px 12.5px 24px;
    box-sizing: border-box;
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 12.5px;
        height: 0.5px;
        width: 35rem;
        background: rgba(34, 34, 34, 0.08);;
    }

    h3 {
        .subTitleStyle();
    }

    .apply-box {
        margin: 24px 0 0 0;

        .apply-item {
            display: block;
            width: 100%;
            padding: 16px 12px;
            box-sizing: border-box;
            background: @CW;
            margin-bottom: 10px;
            border-radius: 2px;
            overflow: hidden;

            &:last-child {
                margin-bottom: 0;
            }

            > h5 {
                position: relative;
                font-family: PingFangSC-Medium;
                font-size: 15px;
                color: @C0;
                line-height: 23px;
                font-weight: 500;
                margin-bottom: 6px;
                &::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    right: 0;
                    transform: translateY(-50%);
                    width: 16px;
                    height: 16px;
                    background: url('https://bce.bdstatic.com/p3m/common-service/uploads/arrow_61c51ce.png');
                    background-size: auto 16px;
                    background-position: -20px 0;
                }
            }
            > p {
                font-family: PingFangSC-Regular;
                font-size: 13px;
                color: rgba(@C0, 0.9);
                text-align: justify;
                line-height: 21px;
                font-weight: 400;
            }
        }
    }
}

.modal-mask {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0;
    transition: all 0.3s ease-out;
    background-color: rgba(0, 0, 0, 0.7);

    &.dialog-show {
        height: 100%;
        opacity: 1;
        z-index: 100000;
        .dialog-wrapper {
            opacity: 1;
        }
    }
    .dialog-wrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease-out;
    }
    .feedback-mask {
        width: 100%;
        height: calc(100% - 211px);
        background: @CW;
        position: absolute;
        bottom: 0;
        transition: 0.3s transform ease-out;
        transform: translateY(100%);
        color: #333;
        border-radius: 16px 16px 0 0;
        overflow: hidden;
        &.dialog-popup {
            transform: translateY(0%);
        }
        .form-title {
            position: relative;
            height: 48px;
            padding: 12.5px;
            font-family: PingFangSC-Medium;
            font-size: 15px;
            color: #222222;
            line-height: 23px;
            font-weight: 500;
            text-align: center;
            .feedback-close {
                position: absolute;
                top: 50%;
                right: 10px;
                transform: translateY(-50%);
                width: 24px;
                height: 24px;
                background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_closed_67c388e.png') center/cover;
            }
        }
        .feedback-form {
            max-height: calc(100% - 88px - 32px);
            overflow-y: scroll;
            .form-row {
                margin-top: 10px;
                .form-name {
                    width: 100%;
                    margin-bottom: 8px;
                    padding: 0 12.5px;
                    box-sizing: border-box;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: rgba(@C0, 0.9);
                    text-align: justify;
                    line-height: 22px;
                    font-weight: 400;
                }
                .form-detail {
                    width: 100%;
                    label {
                        display: flex;
                        align-items: center;
                        position: relative;
                        height: 44px;
                        padding: 0 12.5px;
                        box-sizing: border-box;
                        input[type="checkbox"] {
                            position: absolute;
                            left: 12.5px;
                            top: 50%;
                            transform: translateY(-50%);
                            width: 16px;
                            height: 16px;
                            border-radius: 2px;
                            margin: 0;
                            cursor: pointer;
                            opacity: 0;
                            &:checked {
                                border: none;
                                background-color: @CB;
    
    
                                & + span::before {
                                    background-position: -36px 0;
                                }
                            }
                        }
                        span {
                            margin-left: 24px;
                            font-family: PingFangSC-Regular;
                            font-size: 13px;
                            color: #222222;
                            line-height: 21px;
                            font-weight: 400;
                            &::before {
                                content: '';
                                position: absolute;
                                left: 12.5px;
                                top: 50%;
                                transform: translateY(-50%);
                                width: 16px;
                                height: 16px;
                                border-radius: 2px;
                                background: url('https://bce.bdstatic.com/p3m/common-service/uploads/checkbox-icon_013d5be.png') no-repeat;
                                background-size: auto 16px;
                                background-position: -18px 0;
                            }
                        }
                    }
                    textarea {
                        box-sizing: border-box;
                        width: 35rem;
                        height: 120px;
                        outline: none;
                        padding: 12px;;
                        margin: 0 12.5px;
                        resize: none;
                        background: @CW;
                        border: 1px solid rgba(34,34,34,0.08);
                        border-radius: 2px;
                        caret-color: @CB;
    
                        font-family: PingFangSC-Regular;
                        font-size: 14px;
                        color: @C0;
                        line-height: 22px;
                        font-weight: 400;
    
                        &::placeholder {
                            font-size: 13px;
                            line-height: 21px;
                            color: rgba(34, 34, 34, 0.4);
                        }

                        &:focus {
                            border-color: @CB;
                        }

                        &.show-error {
                            border-color: #F33E3E;
                        }
                    }
                    .validError {
                        margin-top: 4px;
                        padding: 0 12.5px;
                        box-sizing: border-box;
                        font-family: PingFangSC-Regular;
                        font-size: 13px;
                        color: #F33E3E;
                        text-align: justify;
                        line-height: 21px;
                        font-weight: 400;
                    }
                }
            }
        }
        .form-submit {
            width: 350px;
            height: 44px;
            position: absolute;
            left: 12.5px;
            bottom: calc(12px + env(safe-area-inset-bottom));
            display: flex;
            align-items: center;
            justify-content: center;
            background: @CB;
            border-radius: 2px;

            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: @CW;
            line-height: 22px;
            font-weight: 500;
        }
    }
}