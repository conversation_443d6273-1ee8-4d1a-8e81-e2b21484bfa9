import {CSSProperties, useMemo, useState, useRef} from 'react';
import cx from 'classnames';
import {message, List, Spin, Divider} from 'antd';
import InfiniteScroll from 'react-infinite-scroll-component';
import {VideoCenterTypeObj, VideoCenterDetailObj, VideoCenterQueryObj} from '@common/interface/page';
import {netService} from '@baidu/bce-services';
import {getAllParamsFromUrl} from '@baidu/bce-helper';
import {useOnMount} from '@baidu/bce-hooks';
import {urlConst} from '@common/constant/urlConst';
import {INIT_VIDEO_QUERY} from 'pages/video-center/index';
import {VIDEO_CENTER_SORT_LIST, VIDEO_CENTER_SORT_MAP} from '../VideoCenterModel';
import style from './index_m.module.less';

const defaultSubNavStyle = {
    top: '0px',
};

const NavListItem = (props: {
    curNavTabId: string;
    id: string;
    defaultValue: string;
    value: string;
    activeId: string;
    content: Array<{
        name?: string;
        value?: string;
    }>;
    handleNavClick: any;
}) => {
    const {curNavTabId, id, defaultValue, value, activeId, content, handleNavClick} = props;
    return (
        <div
            className={cx(
                curNavTabId === id && style.current,
                (value !== defaultValue) && style.selected
            )}
            onClick={() => handleNavClick(
                id,
                activeId,
                content
            )}
        >
            <span>{value}</span>
        </div>
    );
};

// eslint-disable-next-line max-statements
const VideoCenterIndexWap = (props: {
    typeList: VideoCenterTypeObj;
    videoOption: VideoCenterQueryObj;
    isPreview: boolean;
    isApp: boolean;
}) => {
    const {typeList, videoOption, isPreview, isApp} = props;
    const [videoDataList, setVideoDataList] = useState<VideoCenterDetailObj[]>([]); // 视频列表数据
    const mainBoxRef = useRef(null);
    const bodyRef = useRef<HTMLElement>();
    const [subNavStyle, setSubNavStyle] = useState<CSSProperties>(defaultSubNavStyle);

    // 筛选列表
    const subNavRef = useRef(null);
    const subNavBoxRef = useRef(null);
    const searchBoxRef = useRef(null);
    const searchInputRef = useRef(null);
    const [curNavBoxData, setCurNavBoxData] = useState(VIDEO_CENTER_SORT_LIST);
    const [showSubContent, setShowSubContent] = useState(false);
    const [curNavTabId, setCurNavTabId] = useState('');
    const [curNavContentId, setCurNavContentId] = useState('all');

    // 查询参数
    const [videoQuery, setVideoQuery] = useState<VideoCenterQueryObj>(videoOption);
    const [curClass, curTag, curType, curSort] = useMemo(() => {
        return [videoQuery.firstClassValue, videoQuery.secondClassValue, videoQuery.type, videoQuery.orderBy];
    }, [videoQuery]);

    // 加载状态
    const resetDataRef = useRef(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [hasMore, setHasMore] = useState<boolean>(true);

    // 搜索值
    const [inputValue, setInputValue] = useState<string>('');

    // 筛选数据
    const {classification, types: typeData} = typeList;
    const curTagData = useMemo(() => {
        const res = classification?.content?.filter(item => item.value === curClass);
        return {
            'name': '视频标签',
            'value': 'tag',
            'content': res?.length > 0 ? res[0].content : [],
        };
    }, [classification, curClass]);
    // 筛选下拉框数据
    const [curSortValue, setCurSortValue] = useState(() => {
        return VIDEO_CENTER_SORT_LIST.filter(i => i.value === curSort)[0].name || '综合排序';
    });
    const [curClassValue, setCurClassValue] = useState(() => {
        return (curClass === 'all' || !curClass) ? '产品类型' : classification?.content.filter(item => item.value === curClass)[0].name;
    });
    const [curTagValue, setCurTagValue] = useState(() => {
        return (curTag === 'all' || !curTag) ? '视频标签' : curTagData?.content.filter(item => item.value === curTag)[0].name;
    });
    const [curTypeValue, setCurTypeValue] = useState(() => {
        return (curType === 'all' || !curType) ? '视频分类' : typeData?.content.filter(item => item.value === curType)[0].name;
    });
    const navListData = [
        {
            id: 'sort',
            value: curSortValue,
            defaultValue: '综合排序',
            content: VIDEO_CENTER_SORT_LIST,
            activeId: curSort,
        },
        {
            id: 'class',
            value: curClassValue,
            defaultValue: '产品类型',
            content: classification?.content,
            activeId: curClass,
        },
        {
            id: 'tag',
            value: curTagValue,
            defaultValue: '视频标签',
            content: curTagData?.content,
            activeId: curTag,
        },
        {
            id: 'type',
            value: curTypeValue,
            defaultValue: '视频分类',
            content: typeData?.content,
            activeId: curType,
        },
    ];
    // 设置url
    const setUrl = (query = videoQuery) => {
        const initUrl = `${location.pathname}?belonging=${query.firstClassValue || ''}&tag=${query.secondClassValue || ''}&type=${query.type || ''}`;
        const newUrl = isPreview ? initUrl + '&status=preview' : initUrl;
        !isApp && history.pushState({newUrl, title: document.title}, document.title, newUrl);
    };

    // 重新请求数据，刷新数据
    const getVideoData = (query: VideoCenterQueryObj, notResetURL = false) => {
        if (loading) {
            return;
        }
        const params = {...videoQuery, ...query};
        setLoading(true);
        netService.post<VideoCenterQueryObj, VideoCenterDetailObj>(urlConst.GET_VIDEO_CENTER_LIST, params).then(res => {
            if (resetDataRef.current) {
                setVideoDataList(res.page.result);
            } else {
                setVideoDataList(pre => [...pre, ...res.page.result]);
            }
            setHasMore(res.page.totalCount > params.pageNo * res.page.pageSize);
            setVideoQuery(params);
            !notResetURL && setUrl(params);
        }).catch(() => {
            message.error({
                content: '网络请求错误，请稍候重试',
                style: {
                    marginTop: '60px',
                },
            });
        }).finally(() => {
            setLoading(false);
            resetDataRef.current = false;
        });
    };

    // 筛选点击事件
    const handleNavClick = (
        id: string,
        activeId: string,
        content: Array<{
            name?: string;
            value?: string;
        }>
    ) => {
        if (id === curNavTabId) {
            setShowSubContent(false);
            setCurNavTabId('');
            bodyRef.current.style.overflow = '';
            return;
        }
        setShowSubContent(true);
        setCurNavBoxData(content);
        setCurNavTabId(id);
        setCurNavContentId(activeId);
        bodyRef.current.style.overflow = 'hidden'; // 禁止body滚动事件
        setTimeout(() => {
            const activeDom = subNavBoxRef.current.querySelector(`.sort-${activeId}`);
            activeDom && activeDom.scrollIntoView(false, {behavior: 'smooth'});
        }, 50);
    };

    // 筛选下拉框点击事件
    const handleNavListClick = (item: {value?: string, name?: string}) => {
        resetDataRef.current = true;
        let newQuery = null;
        switch (curNavTabId) {
            case VIDEO_CENTER_SORT_MAP.LEVEL_ONE: {
                newQuery = {...videoQuery, firstClassValue: item.value, secondClassValue: 'all', pageNo: 1};
                setCurClassValue(() => (item.value === 'all' ? '产品类型' : item.name));
                setCurTagValue('视频标签');
                break;
            }
            case VIDEO_CENTER_SORT_MAP.LEVEL_TWO: {
                newQuery = {...videoQuery, secondClassValue: item.value, pageNo: 1};
                setCurTagValue(() => (item.value === 'all' ? '视频标签' : item.name));
                break;
            }
            case VIDEO_CENTER_SORT_MAP.TYPE: {
                newQuery = {...videoQuery, type: item.value, pageNo: 1};
                setCurTypeValue(() => (item.value === 'all' ? '视频分类' : item.name));
                break;
            }
            case VIDEO_CENTER_SORT_MAP.SORT: {
                newQuery = {...videoQuery, orderBy: item.value, pageNo: 1};
                setCurSortValue(() => (item.value === 'default' ? '综合排序' : item.name));
                break;
            }
            default:
                break;
        }
        getVideoData(newQuery);
        setShowSubContent(false);
        setCurNavTabId('');
        if (window.scrollTo) {
            window.scrollTo({
                top: 0,
                behavior: 'smooth',
            });
        } else {
            document.documentElement.scrollTop = 0;
        }
    };

    const searchSubmit = () => {
        const value = inputValue.trim();
        // 搜索一次性参数
        const searchOption = {
            ...INIT_VIDEO_QUERY,
            name: value,
        };
        searchInputRef.current.blur();
        resetDataRef.current = true;
        getVideoData(searchOption);
    };

    useOnMount(() => {
        bodyRef.current = document.body;
        getVideoData(videoQuery);
        resetDataRef.current = false;

        let headerDom: HTMLElement = null;
        let lastScrollOffset = 0;
        let lastScrollTop = 0;
        const navHeight = subNavRef.current.offsetHeight || 0;
        const cloudHeaderHeight = isApp ? 0 : 54;
        // eslint-disable-next-line complexity
        const scrollHandler = () => {
            const scrollTop = document.documentElement.scrollTop;
            if (!headerDom && !isApp) {
                headerDom = document.querySelector('header.cloud-header-wap .cloud-header-wrapper-wap');
            }
            const navTopOffset = searchBoxRef.current.getBoundingClientRect().top - navHeight;
            if (navTopOffset <= cloudHeaderHeight && navTopOffset > lastScrollOffset) {
                if (headerDom) {
                    headerDom.style.top = `${0 - Math.max(0, cloudHeaderHeight - navTopOffset)}px`;
                    headerDom.style.boxShadow = '0 2px 6px 0 rgb(171 171 171 / 30%)';
                }
                setSubNavStyle(prev => {
                    const newStyle: CSSProperties = {
                        ...defaultSubNavStyle,
                        top: `${navTopOffset}px`,
                        position: 'fixed',
                    };
                    if (JSON.stringify(prev) === JSON.stringify(newStyle)) {
                        return prev;
                    }
                    return newStyle;
                });
                searchBoxRef.current.style.marginTop = `${navHeight}px`;
            } else if (navTopOffset <= 0) {
                // 导航吸顶, 下滑时可以滑出官网导航
                if (scrollTop > lastScrollTop) {
                    lastScrollOffset = Math.max(0, lastScrollOffset - (scrollTop - lastScrollTop));
                } else {
                    lastScrollOffset = Math.min(cloudHeaderHeight, lastScrollOffset + (lastScrollTop - scrollTop));
                }
                if (headerDom) {
                    headerDom.style.top = `${0 - (cloudHeaderHeight - lastScrollOffset)}px`;
                    headerDom.style.boxShadow = lastScrollOffset === 0 ? 'none' : '0 2px 6px 0 rgb(171 171 171 / 30%)';
                }
                searchBoxRef.current.style.marginTop = `${navHeight}px`;
                setSubNavStyle(prev => {
                    const newStyle: CSSProperties = {
                        ...defaultSubNavStyle,
                        top: `${lastScrollOffset}px`,
                        position: 'fixed',
                    };
                    if (JSON.stringify(prev) === JSON.stringify(newStyle)) {
                        return prev;
                    }
                    return newStyle;
                });
            } else if (navTopOffset > cloudHeaderHeight) {
                headerDom && (headerDom.style.top = '0px');
                setSubNavStyle(prev => {
                    if (JSON.stringify(prev) === JSON.stringify(defaultSubNavStyle)) {
                        return prev;
                    }
                    return defaultSubNavStyle;
                });
                searchBoxRef.current.style.marginTop = '0px';
                lastScrollOffset = 0;
            }
            lastScrollTop = scrollTop;
        };

        const popStateHandler = () => {
            if (isApp) {
                return;
            }
            const query = getAllParamsFromUrl();
            const newQuery = {
                ...videoQuery,
                firstClassValue: query['belonging'] || 'all',
                secondClassValue: query['tag'] || 'all',
                type: query['type'] || 'all',
            };
            const classContent = classification?.content.find(item => item.value === newQuery.firstClassValue);
            const tagContent = curTagData?.content.find(item => item.value === newQuery.secondClassValue);
            const typeContent = typeData?.content.find(item => item.value === newQuery.type);
            setCurSortValue(() => VIDEO_CENTER_SORT_LIST.filter(i => i.value === newQuery.orderBy)[0].name || '综合排序');
            setCurClassValue(() => (newQuery.firstClassValue === 'all' ? '产品类型' : classContent.name));
            setCurTagValue(() => (newQuery.secondClassValue === 'all' ? '视频标签' : tagContent.name));
            setCurTypeValue(() => (newQuery.type === 'all' ? '视频分类' : typeContent.name));
            resetDataRef.current = true;
            getVideoData(newQuery, true);
        };

        window.addEventListener('scroll', scrollHandler);
        window.addEventListener('popstate', popStateHandler); // 监听回退事件

        return () => {
            window.removeEventListener('scroll', scrollHandler);
            window.removeEventListener('popstate', popStateHandler);
        };
    });

    return (
        <div className={style.main} ref={mainBoxRef}>
            <div className={style['video-banner']}>
                <div className={style['content-box']}>
                    <h1>视频中心</h1>
                    <p className={style.desc}>“适合跑AI的云”和“懂场景的AI”，共同构成智能时代基础设施</p>
                </div>
            </div>
            <div className={style['sub-nav']} ref={subNavRef} style={subNavStyle}>
                <div className={style['sub-nav-box']} ref={subNavBoxRef}>
                    <div className={style['sub-select']}>
                        {
                            navListData && navListData.map((item, index) => (
                                <NavListItem
                                    // eslint-disable-next-line react/no-array-index-key
                                    key={index}
                                    curNavTabId={curNavTabId}
                                    id={item.id}
                                    defaultValue={item.defaultValue}
                                    activeId={item.activeId}
                                    content={item.content}
                                    handleNavClick={handleNavClick}
                                    value={item.value}
                                />
                            ))
                        }
                    </div>
                    {
                        showSubContent && (
                            <div
                                className={style['sub-content']}
                                onClick={e => {
                                    e.stopPropagation();
                                    setShowSubContent(false);
                                    setCurNavTabId('');
                                    bodyRef.current.style.overflow = '';
                                }}
                            >
                                <div className={style['sort-list']}>
                                    {
                                        curNavBoxData?.length > 0 ? curNavBoxData?.map((s, index) => (
                                            <div
                                                // eslint-disable-next-line react/no-array-index-key
                                                key={index}
                                                data-track-category="视频中心首页"
                                                data-track-name="视频筛选"
                                                data-track-value={`筛选：${curNavTabId}-${s.name}`}
                                                className={cx(style['sort-item'], `sort-${s.value}`, curNavContentId === s.value && style.active)}
                                                onClick={() => handleNavListClick(s)}
                                            >
                                                {s.name}
                                            </div>
                                        )) : (
                                            <div
                                                data-track-category="视频中心首页"
                                                data-track-name="视频筛选"
                                                data-track-value={`筛选：${curNavTabId}-全部`}
                                                className={cx(style['sort-item'], 'sort-all', style.active)}
                                            >
                                                全部
                                            </div>
                                        )
                                    }
                                </div>
                            </div>
                        )
                    }
                </div>
            </div>
            <div className={style['search-box']} ref={searchBoxRef}>
                <form
                    onSubmit={e => {
                        e.preventDefault();
                        searchSubmit();
                        return false;
                    }}
                    action=""
                >
                    <input
                        type="search"
                        placeholder="搜索您感兴趣的课程"
                        onChange={e => setInputValue(e.target.value)}
                        ref={searchInputRef}
                    />
                </form>
                <span className={style['search-icon']} onClick={searchSubmit}></span>
            </div>
            <div className={cx(style['video-box'])}>
                {
                    videoDataList.length > 0 ? (
                        <InfiniteScroll
                            className={style['scroll-box']}
                            dataLength={videoDataList.length}
                            next={() => {
                                getVideoData({
                                    ...videoQuery,
                                    pageNo: Number(videoQuery.pageNo) + 1,
                                });
                            }}
                            hasMore={hasMore}
                            loader={<div style={{display: 'flex', justifyContent: 'center'}}><Spin size="large" /></div>}
                            endMessage={<Divider plain>内容已加载完成</Divider>}
                        >
                            <List
                                dataSource={videoDataList}
                                renderItem={(video, index) => (
                                    <List.Item key={index}>
                                        <div className={cx(style.video, style[`video-${video.id}`], style[`video-index-${index}`])}>
                                            <a
                                                href={`/video-center/video/${video.id}`}
                                                data-track-category="视频中心首页"
                                                data-track-name="视频列表"
                                                data-track-value={`视频：${video.name}`}
                                                target="_blank"
                                            >
                                                <img src={video.imgUrl} />
                                                <p className={style['sub-desc']}>{video.name}</p>
                                            </a>
                                        </div>
                                    </List.Item>
                                )}
                            />
                        </InfiniteScroll>
                    ) : (
                        <div className={style['no-result']}>
                            <i></i>
                            <p>抱歉暂无匹配搜索内容，<br />可以修改搜索内容再试试。</p>
                        </div>
                    )
                }
            </div>
        </div>
    );
};

export default VideoCenterIndexWap;
