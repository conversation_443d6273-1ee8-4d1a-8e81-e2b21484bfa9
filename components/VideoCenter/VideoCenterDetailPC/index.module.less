@import '@styles/variables.less';
.main {
    background: #1A1C1C;
    color: #999;
}

.crumb {
    height: 57px;
    border-top: 1px solid rgba(255, 255, 255, .1);
    border-bottom: 1px solid rgba(255, 255, 255, .1);
    font-size: 14px;
    padding: 0;
    line-height: 55px;

    a {
        color: #999;
        text-decoration: none;

        &:hover {
            color: @CW;
        }
    }

    ul {
        width: 1180px;
        margin: 0 auto;
    }

    li {
        float: left;
    }

    .title-crumb {
        &::before {
            content: '>';
            margin: 0 10px;
        }
    }
}

.video-box {
    width: 1180px;
    margin: 0 auto;
    padding-bottom: 0;

    .video-main {
        margin: 40px 0 0 0;
        width: 730px;
        float: left;
    }

    .video-center-main-box {
        height: 462px;
        position: relative;
    }

    #playercontainer {
        height: 100%;
        background: #2A2F33;
    }
    :global {
        .jw-preview {
            background-color: transparent;
        }
    }

    .video-links {
        margin-top: 30px;

        li {
            display: inline-block;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            padding: 0 10px;
            background: #2A2F33;
            min-width: 40px;
            margin-right: 5px;
            vertical-align: top;

            &:last-child {
                margin-right: 0;
            }

            &.views {
                display: inline-flex;
                align-items: center;
            }
        }

        .iconfont {
            margin-right: 10px;
            display: inline-block;
        }

        .like.like-pc .like-box, .helpless {
            cursor: pointer;
            position: relative;
            display: inline-flex;
            align-items: center;
            &:hover {
                background: #108CEE;
                color: @CW;
            }

            &.active {
                background: #108CEE;
                color: @CW;
            }
        }
        .like.like-pc {
            position: relative;
            padding: 0;

            .like-box {
                padding: 0 10px;
            }

            .help-bubble {
                opacity: 0;
                position: absolute;
                top: -46px;
                left: 50%;
                transform: translateX(-50%);
                padding: 12px;
                background: @CB;
                box-shadow: 0px 2px 8px 0px rgba(7,12,20,0.12);
                border-radius: 4px;
                transition: all 0.3s ease-out;
                cursor: pointer;
    
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: @CW;
                line-height: 24px;
                font-weight: 400;
                white-space: nowrap;
    
                &::after {
                    content: '';
                    position: absolute;
                    bottom: -6px;
                    left: 50%;
                    width: 12px;
                    height: 6px;
                    transform: translateX(-50%);
                    background: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_meassage_arrow_9dd609f.png') no-repeat center/cover;
                }
    
                &.active {
                    opacity: 1;
                    top: -58px;
                }
            }
        }
        .helpless {
            > span:first-child {
                transform: rotate(180deg);
            }
        }
    }
}

.video-info {
    margin-left: 800px;
    width: 380px;
    background: #15171a;
    height: 606px;

    .info {
        height: 210px;
        box-sizing: border-box;
        padding: 30px;
        position: relative;
        background: #2A2F33;

        h1 {
            font-size: 20px;
            color: @CW;
            margin: 0;
            line-height: 20px;
        }

        p {
            color: #999;
            font-size: 12px;
            line-height: 20px;
            margin: 10px;
        }

        .type-box {
            position: absolute;
            bottom: 20px;

            a {
                margin-right: 10px;
                margin-top: 5px;
                display: inline-block;
            }
        }

        .info-type,
        .info-tag {
            display: inline-block;
            height: 28px;
            line-height: 28px;
            padding: 0 10px;
            background: rgba(0,0,0,0.20);
            font-size: 12px;
            color: rgba(255, 255, 255, .5);
        }
    }

    .other {
        background: #15171A;

        .title-box {
            padding: 15px 30px;
            overflow: hidden;

            span {
                line-height: 24px;
                font-size: 12px;
                display: block;
                float: left;
                color: rgba(255, 255, 255, .25);
                box-sizing: border-box;
            }

            .more {
                float: right;
                padding: 0 10px;
                background: #222526;
                font-size: 12px;
                color: rgb(255, 255, 255);
                cursor: pointer;
                opacity: 0.5;

                // &:hover {
                //     background: #108CEE;
                //     opacity: 0.1;
                // }
            }
        }

        .video-other {
            li {
                margin-top: 10px;
                overflow: hidden;
                
                &:first-child {
                    margin-top: 0;
                }

                a {
                    text-decoration: none;
                    display: block;
                }
            }

            .other-video-item-box {
                width: 320px;
                height: 90px;
                margin: auto;
                background-color: #1b1e21;

                &:hover {
                    box-shadow: 0 2px 8px rgba(1, 0, 0, .1);

                    h2 {
                        opacity: 1;
                    }
                }
            }

            .img-box {
                float: left;
                
                img {
                    width: 160px;
                    height: 90px;
                }
            }

            .other-info {
                margin-left: 160px;
                padding: 20px;
                box-sizing: border-box;
            }

            h2 {
                font-size: 12px;
                margin: 0;
                color: @CW;
                line-height: 20px;
                opacity: 0.6;
                transition: all ease-out 0.1s;
            }

            P {
                font-size: 12px;
                margin: 10px 0 0 0;
                line-height: 14px;
                color: @CW;
            }
        }
    }
}

.links {
    background: @CW;
    padding: 50px 0 70px 0;

    .group {
        width: 1180px;
        margin: 0 auto;
        color: #666;
    }

    h3 {
        color: #333;
        font-size: 24px;
        line-height: 24px;
        margin: 0;

        &::before {
            content: '';
            width: 4px;
            height: 24px;
            display: inline-block;
            background: #108CEE;
            margin-right: 10px;
            vertical-align: middle;
            position: relative;
            top: -2px;
        }
    }

    .desc {
        margin: 30px 0 0 0;
        font-size: 14px;
        line-height: 26px;
    }

    .apply-box {
        margin: 30px 0 0 0;

        li {
            float: left;
            position: relative;
            margin-left: 30px;
            height: 50px;
            width: 188px;
            font-size: 16px;

            &:first-child {
                margin-left: 0;
            }
        }

        a {
            display: block;
            border: 1px solid #C9C9C9;
            color: #666;
            text-decoration: none;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
            line-height: 48px;
            text-align: center;

            &:hover {
                background: #108CEE;
                color: @CW;
                border-color: #108CEE;
            }
        }
    }
}

.feedback-mask {
    z-index: 2;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    background: rgba(7, 12, 20, 0.5);

    .feedback-form {
        box-sizing: border-box;
        width: 800px;
        padding: 24px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: @CW;
        border-radius: 6px;

        .title {
            position: relative;
            height: 28px;
            font-family: PingFangSC-Medium;
            font-size: 18px;
            color: #151B26;
            line-height: 28px;
            font-weight: 500;

            .feedback-close {
                position: absolute;
                top: 2px;
                right: 0;
                width: 24px;
                height: 24px;
                background: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_close_5f14e89.png') center/cover;
                background-position: -29px 0;
                cursor: pointer;

                &:hover {
                    background-position: 0px 0px;
                }
            }
        }

        .form-row {
            position: relative;
            box-sizing: border-box;
            margin-top: 24px;
            padding-left: 94px;
            width: 100%;

            .form-name {
                position: absolute;
                top: 0;
                left: 0;
                max-width: 70px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: rgba(34, 34, 34, 0.7);
                line-height: 24px;
                font-weight: 400;
            }

            .form-detail {
                width: 100%;
                font-size: 0;

                label {
                    position: relative;
                    display: inline-flex;
                    align-items: center;
                    height: 24px;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: #222222;
                    line-height: 24px;
                    font-weight: 400;
                    padding-right: 20px;
                    cursor: pointer;

                    &:hover {
                        span {
                            background-position: 0 0;
                        }
                    }

                    input[type="checkbox"] {
                        position: absolute;
                        top: 4px;
                        left: 0;
                        width: 16px;
                        height: 16px;
                        border-radius: 2px;
                        margin: 0;
                        cursor: pointer;
                        opacity: 0;

                        &:checked {
                            border: none;
                            background-color: @CB;


                            & + span::before {
                                background-position: -36px 0;
                            }
                        }
                    }

                    span {
                        padding-left: 24px;
                        position: relative;

                        &::before {
                            content: '';
                            position: absolute;
                            left: 0;
                            top: 4px;
                            width: 16px;
                            height: 16px;
                            background: url('https://bce.bdstatic.com/p3m/common-service/uploads/checkbox-icon_013d5be.png') no-repeat;
                            background-size: auto 16px;
                            background-position: -18px 0;
                        }
                        &:hover::before {
                            background-position: 0 0;
                        }
                    }
                }

                textarea {
                    box-sizing: border-box;
                    width: 100%;
                    height: 120px;
                    outline: none;
                    padding: 6px 12px;;
                    margin: 0;
                    resize: none;
                    background: @CW;
                    border: 1px solid rgba(34,34,34,0.08);
                    border-radius: 4px;
                    caret-color: @CB;

                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: #222222;
                    text-align: justify;
                    line-height: 24px;
                    font-weight: 400;

                    &::placeholder {
                        color: rgba(34, 34, 34, 0.4);
                    }

                    &:focus {
                        border-color: @CB;
                    }
                }

                .validError {
                    color: red;
                    font-size: 14px;
                    line-height: 24px;
                }
            }

            .cancel-btn, .submit-btn {
                cursor: pointer;
                float: right;
                box-sizing: border-box;
                height: 32px;
                padding: 4px 22px;
                margin-left: 16px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                text-align: center;
                line-height: 24px;
                font-weight: 400;
                border-radius: 4px;
            }
            .cancel-btn {
                color: @C0;
                background: @CW;
                border: 1px solid rgba(34,34,34,0.08);
                line-height: 22px;
                &:hover {
                    border-color: @CB;
                    color: @CB;
                }
            }
            .submit-btn {
                color: @CW;
                background: @CB;
                &:hover {
                    background: #528EFF;
                }
            }
        }
    }

    &.active {
        display: block;
        z-index: 10000;
    }
}
