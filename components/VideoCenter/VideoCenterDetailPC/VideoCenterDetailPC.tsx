import {useRef, useState} from 'react';
import {message} from 'antd';
import cx from 'classnames';
import {netService} from '@baidu/bce-services';
import {useOnMount} from '@baidu/bce-hooks';
import {VideoCenterDetailObj} from '@common/interface/page';
import {urlConst} from '@common/constant/urlConst';
import {Cyberplayer} from '@common/components/Cyberplayer/Cyberplayer';
import {getAntiParam} from '@common/helper/page';
import {CyberplayerApi} from '@common/interface/common';
import {linksData, QuestionTypeMap} from '../VideoCenterModel';
import style from './index.module.less';

const VideoCenterDetailPC = (props: {
    id: number;
    videoDetail: VideoCenterDetailObj;
    otherVideo: VideoCenterDetailObj[];
}) => {
    const {id, videoDetail, otherVideo} = props;
    const {secondClassValue = '', typeName = '', secondClassName = '', firstClassName = ''} = videoDetail;

    const cyberPlayerRef = useRef<CyberplayerApi>(null);
    const [isViewed, setIsViewed] = useState(false);
    const [showbubble, setShowBubble] = useState(false);
    const [likeNum, setLikeNum] = useState(videoDetail.likes || 0);
    const [isLiked, setIsLiked] = useState(false);
    const [isDisliked, setIsDisliked] = useState(false);
    const [userId, setUserId] = useState<number>(null);

    // 反馈表单数据
    const [showFeedback, setShowFeedback] = useState(false);
    const [questionTypeList, setQuestionTypeList] = useState<string[]>([]);
    const [suggestValue, setSuggestValue] = useState<string>('');
    const [validError, setValidError] = useState(false);

    const handleVideoPlay = () => {
        if (!isViewed) {
            netService.post<{id: number}, number>(urlConst.POST_VIDEO_DETAIL_VIEWED, {id: id}).then(() => {
                setIsViewed(true);
            });
        }
    };

    // 初始化是否展示帮助引导
    let bubbleTimer: NodeJS.Timeout = null;
    const handleBubble = () => {
        const storageData: string = localStorage.getItem('disableVideoHelpBubble');
        if (storageData && new Date().getTime() <= parseInt(storageData, 10)) {
            return;
        }
        setShowBubble(true);
        bubbleTimer = setTimeout(() => {
            setShowBubble(false);
        }, 5000);
    };
    const handleBubbleClose = () => {
        bubbleTimer && clearTimeout(bubbleTimer);
        setShowBubble(false);
        localStorage.setItem('disableVideoHelpBubble', (new Date().getTime() + 24 * 60 * 60 * 1000).toString());
    };

    const handleLikeClick = () => {
        if (isLiked) {
            return;
        }
        netService.post<{id: number}, number>(urlConst.POST_VIDEO_DETAIL_LIKES, {id: id}).then(() => {
            setLikeNum(likeNum + 1);
            setIsLiked(true);
        }).catch(() => {
            message.error('网络请求失败，请稍后再试');
        });
    };

    const handleDislikeClick = () => {
        setShowFeedback(true);
        if (isDisliked) {
            return;
        }
        netService.post<{id: number}, number>(urlConst.POST_VIDEO_DETAIL_DISLIKES, {id: id}).then(() => {
            setIsDisliked(true);
        });
    };
    // 反馈表单处理
    const closeFeedback = () => setShowFeedback(false);
    const submitHandle = async () => {
        const suggest = suggestValue.trim();
        if (suggest.length === 0) {
            setValidError(true);
            return;
        }
        // 表单数据格式化
        const params = {
            id: userId,
            feedbackType: questionTypeList.join(','),
            feedback: suggest,
        };
        netService.put<{
            id: number;
            feedbackType: string;
            feedback: string;
        }, boolean>(urlConst.POST_VIDEO_DETAIL_USERINFO, params, null as never, {
            headers: typeof window === 'undefined' ? {
                'Content-Type': 'application/json',
            } : {
                'Content-Type': 'application/json',
                'Acs-Token': await getAntiParam(),
            },
        }).then(response => {
            if (response.success && response.result) {
                // 重置表单
                setQuestionTypeList([]);
                setSuggestValue('');
                closeFeedback();
                message.success('感谢您的反馈意见');
            }
            else {
                message.error('网络请求失败，请稍后再试');
            }
        }).catch(() => {
            message.error('网络请求失败，请稍后再试');
        });
    };
    const toggleCheckedList = (typeC: string) => {
        if (questionTypeList.includes(typeC)) {
            setQuestionTypeList(pre => pre.filter(q => q !== typeC));
        } else {
            setQuestionTypeList(pre => [...pre, typeC]);
        }
    };
    const updateSuggestText = (value: string) => {
        setSuggestValue(value);
        setValidError(false);
    };

    useOnMount(() => {
        handleBubble();
        if (!userId) {
            // 初始化进入页面，请求获取页面id
            netService.post<{videoName: string}, number>(
                urlConst.POST_VIDEO_DETAIL_USERINFO,
                {videoName: `${firstClassName}-${secondClassName}-${videoDetail.name}`}
            ).then(response => {
                if (response.success && response.result) {
                    // 存储用户id
                    setUserId(response.result);
                }
            });
        }
    });

    return (
        <div className={style.main}>
            <div className={cx(style.crumb, 'clearfix')}>
                <ul>
                    <li
                        className={style['first-crumb']}
                        data-track-category="视频中心详情页"
                        data-track-name="面包屑导航"
                        data-track-value={`视频中心-${typeName}`}
                    >
                        <a href="/video-center/index.html">视频中心</a>
                    </li>
                    <li className={style['title-crumb']}>{videoDetail.name}</li>
                </ul>
            </div>

            <div className={style['video-box']}>
                <div className={style['video-main']}>
                    <div
                        className={style['video-center-main-box']}
                        data-track-category="视频中心详情页"
                        data-track-name="视频播放"
                        data-track-value={`视频：${videoDetail.name}`}
                    >
                        <Cyberplayer
                            onload={player => {
                                cyberPlayerRef.current = player;
                                player.onPlay(handleVideoPlay);
                            }}
                            initSdk
                            config={{
                                controlbar: {
                                    barLogo: false,
                                },
                                file: videoDetail.src,
                                image: null,
                                controls: true,
                                ak: '468b8de7f7e84fb8ba5aa60c38b9edb0',
                            }}
                        />
                    </div>
                    <div className={style['video-links']}>
                        <ul>
                            <li
                                className={style.views}
                            >
                                <span className={cx(style.iconfont, 'iconfont', 'icon-view')}></span>
                                播放量：<span className={style['views-number']}>{videoDetail.views}</span>
                            </li>

                            <li
                                data-track-category="视频中心详情页"
                                data-track-name="按钮：点赞喜欢"
                                data-track-value={`视频：${videoDetail.name}`}
                                className={cx(style.like, style['like-pc'])}
                            >
                                <div
                                    className={cx(style['like-box'], isLiked && style.active)}
                                    onClick={handleLikeClick}
                                >
                                    <span className={cx(style.iconfont, 'iconfont', 'icon-like')}></span>
                                    <span className={style['like-number']}>{likeNum}</span>
                                </div>
                                <div
                                    className={cx(style['help-bubble'], showbubble && style.active)}
                                    onClick={handleBubbleClose}
                                >
                                    快来反馈此视频是否对您有帮助吧
                                </div>
                            </li>
                            <li
                                data-track-category="视频中心详情页"
                                data-track-name="按钮：无帮助"
                                data-track-value={`视频：${videoDetail.name}`}
                                className={style.helpless}
                                onClick={handleDislikeClick}
                            >
                                <span className={cx(style.iconfont, 'iconfont', 'icon-like')}></span>
                                <span>无帮助</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div className={style['video-info']}>
                    <div className={style.info}>
                        <h1>{videoDetail.name}</h1>
                        <p>{videoDetail.videoDesc}</p>
                        <div className={style['type-box']}>
                            {
                                secondClassName && secondClassName.split(',').map((item, index) => {
                                    return (
                                        <a
                                            key={index.toString()}
                                            data-track-category="视频中心详情页"
                                            data-track-name={videoDetail.name}
                                            data-track-value={`视频标签：${item}`}
                                            href={`/video-center/index.html?tag=${secondClassValue.split(',')[index]}`}
                                            target="_blank"
                                        >
                                            <span className={style['info-tag']} data-class="tag" data-value="${item}">{item}</span>
                                        </a>
                                    );
                                })
                            }
                        </div>
                    </div>
                    <div className={style.other}>
                        <div className={cx(style['title-box'], 'clearfix')}>
                            <span>相关视频</span>
                            <a
                                data-track-category="视频中心详情页"
                                data-track-name="链接：查看更多"
                                data-track-value={`视频：${videoDetail.name}-查看更多`}
                                href="/video-center/index.html"
                                target="_blank"
                            >
                                <span className={style.more}>查看更多 &gt;</span>
                            </a>
                        </div>
                        <div className={style['video-other']}>
                            <ul>
                                {otherVideo?.map((d, index) => (
                                    <li
                                        // eslint-disable-next-line react/no-array-index-key
                                        key={index}
                                        className={cx(style['other-video-item'], 'clearfix')}
                                        data-id={d.id}
                                    >
                                        <a
                                            data-track-category="视频中心详情页"
                                            data-track-name={`链接：${videoDetail.name}-相关视频`}
                                            data-track-value={`视频：${d.name}`}
                                            href={`/video-center/video/${d.id}`}
                                            target="_blank"
                                        >
                                            <div className={cx(style['other-video-item-box'], 'clearfix')}>
                                                <div className={style['img-box']}><img src={d.imgUrl} /></div>
                                                <div className={style['other-info']}><h2>{d.name}</h2></div>
                                            </div>
                                        </a>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div className={style.links}>
                {
                    linksData && (
                        <div className={style.group}>
                            <h3>{linksData.title || ''}</h3>
                            <p className={style.desc}>
                                {linksData.desc || ''}
                            </p>
                            <div className={cx(style['apply-box'], 'clearfix')}>
                                <ul>
                                    {linksData.apply.map((apply, index) => {
                                        return (
                                            // eslint-disable-next-line react/no-array-index-key
                                            <li key={index}>
                                                <a
                                                    data-track-category="视频中心详情页"
                                                    data-track-name={`链接：${videoDetail.name}-相关视频`}
                                                    data-track-value={`${linksData.title}：${apply.name}`}
                                                    href={apply.link}
                                                    target="_blank"
                                                >
                                                    {apply.name}
                                                </a>
                                            </li>
                                        );
                                    })}
                                </ul>
                            </div>
                        </div>
                    )
                }
            </div>

            {showFeedback && (
                <div className={style['feedback-mask']}>
                    <form className={style['feedback-form']}>
                        <div className={style.title}>视频反馈建议<span className={style['feedback-close']} onClick={closeFeedback}></span></div>
                        <div className={style['form-row']}>
                            <div className={style['form-name']}>问题类型：</div>
                            <div className={style['form-detail']} id="feedback-questionType">
                                {QuestionTypeMap?.map((typeC, index) => (
                                    // eslint-disable-next-line react/no-array-index-key
                                    <label key={index}>
                                        <input
                                            type="checkbox"
                                            value={typeC}
                                            name="questionType"
                                            onChange={() => toggleCheckedList(typeC)}
                                        />
                                        <span>{typeC}</span>
                                    </label>
                                ))}
                            </div>
                        </div>
                        <div className={style['form-row']}>
                            <div className={style['form-name']}>更多建议：</div>
                            <div className={style['form-detail']} id="feedback-suggest">
                                <textarea
                                    name="suggest"
                                    placeholder="请您输入反馈意见，帮助我们精确定位即解决你的问题"
                                    value={suggestValue}
                                    onChange={e => updateSuggestText(e.target.value)}
                                >
                                </textarea>
                                {validError && <div className={style.validError}>反馈意见不能为空</div>}
                            </div>
                        </div>
                        <div className={cx(style['form-row'], 'clearfix')}>
                            <div className={style['submit-btn']} onClick={submitHandle}>提交</div>
                            <div className={style['cancel-btn']} onClick={closeFeedback}>取消</div>
                        </div>
                    </form>
                </div>
            )}
        </div>
    );
};

export default VideoCenterDetailPC;
