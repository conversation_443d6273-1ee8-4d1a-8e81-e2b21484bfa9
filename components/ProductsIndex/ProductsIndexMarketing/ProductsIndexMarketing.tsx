/**
 * @file 产品聚合页营销位
 */

import {CmsBtnObj} from '@common/interface/common';
import {sendMonitor} from '@common/helper/page';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {UFeMonitorExposure} from '@common/components/UFeMonitorExposure/UFeMonitorExposure';
import styles from './main.module.less';

const CardCompt = (props: {item: ProductMarketCardObj}) => {
    const {item} = props;
    return (
        <>
            <div className={styles.title}>
                <span className={styles.icon} style={{backgroundImage: `url(${item.icon})`}}></span>
                <h3>{item.title}</h3>
                {item.tag && <span className={styles.tag}>{item.tag}</span>}
            </div>
            <div className={styles.desc}>{item.desc}</div>
            <span>{item.btnText}</span>
        </>
    );
};

export const ProductsIndexMarketing = (
    {marketData}: {marketData: ProductsMarketDataObj}
) => {
    const {list} = marketData;
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const openNewLink = (item: ProductMarketCardObj, isFromModal = true) => {
        sendMonitor({
            category: '产品聚合页营销位',
            name: item.title,
            value: `${item.btnText}-跳转${isFromModal ? '-登录成功-已激活' : ''}`,
        });
        window.open(item.link, '_blank');
    };
    const clickHandler = (item: ProductMarketCardObj) => {
        if (userinfo?.hasLogin) {
            openNewLink(item, false);
        } else {
            verifyHandler().then(() => openNewLink(item));
        }
    };

    return (
        <div className={styles['marketing-position']}>
            <UFeMonitorExposure category="产品聚合页营销位" name="营销位" value="营销位">
                <ul className={styles.list}>
                    {
                        list?.map(item => item && (
                            <li key={item.title}>
                                {item.isNeedLogin ? (
                                    <div
                                        className={styles['card-wrapper']}
                                        data-track-action="click"
                                        data-track-category="产品聚合页营销位"
                                        data-track-name={item.title}
                                        data-track-value={item.btnText}
                                        onClick={() => clickHandler(item)}
                                    >
                                        <CardCompt item={item} />
                                    </div>
                                ) : (
                                    <a
                                        className={styles['card-wrapper']}
                                        href={item.link}
                                        target="_blank"
                                        data-track-category="产品聚合页营销位"
                                        data-track-name={item.title}
                                        data-track-value={item.btnText}
                                    >
                                        <CardCompt item={item} />
                                    </a>
                                )}
                            </li>
                        ))
                    }
                </ul>
            </UFeMonitorExposure>
        </div>
    );
};

interface ProductMarketCardObj {
    title: string;
    icon: string;
    tag?: string;
    desc: string;
    btnText: string;
    link: string;
    isNeedLogin?: boolean;
}
export interface ProductsMarketDataObj {
    list: ProductMarketCardObj[];
    hotProduct: {
        title: string;
        list: CmsBtnObj[];
    };
}
