
@import '../../../styles/variables.less';
@import '../../../styles/mixin.less';

.marketing-position {
    width: 100%;
    height: 164px;
    position: absolute;
    left: 0;
    bottom: -138px;
    .list {
        display: flex;
        height: 100%;
        > li {
            position: relative;
            top: 0;
            width: calc(25% - 15px);
            &:not(:first-child) {
                margin-left: 20px;
            }
            padding: 24px;
            background: @CW;
            box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
            border-radius: 4px;
            transition: top 0.3s ease-out;

            .card-wrapper {
                display: block;
                width: 100%;
                height: 100%;
                cursor: pointer;

                .title {
                    display: flex;
                    align-items: center;
                    .icon {
                        width: 24px;
                        height: 24px;
                        margin-right: 8px;
                        background-repeat: no-repeat;
                        background-size: auto 24px;
                        background-position: 0 0;
                    }
                    h3 {
                        font: 500 18px / 28px PingFangSC-Medium;
                        color: @C0;
                    }
                    .tag {
                        margin-left: 12px;
                        box-sizing: border-box;
                        padding: 2px 8px;
                        background: #F33E3E;
                        border-radius: 2px;
                        font-family: PingFangSC-Semibold;
                        font-size: 12px;
                        color: #FFFFFF;
                        line-height: 20px;
                        font-weight: 600;
                    }
                }
    
                .desc {
                    margin: 8px 0;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: @C7;
                    text-align: justify;
                    line-height: 24px;
                    font-weight: 400;
                    .ellipsis2(2);
                }

                > span {
                    position: relative;
                    font-family: PingFangSC-Medium;
                    font-size: 14px;
                    color: @CB;
                    text-align: center;
                    line-height: 24px;
                    font-weight: 500;

                    &::after {
                        content: '';
                        position: absolute;
                        width: 16px;
                        height: 16px;
                        right: -20px;
                        top: 50%;
                        transform: translateY(-50%);
                        background: url(https://bce.bdstatic.com/p3m/common-service/uploads/arrow_314baff.png) no-repeat;
                        background-position: 0 0;
                        background-size: auto 16px;
                        transition: right 0.3s ease-out;
                    }

                    &:hover {
                        &::after {
                            right: -28px;
                        }
                    }
                }
            }

            &:hover {
                top: -12px;
                .card-wrapper {
                    .title {
                        display: flex;
                        align-items: center;
                        span {
                            background-position: -29px 0;
                        }
                        h3 {
                            color: @CB;
                        }
                    }
                }
                &::after {
                    content: "";
                    position: absolute;
                    left: 0;
                    right: 0;
                    bottom: -12px;
                    width: 100%;
                    height: 12px;
                    background: transparent;
                }
            }
        }
    }
}