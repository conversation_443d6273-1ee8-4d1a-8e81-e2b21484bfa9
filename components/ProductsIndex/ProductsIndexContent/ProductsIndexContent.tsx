/**
 * @file 产品聚合页内容部分
 * <AUTHOR>
 */

import {isMobile} from '@baidu/bce-helper';
import cn from 'classnames';
import {UStickyContainer} from '@common/components/UStickyContainer/UStickyContainer';
import {getHeaderNavHeight, sendMonitor} from '@common/helper/page';
import {useOnMount, useOnUpdate} from '@baidu/bce-hooks';
import {useScrollBind} from '@common/hooks/useScrollBind';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {CSSProperties, useRef, useState, useMemo} from 'react';
import styles from './main.module.less';

const bottomDistance = 414;
const fixTop = 20;
const navLiHeight = 24;
const navPadding = 24;

const DEFAULT_DOWNLOAD_LINK = 'https://cloud.baidu.com/survey/business-guide.html'; // 默认下载链接
const ProductsListModule = (
    {categories}: {categories: ProductsIndexProItemObj}
) => {
    const [current, setCurrent] = useState(0);
    const contentRef = useRef<HTMLDivElement>(null);

    const handleClick = (index: number) => {
        setCurrent(index);
        if (contentRef.current) {
            const element = contentRef.current.children[index];
            element?.scrollIntoView({behavior: 'smooth'});
        }
    };
    return (
        <>
            <ul className={styles.category}>
                {
                    categories.list?.map((cateItem, cateIndex) => (
                        <li
                            className={cateIndex === current ? styles.current : ''}
                            key={cateItem.type}
                            onClick={() => handleClick(cateIndex)}
                        >
                            {cateItem.type}
                        </li>
                    ))
                }
            </ul>
            <div className={styles['products-list']} ref={contentRef}>
                {
                    categories.list?.map((cateItem, cateIndex) => (
                        <div className={cateIndex === current ? styles.current : ''} key={cateItem.type}>
                            {
                                cateItem.link ? (
                                    <a
                                        target="_blank"
                                        className={cn(styles.subTitle, styles.link)}
                                        href={cateItem.link}
                                        data-track-category="产品列表区"
                                        data-track-name={`${categories.category}-${cateItem.type}`}
                                        data-track-value={cateItem.type}
                                    >
                                        {cateItem.type}
                                    </a>
                                ) : <div className={styles.subTitle}>{cateItem.type}</div>
                            }
                            <div key={cateItem.type} className={styles['sub-product-box']}>
                                {
                                    cateItem.products.map(product => (
                                        <div
                                            key={product.name}
                                            className={styles['sub-product']}
                                        >
                                            <a
                                                href={product.link}
                                                target="_blank"
                                                data-track-category="产品列表区"
                                                data-track-name={`${categories.category}-${categories.list[cateIndex].type}`}
                                                data-track-value={product.name}
                                            >
                                                <div className={styles['title-wrapper']}>
                                                    <h4>{product.name}</h4>
                                                    {
                                                        product.tag && (
                                                            // eslint-disable-next-line max-len
                                                            <span className={`${styles.tag} ${product.tag === '公测中' ? styles.gongcezhong : ''}`}>
                                                                {product.tag.toUpperCase()}
                                                            </span>
                                                        )
                                                    }
                                                </div>
                                                <p>{product.desc}</p>
                                            </a>
                                        </div>
                                    ))
                                }
                            </div>
                        </div>
                    ))
                }
            </div>
        </>
    );
};

export const ProductsIndexContent = (
    {newProductsList}: {newProductsList: ProductsIndexProObj[]}
) => {
    const [current, setCurrent] = useScrollBind({
        itemSelector: '#productsContent .product-content-item',
        fixTop: -100,
    });
    const [leftNavFixedStyle, setLeftNavFixedStyle] = useState<CSSProperties>({});
    const iscrollRef = useRef(null);
    const liDomRef = useRef(null);
    const navLength = useMemo(() => {
        const res = newProductsList.reduce((accumulator: number[], currentItem, currentIndex) => {
            if (currentIndex === 0) {
                accumulator.push(0);
            } else {
                const preItem = newProductsList[currentIndex - 1];
                const preLength = preItem.categories.length;
                const preSum = accumulator[currentIndex - 1] + preLength;
                accumulator.push(preSum); // 将前面元素的长度总和添加到新数组中
            }
            return accumulator;
        }, []);
        return res;
    }, [newProductsList]);

    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const openNewLink = (isFromModal = true) => {
        sendMonitor({
            category: '主标题',
            name: '副按钮',
            value: `下载百度智能云产品手册-跳转${isFromModal ? '-登录成功-已激活' : ''}`,
        });
        window.open(DEFAULT_DOWNLOAD_LINK, '_blank');
    };
    const clickHandler = () => {
        if (userinfo?.hasLogin) {
            openNewLink(false);
        } else {
            verifyHandler().then(() => openNewLink());
        }
    };

    // 吸顶底部判断
    const leftNavEndHandle: (scrollTop: number) => void = scrollTop => {
        // 如果是移动端的话下边的逻辑都不需要走了
        if (isMobile()) {
            return;
        }
        const contentHeight = document.getElementById('__next')?.offsetHeight || 0;
        if (!contentHeight) {
            return;
        }
        // 到了最底部要脱离吸顶
        const newHeight = Math.min(liDomRef.current?.offsetHeight, window.innerHeight);
        if (scrollTop > contentHeight + getHeaderNavHeight(window.innerWidth) - bottomDistance - newHeight) {
            setLeftNavFixedStyle(prev => {
                const newStyle: CSSProperties = {
                    top: contentHeight + getHeaderNavHeight(window.innerWidth) - bottomDistance + fixTop - newHeight,
                    position: 'absolute',
                };
                if (JSON.stringify(prev) === JSON.stringify(newStyle)) {
                    return prev;
                }
                return newStyle;
            });
        } else { // 离开底部要继续吸顶
            setLeftNavFixedStyle(prev => {
                if (JSON.stringify(prev) === JSON.stringify({})) {
                    return prev;
                }
                return {};
            });
        }
    };

    useOnMount(() => {
        // 滚动条
        // eslint-disable-next-line no-new
        iscrollRef.current = new (window as any).IScroll('#leftNavWrapper', {
            mouseWheel: true,
            scrollbars: true,
            fadeScrollbars: true,
            interactiveScrollbars: true,
        });
    });

    // 当内容滚动时，如果左边对应的title在容器外边，左边容器进行滚动
    useOnUpdate(() => {
        const navWrapper = document.getElementById('leftNavWrapper');
        const el = navWrapper.querySelectorAll('li')?.[current];
        if (!el) {
            return;
        }
        const navWrapperHeight = navWrapper.offsetHeight;
        const y = iscrollRef.current.y as number;
        if (el.offsetTop + navLiHeight > navWrapperHeight - y) {
            iscrollRef.current.scrollTo(0, navWrapperHeight - navLiHeight - navPadding - el.offsetTop, 300,
                (window as any).IScroll.utils.ease.quadratic);
        } else if (el.offsetTop + y < 0) {
            iscrollRef.current.scrollTo(0, navPadding - el.offsetTop, 300, (window as any).IScroll.utils.ease.quadratic);
        }
    }, [current]);

    return (
        <section className={cn('container', styles['products-content-wrapper'])}>
            <h2>了解所有百度智能云产品</h2>
            <p className={styles['manual-link']}>
                <span
                    className={styles['sub-link']}
                    data-track-action="click"
                    data-track-category="主标题"
                    data-track-name="副按钮"
                    data-track-value="下载百度智能云产品手册"
                    onClick={clickHandler}
                >
                    下载百度智能云产品手册
                </span>
            </p>
            <div className={styles['products-content']}>
                <div className={styles['nav-left-wrapper']}>
                    <UStickyContainer
                        defaultStyle={{left: 'auto', top: fixTop}}
                        fixedStyle={leftNavFixedStyle}
                        startFixed={0 - fixTop}
                        onScrollHandler={leftNavEndHandle}
                        fixedHiddenHeader
                    >
                        <div id="leftNavWrapper">
                            <div className={styles['nav-left']} ref={liDomRef}>
                                {
                                    newProductsList.map((navList, i) => {
                                        return (
                                            <div key={navList.name} className={styles['nav-list']}>
                                                <h3>{navList.name}</h3>
                                                <ul>
                                                    {
                                                        navList.categories.map((nav, index) => (
                                                            <li
                                                                key={nav.category}
                                                                className={navLength[i] + index === current ? styles.current : ''}
                                                                onClick={e => {
                                                                    const newIndex = navLength[i] + index;
                                                                    liDomRef.current?.querySelectorAll('li')?.[newIndex]?.classList.add('clicked');
                                                                    setCurrent(newIndex);
                                                                    e.stopPropagation();
                                                                }}
                                                            >
                                                                <h4>{nav.category}</h4>
                                                            </li>
                                                        ))
                                                    }
                                                </ul>
                                            </div>
                                        );
                                    })
                                }
                            </div>
                        </div>
                    </UStickyContainer>
                </div>
                <div className={styles['right-content']} id="productsContent">
                    {
                        newProductsList.map(categoriesList => {
                            return categoriesList.categories.map(categories => (
                                <div className={`product-content-item ${styles['product-content-item']}`} key={categories.category}>
                                    {categories.link ? (
                                        <a
                                            target="_blank"
                                            className={cn(styles.title, styles.link)}
                                            href={categories.link}
                                            data-track-category="产品列表区"
                                            data-track-name={`${categoriesList.name}-${categories.category}`}
                                        >
                                            {categories.category}
                                        </a>
                                    ) : <h3 className={styles.title}>{categories.category}</h3>
                                    }
                                    <ProductsListModule categories={categories} />
                                </div>
                            ));
                        })
                    }
                </div>
            </div>
        </section>
    );
};

export interface ProductsIndexProItemObj {
    category: string;
    icon: string;
    link: string;
    list: Array<{
        type: string;
        link: string;
        products: Array<{
            desc: string;
            link: string;
            name: string;
            tag: string;
        }>;
    }>;
}

export interface ProductsIndexProObj {
    name: string;
    categories: ProductsIndexProItemObj[];
}
