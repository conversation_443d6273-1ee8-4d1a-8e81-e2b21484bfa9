@import '../../../styles/variables.less';
@import '../../../styles/mixin.less';

.products-content-wrapper {
    padding-top: 190px;
    padding-bottom: 40px;
    h2 {
        text-align: center;
        font: 500 28px / 40px PingFangSC-Medium;
        color: @C0;
    }
    .manual-link {
        margin-top: 8px;
        text-align: center;
        .sub-link {
            font: 400 14px / 24px PingFangSC-Regular;
            color: @CB;
            cursor: pointer;
        }
    }
    .products-content {
        display: flex;
        .nav-left-wrapper {
            flex-shrink: 0;
            width: 200px;
            min-height: 100px;
            > div {
                max-height: calc(100vh - 40px);
                overflow: hidden;
                position: relative;
                width: 200px;
                box-sizing: border-box;
            }
            .nav-left {
                // background: #F2F6FA;
                .nav-list {
                    &:not(:first-child) {
                        padding-top: 20px;
                    }
                    > h3 {
                        position: relative;
                        font-family: PingFangSC-Semibold;
                        font-size: 16px;
                        color: #222222;
                        letter-spacing: 0;
                        height: 36px;
                        line-height: 36px;
                        font-weight: 500;
                        background-image: linear-gradient(270deg, rgba(255,255,255,0.00) 0%, rgba(36,104,242,0.12) 100%);
                        border-radius: 4px;
                        padding-left: 16px;
                    }
                    > ul {
                        width: 100%;
                        li {
                            position: relative;
                            cursor: pointer;
                            padding-left: 16px;
                            height: 24px;
                            line-height: 24px;
                            margin-top: 16px;

                            h4 {
                                font-family: PingFangSC-Regular;
                                font-size: 16px;
                                color: #222222;
                                letter-spacing: 0;
                                line-height: 24px;
                                font-weight: 400;
                                transition: color 0.2s ease-out;
                                .ellipsis();
                            }
                            &.current, &:hover {
                                h4 {
                                    color: @CB;
                                }
                            }
                        }
                        :global {
                            li.clicked {
                                color: @CB;
                            }
                        }
                    }
                }
            }
            :global {
                .iScrollVerticalScrollbar {
                    width: 6px!important;
                    .iScrollIndicator {
                        background: #DFE3EC!important;
                        border-radius: 3px!important;
                        width: 6px!important;
                        border: none!important;
                    }
                } 
            }
        }
        .right-content {
            width: 100%;
            padding-left: 44px;
            box-sizing: border-box;
            position: relative;
            background-color: @CW;
            .product-content-item {
                &:first-child {
                    .title {
                        margin-top: 0;
                    }
                }
            }
            .title {
                font: 500 22px / 28px PingFangSC-Medium;
                color: @C0;
                margin-top: 40px;
                
                &.link {
                    display: inline-block;
                    position: relative;
                    padding-right: 32px;
                    &::after {
                        content: '';
                        position: absolute;
                        width: 20px;
                        height: 20px;
                        right: 8px;
                        top: 50%;
                        transform: translateY(-50%);
                        background: url(https://bce.bdstatic.com/p3m/common-service/uploads/btn-arrow_131b1aa.png) no-repeat;
                        background-position: -121px 0;
                        background-size: auto 20px;
                        transition: right 0.3s ease-out;
                    }
                    &:hover {
                        color: @CB;
                        &::after {
                            right: 0;
                            background-position: -72px 0;
                        }
                    }
                }
            }
            .category {
                display: flex;
                flex-wrap: wrap;
                padding-top: 8px;
                li {
                    margin: 12px 12px 0 0;
                    padding: 2px 8px;
                    font: 400 14px / 24px PingFangSC-Regular;
                    color: @C0;
                    text-align: center;
                    cursor: pointer;
                    border-radius: 2px;
                    background-color: rgba(@CB, 0.08);
                    transition: color 0.3s ease-out;
                    &:last-child {
                        margin-right: 0;
                    }
                    &:hover {
                        color: @CB;
                    }
                }
            }
            .products-list {
                > div {
                    padding-top: 40px;
                    box-sizing: border-box;
                    .subTitle {
                        position: relative;
                        padding-left: 11px;
                        font-family: PingFangSC-Medium;
                        font-size: 20px;
                        color: @CB;
                        line-height: 28px;
                        font-weight: 500;
                        &::before {
                            content: '';
                            position: absolute;
                            left: 0;
                            top: 50%;
                            transform: translateY(-50%);
                            width: 3px;
                            height: 20px;
                            background: @CB;
                        }
                        &.link {
                            padding-right: 32px;
                            &::after {
                                content: '';
                                position: absolute;
                                width: 20px;
                                height: 20px;
                                right: 8px;
                                top: 50%;
                                transform: translateY(-50%);
                                background: url(https://bce.bdstatic.com/p3m/common-service/uploads/btn-arrow_131b1aa.png) no-repeat;
                                background-position: -72px 0;
                                background-size: auto 20px;
                                transition: right 0.3s ease-out;
                            }
                            &:hover {
                                color: @CB;
                                &::after {
                                    right: 0;
                                }
                            }
                        }
                    }
                    .sub-product-box {
                        display: flex;
                        flex-wrap: wrap;
                        .sub-product {
                            width: 33.33%;
                            margin-top: 20px;
                            a {
                                margin-left: 20px;
                                position: relative;
                                top: 0;
                                display: block;
                                padding: 24px;
                                border-radius: 4px;
                                height: 100%;
                                background: #F2F6FA no-repeat top right;
                                background-size: auto 148px;
                                transition: all 0.3s ease-out;
                                .title-wrapper {
                                    &::after {
                                        content: '';
                                        position: absolute;
                                        right: 0;
                                        top: 3px;
                                        width: 20px;
                                        height: 20px;
                                        background: url(https://bce.bdstatic.com/p3m/common-service/uploads/arrow_61c51ce.png) no-repeat -25px 0;
                                        background-size: 70px 20px;
                                    }
                                }
                                &:hover {
                                    box-shadow: rgba(5, 12, 20, 0.08) 0 6px 16px 2px;
                                    top: -8px;
                                    background-image: url(https://bce.bdstatic.com/p3m/common-service/uploads/img_plan_card_d8e3de1.png);
                                    .title-wrapper {
                                        h4 {
                                            color: @CB;
                                        }
                                        &::after {
                                            background-position: 0 0;
                                            animation: arrowMove 0.9s linear infinite;
                                        }
                                    }
                                    &::after {
                                        content: '';
                                        position: absolute;
                                        top: 100%;
                                        left: 0;
                                        width: 100%;
                                        height: 8px;
                                    }
                                }
                            }
                            .title-wrapper {
                                display: flex;
                                align-items: center;
                                position: relative;
                                padding-right: 36px;
                                h4 {
                                    position: relative;
                                    font: 16px / 26px PingFangSC-Medium;
                                    color: @C0;
                                    font-weight: 500;
                                    .ellipsis();
                                }
                                .tag {
                                    flex-shrink: 0;
                                    margin-left: 8px;
                                    width: 42px;
                                    height: 20px;
                                    background-image: linear-gradient(270deg, #FF6966 0%, #F33E3E 100%);
                                    border-radius: 2px;
                                    font: 500 12px / 20px PingFangSC-Medium;
                                    color: #FFFFFF;
                                    text-align: center;
                
                                    &.gongcezhong {
                                        width: auto;
                                        background-image: none;
                                        border-radius: 0;
                                        color: #FF6300;
                                    }
                                }
                            }
                            p {
                                margin-top: 4px;
                                font: 14px / 24px PingFangSC-Regular;
                                color: rgba(@C0, 0.7);
                                .ellipsis2(3);
                            }
                            &:nth-child(3n+1) {
                                a {
                                    margin-left: 0;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@keyframes arrowMove {
    0% {
        transform: translateX(0);
    }
    33% {
        transform: translateX(4px);
    }
    67% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(0);
    }
}
