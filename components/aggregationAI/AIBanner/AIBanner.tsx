import {ULazyLoad} from '@baidu/bce-components';
import {aiDataObj} from '../interface';
import styles from './main.module.less';

export const AIBanner = ({bannerData}: {bannerData: aiDataObj['bannerData']}) => {
    return (
        <ULazyLoad type="backgroundImage">
            <section
                className={styles['banner']}
                data-url={bannerData.bg}
            >
                <div className="container">
                    <h1 className={styles.title}>{bannerData.title}</h1>
                    {bannerData.textLink && bannerData.textLink.text && (
                        <div className={styles['text-link']}>
                            <span>{bannerData.textLink.text}</span>
                            {bannerData.textLink.btnText && (
                                <a
                                    className={styles['btn']}
                                    href={bannerData.textLink.href}
                                    target="_blank"
                                    data-track-category="AI聚合页-banner区域-pc"
                                    data-track-name="文字链"
                                    data-track-value={bannerData.textLink.btnText}
                                >
                                    {bannerData.textLink.btnText}
                                </a>
                            )}
                        </div>
                    )}
                    <div className={styles['btn-group']}>
                        {bannerData.btnGroup && bannerData.btnGroup.length > 0 && bannerData.btnGroup.map(
                            btn => btn && !btn.disabled && btn.text && (
                                <a
                                    key={btn.text}
                                    href={btn.href}
                                    target="_blank"
                                    data-track-category="AI聚合页-banner区域-pc"
                                    data-track-name="营销位"
                                    data-track-value={btn.text}
                                >
                                    <div className={styles['btn-item']}>
                                        {btn.icon && <i style={{backgroundImage: `url(${btn.icon})`}} />}
                                        <span>{btn.text}</span>
                                    </div>
                                </a>
                            )
                        )}
                    </div>
                </div>
            </section>
        </ULazyLoad>
    );
};
