@import '@styles/variables.less';

.banner {
    height: 310px;
    background: no-repeat center/cover;
    padding-top: 74px;
    box-sizing: border-box;

    .title {
        color: @C0;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        font-size: 36px;
        line-height: 50px;
        letter-spacing: 0px;
        text-align: left;
    }

    .text-link {
        margin-top: 14px;
        display: inline-flex;
        align-items: center;
        position: relative;
        height: 24px;
        box-sizing: border-box;
        text-decoration: none;
        
        span, .btn {
            color: rgba(@C0, .8);
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            letter-spacing: 0px;
            text-align: left;                
        }
        .btn {
            position: relative;
            margin-left: 8px;
            padding-right: 24px;
            color: rgba(@CB, .9);
            font-weight: 500;
            &::after {
                content: '';
                position: absolute;
                right: 4px;
                top: 4px;
                width: 16px;
                height: 16px;
                background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/arrow-hover_5c62c57.svg') center/cover;
                transition: right .3s ease;
            }
            &:hover {
                &::after {
                    right: 0;
                }
            }
        }
    }

    .btn-group {
        margin-top: 46px;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        > a {
            display: inline-block;
            position: relative;
            &:not(:first-child) {
                margin-left: 8px;
            }

            .btn-item {
                display: flex;
                height: 50px;
                align-items: center;
                border-radius: 4px;
                backdrop-filter: blur(10px);
                background: linear-gradient(93deg, rgba(255, 255, 255, 0.52) 0%, rgba(255, 255, 255, 0.19) 100%);
                border: 1px solid rgba(@CW, 0.44);
                > i {
                    position: absolute;
                    width: 27px;
                    height: 34px;
                    top: 8px;
                    left: 12.5px;
                    background: no-repeat center/cover;
                }
    
                > span {
                    position: relative;
                    display: inline-block;
                    color: #000000;
                    font-family: PingFangSC-Regular;
                    font-weight: 500;
                    font-size: 16px;
                    line-height: 26px;
                    letter-spacing: 0px;
                    text-align: left;
                    padding-left: 14px;
                    padding-right: 34px;
                    &::after {
                        content: '';
                        position: absolute;
                        width: 16px;
                        height: 16px;
                        top: 5px;
                        right: 10px;
                        background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/arrow_e5dc140.svg') center/cover;
                        transition: right .3s ease;
                    }
                }
    
                > i + span {
                    padding-left: 50px;
                }
            }

            &:hover {
                .btn-item > span {
                    &::after {
                        right: 6px;
                    }
                }
            }
        }
    }
}

:global {
    .container {
        max-width: @Width1444;
        min-width: @Width1180;
        margin: 0 auto;
        width: calc(100vw - 140px);
    }
}