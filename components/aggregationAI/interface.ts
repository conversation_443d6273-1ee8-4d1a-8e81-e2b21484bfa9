export interface cardBtnObj {
    disabled: boolean;
    text: string;
    href: string;
    mobileHref: string;
    login: boolean;
    realName: boolean;
}

export interface aiContentCardObj {
    title: string;
    tag: string;
    desc: string;
    subTitle: string;
    subTitleLast?: string;
    infoList: Array<{
        title: string;
        desc: string;
    }>;
    hotTags: string;
    btn: cardBtnObj;
}

export interface aiBannerObj {
    title: string;
    bg: string;
    bgWap: string;
    textLink: {
        text: string;
        href: string;
        btnText: string;
    };
    btnGroup: Array<{
        disabled?: boolean;
        text: string;
        href: string;
        icon?: string;
    }>;
}

export interface aiContentObj {
    name: string;
    list: Array<{
        title: string;
        tag?: string;
        cards: aiContentCardObj[];
    }>;
}

export interface aiDataObj {
    bannerData: {
        title: string;
        bg: string;
        bgWap: string;
        textLink: {
            text: string;
            href: string;
            btnText: string;
        };
        btnGroup: Array<{
            disabled?: boolean;
            text: string;
            href: string;
            icon?: string;
        }>;
    };
    contentData: aiContentObj[];
}
