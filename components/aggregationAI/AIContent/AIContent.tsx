import cn from 'classnames';
import {useRef, useMemo} from 'react';
import {Ioc} from '@baidu/bce-decorators';
import {UEnvService} from '@common/services/env';
import {useOnMount, useOnUpdate} from '@baidu/bce-hooks';
import {useScrollBind} from '@common/hooks/useScrollBind';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {UDialog} from '@common/components/UDialog/UDialog';
import {REAL_NAME_URL} from '@common/constant/variableConst';
import {aiDataObj, aiContentCardObj, cardBtnObj} from '../interface';
import {DialogMessageMap} from './model';
import styles from './main.module.less';

export const checkDialog = ({title, content, okText, cb}: {
    title: string;
    content: string;
    okText: string;
    cb?: () => void;
}) => {
    UDialog.open({
        title: title,
        content: content,
        okText: okText,
        maskClosable: true,
        showCloseIcon: true,
        zIndex: 10000,
        onOk: close => {
            close();
            cb && cb();
        },
    });
};

const CardItem = (props: {
    card: aiContentCardObj;
}) => {
    const {card} = props;
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const isSandbox = Ioc(UEnvService).isProdSandbox;

    const btnSuccessCb = (href: string) => {
        window.location.href = href;
    };

    const handleClick = (btn: cardBtnObj) => {
        const {login = false, realName = false, href} = btn;
        // 登陆 & 实名 ｜ 激活判断
        if (login) {
            // 获取用户状态
            if (userinfo?.hasLogin && !realName) {
                window.location.href = href;
            } else {
                verifyHandler().then(code => {
                    // code表示当前已完成状态
                    if (code === 0) {
                        // 均正常
                        btnSuccessCb(href);
                    } else if (code === 201) {
                        if (realName) {
                            handleClick(btn);
                        } else {
                            btnSuccessCb(href);
                        }
                    } else if (code === 204) {
                        if (realName) {
                            checkDialog({
                                ...DialogMessageMap.NO_REALNAME,
                                cb: () => btnSuccessCb(REAL_NAME_URL[isSandbox ? 'sandbox' : 'online']),
                            });
                        } else {
                            btnSuccessCb(href);
                        }
                    } else {
                        checkDialog({...DialogMessageMap.UNKNOWN_ERROR});
                    }
                }).catch(err => {
                    console.error(err);
                    checkDialog({...DialogMessageMap.UNKNOWN_ERROR});
                });
            }
        } else {
            btnSuccessCb(href);
        }
    };
    return (
        <div className={styles['card-wrapper']}>
            <div className={styles['card-item_content']}>
                {card.tag && <p className={styles['card-item_tag']}><span>{card.tag}</span></p>}
                <h4>{card.title}</h4>
                <p className={cn('desc-0', styles['card-item_desc'])}>{card.desc}</p>
                <p className={styles['card-item_subTitle']}>
                    {card.subTitle}{card.subTitleLast && <><i />{card.subTitleLast}</>}
                </p>
                {card.infoList && card.infoList.length > 0 && (
                    <div className={styles['card-item_info']}>
                        {card.infoList.map(info => info && info.title && (
                            <p key={info.title}>
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <span className={styles['item-info_title']}>{info.title}</span>
                                <span>{info.desc}</span>
                            </p>
                        ))}
                    </div>
                )}
            </div>
            <div className={styles['card-item_footer']}>
                {card.hotTags && (
                    <div className={styles['footer_tags']}>
                        <span>{card.hotTags}</span>
                    </div>
                )}
                {card.btn && !card.btn.disabled && (
                    <div
                        className={styles['footer_btn']}
                        onClick={() => handleClick(card.btn)}
                        data-track-action="click"
                        data-track-category="AI聚合页-产品卡片-pc"
                        data-track-name={card.title}
                        data-track-value={card.btn.text}
                    >{card.btn.text || ''}
                    </div>
                )}
            </div>
        </div>
    );
};

export const AIContent = ({contentData}: {contentData: aiDataObj['contentData']}) => {
    const contentRef = useRef<HTMLDivElement>(null);
    const liDomRef = useRef<HTMLDivElement>(null);
    const iScrollRef = useRef(null);

    const [current, setCurrent] = useScrollBind({
        itemSelector: '#mainContent .content-group',
        fixTop: 0,
    });

    const navLiHeight = 40;
    const navPadding = 24;
    const navLiMargin = 34;

    const navLength = useMemo(() => {
        const res = contentData.reduce((accumulator: number[], currentItem, currentIndex) => {
            if (currentIndex === 0) {
                accumulator.push(0);
            } else {
                const preItem = contentData[currentIndex - 1];
                const preLength = preItem.list.length;
                const preSum = accumulator[currentIndex - 1] + preLength;
                accumulator.push(preSum); // 将前面元素的长度总和添加到新数组中
            }
            return accumulator;
        }, []);
        return res;
    }, [contentData]);

    useOnMount(() => {
        // 滚动条
        // eslint-disable-next-line no-new
        iScrollRef.current = new (window as any).IScroll('#leftNavWrapper', {
            mouseWheel: true,
            scrollbars: true,
            fadeScrollbars: true,
            interactiveScrollbars: true,
        });
    });
    // 当内容滚动时，如果左边对应的title在容器外边，左边容器进行滚动
    useOnUpdate(() => {
        const navWrapper = document.getElementById('leftNavWrapper');
        const el = navWrapper.querySelectorAll('li')?.[current];
        if (!el) {
            return;
        }
        const navWrapperHeight = navWrapper.offsetHeight;
        const y = iScrollRef.current.y as number;
        if (el.offsetTop + navLiHeight > navWrapperHeight - y) {
            iScrollRef.current.scrollTo(0, navWrapperHeight - (navLiHeight + navPadding + navLiMargin + 8) - el.offsetTop, 300,
                (window as any).IScroll.utils.ease.quadratic);
        } else if (el.offsetTop + y < 0) {
            const dist = navLiHeight + navPadding + navLiMargin - el.offsetTop;
            iScrollRef.current.scrollTo(0, (dist - navLiHeight < 0 ? 0 : dist), 300, (window as any).IScroll.utils.ease.quadratic);
        }
    }, [current]);

    return (
        <section className={styles.content}>
            <div className={cn('container', styles.container)}>
                <div className={styles['nav-left-wrapper']} id="leftNavWrapper">
                    <div className={styles['nav-left']} ref={liDomRef}>
                        {contentData?.map((group, groupIndex) => group && (
                            <div
                                // eslint-disable-next-line react/no-array-index-key
                                key={groupIndex}
                                className={styles['nav-list']}
                            >
                                <h3>{group.name}</h3>
                                <ul>
                                    {group.list?.map((nav, index) => (
                                        <li
                                            // eslint-disable-next-line react/no-array-index-key
                                            key={index}
                                            className={navLength[groupIndex] + index === current ? styles.current : ''}
                                            onClick={e => {
                                                const newIndex = navLength[groupIndex] + index;
                                                liDomRef.current?.querySelectorAll('li')?.[newIndex]?.classList.add('clicked');
                                                setCurrent(newIndex);
                                                e.stopPropagation();
                                            }}
                                        >
                                            <h4 className="desc-0">{nav.title}</h4>
                                            {nav.tag && <span><i>{nav.tag}</i></span>}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        ))}
                    </div>
                </div>
                <div className={styles['main-content']} id="mainContent" ref={contentRef}>
                    {contentData?.map((group, groupIndex) => group && (
                        // eslint-disable-next-line react/no-array-index-key
                        <div key={groupIndex} className={styles['content-item']}>
                            {
                                group.list?.map((cardList, index) => cardList && (
                                    // eslint-disable-next-line react/no-array-index-key
                                    <div key={index} className={cn('content-group', styles['content-group'])}>
                                        <h3 className={styles['group-title']}>{cardList.title}</h3>
                                        <div className={styles['card-list']}>
                                            {cardList.cards?.map((card, index) => card && (
                                                // eslint-disable-next-line react/no-array-index-key
                                                <CardItem key={index} card={card} />
                                            ))}
                                        </div>
                                    </div>
                                ))
                            }
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
};
