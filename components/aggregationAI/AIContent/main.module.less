@import '@styles/variables.less';
@import '@styles/mixin.less';

.content {
    padding: 49px 0 40px;
    .container {
        display: flex;
        position: relative;
        .nav-left-wrapper {
            position: sticky;
            top: 0;
            max-height: 100vh;
            height: fit-content;
            overflow-y: hidden;
            width: 274px;
            transition: width .3s ease;
            flex-shrink: 0;
        }
        .nav-left {
            width: 100%;
            padding: 24px;
            box-sizing: border-box;
            border-radius: 4px;
            background: #F2F6FA;
            .nav-list {
                &:not(:first-child) {
                    margin-top: 24px;
                }
                > h3 {
                    position: relative;
                    font-family: PingFangSC-Semibold;
                    font-size: 16px;
                    color: @C0;
                    letter-spacing: 0;
                    line-height: 26px;
                    font-weight: 600;
                }
                > ul {
                    margin-top: 8px;
                    border-left: 1px solid #D9D9D9;
                    width: 100%;
                    li {
                        position: relative;
                        height: 40px;
                        padding-left: 21px;
                        box-sizing: border-box;
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        cursor: pointer;
                        h4 {
                            transition: color 0.2s ease-out;
                            .ellipsis();
                        }
                        > span {
                            display: inline-flex;
                            align-items: center;
                            padding: 0 7px;
                            height: 18px;
                            box-sizing: border-box;
                            margin-left: 8px;
                            color: #F33E3E;
                            border-radius: 4px;
                            border: 1px solid #F33E3E;
                            i {
                                display: inline-block;
                                font-family: PingFangSC-Regular;
                                font-weight: 400;
                                line-height: 19px;
                                letter-spacing: 0px;
                                text-align: left;
                                font-size: 12px;
                                transform: scale(0.83); // 处理10px
                            }
                        }
                        &.current, &:hover {
                            h4 {
                                color: @CB;
                            }
                        }
                        &.current {
                            &::before {
                                content: '';
                                position: absolute;
                                width: 2px;
                                height: 100%;
                                background-color: @CB;
                                left: 0;
                                top: 0;
                            }
                            background: linear-gradient(90deg, rgba(@CB, 0.06) 5.11%, rgba(@CB, 0.03) 66.39%, rgba(@CB, 0) 100%);
                        }
                    }
                    :global {
                        li.clicked {
                            color: @CB;
                        }
                    }
                }
            }
        }
        .main-content {
            width: 100%;
            padding-left: 20px;
            box-sizing: border-box;
            position: relative;
            background-color: @CW;
            .content-item {
                box-sizing: border-box;
                &:first-child .content-group:first-child {
                    padding-top: 0;
                }
                .content-group {
                    box-sizing: border-box;
                    padding-top: 52px;
                    .group-title {
                        color: @C0;
                        font-family: PingFangSC-Medium;
                        font-weight: 600;
                        font-size: 22px;
                        line-height: 28px;
                        letter-spacing: 0px;
                        text-align: left;                    
                    }
                    .card-list {
                        margin-top: 20px;
                        display: flex;
                        justify-content: flex-start;
                        flex-wrap: wrap;
                        .card-wrapper {
                            position: relative;
                            top: 0;
                            border-radius: 4px;
                            width: calc((100% - 40px) / 3);
                            background: #F2F6FA;
                            padding: 0 0 92px;
                            box-sizing: border-box;
                            transition: all .4s ease;
                            &:not(:nth-child(3n + 1)) {
                                margin-left: 20px;
                            }
                            &:nth-child(n + 4) {
                                margin-top: 20px;
                            }
                            .card-item {
                                &_content {
                                    padding: 24px 24px 0;
                                    box-sizing: border-box;
                                    position: relative;
                                    border-radius: 0 4px 0 0;
                                    overflow: hidden;
                                    .card-item_tag {
                                        position: absolute;
                                        top: 0;
                                        right: -9px;
                                        display: flex;
                                        align-items: center;
                                        height: 24px;
                                        padding: 4px 20px 4px 12px;
                                        border-radius: 0 0 0 6px;
                                        box-sizing: border-box;
                                        background: #E9EDF0;
                                        transform: skewX(15deg);
                                        > span {
                                            display: inline-block;
                                            color: rgba(34,34,34,0.5);
                                            font-family: PingFangSC-Medium;
                                            font-weight: 600;
                                            font-size: 12px;
                                            line-height: 16px;
                                            letter-spacing: 0px;
                                            text-align: center;
                                            transform: skewX(-15deg);
                                        }
                                    }
                                    > h4 {
                                        color: @C0;
                                        font-family: PingFangSC-Medium;
                                        font-weight: 600;
                                        font-size: 18px;
                                        line-height: 28px;
                                        letter-spacing: 0px;
                                        text-align: left;
                                    }
                                    .card-item_desc {
                                        .ellipsis2(2);
                                    }
                                }
                                &_subTitle {
                                    margin-top: 18px;
                                    color: @CB;
                                    font-family: PingFangSC-Medium;
                                    font-weight: 500;
                                    font-size: 20px;
                                    line-height: 28px;
                                    letter-spacing: 0px;
                                    text-align: left;
                                    .ellipsis();

                                    > i {
                                        display: inline-block;
                                        width: 20px;
                                        height: 1px;
                                        background: transparent;
                                    }
                                }
                                &_info {
                                    margin-top: 12px;
                                    border-top: 1px solid @C08;
                                    padding: 16px 0 0;
                                    box-sizing: border-box;
                                    > p {
                                        background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/check_ee3c380.svg');
                                        background-size: 16px 16px;
                                        background-position: left top 4px;
                                        .ellipsis2(2);
                                        &:nth-child(n+2) {
                                            margin-top: 4px;
                                        }
                                        span {
                                            color: #222222;
                                            font-family: PingFangSC-Regular;
                                            font-size: 14px;
                                            font-weight: 400;
                                            line-height: 24px;
                                            letter-spacing: 0px;
                                            text-align: left;
                                            vertical-align: top;
                                            &.item-info_title {
                                                font-weight: 600;
                                            }
                                        }
                                    }
                                }
                                &_footer {
                                    position: absolute;
                                    bottom: 0;
                                    left: 0;
                                    width: 100%;
                                    height: 80px;
    
                                    .footer_tags {
                                        width: 100%;
                                        height: 28px;
                                        padding: 0 24px;
                                        box-sizing: border-box;
                                        margin-bottom: 8px;
                                        > span {
                                            color: #F33E3E;
                                            font-family: PingFangSC-Regular;
                                            font-weight: 600;
                                            font-size: 18px;
                                            line-height: 28px;
                                            letter-spacing: 0px;
                                            text-align: left;
                                        }
                                    }
                                    .footer_btn {
                                        width: 100%;
                                        height: 44px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        background-color: @CB;
                                        border-radius: 0 0 4px 4px;
                                        cursor: pointer;
    
                                        color: @CW;
                                        font-family: PingFangSC-Medium;
                                        font-weight: 500;
                                        font-size: 16px;
                                        line-height: 28px;
                                        letter-spacing: 0px;
                                        transition: background-color .3s ease;
                                        &:hover {
                                            background-color: #528eff;
                                        }
                                    }
                                }
                            }
                            &:hover {
                                box-shadow: 0 6px 16px 2px rgba(7,12,20,.08);
                                top: -12px;
                                &::after {
                                    content: "";
                                    position: absolute;
                                    left: 0;
                                    right: 0;
                                    bottom: -12px;
                                    width: 100%;
                                    height: 12px;
                                    background: transparent;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

:global {
    .container {
        max-width: @Width1444;
        min-width: @Width1180;
        margin: 0 auto;
        width: calc(100vw - 140px);
    }
    .desc-0 {
        color: @C0;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: 0px;
        text-align: left;
    }
}

@media screen and (max-width: 1320px) {
    .content .container {
        .nav-left-wrapper {
            width: 220px;
        }
        .main-content .content-item .content-group .card-list .card-wrapper {
            .card-item_subTitle {
                font-size: 18px;
            }
        }
    }
}

@media screen and (max-width: @MaxNavMobileWidth) {
    .content .container {
        .nav-left-wrapper {
            top: var(--cloud-nav-height);
        }
    }
}