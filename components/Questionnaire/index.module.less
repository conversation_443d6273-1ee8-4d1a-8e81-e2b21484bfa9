// 小型移动端设备
@mobile-sm: ~'only screen and (max-width: 374.98PX)';
// 中型移动端设备
@mobile-md: ~'only screen and (min-width: 375PX) and (max-width: 599.98PX)';
// 大多数的移动设备
@mobile-sm-md: ~'@{mobile-sm}, @{mobile-md}';
// 大型移动端设备
// @mobile-lg: ~'only screen and (min-width: 600PX) and (max-width: 767.98PX)';
@mobile-lg: ~'only screen and (max-width: 767.98PX)';
// 所有移动端设备 (组合变量)
@mobile: ~'@{mobile-sm}, @{mobile-md}, @{mobile-lg}';

.questionnaire {
    bottom: 324px;
    right: 1px;
    cursor: pointer;
    opacity: 1;
    position: fixed;
    -webkit-transition: all .3s ease-out;
    transition: all .3s ease-out;
    z-index: 100;

    &-icon {
        width: 64px;
        height: 68px;
    }
}

@media screen and (max-height: calc(516px)) and (min-width: 1221px) {
    .questionnaire {
        top: 184px;
    }
}

@media @mobile {
    .questionnaire {
        &-icon {
            height: 56PX;
            width: 52PX;
        }
    }
}