/**
 * @file BCD域名
 * @desc 域名搜索框模块
 */
import React, {useEffect, useState} from 'react';
import styles from './search.module.less';

export const Search: React.FC<{
  pageFrom: string;
  handleDomainSearch?: (value?: string) => void;
}> = props => {
    const {pageFrom, handleDomainSearch} = props;
    const [searchVal, setSearchVal] = useState<string>('');
    const [isShowErr, setIsShowErr] = useState<string>('');

    const parserUrl = (key: string): string => {
        const paramStr = window.location.search?.substr(1);
        if (paramStr) {
            const paramArr = paramStr?.split('&')?.map(item => item.split('='));
            for (const item of paramArr) {
                if (item[0] === key) {
                    return item[1];
                }
            }
        }
        return '';
    };

    const jumpSearchDetail = (
        e?: React.KeyboardEvent<HTMLInputElement>
    ): void => {
        if (!searchVal) {
            setIsShowErr('请填写域名，例如：baidu.com');
            return;
        }

        if (e && e.code !== 'Enter') {
            return;
        }

        const domain = searchVal.trim();
        if (
            !(/^(([-\u4E00-\u9FA5a-z0-9]{1,63})\.)+([\u4E00-\u9FA5a-z]{2,63})\.?$/.exec(domain))
      || domain.split('.').length <= 1
        ) {
            setIsShowErr('域名格式不正确，请输入正确的域名格式，以"."进行区分');
            return;
        }

        if (pageFrom === 'detail') {
            handleDomainSearch(searchVal);
        }
    };
    const onChangeInput = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchVal(e.target.value);
        setIsShowErr('');
    };

    useEffect(() => {
        const url = decodeURIComponent(parserUrl('domain'));
        setSearchVal(url || searchVal);
        if (pageFrom === 'detail') {
            handleDomainSearch(url || searchVal);
        }
  }, []); // eslint-disable-line
    return (
        <div className={styles.search}>
            <div
                className={`${styles['search-box']} ${
                    pageFrom === 'detail' ? styles['is-whois-detail'] : ''
                }`}
            >
                <input
                    className={styles.inputBox}
                    type="text"
                    placeholder="请输入域名，如baidu.com"
                    value={searchVal ? searchVal : ''}
                    onChange={e => onChangeInput(e)}
                    onKeyDown={e => jumpSearchDetail(e)}
                />
                <div className={styles.btn} onClick={() => jumpSearchDetail()}>
                    检测
                </div>
            </div>
            {isShowErr ? (
                <div className={styles['error-tip']}>{isShowErr}</div>
            ) : null}
        </div>
    );
};
