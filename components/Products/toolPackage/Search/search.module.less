.search {
  width: 980px;
  margin: 0 auto;
  .search-box {
    width: 980px;
    height: 52px;
    position: relative;
    background: #FFF;
    border-radius: 6px;
    overflow: hidden;
    display: flex;
    align-items: center;
    margin-top: 40px;
    .inputBox {
      width: 800px;
      height: 52px;
      box-sizing: border-box;
      border: none;
      outline: none;
      padding-left: 20px;
      font-size: 14px;
      line-height: 24px;
      color: #191A24;
    }
    .btn {
      width: 180px;
      height: 52px;
      background: #2468F2;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: PingFangSC-Regular;
      font-size: 16px;
      color: #FFF;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      cursor: pointer;
    }
    .btn:hover {
      background: #528EFF;
    }
  }
  .error-tip {
    color: #F6654D;
  }
  .is-whois-detail {
    width: 980px;
    margin: 0 auto;
    box-sizing: border-box;
    .inputBox {
      width: 800px;
    }
  }
  .inputBox::placeholder {
    opacity: 0.4;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222;
  }
}
