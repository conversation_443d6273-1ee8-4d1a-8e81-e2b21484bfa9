/**
 * @file BCD域名
 * @desc 域名网站检测结果页
 */
import axios from 'axios';
import {Tabs} from 'antd';
import React, {useEffect, useState} from 'react';
import {WhoisPage} from '@components/Products/toolPackage/Whois/Whois';
import {DnsPage} from '@components/Products/toolPackage/Dns/Dns';
import {ToolPage} from '@components/Products/toolPackage/ToolCheck/ToolCheck';
import moment from 'moment';
import md5 from 'js-md5';
import Head from 'next/head';
import styles from './tabResult.module.less';


const {TabPane} = Tabs;
interface Request {
  url: string;
  data?: { [index: string]: any };
  timestampPath?: string;
}

interface DomainInfo {
  domain?: string;
  domainStatus?: string[];
  expirationDate?: string;
  nameServer?: string[];
  queryTime?: string;
  rawData?: string[];
  registerOnBaidu?: boolean;
  registrantEmail?: string;
  registrantName?: string;
  registrationDate?: string;
  sponsoringRegistrar?: string;
  [index: string]: any;
}

const initDomainInfo: DomainInfo = {
    domain: '',
    domainStatus: [],
    expirationDate: '',
    nameServer: [],
    queryTime: '',
    rawData: [],
    registrantEmail: '',
    registrantName: '',
    registrationDate: '',
    sponsoringRegistrar: '',
};

export function TabResultPage(props: { searchVal: string, activeKey: string }) {
    // const activeKey: string = props.activeKey ? props.activeKey : '0';
    const activeKey: string = props.activeKey;
    const [activeTab, setActiveTab] = useState<string>(activeKey);

    const searchVal: string = props.searchVal;

    // 域名主体信息
    const [domainInfo, setDomainInfo] = useState<DomainInfo>(initDomainInfo);

    // 错误提示
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [tip, setTip] = useState<string>('');

    // ping查询
    const [ping, setPing] = useState<DomainInfo>(initDomainInfo);

    // dial查询
    const [dial, setDial] = useState<DomainInfo>(initDomainInfo);

    // 健康检测的公共数据
    const [toolCommon, setToolCommon] = useState<DomainInfo>(initDomainInfo);

    // 站点企业信息
    const [business, setBusiness] = useState<DomainInfo>(initDomainInfo);
    const [hasBusiness, setHasBusiness] = useState<boolean>(false);

    const [hasDNS, setHasDns] = useState<boolean>(false);
    // 企业证书信息
    const [cert, setCert] = useState<DomainInfo>(initDomainInfo);
    const [hascert, setHasCert] = useState<boolean>(false);

    // 备案检查
    const [checkMsg, setCheckMsg] = useState<DomainInfo>(initDomainInfo);

    // 验证弹框
    const [isOpenDialog, setIsOpenDialog] = useState<boolean>(false);

    // 计算timestamp
    const calcTimestamp: (url: string) => string = url => {
        const time = moment(new Date()).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        const timestamp
      = time + '@' + md5('POST' + url + time + 'ieq%$jsaf23!@fkjwie');
        return timestamp;
    };

    // 请求方法
    const request = (params: Request): Promise<any> => {
        const {url, data, timestampPath} = params;
        return axios({
            url: url,
            method: 'POST',
            data,
            headers: {
                timestamp: timestampPath ? calcTimestamp(timestampPath) : calcTimestamp(url),
            },
        });
    };

    const [searchValue, setSearchValue] = useState<string>('');
    const [type, setType] = useState<number>(255);


    // 获取最新whois消息
    const verifySuccessed: () => void = () => {
        request({
            url: '/api/bcd/open/whois/query',
            data: {
                domain: searchVal,
                type: 'REFRESH',
            },
            timestampPath: '/open/whois/query',
        })
            .then((res: any) => {
                setIsOpenDialog(false);
                const result = res?.data?.result?.data || {};
                setDomainInfo(result);
            })
            .catch(() => setIsOpenDialog(false));
    };

    // 获取whois消息
    const getWhois: () => void = () => {
        setTip('');
        setDomainInfo({});
        request({
            url: '/api/bcd/open/whois/query',
            data: {
                domain: searchVal,
                type: 'NORMAL',
            },
            timestampPath: '/open/whois/query',
        })
            .then((res: any) => {
                const result = res?.data?.result || {};
                if (result && !result.code) {
                    setDomainInfo(result?.data || {});
                } else {
                    setTip(result?.message);
                }
            })
            .catch(() => setDomainInfo({}));
    };

    // 获取dns
    const getDns: (value: string, type: number) => void = (value, type) => {
        setType(type);
        setSearchValue(value);
        setHasDns(false);
        request({
            url: '/api/mnt/dns/dig',
            data: {
                addr: searchVal,
                nsServer: value || '*******',
                type: type || 255,
            },
        })
            .then((res: any) => {
                const result = res?.data?.result || {};
                setDomainInfo(result);
                setHasDns(true);
            })
            .catch(() => setDomainInfo({}));
    };

    const getBusiness: () => void = () => {
        request({
            url: '/api/mnt/host/business',
            data: {
                host: searchVal,
            },
            timestampPath: '/host/business',
        })
            .then((res: any) => {
                const result = res?.data?.result;
                if (result && result.business_exist) {
                    setBusiness(result);
                    setHasBusiness(true);
                } else {
                    setBusiness({});
                    setHasBusiness(false);
                }

            })
            .catch(() => setBusiness({}));
    };

    // ping查询
    const getPing: () => void = () => {
        request({
            url: '/api/mnt/dns/ping',
            data: {
                addr: searchVal,
            },
        })
            .then((res: any) => {
                const result = res?.data?.result || {};
                setPing(result);
            })
            .catch(() => setPing({}));
    };

    // dial查询
    const getDial: () => void = () => {
        request({
            url: '/api/mnt/dns/dial',
            data: {
                addr: searchVal,
            },
        })
            .then((res: any) => {
                const result = res?.data?.result || {};
                if (result.code) {
                    setDial(result);
                }
            })
            .catch(() => setDial({}));
    };

    // 健康检测
    const getCheck: () => void = () => {
        request({
            url: '/api/mnt/dns/dnsCheck',
            data: {
                addr: searchVal,
                type: 1,
                nsServer: '*************',
            },
        })
            .then((res: any) => {
                const result = res?.data?.result || {};
                setToolCommon(result);
            })
            .catch(() => setToolCommon({}));
    };

    // 网站证书检查
    const getCertMsg: () => void = () => {
        request({
            url: '/api/mnt/host/cert',
            data: {
                host: searchVal,
            },
            timestampPath: '/host/cert',
        })
            .then((res: any) => {
                const result = res?.data?.result;
                if (result && result.cert_exist) {
                    setCert(result);
                    setHasCert(true);
                } else {
                    setCert({});
                    setHasCert(false);
                }
            })
            .catch(() => setCert({}));
    };

    // 备案查询
    const getBAMsg: () => void = () => {
        request({
            url: '/api/sme/aladdin/icpquery',
            data: {
                host: searchVal,
            },
            timestampPath: '/sme/aladdin/icpquery',
        })
            .then((res: any) => {
                const result = res?.data?.result || {};
                setCheckMsg(result);
            })
            .catch(() => setCheckMsg({}));
    };

    // 获取健康检测信息
    const getTool: () => void = () => {
        getWhois(); // 获得whois信息
        getPing(); // ping查询
        getDial(); // dial查询
        getCheck(); // 健康检测
        getBusiness(); // 调用诊站接口信息
        getCertMsg(); // 网站证书检查
        getBAMsg(); // 备案查询
    };

    const changeTabs: (key: string) => void = key => {
        setActiveTab(key);
    };

    useEffect(() => {
        setActiveTab(activeTab);
        if (activeTab === '1') {
            getWhois();
        } else if (activeTab === '2') {
            getDns(searchValue, type);
        } else if (activeTab === '0') {
            getTool();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activeTab, activeKey, searchVal]);

    return (
        <React.Fragment>
            <Head>
                <title>百度智能云-域名网站监测工具</title>
                <meta
                    name="description"
                    content="百度域名网站监测工具，了解域名信息及状态，查看DNS解析，网站备案、HTTP状态、Ping检查，端口安全监测等"
                />
                <meta name="keywords" content="百度云,域名,域名注册,域名查询,Whois查询,域名信息,域名解析,DNS解析,网站备案,HTTP,Ping,证书检查,安全监测" />
            </Head>
            <div className={styles.enterprise}>
                <Tabs
                    activeKey={activeTab}
                    className={styles.tabsPanel}
                    onChange={changeTabs}
                >
                    <TabPane key="0" tab="网站健康检测">
                        <ToolPage
                            domainInfo={domainInfo}
                            dial={dial}
                            ping={ping}
                            toolCommon={toolCommon}
                            business={business}
                            cert={cert}
                            checkMsg={checkMsg}
                            tip={tip}
                            hascert={hascert}
                            hasBusiness={hasBusiness}
                        />
                    </TabPane>
                    <TabPane key="1" tab="WHOIS信息查询">
                        <WhoisPage
                            domainInfo={domainInfo}
                            searchVal={searchVal}
                            isOpenDialog={isOpenDialog}
                            verifySuccessed={verifySuccessed}
                            tip={tip}
                        />
                    </TabPane>
                    <TabPane key="2" tab="解析查询">
                        <DnsPage
                            domainInfo={domainInfo}
                            searchVal={searchVal}
                            getDns={getDns}
                            hasDNS={hasDNS}
                        />
                    </TabPane>
                </Tabs>
            </div>
        </React.Fragment>
    );
}
