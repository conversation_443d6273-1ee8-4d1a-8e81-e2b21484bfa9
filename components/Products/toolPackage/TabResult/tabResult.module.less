.enterprise {
  margin-top: 72px;
  text-align: center;
  .tabsPanel {
    margin: 0 auto;
    :global {
      .ant-tabs-nav {
        margin-bottom: 0px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        &::before {
          background-color: #CCD7F0;
        }
        .ant-tabs-nav-wrap {
          justify-content: center;
          .ant-tabs-nav-list {
            width: 1180px;
            display: flex;
            justify-content: center;
            .ant-tabs-tab {
              margin: 0 86px;
              opacity: 0.9;
              font-family: PingFangSC-Semibold;
              font-size: 18px;
              color: #222222;
              text-align: center;
              line-height: 28px;
              font-weight: 600;
              .ant-tabs-tab-btn {
                font-weight: 600;
                animation-play-state: paused;
              }
              &:hover,
              &.ant-tabs-tab-active {
                .ant-tabs-tab-btn {
                  color: #2468F2;
                  font-weight: 600;
                  animation-play-state: paused;
                }
              }
            }
            .ant-tabs-ink-bar {
              background-color: #2468F2;
            }
          }
        }
      }
      .ant-tabs-content-holder {
        background-color: #F8F7F9;
      }
      .ant-tabs-content {
        width: 100%;
        background-color: #F8F7F9;
        margin-top: 20px;
      }
    }
  }
}
