.left-module-item {
  width: 1180px;
  box-sizing: border-box;
  padding: 24px;
  background-color: #FFF;
  border-radius: 6px;
  margin: 20px auto;
  .domain-title-des {
    font-family: PingFangSC-Semibold;
    font-size: 18px;
    color: #222;
    letter-spacing: 0;
    line-height: 28px;
    display: flex;
    .domain-title-text {
      font-weight: 600;
    }
    .domain-des {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #222222;
      letter-spacing: 0;
      line-height: 24px;
      font-weight: 400;
      margin-left: 125px;
      line-height: 28px;
    }
    .cloundBtn {
      display: inline-block;
      background: #2468F2;
      border-radius: 4px;
      margin-left: 16px;
      border: none;
      height: 28px;
      padding: 2px 16px;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #FFFFFF;
      letter-spacing: 0;
      line-height: 24px;
      font-weight: 400;
    }
    .cloundBtn:hover {
      background-color: #528EFF;
    }
  }


  .domain-title {
    font-family: PingFangSC-Semibold;
    font-size: 18px;
    color: #222;
    letter-spacing: 0;
    line-height: 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .domain-title-text {
      font-weight: 600;
    }
    .domain-title-right {
      display: flex;
      align-items: center;

      img {
        margin-right: 12px;
        cursor: pointer;
      }
    }

    .entrust-btn {
      width: 102px;
      height: 32px;
      border-radius: 4px;
      background-color: #2468F2;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #FFF;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .entrust-btn:hover {
      background-color: #528EFF;
    }
  }

  .desc {
    margin-top: 2px;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 20px;
    font-weight: 400;
    display: flex;

    .desc-time {
      opacity: 0.4;
      color: #222222;
    }

    .refresh-btn {
      margin-left: 12px;
      color: #2468F2;
      cursor: pointer;
      display: flex;
      align-items: center;

      img {
        margin-right: 4px;
      }
    }
  }

  .info-item {
    margin-top: 24px;
    display: flex;

    .key {
      width: 224px;
      opacity: 0.7;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #222;
      letter-spacing: 0;
      line-height: 24px;
      font-weight: 400;
      display: flex;
      flex-direction: column;
      text-align: left;
      .chinese {
        font-size: 14px;
        line-height: 24px;
      }

      .english {
        font-size: 12px;
        line-height: 20px;
      }
    }

    .value {
      flex: 1;
      display: flex;
      flex-direction: column;
      text-align: left;
      .comment {
        display: inline-block;
        margin-left: 10px;
      }
      .last-time-tip {
        margin-top: 8px;
        width: 552px;
        height: 56px;
        box-sizing: border-box;
        padding: 8px 12px;
        background-color: rgba(22, 22, 22, 0.04);
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #5E626A;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }
      .last-time-tipOther {
        opacity: 0.4;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #222222;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }

      span {
        margin-bottom: 4px;
      }
    }
    .dnsValue {
      text-align: left;
      .link {
        margin-left: 8px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #2468F2;
        letter-spacing: 0;
        line-height: 24px;
        font-weight: 400;
        img {
          height: 10px;
          width: 10px;
        }
      }
      .link:hover {
        color: #528EFF;
      }
      img {
        height: 14px;
        width: 14px;
        display: inline-block;
        margin-top: -3px;
      }
      .statusOk,
      .errorStstus {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #30BF13;
        letter-spacing: 0;
        line-height: 24px;
        font-weight: 400;
        display: inline-block;
        margin-left: 5px;
        .text {
          font-weight: 500;
        }
      }
      .errorStstus {
        color: #FF9326;
      }
      .code {
        opacity: 0.7;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #222222;
        letter-spacing: 0;
        line-height: 24px;
        font-weight: 400;
        display: inline-block;
        margin-left: 12px;
      }
      .detail {
        display: inline-block;
        margin-left: 10px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #2468F2;
        letter-spacing: 0;
        line-height: 24px;
        font-weight: 400;
        text-align: center;
        cursor: pointer;
        img {
          display: inline-block;
          margin-left: 5px;
          margin-top: -3px;
          height: 10px;
          width: 10px;
        }
      }
      .detail:hover {
        color: #528EFF;
      }
      .value,
      .valueFixHeight {
        background: #F7F7F9;
        border-radius: 4px;
        height: auto;
        width: 550px;
        padding: 8px 12px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #5C5F66;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
        .englist-info-item {
          margin-bottom: 4px;
        }
      }
      .cloundBtn {
        display: inline-block;
        background: #2468F2;
        border-radius: 4px;
        margin-left: 16px;
        border: none;
        height: 28px;
        padding: 2px 16px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #FFFFFF;
        letter-spacing: 0;
        line-height: 24px;
        font-weight: 400;
      }
      .cloundBtn:hover {
        background-color: #528EFF;
      }
      .valueFixHeight {
        margin-top: 8px;
        max-height: 260px;
        overflow-y: auto;
      }
      .hiddenValue {
        display: none;
      }
    }
    .openAll {
      font-size: 14px;
      line-height: 24px;
      font-weight: 400;
      .detail {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #2468F2;
        letter-spacing: 0;
        line-height: 24px;
        font-weight: 400;
        text-align: center;
        cursor: pointer;
        float: right;
        clear: both;
        img {
          display: inline-block;
          margin-left: 5px;
          margin-top: -3px;
          height: 10px;
          width: 10px;
        }
      }
      .detail:hover {
        color: #528EFF;
      }
      &::before {
        content: "";
        float: right;
        width: 0;
        height: 100%;
        margin-bottom: -24px;
      }
    }

    .closeAll {
      max-height: 48px;
      font-size: 14px;
      line-height: 24px;
      font-weight: 400;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-align: left;
      .detail {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #2468F2;
        letter-spacing: 0;
        line-height: 24px;
        font-weight: 400;
        text-align: center;
        cursor: pointer;
        float: right;
        clear: both;
        // margin-top: 20px;
        img {
          display: inline-block;
          margin-left: 5px;
          margin-top: -3px;
          height: 10px;
          width: 10px;
        }
      }
      .detail:hover {
        color: #528EFF;
      }
      &::before {
        content: "";
        float: right;
        width: 0;
        height: 100%;
        margin-bottom: -24px;
      }
    }
  }

  .english-info {
    overflow: hidden;
    text-align: left;
    .englist-info-item {
      margin-bottom: 4px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #222;
      line-height: 24px;
      font-weight: 400;
    }
    .detail {
      display: inline-block;
      margin-left: 10px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #2468F2;
      letter-spacing: 0;
      line-height: 24px;
      font-weight: 400;
      text-align: center;
      cursor: pointer;
      img {
        display: inline-block;
        margin-left: 5px;
        margin-top: -3px;
        height: 10px;
        width: 10px;
      }
    }
    .detail:hover {
      color: #528EFF;
    }
  }

  .left-item-open {
    height: 24px;
  }

  .open-more {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #2468F2;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
    text-align: center;
    cursor: pointer;
  }

  .open-more:hover {
    color: #528EFF;
  }
  img {
    display: inline-block;
    margin-top: -3px;
    margin-left: 5px;
  }
}
.error-tip {
  height: 336px;
  display: flex;
  justify-content: center;
  align-items: center;
  .error-img {
    img {
      height: 160px;
      width: 160px;
    }
    .error-text {
      font-size: 12px;
      color: #84868C;
      text-align: center;
      line-height: 20px;
      font-weight: 400;
    }
  }
}
.model-container {
  width: 100vw;
  height: 100vh;
  background: rgba(22, 22, 22, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;

  .model {
    width: 520px;
    height: 240px;
    background: #FFF;
    box-shadow: 0 6px 16px 2px rgba(7, 12, 20, 0.12);
    border-radius: 4px;
    box-sizing: border-box;
    padding: 24px;

    .model-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 50px;

      .model-title {
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #151B26;
        line-height: 22px;
        font-weight: 500;
      }
    }
  }
}