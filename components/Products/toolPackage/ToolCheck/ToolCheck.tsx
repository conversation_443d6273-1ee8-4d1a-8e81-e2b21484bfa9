/**
 * @file BCD域名
 * @desc 网站检测模块
 */
import React, {useState, useEffect} from 'react';
import {Spin} from 'antd';
import moment from 'moment';
import {hostMap} from '../../util';
import styles from './toolCheck.module.less';

const BCD_BASE_URL = 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220723';
// eslint-disable-next-line complexity
export function ToolPage(props: {
    domainInfo: any;
    dial: any;
    ping: any;
    toolCommon: any;
    business: any;
    cert: any;
    checkMsg: any;
    tip: string;
    hascert: boolean;
    hasBusiness: boolean;
}) {
    const {domainInfo, dial, ping, toolCommon, business, cert, checkMsg, tip, hascert, hasBusiness}
        = props;

    const [isOpenMore, setIsOpenMore] = useState<boolean>(false);
    const [isOpenOne, setIsOpenOne] = useState<boolean>(false);

    const [isOpenMoreOther, setIsOpenMoreOther] = useState<boolean>(false);
    const [isOpenMoreCert, setIsOpenMoreCert] = useState<boolean>(false);

    const [isOpenAll, setIsOpenAll] = useState<boolean>(false);
    // 时间转化
    const calcTimestamp: (date: string) => string = date => {
        if (!date) {
            return '-';
        }
        return moment(date).utc().format('YYYY-MM-DD HH:mm:ss');
    };

    // 两个日期的对比
    const compareDate: (date: string) => boolean = date => {
        if (!date) {
            return false;
        }
        date = date.replace(/年/g, '-');
        date = date.replace(/月/g, '-');
        date = date.replace(/日/g, '');
        const endTime = new Date(date);
        const startTime = new Date();
        return endTime.getTime() > startTime.getTime();
    };
    // 切换英文是否展开状态
    const switchOpenMore: () => void = () => {
        setIsOpenMore(!isOpenMore);
    };
    const switchOpenMoreOther: () => void = () => {
        setIsOpenMoreOther(!isOpenMoreOther);
    };
    const switchOpenMoreOne: () => void = () => {
        setIsOpenOne(!isOpenOne);
    };
    const switchOpenCert: () => void = () => {
        setIsOpenMoreCert(!isOpenMoreCert);
    };
    const switchOpenAll: () => void = () => {
        setIsOpenAll(!isOpenAll);
    };
    useEffect(() => {
        setIsOpenMore(!!toolCommon?.ns?.length);
        setIsOpenOne(!!toolCommon?.publicDigResp?.answer?.length);
        setIsOpenMoreOther(!!toolCommon?.digResp?.answer?.length);
    }, [toolCommon]);
    return (
        (!tip && !domainInfo.domain) ? (
            <section className={`${styles['left-module-item']}`}>
                <div className={styles['error-tip']}>
                    <div className={styles['error-img']}>
                        <Spin />
                        <div className={styles['error-text']}>正在加载中…</div>
                    </div>
                </div>
            </section>
        ) : tip ? (
            <section className={`${styles['left-module-item']}`}>
                <div className={styles['error-tip']}>
                    <div className={styles['error-img']}>
                        <img src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220809/errBack.png" alt="域名检测工具" title="域名检测工具" />
                        <div className={styles['error-text']}>{tip}</div>
                    </div>
                </div>
            </section>
        ) : (
            <>
                <section className={styles['left-module-item']}>
                    <div className={styles['domain-title']}>
                        <div className={styles['domain-title-text']}>域名检查</div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>域名注册商</span>
                        </div>
                        <div className={styles.dnsValue}>
                            {domainInfo.sponsoringRegistrar || '-'}

                            <span
                                className={
                                    domainInfo.registerOnBaidu
                                        ? styles.statusOk
                                        : styles.errorStstus
                                }
                            >
                                {domainInfo.registerOnBaidu
                                    ? (<span className={styles.text}>(域名已在百度云注册)</span>)
                                    : (<span className={styles.text}>(域名未在百度云注册)</span>)}
                                {domainInfo.registerOnBaidu ? (
                                    ''
                                ) : (
                                    <a
                                        className={styles.cloundBtn}
                                        href={`${hostMap.getHost('console')}/bcd/?_=1657002844738#/bcd/transferin/list`}
                                        target="_blank"
                                        title="转入百度云"
                                    >
                                        转入百度云
                                    </a>
                                )}
                            </span>
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>域名时间</span>
                        </div>
                        <div className={styles.dnsValue}>
                            {domainInfo.expirationDate || '-'}
                            <img
                                src={
                                    compareDate(domainInfo.expirationDate)
                                        ? `${BCD_BASE_URL}/status-ok.png`
                                        : `${BCD_BASE_URL}/status-error.png`
                                }
                                alt="状态"
                                title="状态"
                            />
                            <span
                                className={
                                    compareDate(domainInfo.expirationDate)
                                        ? styles.statusOk
                                        : styles.errorStstus
                                }
                            >
                                {compareDate(domainInfo.expirationDate)
                                    ? '域名在有效期內'
                                    : '域名不在有效期內'}
                            </span>
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>域名状态</span>
                        </div>
                        <div className={styles.value}>
                            {domainInfo?.domainStatus?.map((item: string) => (
                                <span key={item}>
                                    {item}
                                    <span className={styles.comment}>
                                        {domainInfo.domainStatusMap[item]}
                                    </span>
                                </span>
                            )) || '-'}
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>WHOIS最后更新时间</span>
                        </div>
                        <div className={styles.value}>
                            {calcTimestamp(domainInfo.queryTime) || '-'}
                        </div>
                    </div>
                </section>
                <section className={styles['left-module-item']}>
                    <div className={styles['domain-title']}>
                        <div className={styles['domain-title-text']}>DNS检查</div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>DNS服务商</span>
                        </div>
                        <div className={styles.dnsValue}>
                            {toolCommon?.ns && toolCommon?.ns[0].endsWith('bdydns.cn.') ? '使用百度云解析DNS' : '未使用百度云解析DNS'}
                            <span onClick={() => switchOpenMore()} className={styles.detail}>
                                详情
                                <img
                                    style={{transform: isOpenMore ? 'rotate(180deg)' : ''}}
                                    src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220723/arrow.svg"
                                    alt="箭头"
                                    title="箭头"
                                />
                            </span>
                            <div
                                className={`${isOpenMore ? styles.valueFixHeight : styles.hiddenValue}`}
                            >
                                {toolCommon?.ns?.map((item: string) => (
                                    <div className={styles['englist-info-item']} key={item}>
                                        {item}
                                    </div>
                                )) || '-'}
                            </div>
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>DNS服务商解析结果</span>
                        </div>
                        <div className={styles.dnsValue}>
                            解析记录
                            <span
                                onClick={() => switchOpenMoreOther()}
                                className={styles.detail}
                            >
                                详情
                                <img
                                    style={{transform: isOpenMoreOther ? 'rotate(180deg)' : ''}}
                                    src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220723/arrow.svg"
                                    alt="箭头"
                                    title="箭头"
                                />
                            </span>
                            <div
                                className={`${isOpenMoreOther ? styles.valueFixHeight : styles.hiddenValue
                                }`}
                            >
                                {toolCommon?.digResp?.answer?.map((item: string) => (
                                    <div className={styles['englist-info-item']} key={item}>
                                        {item}
                                    </div>
                                )) || '-'}
                            </div>
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>递归解析追踪</span>
                        </div>
                        <div className={styles.dnsValue}>
                            <img
                                src={
                                    toolCommon.recursionHealth
                                        ? `${BCD_BASE_URL}/status-ok.png`
                                        : `${BCD_BASE_URL}/status-error.png`
                                }
                                alt="状态"
                                title="箭头"
                            />
                            <span
                                className={
                                    toolCommon.recursionHealth
                                        ? styles.statusOk
                                        : styles.errorStstus
                                }
                            >
                                {toolCommon.recursionHealth
                                    ? '域名递归解析正常'
                                    : '域名递归解析异常'}
                            </span>
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>IPV6检测</span>
                        </div>
                        <div className={styles.dnsValue}>
                            <img
                                src={
                                    toolCommon.ipv6Enable
                                        ? `${BCD_BASE_URL}/status-ok.png`
                                        : `${BCD_BASE_URL}/status-error.png`
                                }
                                alt="状态"
                                title="状态"
                            />
                            <span
                                className={
                                    toolCommon.ipv6Enable ? styles.statusOk : styles.errorStstus
                                }
                            >
                                {toolCommon.ipv6Enable ? '支持IPv6' : '不支持IPv6'}
                            </span>
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>Public DNS 解析结果</span>
                        </div>
                        <div className={styles.dnsValue}>
                            解析记录
                            <span onClick={() => switchOpenMoreOne()} className={styles.detail}>
                                详情
                                <img
                                    style={{transform: isOpenOne ? 'rotate(180deg)' : ''}}
                                    src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220723/arrow.svg"
                                    alt="箭头"
                                    title="箭头"
                                />
                            </span>
                            <div
                                className={`${isOpenOne ? styles.valueFixHeight : styles.hiddenValue
                                }`}
                            >
                                {toolCommon?.publicDigResp?.answer?.map((item: string) => (
                                    <div className={styles['englist-info-item']} key={item}>
                                        {item}
                                    </div>
                                )) || '-'}
                            </div>
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>TTL生效时间</span>
                        </div>
                        <div className={styles.value}>
                            域名TTL生效时间为{toolCommon?.digResp?.ttl || '0'}秒
                            <div className={styles['last-time-tipOther']}>
                                如果域名记录修改不久，请等待TTL生效时间后再次检测
                            </div>
                        </div>
                    </div>
                </section>
                <section className={styles['left-module-item']}>
                    <div className={styles['domain-title']}>
                        <div className={styles['domain-title-text']}>网站检查</div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>备案检查</span>
                        </div>
                        <div className={styles.dnsValue}>
                            <img
                                src={
                                    checkMsg.exists
                                        ? `${BCD_BASE_URL}/status-ok.png`
                                        : `${BCD_BASE_URL}/status-error.png`
                                }
                                alt="状态"
                                title="状态"
                            />
                            <span
                                className={checkMsg.exists ? styles.statusOk : styles.errorStstus}
                            >
                                {checkMsg.exists ? `网站已备案(${checkMsg.number})` : '网站未备案'}
                            </span>
                        </div>
                        {/* <div className={styles.value}>{checkMsg.registrantName || "-"}</div> */}
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>Ping 检查</span>
                        </div>
                        <div className={styles.dnsValue}>
                            <img
                                src={
                                    ping.ok
                                        ? `${BCD_BASE_URL}/status-ok.png`
                                        : `${BCD_BASE_URL}/status-error.png`
                                }
                                alt="状态"
                                title="状态"
                            />
                            <span className={ping.ok ? styles.statusOk : styles.errorStstus}>
                                {ping.ok ? 'Ping 检查正常' : 'Ping 检查异常'}
                            </span>
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>HTTP 状态码</span>
                        </div>
                        <div className={styles.dnsValue}>
                            <img
                                src={
                                    dial.ok
                                        ? `${BCD_BASE_URL}/status-ok.png`
                                        : `${BCD_BASE_URL}/status-error.png`
                                }
                                alt="状态"
                                title="状态"
                            />
                            <span className={dial.ok ? styles.statusOk : styles.errorStstus}>
                                {dial.ok ? '访问正常' : '访问异常'}
                            </span>
                            <span className={styles.code}>(状态码{dial.code})</span>
                        </div>
                    </div>
                </section>
                {hascert ? (
                    <section className={styles['left-module-item']}>
                        <div className={styles['domain-title']}>
                            <div className={styles['domain-title-text']}>网站证书检查</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>信任状态</span>
                            </div>
                            <div className={styles.dnsValue}>
                                <img
                                    src={
                                        cert.trust
                                            ? `${BCD_BASE_URL}/status-ok.png`
                                            : `${BCD_BASE_URL}/status-error.png`
                                    }
                                    alt="状态"
                                    title="状态"
                                />
                                <span className={cert.trust ? styles.statusOk : styles.errorStstus}>
                                    {cert.trust ? '可信' : '不可信'}
                                </span>
                            </div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>通用名称</span>
                            </div>
                            <div className={styles.value}>{cert.common_name || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>颁发者</span>
                            </div>
                            <div className={styles.value}>{cert.issuer_common_name || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>启动SNI</span>
                            </div>
                            <div className={styles.value}>{cert?.sni_enable ? '是' : '否'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>加密算法</span>
                            </div>
                            <div className={styles.value}>{cert.cipher_suite || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>签名算法</span>
                            </div>
                            <div className={styles.value}>{cert.signature_algorithm || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>证书品牌</span>
                            </div>
                            <div className={styles.value}>{cert.cert_brand || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>签名算法</span>
                            </div>
                            <div className={styles.value}>{cert.signature_algorithm || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>证书类型</span>
                            </div>
                            <div className={styles.value}>{cert.cert_type || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>开始时间</span>
                            </div>
                            <div className={styles.value}>
                                {calcTimestamp(cert.not_before) || '-'}
                            </div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>结束时间</span>
                            </div>
                            <div className={styles.value}>
                                {calcTimestamp(cert.not_after) || '-'}
                            </div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>OCSP装订状态</span>
                            </div>
                            <div className={styles.value}>
                                {cert?.info?.ocsp_stapled ? '支持' : '不支持'}
                            </div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>组织结构</span>
                            </div>
                            <div className={styles.value}>{cert.organization || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>部门</span>
                            </div>
                            <div className={styles.value}>{cert.department || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>备用名称</span>
                            </div>

                            {cert?.dns_names ? (
                                isOpenMoreCert ? (
                                    <div className={styles['english-info']}>
                                        {cert?.dns_names?.map((item: string, index: number) => (
                                            <div key={item}>
                                                {item}
                                                {index === cert.dns_names.length - 1 ? (
                                                    <span
                                                        className={styles.detail}
                                                        onClick={() => switchOpenCert()}
                                                    >
                                                        收起全部
                                                        <img
                                                            style={{transform: 'rotate(180deg)'}}
                                                            src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220723/arrow.svg"
                                                            alt="箭头"
                                                            title="箭头"
                                                        />
                                                    </span>
                                                ) : (
                                                    ''
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className={styles.dnsValue}>
                                        {cert?.dns_names[0]}
                                        <span
                                            className={styles.detail}
                                            onClick={() => switchOpenCert()}
                                        >
                                            展开全部
                                            <img
                                                style={{transform: ''}}
                                                src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220723/arrow.svg"
                                                alt="箭头"
                                                title="箭头"
                                            />
                                        </span>
                                    </div>
                                )
                            ) : (
                                '-'
                            )}
                        </div>
                    </section>)
                    : (
                        <section className={styles['left-module-item']}>
                            <div className={styles['domain-title-des']}>
                                <div className={styles['domain-title-text']}>网站证书检查</div>
                                <div className={styles['domain-des']}>暂未检测到网站证书</div>
                                <a
                                    className={styles.cloundBtn}
                                    href={`${hostMap.getHost('cloud')}/product/ssl.html`}
                                    target="_blank"
                                    title="创建证书"
                                >
                                    创建证书
                                </a>
                            </div>
                        </section>
                    )
                }
                {hasBusiness ? (
                    <section className={styles['left-module-item']}>
                        <div className={styles['domain-title']}>
                            <div className={styles['domain-title-text']}>站点企业信息</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>法定代表人</span>
                            </div>
                            <div className={styles.value}>{business.legal_person || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>注册资本</span>
                            </div>
                            <div className={styles.value}>{business.reg_capital || '0'}(万)</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>曾用名</span>
                            </div>
                            <div className={styles.value}>{'-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>统一社会信用代码</span>
                            </div>
                            <div className={styles.value}>{business.license_number || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>工商注册号</span>
                            </div>
                            <div className={styles.value}>{business.reg_no || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>登记机关</span>
                            </div>
                            <div className={styles.value}>{business.authority || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>企业类型</span>
                            </div>
                            <div className={styles.value}>{business.prin_type || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>行政区划</span>
                            </div>
                            <div className={styles.value}>{business.district_code || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>经营状态</span>
                            </div>
                            <div className={styles.value}>{business.open_status || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>实缴资本</span>
                            </div>
                            <div className={styles.value}>{business.paidin_capital || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>所属行业</span>
                            </div>
                            <div className={styles.value}>{business.industry || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>纳税人识别号</span>
                            </div>
                            <div className={styles.value}>{business.tax_no || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>组织机构代码</span>
                            </div>
                            <div className={styles.value}>{business.org_no || '-'}</div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>成立日期</span>
                            </div>
                            <div className={styles.value}>
                                {calcTimestamp(business.established_date) || '-'}
                            </div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>营业期限</span>
                            </div>
                            <div className={styles.value}>
                                {calcTimestamp(business.open_time_start) || '-'}至
                                {calcTimestamp(business.open_time_end) || '-'}
                            </div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>审核/年检日期</span>
                            </div>
                            <div className={styles.value}>
                                {calcTimestamp(business.verification_date) || '-'}
                            </div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>注册地址</span>
                            </div>
                            <div className={styles.dnsValue}>
                                {business.reg_addr || '-'}

                                {business.reg_addr ? (
                                    <a
                                        href={`http://api.map.baidu.com/geocoder?address=${business.reg_addr}&output=html`}
                                        target="blank"
                                        className={styles.link}
                                        title="查看地图"
                                    >
                                        查看地图
                                        <img
                                            style={{transform: 'rotate(270deg)'}}
                                            src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220723/arrow.svg"
                                            alt="箭头"
                                            title="箭头"
                                        />
                                    </a>
                                ) : (
                                    ''
                                )}
                            </div>
                        </div>
                        <div className={styles['info-item']}>
                            <div className={styles.key}>
                                <span className={styles.chinese}>经营范围</span>
                            </div>
                            {business?.scope ? (
                                isOpenAll ? (
                                    <div className={styles.value}>
                                        <div className={styles.openAll}>
                                            <div
                                                className={styles.detail}
                                                onClick={() => switchOpenAll()}
                                            >
                                                收起全部
                                                <img
                                                    style={{transform: 'rotate(180deg)'}}
                                                    src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220723/arrow.svg"
                                                    alt="箭头"
                                                    title="箭头"
                                                />
                                            </div>
                                            {business.scope}
                                        </div>
                                    </div>
                                ) : (
                                    <div className={styles.value}>
                                        <div className={styles.closeAll}>
                                            <div
                                                className={styles.detail}
                                                onClick={() => switchOpenAll()}
                                            >
                                                展开全部
                                                <img
                                                    style={{transform: ''}}
                                                    src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220723/arrow.svg"
                                                    alt="箭头"
                                                    title="箭头"
                                                />
                                            </div>
                                            {business.scope}
                                        </div>
                                    </div>
                                )
                            ) : (
                                '-'
                            )}
                        </div>
                    </section>)
                    : (
                        <section className={styles['left-module-item']}>
                            <div className={styles['domain-title-des']}>
                                <div className={styles['domain-title-text']}>站点企业信息</div>
                                <div className={styles['domain-des']}>暂未检测到站点企业信息</div>
                                <a
                                    className={styles.cloundBtn}
                                    href={`${hostMap.getHost('cloud')}/product/cbs#/park?trace=bcdsearch&pageResource=bcdsearch`}
                                    target="_blank"
                                    title="企业注册"
                                >
                                    企业注册
                                </a>
                            </div>
                        </section>
                    )
                }

            </>
        )
    );
}
