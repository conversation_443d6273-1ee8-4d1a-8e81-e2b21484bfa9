/**
 * @file BCD域名
 * @desc dns解析模块
 */
import {Select, Spin} from 'antd';
import React, {useState} from 'react';
import {dnsSelectData} from '../defaultModel';
import styles from './dns.module.less';

const {Option} = Select;

export function DnsPage(props: {
  domainInfo: any;
  searchVal: string;
  getDns: any;
  hasDNS: boolean;
}) {
    const searchVal: string = props.searchVal;
    const domainInfo: any = props.domainInfo;
    const hasDns: boolean = props.hasDNS;

    // 声明搜索框内的数据
    const [searchValue, setSearchValue] = useState<string>('');
    const [type, setType] = useState<number>(255);

    const handleSuccess = (value: string, type: number) => {
        props.getDns(value, type);
    };

    const onChange: (value: string) => void = value => {
        setType(Number(value));
        handleSuccess(searchValue, Number(value));
    };
    const handleDNSPage: (event: React.KeyboardEvent<HTMLInputElement>) => void = e => {
        if (e.key === 'Enter') {
            handleSuccess(searchValue, type);
        }
    };
    return (
        (!hasDns ? (
            <section className={`${styles['left-module-item']}`}>
                <div className={styles['error-tip']}>
                    <div className={styles['error-img']}>
                        <Spin />
                        <div className={styles['error-text']}>正在加载中…</div>
                    </div>
                </div>
            </section>
        ) : (
            <section className={styles['left-module-item']}>
                <div className={styles['domain-title']}>
                    <div className={styles['domain-title-text']}>{searchVal}</div>
                    <div className={styles['domain-title-right']}>
                        <input
                            className={styles['input-box']}
                            type="text"
                            placeholder="指定DNS服务器 （选填）"
                            value={searchValue ? searchValue : ''}
                            onChange={e => setSearchValue(e.target.value)}
                            onKeyUp={handleDNSPage}
                        />
                        <Select
                            defaultValue="ALL"
                            className={styles['select-box']}
                            onChange={onChange}
                        >
                            {
                                dnsSelectData.map((item: any) => (
                                    <Option value={item.value} key={item.value}>{item.label}</Option>
                                ))
                            }
                        </Select>
                    </div>
                </div>
                <div className={styles.desc}>
                    <span className={styles['desc-time']}>
                        QUERY:{domainInfo?.metaCount?.query || '0'}
                    </span>

                    <span className={styles['desc-time']}>
                        ANSWER:{domainInfo?.metaCount?.answer || '0'}
                    </span>

                    <span className={styles['desc-time']}>
                        AUTHORITY:{domainInfo?.metaCount?.authority || '0'}
                    </span>

                    <span className={styles['desc-time']}>
                        ADDITIONAL:{domainInfo?.metaCount?.additiona || '0'}
                    </span>
                </div>
                <div className={styles['info-item']}>
                    <div className={styles.key}>
                        <span className={styles.chinese}>原始请求</span>
                        <span className={styles.english}>QUESTION</span>
                    </div>
                    <div className={styles.value}>
                        {domainInfo?.question?.map((item: string) => (
                            <span key={item}>{item}</span>
                        )) || '-'}
                    </div>
                </div>
                <div className={styles['info-item']}>
                    <div className={styles.key}>
                        <span className={styles.chinese}>解析数据</span>
                        <span className={styles.english}>ANSWER</span>
                    </div>
                    <div className={styles.value}>
                        {domainInfo?.answer?.map((item: string) => (
                            <span key={item}>{item}</span>
                        )) || '-'}
                    </div>
                </div>
                <div className={styles['info-item']}>
                    <div className={styles.key}>
                        <span className={styles.chinese}>权威服务器</span>
                        <span className={styles.english}>ANSWER</span>
                    </div>
                    <div className={styles.value}>{domainInfo?.authority || '-'}</div>
                </div>
                <div className={styles['info-item']}>
                    <div className={styles.key}>
                        <span className={styles.chinese}>权威服务器地址</span>
                        <span className={styles.english}>ADDITONA</span>
                    </div>
                    <div className={styles.value}>{domainInfo?.additional || '-'}</div>
                </div>
            </section>
        ))
    );
}
