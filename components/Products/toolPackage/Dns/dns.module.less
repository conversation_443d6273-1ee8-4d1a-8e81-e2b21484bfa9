.left-module-item {
  width: 1180px;
  box-sizing: border-box;
  padding: 24px;
  background-color: #FFF;
  border-radius: 6px;
  margin: 20px auto;

  .domain-title {
    font-family: PingFangSC-Semibold;
    font-size: 18px;
    color: #222;
    letter-spacing: 0;
    line-height: 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .domain-title-text {
      font-weight: 600;
    }
    .domain-title-right {
      display: flex;
      align-items: center;
      .input-box {
        font-family: PingFangSC-Regular;
        height: 32px;
        font-size: 12px;
        color: #222;
        line-height: 32px;
        font-weight: 400;
        width: 200px;
        border: 1px solid #E8E9EB;
        border-radius: 4px;
        padding: 8px;
      }
      .select-box {
        width: 80px;
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #151B26;
        line-height: 20px;
        font-weight: 400;
        height: 32px;
        line-height: 32px;
        margin-left: 10px;
        border-radius: 4px;
      }
      :global {
        .ant-select-selector {
          border-radius: 4px;
          border: 1px solid #E8E9EB;
        }
      }
    }
    .input-box::placeholder {
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #B8BABF;
      line-height: 20px;
      font-weight: 400;
    }
    .input-box:focus {
      outline: none;
      color: #222;
    }

    .entrust-btn {
      width: 102px;
      height: 32px;
      border-radius: 4px;
      background-color: #2468F2;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #FFF;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .entrust-btn:hover {
      background-color: #528EFF;
    }
  }

  .desc {
    margin-top: 2px;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 20px;
    font-weight: 400;
    display: flex;

    .desc-time {
      opacity: 0.4;
      color: #222222;
      display: inline-block;
      margin-right: 8px;
    }

    .refresh-btn {
      margin-left: 12px;
      color: #2468F2;
      cursor: pointer;
      display: flex;
      align-items: center;

      img {
        margin-right: 4px;
      }
    }
  }

  .info-item {
    margin-top: 24px;
    display: flex;

    .key {
      width: 224px;
      opacity: 0.7;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #222;
      letter-spacing: 0;
      line-height: 24px;
      font-weight: 400;
      display: flex;
      flex-direction: column;
      text-align: left;
      .chinese {
        font-size: 14px;
        line-height: 24px;
      }

      .english {
        font-size: 12px;
        line-height: 20px;
      }
    }

    .value {
      flex: 1;
      display: flex;
      flex-direction: column;
      text-align: left;
      .comment {
        display: inline-block;
        margin-left: 10px;
      }
      .last-time-tip {
        margin-top: 8px;
        width: 552px;
        height: 56px;
        box-sizing: border-box;
        padding: 8px 12px;
        background-color: rgba(22, 22, 22, 0.04);
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #5E626A;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }

      span {
        margin-bottom: 4px;
      }
    }
  }

  .english-info {
    margin-top: 14px;
    overflow: hidden;
    text-align: left;
    .englist-info-item {
      margin-bottom: 4px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #222;
      line-height: 24px;
      font-weight: 400;
    }
  }

  .left-item-open {
    height: 278px;
  }

  .open-more {
    margin: 8px auto 0;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #2468F2;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
    text-align: center;
    cursor: pointer;
  }

  .open-more:hover {
    color: #528EFF;
  }
  .error-tip {
    height: 336px;
    display: flex;
    justify-content: center;
    align-items: center;
    .error-img {
      img {
        height: 160px;
        width: 160px;
      }
      .error-text {
        font-size: 12px;
        color: #84868C;
        text-align: center;
        line-height: 20px;
        font-weight: 400;
      }
    }
  }
}
.model-container {
  width: 100vw;
  height: 100vh;
  background: rgba(22, 22, 22, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;

  .model {
    width: 520px;
    height: 240px;
    background: #FFF;
    box-shadow: 0 6px 16px 2px rgba(7, 12, 20, 0.12);
    border-radius: 4px;
    box-sizing: border-box;
    padding: 24px;

    .model-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 50px;

      .model-title {
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #151b26;
        line-height: 22px;
        font-weight: 500;
      }
    }
  }
}
