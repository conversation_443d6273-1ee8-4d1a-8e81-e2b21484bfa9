/**
 * @file BCD域名
 * @desc 网站检测模块
 */
import {Popover, Spin} from 'antd';
import React, {useState} from 'react';
import {SimpleVerify as ReactSimpleVerify} from '@components/Products/whois/SimpleVerify/SimpleVerify';
import {v4 as uuidv4} from 'uuid';
import {hostMap} from '../../util';
import styles from './whois.module.less';

const BCD_WHOIS_URL = 'https://bcd-public.cdn.bcebos.com/whois';
const BCD_WHOIS_BASE_URL
  = 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220723';

// eslint-disable-next-line complexity
export function WhoisPage(props: {
  domainInfo: any;
  searchVal: string;
  isOpenDialog: boolean;
  verifySuccessed: any;
  tip: string;
}) {
    const domainInfo: any = props.domainInfo;
    const openDialog: boolean = props.isOpenDialog;
    const tip: string = props.tip;
    // 标记是否展开更多
    const [isOpenMore, setIsOpenMore] = useState<boolean>(true);
    const [isOpenDialog, setIsOpenDialog] = useState<boolean>(openDialog);

    const handleSuccess = () => {
        props.verifySuccessed();
        setTimeout(() => {
            setIsOpenDialog(false);
        }, 1000);
    };

    const clickBtn = () => {
        setIsOpenDialog(true);
    };

    // 切换英文是否展开状态
    const switchOpenMore: () => void = () => {
        setIsOpenMore(!isOpenMore);
    };

    return (
        (!tip && !domainInfo.domain) ? (
            <section className={`${styles['left-module-item']}`}>
                <div className={styles['error-tip']}>
                    <div className={styles['error-img']}>
                        <Spin />
                        <div className={styles['error-text']}>正在加载中…</div>
                    </div>
                </div>
            </section>
        ) : tip ? (
            <section className={`${styles['left-module-item']}`}>
                <div className={styles['error-tip']}>
                    <div className={styles['error-img']}>
                        <img
                            src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220809/errBack.png"
                            alt="whois查询"
                            title="whois查询"
                        />
                        <div className={styles['error-text']}>{tip}</div>
                    </div>
                </div>
            </section>
        ) : (
            <>
                <section className={styles['left-module-item']}>
                    <div className={styles['domain-title']}>
                        <div className={styles['domain-title-text']}>域名{domainInfo.domain || '-'}注册信息</div>
                        <div className={styles['domain-title-right']}>
                            <Popover
                                content={'委托专业经纪人帮助您回购该域名，助力您以合理地价格成交'}
                                trigger="hover"
                            >
                                <img src={`${BCD_WHOIS_BASE_URL}/mark.svg`} alt="委托购买" title="委托购买" />
                            </Popover>
                            <a
                                className={styles['entrust-btn']}
                                href={`${hostMap.getHost(
                                    'console'
                                )}/bcd/#/bcd/entrust/create~domain=${
                                    domainInfo.domain
                                }&pageResource=BCD_WHOIS_PORTAL`}
                                target="_blank"
                                title="委托购买该域名"
                            >
                                委托购买该域名
                            </a>
                        </div>
                    </div>
                    <div className={styles.desc}>
                        <span className={styles['desc-time']}>
                            更新时间:{domainInfo.queryTime || '-'}
                            （域名注册信息为缓存信息，非实时信息）
                        </span>
                        <span className={styles['refresh-btn']} onClick={clickBtn}>
                            <img src={`${BCD_WHOIS_BASE_URL}/reload.svg`} alt="重新加载" title="重新加载" />
                            获取最新信息
                        </span>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>域名所有者</span>
                            <span className={styles.english}>Registrant</span>
                        </div>
                        <div className={styles.value}>{domainInfo.registrantName || '-'}</div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>所有者联系邮箱</span>
                            <span className={styles.english}>Registrant Email</span>
                        </div>
                        <div className={styles.value}>
                            {domainInfo.registrantEmail || '-'}
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>注册商</span>
                            <span className={styles.english}>Sponsoring Registrant</span>
                        </div>
                        <div className={styles.value}>
                            {domainInfo.sponsoringRegistrar || '-'}
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>注册日期</span>
                            <span className={styles.english}>Sponsoring Data</span>
                        </div>
                        <div className={styles.value}>
                            {domainInfo.registrationDate || '-'}
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>到期日期</span>
                            <span className={styles.english}>Expirantion Data</span>
                        </div>
                        <div className={styles.value}>
                            {domainInfo.expirationDate || '-'}
                            <div className={styles['last-time-tip']}>
                                受WHOIS信息同步影响，域名到期日期仅供参考，实际到期日期请咨询注册商。在实际到期日期后续费，可能会导致域名使用异常，如网站无法访问等
                            </div>
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>域名状态</span>
                            <span className={styles.english}>Domain Status</span>
                        </div>
                        <div className={styles.value}>
                            {domainInfo?.domainStatus?.map((item: string) => (
                                <span key={item}>
                                    {item}
                                    <span className={styles.comment}>
                                        {domainInfo.domainStatusMap[item]}
                                    </span>
                                </span>
                            )) || '-'}
                        </div>
                    </div>
                    <div className={styles['info-item']}>
                        <div className={styles.key}>
                            <span className={styles.chinese}>DNS服务器</span>
                            <span className={styles.english}>Name Serves</span>
                        </div>
                        <div className={styles.value}>
                            {domainInfo?.nameServer?.map((item: string) => (
                                <span key={item}>{item}</span>
                            )) || '-'}
                        </div>
                    </div>
                </section>
                <section className={`${styles['left-module-item']}`}>
                    <div className={styles['domain-title']}>
                        <div className={styles['domain-title-text']}>详细英文信息</div>
                    </div>
                    <div
                        className={`${styles['english-info']} ${
                            isOpenMore ? styles['left-item-open'] : ''
                        }`}
                    >
                        {domainInfo?.rawData?.map((item: string) => (
                            <div className={styles['englist-info-item']} key={uuidv4()}>
                                {item}
                            </div>
                        )) || '-'}
                    </div>
                    <div className={styles['open-more']}>
                        <span onClick={() => switchOpenMore()}>
                            {isOpenMore ? '展开' : '收起'}全部
                        </span>
                        <img
                            style={{transform: isOpenMore ? '' : 'rotate(180deg)'}}
                            src={`${BCD_WHOIS_BASE_URL}/arrow.svg`}
                            alt="箭头"
                            title="箭头"
                        />
                    </div>
                </section>
                {isOpenDialog ? (
                    <div className={styles['model-container']}>
                        <div className={styles.model}>
                            <div className={styles['model-header']}>
                                <div className={styles['model-title']}>安全验证</div>
                                <img
                                    style={{cursor: 'pointer'}}
                                    src={`${BCD_WHOIS_URL}/close.svg`}
                                    onClick={() => setIsOpenDialog(false)}
                                    alt="关闭"
                                    title="关闭"
                                />
                            </div>
                            <ReactSimpleVerify success={handleSuccess} />
                        </div>
                    </div>
                ) : (
                    ''
                )}
            </>
        )
    );
}
