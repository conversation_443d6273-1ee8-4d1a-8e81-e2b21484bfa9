/**
 * @file BCD域名
 * @desc 域名网站检测首页
 */
import {Tabs} from 'antd';
import React, {useEffect, useState} from 'react';
import Head from 'next/head';
import {hostMap} from '../../util';
import styles from './tab.module.less';

const {TabPane} = Tabs;
const TabsContent = (props: any) => {
    const {data, tabClassName} = props;
    return (
        <div className={styles.tabsContainer}>
            <div className={styles.tabsTitle}>{data.tabsData.tabsTitle}</div>
            <ul className={`${styles['tab-content']} ${styles[tabClassName]}`}>
                {data.tabsData.tabsList.map((item: any) => (
                    <li key={item.name}>
                        <div className={styles.tabsContent}>
                            <div className={styles.tabsImg}>
                                <img src={item.backImg} alt={item.name} title={item.name} />
                            </div>
                            <div className={styles.tabName}>{item.name}</div>
                            <div className={styles.Content}>{item.content}</div>
                            {item.target
                                ? (
                                    <div className={styles.tabLink}>
                                        <a
                                            href={`${hostMap.getHost('cloud')}/product/bcd/land/#/domain`}
                                            target="_blank"
                                        >
                                            {item.target}
                                            <img
                                                className={styles['arrow-hover']}
                                                src={item.targetIcon}
                                                alt={item.name}
                                                title={item.name}
                                            />
                                        </a>
                                    </div>)
                                : ''}
                        </div>
                    </li>
                ))}
            </ul>
        </div>
    );
};

export function ToolPageTab(props: { tabsData: any, activeKey: string }) {
    const activeKey: string = props.activeKey || '0';
    const [activeTab, setActiveTab] = useState<string>(activeKey);
    const changeTabs: (key: string) => void = key => {
        setActiveTab(key);
    };
    useEffect(() => {
        setActiveTab(activeKey);
    }, [activeKey]);
    useEffect(() => {
        setActiveTab(activeTab);
    }, [activeTab]);
    return (
        <React.Fragment>
            <Head>
                <title>百度智能云-域名网站监测工具</title>
                <meta
                    name="description"
                    content="百度域名网站监测工具，了解域名信息及状态，查看DNS解析，网站备案、HTTP状态、Ping检查，端口安全监测等"
                />
                <meta name="keywords" content="百度云,域名,域名注册,域名查询,Whois查询,域名信息,域名解析,DNS解析,网站备案,HTTP,Ping,证书检查,安全监测" />
            </Head>
            <div className={styles.enterprise}>
                <Tabs
                    activeKey={activeTab}
                    onChange={changeTabs}
                    className={styles.tabsPanel}
                >
                    {props.tabsData.map((item: any) => (
                        <TabPane key={item.index} tab={item.category}>
                            <TabsContent data={item} tabClassName={`tab-${item.index}`} category={item.category} />
                        </TabPane>
                    ))}
                </Tabs>
            </div>
        </React.Fragment>
    );
}
