.enterprise {
  margin-top: 72px;
  text-align: center;
  .tabsPanel {
    margin: 0 auto;
    .tabsContainer {
      background-color: #F8F7F9;
      padding-bottom: 80px;
      .tabsTitle {
        height: 90px;
        font-family: PingFangSC-Medium;
        font-size: 28px;
        color: #222222;
        text-align: center;
        font-weight: 500;
        padding-top: 52px;
        margin-bottom: 30px;
      }

      .tab-content {
        width: 1180px;
        display: flex;
        flex-wrap: nowrap;
        border-radius: 6px;
        margin: 0 auto;
        justify-content: space-between;
        li {
          min-width: 280px;
          max-width: 580px;
          min-height: 284px;
          max-height: 350px;
          position: relative;
          background: #FFF;
          border-radius: 4px;
          transition-duration: 0.3s;
          transition-timing-function: ease-out;
          .tabsContent {
            padding: 0 28px;
            .tabsImg {
              height: 120px;
              width: 120px;
              margin: 30px auto 20px;
              img {
                height: 100%;
                width: 100%;
              }
            }
            .tabName {
              font-family: PingFangSC-Medium;
              font-size: 18px;
              color: #222222;
              text-align: center;
              line-height: 28px;
              font-weight: 500;
            }
            .Content {
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: #222222;
              text-align: center;
              line-height: 24px;
              font-weight: 400;
              margin-top: 12px;
            }
            .tabLink {
              margin: 27px auto 40px;
              a {
                font-size: 14px;
                color: #2468F2;
                line-height: 24px;
                font-weight: 500;
                display: flex;
                align-items: center;
                justify-content: center;

                .arrow-hover {
                  transition: 0.2s all ease-in;
                }
              }

              a:hover {
                color: #528EFF;
              }
            }
          }
        }
        li:hover {
          background: #FFF;
          box-shadow: 0 6px 16px 2px rgba(7, 12, 20, 0.08);
          border-radius: 4px;
          transition-duration: 0.3s;
          transform: translate(0, -12px);
          transition-timing-function: ease-out;
        }

      }
      .tab-2 li {
        width: 380px;
      }
      .tab-1 li {
        width: 580px;
      }
    }
    :global {
      .ant-tabs-nav {
        margin-bottom: 0px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        &::before {
          background-color: #CCD7F0;
        }
        .ant-tabs-nav-wrap {
          justify-content: center;
          .ant-tabs-nav-list {
            width: 1180px;
            display: flex;
            justify-content: center;
            .ant-tabs-tab {
              margin: 0 86px;
              opacity: 0.9;
              font-family: PingFangSC-Semibold;
              font-size: 18px;
              color: #222222;
              text-align: center;
              line-height: 28px;
              font-weight: 600;
              .ant-tabs-tab-btn {
                font-weight: 600;
                animation-play-state: paused;
              }
              &:hover,
              &.ant-tabs-tab-active {
                .ant-tabs-tab-btn {
                  color: #2468F2;
                  font-weight: 600;
                  animation-play-state: paused;
                }
              }
            }
            .ant-tabs-ink-bar {
              background-color: #2468F2;
            }
          }
        }
      }
    }
  }
}
