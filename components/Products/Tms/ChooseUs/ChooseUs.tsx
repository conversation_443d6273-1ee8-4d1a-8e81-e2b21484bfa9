/**
 * @file TMS商标官网首页
 * @desc 为什么选择我们模块
 */

import {BlockTemplate} from '@baidu/bce-portal-ui';

import {chooseData} from '../defaultModel';

import styles from './chooseus.module.less';

export const ChooseUs = () => {
    return (
        <BlockTemplate
            title="为什么选择我们？"
            des="更专业的商标服务流程，更优质的商标申请体验"
            className={styles.chooseBack}
        >
            <div className={styles.aboutContainer}>
                {
                    chooseData.map(item => (
                        <div key={item.class}>
                            <div className={styles['item-' + item.class]} key={item.class}>
                                <div className={styles.title}>{item.title}</div>
                                <div className={styles.des}>
                                    {item.des1}
                                </div>
                                <div className={styles.des}>
                                    {item.des2}
                                </div>
                            </div>
                            <div className={styles['line-' + item.class]}></div>
                        </div>
                    ))
                }
                <div className={styles['img-box']}>
                    {
                        <video
                            playsInline
                            webkit-playsinline
                            style={
                                {
                                    width: '886px',
                                    position: 'absolute',
                                    left: '50%',
                                    top: '50%',
                                    transform: 'translate(-50%, -50%)',
                                }
                            }
                            autoPlay
                            loop
                            muted
                            src="https://tms-static.cdn.bcebos.com/tms-server-portal/why-select-us.mp4"
                        >

                        </video>
                    }
                </div>
            </div>
        </BlockTemplate>
    );
};
