.chooseBack {
    background: #F8FAFC;
}
.aboutContainer {
    width: 1180px;
    height: 460px;
    margin: 0 auto;
    position: relative;
    color: #222;
    // overflow: hidden;

    .title {
        font-family: PingFangSC-Medium;
        font-size: 18px;
        font-weight: 700;
        line-height: 25px;
        margin-bottom: 4px;
    }

    .des {
        color: #777;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        line-height: 24px;
        font-weight: 400;
        margin: 4px 0;
        display: flex;
        align-items: center;
    }

    .des::before {
        content: '';
        display: block;
        width: 6px;
        height: 6px;
        background: #2468F2;
        border-radius: 50%;
        margin-right: 8px;
    }

    .item-l-t {
        position: absolute;
        left: 0;
        top: 40px;
        z-index: 999;
    }

    .item-r-t {
        position: absolute;
        right: -18px;
        top: 40px;
        z-index: 999;
    }

    .item-l-b {
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 999;
    }

    .item-r-b {
        position: absolute;
        right: -16px;
        bottom: 0;
        z-index: 999;
    }

    .img-box {
        width: 886px;
        height: 442px;
        margin: 20px auto 0;
        position: relative;

        span {
            position: absolute;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: #2468F2;
        }

        .spot-l-t {
            left: 23%;
            top: 22.65%;
            z-index: 99;
        }

        .spot-r-t {
            right: 23%;
            top: 22.65%;
            z-index: 99;
        }

        .spot-l-b {
            left: 15.3%;
            bottom: 14%;
            z-index: 99;
        }

        .spot-r-b {
            right: 15.3%;
            bottom: 14.2%;
            z-index: 99;
        }

        video {
            mix-blend-mode: darken;
        }
        
        video:focus {
            outline: none;
        }
    }

    .line-l-t {
        width: 200px;
        height: 4px;
        border-top: 1px dashed #888;
        position: absolute;
        top: 54px;
        left: 112px;
        z-index: 999;
    }

    .line-l-t::after {
        display: block;
        content: '';
        width: 28px;
        height: 1px;
        border-top: 1px dashed #888;
        position: absolute;
        top: 10px;
        right: -26px;
        transform: rotate(50deg);
    }

    .line-r-t {
        width: 126px;
        height: 4px;
        border-top: 1px dashed #888;
        position: absolute;
        top: 54px;
        right: 230px;
        z-index: 999;
    }

    .line-r-t::after {
        display: block;
        content: '';
        width: 28px;
        height: 1px;
        border-top: 1px dashed #888;
        position: absolute;
        top: 10px;
        left: -25px;
        transform: rotate(-50deg);
    }

    .line-l-b {
        width: 136px;
        height: 4px;
        border-top: 1px dashed #888;
        position: absolute;
        bottom: 66px;
        left: 108px;
        z-index: 999;
    }

    .line-l-b::after {
        display: block;
        content: '';
        width: 20px;
        height: 1px;
        border-top: 1px dashed #888;
        position: absolute;
        bottom: 11px;
        right: -17px;
        transform: rotate(120deg);
    }

    .line-r-b {
        width: 74px;
        height: 4px;
        border-top: 1px dashed #888;
        position: absolute;
        bottom: 68px;
        right: 220px;
        z-index: 999;
    }

    .line-r-b::after {
        display: block;
        content: '';
        width: 16px;
        height: 1px;
        border-top: 1px dashed #888;
        position: absolute;
        bottom: 10px;
        left: -14px;
        transform: rotate(-120deg);
    }
}