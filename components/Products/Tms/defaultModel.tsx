/**
 * @file TMS商标官网(全局静态数据)
 * /components/Products/Tms/defaultModel.tsx
 */

import axios from 'axios';
import moment from 'moment';
import md5 from 'js-md5';
import {Ioc} from '@baidu/bce-decorators';

import {httpUrlService} from '../../../common/services/httpUrl';

export const isSandbox = () => {
    if (typeof window !== 'undefined') {
        return /(cloudtest)|(baidu-int)|(qasandbox)|(bcetest)|(localhost)/.test(location.origin);
    }
    return false;
};

export const softwareRightListData = [
    {
        title: '普通自助登记',
        des: '适用于有一定软著申请经验并能自主评估风险的用户，性价比高',
        advantage: [
            '全流程高效办理，递交快速',
            '人工初步审查，确保材料符合递交要求',
            '全线上全方位管理，进度流程透明',
            '普通审查2-4个月完成',
        ],
        price: '260',
        showNewActity: true,
        actityText: '新客专享前3单260元',
        buyUrl: '#/srs/ruanzhu/order?type=INTELLIGENT_SELF_REGISTRATION',
        consultUrl: '#/srs/ruanzhu/requirement?type=INTELLIGENT_SELF_REGISTRATION',
        showBadge: true,
        badgeText: '新客特惠',
        unit: '/次',
        oldPrice: '原价：320元/次',
    },
    {
        title: '无忧自助登记',
        des: '适用于有一定软著申请经验并能自主评估风险的用户，通过率更高',
        advantage: [
            '申请材料双重严格审核，成功率更高',
            '享受审查优先权，版权保护快人一步',
            '标准化流程，申请进度一目了然',
            '加急审查1个半月完成',
        ],
        price: '999',
        unit: '/次',
        buyUrl: '#/srs/ruanzhu/order?type=WORRY_FREE_SELF_REGISTRATION',
        consultUrl: '#/srs/ruanzhu/requirement?type=WORRY_FREE_SELF_REGISTRATION',
        showBadge: false,
    },
    {
        title: '尊享无忧登记',
        des: '提供申请额外保障，补正两次不通过可退款或重做，增加通过率，申请更安心',
        advantage: [
            '补正两次不通过可退全款或重做',
            '资深专家全程包揽服务，成功率极高',
            '高效受理快速出证，省心更省时',
            '加急审查1个半月完成',
        ],
        price: '1899',
        unit: '/次',
        buyUrl: '#/srs/ruanzhu/order?type=PREMIUM_WORRY_FREE_REGISTRATION',
        consultUrl: '#/srs/ruanzhu/requirement?type=PREMIUM_WORRY_FREE_REGISTRATION',
        showBadge: true,
        badgeText: '省心推荐',
    },
];

export const registryData = [
    {
        title: '智能申请注册',
        des: '0服务费，针对有一定商标申请经验并能自主评估风险的用户',
        type: 'AUXILIARY_REGISTRATION',
        advantage: () => [
            '自助办理，性价比高',
            '自助查询，排查风险',
            '人工严审，提升受理率',
        ],
        price: () => '270',
        showNewActity: true,
        buyUrl: '#/tms/create/self?pageResource=PORTAL_001',
        showBadge: true,
        badgeText: '新客特惠',
        unit: '起',
        oldPrice: '原价：318元',
        contentLabel: '性价比',
        contentValue: '高',
    },
    {
        title: '专家辅助注册',
        des: '流程专业代办，适用于商标风险评估、类别选择无经验的新用户',
        type: 'AUXILIARY_REGISTRATION',
        advantage: () => [
            '专家一对一，全流程代办',
            '分析风险，优化注册方案',
            '规避风险，提升注册成功率',
        ],
        price: () => '499',
        buyUrl: '#/tms/create/advance?type=AUXILIARY_REGISTRATION&pageResource=PORTAL_001',
        consultUrl: '#/tms/create/assistance?type=AUXILIARY_REGISTRATION&pageResource=PORTAL_002',
        showBadge: false,
        contentLabel: '成功率',
        contentValue: '高',
    },
    {
        title: '指定类目至尊无忧注册',
        des: '专家评估提高通过率，用户指定一个小类注册失败，可赔付全款',
        type: 'GUARANTEE_REGISTRATION_ONE_CLS',
        advantage: () => [
            '你注册，我投保',
            '每件商标都有保单',
            '指定类目驳回，赔付全款',
        ],
        price: () => '599',
        buyUrl: '#/tms/create/advance?type=GUARANTEE_REGISTRATION_ONE_CLS&pageResource=PORTAL_001',
        consultUrl: '#/tms/create/assistance?type=GUARANTEE_REGISTRATION_ONE_CLS&pageResource=PORTAL_002',
        showBadge: false,
        contentLabel: '类型',
        contentValue: '指定类目',
    },
    // {
    //     title: '全部小类至尊无忧注册',
    //     des: '专家评估提高通过率，任意一个小类注册失败，可赔付全款',
    //     type: 'GUARANTEE_REGISTRATION',
    //     advantage: () => [
    //         '无忧注册，驳回可退款',
    //         '专家一对一，全流程代办',
    //         '规避风险，提升注册成功率',
    //     ],
    //     price: () => '1680',
    //     buyUrl: '#/tms/create/advance?type=GUARANTEE_REGISTRATION&pageResource=PORTAL_001',
    //     consultUrl: '#/tms/create/assistance?type=GUARANTEE_REGISTRATION&pageResource=PORTAL_002',
    //     showBadge: false,
    //     contentLabel: '类型',
    //     contentValue: '全部小类',
    // },
];

export const transactionListData = [
    {
        title: '精选商标交易',
        des: '海量现成商标，专业商标购买平台，专家全流程服务，交易省心放心',
        imgSrc: 'https://tms-static.cdn.bcebos.com/tms-server-portal/transaction1.png',
        link: '/product/tms/transaction?track=portal&pageResource=portal',
        price: '即买即用',
        hiddenSymbol: true,
        transactionSymbol: true,
        advantage: [
            '标准化流程，专业品质保障',
            '正规标源，快速获得心仪商标',
            '商标即买即用，助力品牌升级',
        ],
    },
    {
        title: '商标驳回复审',
        des: '专家全程提供服务，适用于商标注册申请被驳回，仍想争取被驳商标的用户',
        imgSrc: 'https://tms-static.cdn.bcebos.com/tms-server-portal/transaction2.png',
        link: '#/tms/create/advance?type=REVIEW&pageResource=PORTAL_001',
        price: '1799',
        advantage: [
            '收到商标驳回通知书30天内申请',
            '专业制定优质驳回复审方案',
            '专业复审分析省心放心',
        ],
        consultUrl: '#/tms/create/resubmit?pageResource=PORTAL_001',
    },
    {
        children: [
            {
                title: '商标续展申请',
                des: '智能快捷办理，防止商标被抢注，适用于希望继续保有原商标的用户',
                imgSrc: 'https://tms-static.cdn.bcebos.com/tms-server-portal/transaction3.png',
                link: '#/tms/create/advance?type=XUZHAN&pageResource=PORTAL_001',
                price: '799',
                advantage: [
                    '商标到期前12个月内申请',
                    '流程透明，申请进度实时掌握',
                    '优势价格，专业服务全程护航',
                ],
                consultUrl: '#/tms/create/assistance?type=XUZHAN&pageResource=PORTAL_001',
            },
            {
                title: '商标宽展申请',
                des: '智能自助办理，保护品牌权益，适用于商标过期仍想保有原商标的用户',
                imgSrc: 'https://tms-static.cdn.bcebos.com/tms-server-portal/transaction3.png',
                link: '#/tms/create/advance?type=KUANZHAN&pageResource=PORTAL_001',
                price: '999',
                advantage: [
                    '商标过期6个月内申请',
                    '自助办理，智能高效完成申报',
                    '专业服务，品牌保护全程护航',
                ],
                consultUrl: '#/tms/create/assistance?type=XUZHAN&pageResource=PORTAL_001',
            },
        ],
        title: '续展宽展',
    },
    {
        title: '商标智能监控',
        des: '智能监测指定商标名称、申请人相关商标，展现商标中存在的潜在驳回、可异议、可撤三等风险，同时通过短信、微信、邮件实时告知风险动态。',
        imgSrc: 'https://tms-static.cdn.bcebos.com/tms-server-portal/transaction4.png',
        link: '#/tms/monitor/list?pageResource=portal',
        price: '前100名限免',
        advantage: [
            '商标到期前12个月内申请',
            '流程透明，申请进度实时掌握',
            '优势价格，专业服务全程护航',
        ],
        labelText: '立即体验',
        hiddenSymbol: true,
    },
];

export const extendLeftData = [
    {
        title: '商标确权维护',
        des: '商标异议答辩 | 商标转让申请 | 商标变更申请',
        key: '1',
    },
    {
        title: '保护品牌权益',
        des: '商标撤回申请 | 商标许可备案 | 无效宣告申请',
        key: '2',
    },
    {
        title: '配套服务',
        des: '工商财税 | 资质代办 | SSL证书 | 域名服务',
        key: '3',
    },
];

export const extendData = [
    {
        title: '商标异议答辩',
        des: '适用于商标申请公示期被他人提出异议，希望进行答辩申请以挽回注册的用户。',
        price: '850',
        link: '#/tms/create/assistance?type=OBJECTION_DEFENSE&pageResource=portal',
    },
    {
        title: '商标转让申请',
        des: '适用于需要将自有商标转让给他人的用户，需依照商标法规定向商标局提交转让申请。',
        price: '850',
        link: '#/tms/create/assistance?type=TRANSFER&pageResource=portal',
    },
    {
        title: '商标变更申请',
        des: '适用于需要变更已注册商标的注册人名义、登记地址等注册事项的用户。',
        price: '200',
        link: '#/tms/create/assistance?type=CHANGE&pageResource=portal',
    },
    {
        title: '商标撤三申请',
        des: '适用于因商标资源被恶意抢占，需要撤销他人商标（注册满三年且不使用）的用户。',
        price: '1350',
        link: '#/tms/create/assistance?type=INVALIDATION_APPLICATION&pageResource=portal',
    },
    {
        title: '商标撤三答辩',
        des: '适用于商标被他人申请撤销，希望向商标局提交答辩申请以保留商标使用权的用户。',
        price: '1350',
        link: '#/tms/create/assistance?type=INVALIDATION_DEFENSE&pageResource=portal',
    },
    {
        title: '商标异议申请',
        des: '适用于被他人申请的商标侵权，希望在其公示期提出异议申请以阻止注册的用户。',
        price: '1800',
        link: '#/tms/create/assistance?type=OBJECTION_APPLICATION&pageResource=portal',
    },
    {
        title: '商标撤回申请',
        des: '适用于办理商标注册等商标事宜，在商标局或商评委决定前想要撤回申请的用户。',
        price: '400',
        link: '#/tms/create/assistance?type=RECALL&pageResource=portal',
    },
    {
        title: '商标许可备案',
        des: '适用于需要将自有商标授权许可他人使用的用户，需向商标局提交申请并报送材料。',
        price: '350',
        link: '#/tms/create/assistance?type=LICENSE&pageResource=portal',
    },
    {
        title: '无效宣告申请',
        des: '适用于他人的商标侵犯自身在先权或违反《商标法》，希望宣告该商标无效的用户。',
        price: '2300',
        link: '#/tms/create/assistance?type=APPLICATION_INVALIDATION&pageResource=portal',
    },
    {
        title: '无效宣告答辩',
        des: '适用于注册商标被他人宣告无效后，希望向商标局提交答辩，以维护自身商标权益的用户。',
        price: '1350',
        link: '#/tms/create/assistance?type=DEFENSE_INVALIDATION&pageResource=portal',
    },
    {
        title: '国际商标注册',
        des: '适用于通过《马德里协定》申请办理商标的国际注册，获得其他国家的保护。',
        price: '敬请期待',
        noPrice: true,
    },
    {
        title: '公司注册',
        des: `专业顾问一对一
            价格，流程透明
            极速办理`,
        price: '1',
        unit: '起',
        noBaseUrl: true,
        link: 'https://cloud.baidu.com/product/cbs?trace=tms_portal_qifuheader&pageResource=tms_portal_qifuheader&select=3',
    },
    {
        title: '个体工商户注册',
        des: `适合商标注册场景个体营业执照注册
            最快当天递交材料，高效过审
            资深顾问全程代办，零跑腿`,
        price: '500',
        noBaseUrl: true,
        link: `https://console.bce.baidu.com/cbs/#/park/order/submit?
        stepStart=2&comType=INDIVIDUAL&area=CP0003&taxpayerType=SMALL_SCALE_TAXPAYER&packageType=7`,
    },
    {
        title: '代理记账服务',
        des: '全程资深会计代理，极简流程，无需操心，服务进度实时可查，安全无忧！',
        price: '1199',
        unit: '起',
        noBaseUrl: true,
        link: 'https://cloud.baidu.com/product/bookkeeping#/',
    },
    {
        title: 'ICP经营备案',
        des: `适用于电商、游戏、金融、直播等
            全国代办，1v1服务，售后保障`,
        price: '1200',
        unit: '起',
        noBaseUrl: true,
        link: 'https://cloud.baidu.com/product/bla?',
    },
    {
        title: 'SSL证书',
        des: `提供国内外多个知名品牌的SSL证书
            一站式申请、签发、管理、部署等服务`,
        price: '288',
        unit: '/年起',
        noBaseUrl: true,
        link: 'https://cloud.baidu.com/product/ssl.html?trace=tms_portal_qifuheader&pageResource=tms_portal_qifuheader&select=2',
    },
    {
        title: '域名服务',
        des: `抢注精选域名，塑造品牌形象
            上万优质域名，一口价交易
            备案管家代劳，极速提交，全程跟进`,
        price: '1',
        unit: '/元起',
        noBaseUrl: true,
        link: 'https://cloud.baidu.com/product/bcd.html?trace=tms_portal_qifuheader&pageResource=tms_portal_qifuheader&select=1',
    },
];

export const chooseData = [
    {
        title: '专业服务',
        des1: '资深商标顾问提供服务',
        des2: '专业商标检索、注册、咨询',
        class: 'l-t',
    },
    {
        title: '无忧注册',
        des1: '商标申报全流程代办（非自助注册）',
        des2: '专属顾问提供服务，进度实时知晓',
        class: 'r-t',
    },
    {
        title: '极速递交',
        des1: 'OCR识别资料，填写更快捷',
        des2: '申请资料快至1分钟递交商标局（非工作日顺延次日）',
        class: 'l-b',
    },
    {
        title: '品质保证',
        des1: '注册成功率更高、注册结果更精准',
        des2: '商标服务质量有保证',
        class: 'r-b',
    },
];

export const actionData = [
    {
        title: '新闻公告',
        list: [
            {
                text: '【公告】关于2025蛇年春节放假安排',
                link: 'https://cloud.baidu.com/doc/TMS/s/7l8lj3dq1',
            },
            {
                text: '【公告】关于新用户专享270元起优惠活动调整通知',
                link: 'https://cloud.baidu.com/doc/TMS/s/Kl1yvx1az',
            },
            {
                text: '【公告】关于商标委托书更新的通知',
                link: 'https://cloud.baidu.com/doc/TMS/s/Gkxtv68ih',
            },
        ],
    },
    {
        title: '操作指南',
        list: [
            {
                text: '【操作】商标注册申请操作流程',
                link: 'https://cloud.baidu.com/doc/TMS/s/vkjxq8sca',
            },
            {
                text: '【操作】购买商标操作指南',
                link: 'https://cloud.baidu.com/doc/TMS/s/tkqpaawvy',
            },
            {
                text: '【操作】百度智能云商标服务小程序操作指南',
                link: 'https://cloud.baidu.com/doc/TMS/s/Qkvlu27fx',
            },
        ],
    },
    {
        title: '常见问题',
        list: [
            {
                text: '【问题】不同申请主体需要提供的材料有哪些？',
                link: 'https://cloud.baidu.com/doc/TMS/s/wkjxq8sva',
            },
            {
                text: '【问题】商标注册时应该如何选择注册类目？',
                link: 'https://cloud.baidu.com/doc/TMS/s/akjxq8t26',
            },
            {
                text: '【经验】商标注册被驳回应该如何处理？',
                link: 'https://cloud.baidu.com/doc/TMS/s/9kjxq8t4n',
            },
        ],
    },
];

export const tmsStatus = [
    {
        text: '不限',
        value: 'UNLIMTED',
    },
    {
        text: '已销亡',
        value: 'DEAD',
    },
    {
        text: '待审核',
        value: 'AUDITING',
    },
    {
        text: '已注册',
        value: 'REGISTERED',
    },
    {
        text: '已驳回',
        value: 'REJECTED',
    },
    {
        text: '已初审',
        value: 'FIRST_TRAILED',
    },
    {
        text: '商标无效',
        value: 'INVALID',
    },
];

export const listRegistryData = [
    {
        title: '智能申请注册',
        des: '自助办理提交申请',
        price: '270',
        link: '#/tms/create/self?pageResource=PORTAL_001',
        hot: false,
        url: 'https://tms-static.cdn.bcebos.com/tms-server-portal/self-reg.png',
    },
    {
        title: '专家辅助注册',
        des: '专业顾问全流程代办',
        price: '499',
        link: '#/tms/create/assistance?type=AUXILIARY_REGISTRATION&pageResource=PORTAL_001',
        hot: true,
        url: 'https://tms-static.cdn.bcebos.com/tms-server-portal/aux-reg.png',
    },
    {
        title: '指定类目至尊无忧注册',
        des: '用户指定小类驳回可赔付全款',
        price: '599',
        link: '#/tms/create/assistance?type=GUARANTEE_REGISTRATION_ONE_CLS&pageResource=PORTAL_001',
        hot: false,
        url: 'https://tms-static.cdn.bcebos.com/tms-server-portal/gua-reg.png',
    },
];

export const tmsRouter = [
    {
        text: '商标服务',
        path: '/product/tms',
        asPath: '/product/tms.html',
    },
    {
        text: '商标列表',
        path: '/product/tms/list',
    },
    {
        text: '商标详情',
        path: '/product/tms/detail',
    },
];

export const tradeType = [
    {
        text: '普通商标',
        value: 'NORMAL',
    },
    {
        text: '企业商标',
        value: 'GROUP',
    },
    {
        text: '特殊商标',
        value: 'SPECIAL',
    },
    {
        text: '证书商标',
        value: 'CERTIFICATE',
    },
];

export const defaultProductsTmsProcess = {
    processData: [
        {
            title: '选购域名',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_1.png',
            detail: '作为互联网服务的入口，在网上“安家立户”，您首先需要先拥有一个域名',
            linkArray: [
                {
                    name: '新域名注册',
                    link: 'https://cloud.baidu.com/product/bcd/search.html',
                },
                {
                    name: '域名委托购买',
                    link: 'https://cloud.baidu.com/product/bcd/land/#/domain?track=BCD_PORTAL&clientFrom=CLOUD',
                },
                {
                    name: '二手域名交易',
                    link: 'https://cloud.baidu.com/product/bcd/land/#/fixed?select=1',
                },
            ],
        },
        {
            title: '制作发布网站',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_2.png',
            detail: '您可以在线自行设计制作，也可以委托我们为您量身定做一个网站',
            linkArray: [
                {
                    name: '智能门户建站',
                    link: 'https://aipage.baidu.com/',
                },
                {
                    name: '云服务器',
                    link: 'https://cloud.baidu.com/product/bcc.html',
                },
            ],
        },
        {
            title: 'ICP备案',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_3.png',
            detail: '根据工信部要求，您的域名需在完成ICP备案后，方可开通服务',
            linkArray: [
                {
                    name: '网站ICP备案',
                    link: 'https://cloud.baidu.com/beian/index.html',
                },
                {
                    name: 'ICP许可证办理',
                    link: 'https://cloud.baidu.com/product/bla?linkFrom=bcd',
                },
            ],
        },
        {
            title: '网站优化加固',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_4.png',
            detail: '如何给用户提供速度更快、更安全、更稳定的网站？可以完成以下优化方案：',
            linkArray: [
                {
                    name: '智能解析调度',
                    link: 'https://cloud.baidu.com/product/dns.html',
                },
                {
                    name: '网站证书加密',
                    link: 'https://cloud.baidu.com/product/ssl.html',
                },
                {
                    name: 'CDN网站加速',
                    link: 'https://cloud.baidu.com/product/cdn.html',
                },
                {
                    name: 'BOS对象存储',
                    link: 'https://cloud.baidu.com/product/bos.html',
                },
            ],
        },
        {
            title: '数字营销推广',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_5.png',
            detail: '网站建设完备后，就可以启动互联网和全方位的数字化营销推广工作了，您可以进行：',
            linkArray: [
                {
                    name: '消息服务',
                    link: 'https://cloud.baidu.com/product/sms.html',
                },
                {
                    name: '智能推荐引擎',
                    link: 'https://cloud.baidu.com/product/ai-rec.html',
                },
            ],
        },
        {
            title: '企业品牌保护',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_6.png',
            detail: '数字化时代，作为核心数字资产的企业自有品牌、知识产权需要高度重视并合理利用',
            linkArray: [
                {
                    name: '为公司品牌注册商标',
                    link: 'https://cloud.baidu.com/product/tms.html',
                },
                {
                    name: '品牌中文域名直达',
                    link: 'https://cloud.baidu.com/product/bcd/search.html/#/land?track=BCD_PORTAL&clientFrom=CLOUD',
                },
                {
                    name: '直接选购已有商标',
                    link: 'https://cloud.baidu.com/product/tms/transaction?linkFrom=bcd',
                },
            ],
        },
        {
            title: '运营提效',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_7.png',
            detail: '前端营销体系运营启动后，后端办公与运营效率的提升就显得尤为重要。您可以',
            linkArray: [
                {
                    name: '可视化经营分析 ',
                    link: 'https://cloud.baidu.com/product/sugar.html',
                },
                {
                    name: '企业网盘协同办公',
                    link: 'https://cloud.baidu.com/product/bnd-e.html',
                },
                {
                    name: '电子合同在线签署',
                    link: 'https://cloud.baidu.com/product/xuperevid.html',
                },
            ],
        },
    ],
};

// 获取服务器时间
export const getServerDate = () => {
    return new Promise((resolve, reject) => {
        axios.get(Ioc(httpUrlService).GET_TMS_SERVICE_TIMS).then(res => {
            if (res?.headers?.date) {
                resolve(res.headers.date);
            }
        }).catch(err => reject(err));
    });
};

// 反爬处理
export const antiCrawler = async (requestRoute: string) => {
    try {
        const date = await getServerDate();
        const time = moment(date).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        const timestamp = `${time}@${md5(`POST${requestRoute}${time}ieq%$jsaf23!@fkjwie`)}`;
        return Promise.resolve(timestamp);
    } catch (err) {
        return Promise.reject(err);
    }
};

// 添加百度统计
export const addTmsBaiduStatistics = () => {
    if (/(cloud|qifu)\.baidu\.com/.test(window.location.host)) {
        const hm = document.createElement('script');
        hm.src = 'https://hm.baidu.com/hm.js?46fb8e8aefbfafe9b0ad87964a4f546b';
        const s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(hm, s);
    }
};
