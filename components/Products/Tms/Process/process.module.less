.process {
    width: 100%;
    background: url('https://bcd-public.bj.bcebos.com/bcd_portal/20230309/process_bj.jpg') no-repeat 50%/cover;
    .container {
        width: 1180px;
        margin: 0 auto;
        padding: 60px 0;
        text-align: center;
        .header {
            font-size: 28px;
            color: #222222;
            text-align: center;
            font-weight: 500;
            margin-bottom: 8px;
        }
        .introduce {
            opacity: 0.7;
            font-size: 14px;
            color: #222222;
            text-align: center;
            line-height: 22px;
            font-weight: 400;
            
        }
        .box {
            display: flex;
            margin-top: 32px;
            justify-content: start;
            flex-wrap: wrap;
            text-align: left;
            .card {
                position: relative;
                width: 280px;
                height: 216px;
                box-sizing: border-box;
                padding: 20px;
                background-color: #FFF;
                border-radius: 6px;
                margin-right: 16px;
                z-index: 1;
                border: 1px solid transparent;
                transition: 0.2s all ease-in;
                .card-img {
                    width: 40px;
                    height: 30px;
                    position: absolute;
                    opacity: 0.4;
                    z-index: -1;
                    transition: 0.2s all ease-in;
                }
                .card-title {
                    font-size: 20px;
                    color: #222222;
                    line-height: 28px;
                    font-weight: 600;
                    margin: 24px 0 8px;
                }
                .card-detail {
                    height: 44px;
                    font-size: 14px;
                    color: rgba(34,34,34,0.70);
                    letter-spacing: 0;
                    text-align: justify;
                    line-height: 22px;
                    font-weight: 400;
                    margin-bottom: 16px;
                }
                .card-link-box {
                    display: grid;
                    grid-row-gap: 8px;
                    grid-template-columns: 1fr 1fr;
                    grid-template-rows: 1fr 1fr;
                    grid-auto-flow: column;
                    .link-item {
                        width: fit-content;
                        font-size: 14px;
                        color: #2468F2;
                        text-align: left;
                        line-height: 24px;
                        cursor: pointer;
                        span {
                            vertical-align: middle;
                        }
                        img {
                            width: 16px;
                            height: 16px;
                            margin-left: 4px;
                            transform: rotate(90deg);
                            transition-duration: 0.3s;
                            transition-timing-function: ease-out;
                            transition-delay: 0s;
                        }
                        &:hover {
                            img {
                                transform: rotate(90deg) translateY(-8px);
                            }
                        }
                    }
                }
                .card-bg {
                    transition: 0.2s all ease-in;
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    width: 260px;
                    height: 100px;
                    opacity: 0.65;
                    z-index: -1;
                }
            }
            .card:hover {
                border: 1px solid rgba(34,34,34,0.08);
                box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
                .card-img {
                    opacity: 1;
                }
                .card-bg {
                    opacity: 1;
                }
            }
        }
        .box div:nth-child(4) {
            margin: 0;
        }
        .box div:nth-child(7) {
            margin: 0;
        }
        .box div:nth-child(n+5) {
            width: 380px;
            margin-top: 20px;
        }
    }
}