/**
 * @file TMS商标官网首页
 * @desc 精选优质商标模块
 */

import {useEffect, useState} from 'react';
import {BlockTemplate} from '@baidu/bce-portal-ui';
import {netService} from '@baidu/bce-services';

import {urlConst} from '@common/constant/urlConst';

import styles from './choice.module.less';

export const Choice = () => {
    const [pageNum, changePageNum] = useState(1);
    const [transactionList, changeTransactionList] = useState([]);

    const getList = async (pageNum: number) => {
        const res = await netService.post(
            urlConst.GET_TMS_GET_TRANSACTION_LIST,
            {
                selectedType: 'HOT',
                pageNo: pageNum,
                pageSize: 5,
            }
        ).catch(err => err);
        if (res && res.success) {
            changeTransactionList(res.page.result);
        }
    };

    const changeNum = (add?: boolean) => {
        if (add) {
            changePageNum(pageNum + 1);
            return;
        }
        if (pageNum === 1) {
            return;
        }
        changePageNum(pageNum - 1);
    };

    const jumpToDetail = (goodsId: string) => {
        window.open(`/product/tms/transaction/detail?goodsId=${goodsId}`);
    };

    useEffect(() => {
        getList(pageNum);
    }, [pageNum]);

    return (
        <BlockTemplate
            title="精选优质商标"
            des="精选商标推荐，即买即用，开启品牌升级之路"
            className={styles.choiceBg}
        >
            <div
                className={styles.slide}
            >
                {
                    transactionList.map(d => {
                        return (
                            <div
                                key={d.goodsId}
                                className={styles.slideItem}
                                onClick={() => jumpToDetail(d.goodsId)}
                            >
                                <img src={d.iconUrl} className={styles.img} />
                                <div className={styles.content}>
                                    <div className={styles.price}>
                                        <div className={styles.priceSymbol}>￥</div>
                                        <div className={styles.priceNumber}>{d.price}</div>
                                    </div>
                                    <div className={styles.title}></div>
                                    <div title={d.items} className={styles.des}>{d.items}</div>
                                </div>
                            </div>
                        );
                    })
                }
                <div
                    className={styles.left}
                    onClick={() => changeNum()}
                >
                    <img
                        src={
                            pageNum === 1
                                ? 'https://tms-static.cdn.bcebos.com/tms-server-portal/left-arrow-disable.png'
                                : 'https://tms-static.cdn.bcebos.com/tms-server-portal/left-arrow.png'
                        }
                    />
                </div>
                <div
                    className={styles.right}
                    onClick={() => changeNum(true)}
                >
                    <img src="https://tms-static.cdn.bcebos.com/tms-server-portal/right-arrow.png" />
                </div>
            </div>
        </BlockTemplate>
    );
};
