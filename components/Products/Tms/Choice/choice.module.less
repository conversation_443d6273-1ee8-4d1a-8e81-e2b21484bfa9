.slide {
    display: flex;
    justify-content: space-between;
    position: relative;
    &:hover {
        .left, .right {
            display: block;
        }
    }
    .slideItem {
        width: 220px;
        background: #fff;
        border-radius: 4px;
        cursor: pointer;
        &:hover {
            transform: translateY(-12px);
            transition: 100ms;
            box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
        }
    }
    .img {
        width: 100%;
        height: 124px;
    }
    .price {
        display: flex;
        align-items: baseline;
        color: #FB5037;
        margin-top: 16px;
        .priceSymbol {
            font-size: 18px;
            font-weight: 500;
        }
        .priceNumber {
            font-size: 28px;
            font-weight: 500;
        }
    }
    .title {
        font-size: 18px;
        color: #222;
        font-weight: 500;
        margin-top: 16px;
    }
    .des {
        color: #222;
        opacity: 0.7;
        line-height: 24px;
        margin-top: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .content {
        padding: 0 20px 24px;
    }
    .left, .right {
        width: 52px;
        height: 52px;
        border-radius: 50%;
        background: #fff;
        box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
        display: none;
        cursor: pointer;
        img {
            width: 52px;
            height: 52px;
        }
    }
    .left {
        position: absolute;
        top: 50%;
        margin-top: -26px;
        left: -26px;
    }
    .right {
        position: absolute;
        top: 50%;
        margin-top: -26px;
        right: -26px;
    }
}

.choiceBg {
    background: url('https://tms-static.cdn.bcebos.com/tms-server-portal/quality-banner-background.jpg') no-repeat;
    background-size: 100% 100%;
}