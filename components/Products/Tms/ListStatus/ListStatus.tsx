/**
 * @file TMS商标官网列表页
 * @desc 商标类别与状态选择模块
 */

import {useState} from 'react';
import cx from 'classnames';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';

import {tmsStatus} from '../defaultModel';

import styles from './liststatus.module.less';

export const ListStatus = (props: any) => {
    const {
        registryCategory,
        selectCategory,
        changeSelectCategory,
        selectStatus,
        changeSelectStatus,
        baseUrl,
        name,
    } = props;
    const [show, changeShow] = useState(false);
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});

    const changeSelect = (code: string) => {
        if (selectCategory.some((d: string) => d === code)) {
            changeSelectCategory(selectCategory.filter((d: string) => d !== code));
            return;
        }
        changeSelectCategory([...selectCategory, code]);
    };

    const goToAuxiliary = (targetUrl: string) => {
        if (userinfo?.hasLogin) {
            window.open(targetUrl);
        } else {
            verifyHandler().then(() => {
                window.open(targetUrl);
            });
        }
    };

    return (
        <div
            className={styles.wrap}
        >
            <div
                className={styles.head}
            >
                <div className={styles.des}>
                    经查询，{name}相关商标在如下类别均有注册，建议使用专家辅助注册提高成功率：
                </div>
                <div
                    className={styles.registryButton}
                    onClick={() => goToAuxiliary(`${baseUrl}#/tms/create/assistance?type=AUXILIARY_REGISTRATION&pageResource=PORTAL_001`)}
                >
                    专家辅助
                </div>
            </div>
            <div className={styles.line}></div>
            <div className={styles.wrapItem}>
                <div className={styles.label}>商标类别：</div>
                <div className={styles.itemContent}>
                    <div className={cx(styles.itemContentWrap, {
                        [styles.showItemWrap]: show,
                    })}
                    >
                        {
                            registryCategory.map((d: any) => (
                                <div
                                    key={d.firstCode}
                                    className={cx(styles.categoryItem, {
                                        [styles.selectItem]: selectCategory.some((v: string) => v === d.firstCode),
                                    })}
                                    onClick={() => changeSelect(d.firstCode)}
                                >
                                    {`${Number(d.firstCode)}-${d.firstName}(${d.count})`}
                                </div>
                            ))
                        }
                    </div>
                    <div
                        className={cx(styles.arrow, {
                            [styles.show]: show,
                        })}
                        onClick={() => changeShow(!show)}
                    >
                        {show ? '收起' : '展开'}全部
                    </div>
                </div>
            </div>
            <div className={styles.wrapItem}>
                <div className={styles.label}>商标状态：</div>
                <div className={styles.statusContent}>
                    {
                        tmsStatus.map(d => (
                            <div
                                key={d.value}
                                className={cx(styles.itemStatus, {
                                    [styles.selectStatus]: selectStatus === d.value,
                                })}
                                onClick={() => changeSelectStatus(d.value)}
                            >
                                {d.text}
                            </div>
                        ))
                    }
                </div>
            </div>
        </div>
    );
};
