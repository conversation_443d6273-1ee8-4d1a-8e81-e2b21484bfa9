.wrap {
    width: 100%;
    background: #fff;
    padding: 24px;
    .head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .des {
            font-size: 16px;
            color: #2468f2;
            font-weight: 500;
            flex: 1;
            line-height: 28px;
        }
        .registryButton {
            width: 92px;
            height: 32px;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
            line-height: 32px;
            cursor: pointer;
            background: #2468f2;
            color: #fff;
            &:hover {
                background: #5083F1;
            }
        }
    }
    .line {
        width: 100%;
        height: 1px;
        background: #222;
        opacity: 0.08;
        margin: 24px 0;
    }
    .wrapItem {
        display: flex;
        margin-top: 14px;
        .label {
            font-size: 16px;
            color: #222;
            height: 40px;
            line-height: 40px;
            margin-right: 12px;
        }
        .itemContent {
            display: flex;
            justify-content: space-between;
            flex: 1;
            .itemContentWrap {
                display: flex;
                flex-wrap: wrap;
                flex: 1;
                height: 40px;
                overflow: hidden;
                .categoryItem {
                    border-radius: 2px;
                    border: 1px solid rgba(34, 34, 34, 0.24);
                    padding: 8px 12px;
                    margin-right: 12px;
                    margin-bottom: 12px;
                    color: #222;
                    opacity: 0.7;
                    font-size: 14px;
                    cursor: pointer;
                    &:hover {
                        border: 1px solid #2468F2;
                        color: #2468F2;
                    }
                }
                .selectItem {
                    background: rgba(36, 104, 242, 0.08);
                    color: #2468f2;
                    border: none;
                    padding: 9px 13px;
                    &:hover {
                        border: 1px solid #2468F2;
                        color: #2468F2;
                        padding: 8px 12px;
                    }
                }
            }
            .showItemWrap {
                height: auto;
            }
            .arrow {
                font-size: 14px;
                color: #2468f2;
                display: flex;
                align-items: center;
                height: 40px;
                cursor: pointer;
                &::after {
                    width: 16px;
                    height: 16px;
                    content: url('https://tms-static.bj.bcebos.com/tms-server-portal/arrow.svg');
                    transform: rotate(90deg);
                    margin-left: 4px;
                }
            }
            .show {
                &::after {
                    transform: rotate(270deg);
                }
            }
        }
        .statusContent {
            display: flex;
            .itemStatus {
                font-size: 14px;
                color: #222;
                opacity: 0.7;
                height: 40px;
                line-height: 40px;
                margin-right: 32px;
                cursor: pointer;
            }
            .selectStatus {
                color: #2468F2;
                opacity: 1;
            }
        }
    }
}