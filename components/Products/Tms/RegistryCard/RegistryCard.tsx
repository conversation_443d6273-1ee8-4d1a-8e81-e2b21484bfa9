import React from 'react';
import cx from 'classnames';
import styles from './registrycard.module.less';

export interface RegistryCardProps {
    className?: string;
    title: string;
    des: string;
    advantage: string[];
    price?: string;
    showNewActity?: boolean;
    actityText?: string;
    consultUrl?: string;
    buyUrl?: string;
    showPriceUnit?: boolean;
    consultText?: string;
    buyText?: string;
    showBadge?: boolean;
    badgeText?: string;
    contentClassName?: string;
    advantageIcon?: string;
    badgeClassName?: string;
    badgeTextClassName?: string;
    unit?: string;
    oldPrice?: string;
    onConsult?: (params: string) => void;
    onBuy?: (params: string) => void;
    children?: React.ReactNode;
}

const RegistryCard: React.FC<RegistryCardProps> = props => {
    const {
        className,
        title,
        des,
        advantage,
        price,
        showNewActity,
        consultUrl,
        buyUrl,
        showPriceUnit = true,
        actityText = '新用户专享价限购1件',
        consultText = '专家咨询',
        buyText = '立即购买',
        showBadge = true,
        badgeText = '限时抢购',
        contentClassName,
        advantageIcon = 'https://tms-static.cdn.bcebos.com/tms-server-portal/tick.png',
        badgeClassName,
        badgeTextClassName,
        unit,
        oldPrice,
        onBuy,
        onConsult,
        children,
    } = props;
    return (
        <div className={cx(styles.RegistryCard, className)}>
            <div className={cx(styles['RegistryCard-content'], contentClassName)}>
                <div className={styles['RegistryCard-title']}>{title}</div>
                <div className={styles['RegistryCard-des']}>{des}</div>
            </div>
            {children}
            <div className={styles['RegistryCard-list']}>
                {advantage.map((d: string) => (
                    <div
                        className={styles['RegistryCard-advantage']}
                        key={d}
                    >
                        <img
                            className={styles['RegistryCard-advantage-icon']}
                            src={advantageIcon}
                            alt=""
                        />
                        <div className={styles['RegistryCard-advantage-text']}>{d}</div>
                    </div>
                ))}
            </div>
            <div className={styles['RegistryCard-label']}>
                {showNewActity && (
                    <div className={styles['RegistryCard-label-wrap']}>
                        <div className={styles['RegistryCard-label-new']}>{actityText}</div>
                    </div>
                )}
            </div>
            <div className={styles['RegistryCard-price']}>
                {showPriceUnit && !!price && <div className={styles['RegistryCard-price-symbol']}>￥</div>}
                <div className={styles['RegistryCard-price-number']}>{price}</div>
                {unit && <div className={styles['RegistryCard-price-unit']}>{unit}</div>}
                {oldPrice && <div className={styles['RegistryCard-price-oldPrice']}>{oldPrice}</div>}
            </div>
            <div className={styles['RegistryCard-bottom']}>
                {consultUrl && (
                    <a
                        className={cx(styles['RegistryCard-bottom-button'], styles['RegistryCard-bottom-consult'], {
                            leftButton: !buyUrl,
                        })}
                        onClick={() => onConsult && onConsult(consultUrl)}
                        rel="noreferrer"
                    >
                        {consultText}
                    </a>
                )}
                {buyUrl && (
                    <a
                        className={cx(styles['RegistryCard-bottom-button'], styles['RegistryCard-bottom-buy'], {
                            rightButton: !consultUrl,
                        })}
                        onClick={() => onBuy && onBuy(buyUrl)}
                        rel="noreferrer"
                    >
                        {buyText}
                    </a>
                )}
            </div>
            {showBadge && (
                <div className={cx(styles['RegistryCard-badge'], badgeClassName)}>
                    <div className={cx(styles['RegistryCard-badge-text'], badgeTextClassName)}>{badgeText}</div>
                </div>
            )}
        </div>
    );
};

export default RegistryCard;
