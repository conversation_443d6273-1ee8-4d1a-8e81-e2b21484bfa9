/* eslint-disable camelcase */
/**
 * @file TMS商标官网列表页
 * @desc 商标信息模块
 */

import React, {MouseEvent} from 'react';
import cx from 'classnames';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {useRouter} from 'next/router';
import qs from 'qs';

import {tmsStatus} from '../defaultModel';

import styles from './listitem.module.less';

// eslint-disable-next-line complexity
export const ListItem = (props: any) => {
    const {data, baseUrl} = props;
    const {
        name,
        iconUrl,
        status,
        registrationNumber,
        category,
        groups,
        applicantName,
        announcementIssue,
        announcementDate,
        applicationDate,
        registrationIssue,
        registrationDate,
        int_cls,
        transactionInfo,
    } = data;
    const router = useRouter();
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});


    const gotoDetail = () => {
        const query = qs.stringify({
            ...router.query,
            registrationNumber,
            firstCode: int_cls || '',
        });
        window.location.href = `${window.location.origin}/product/tms/detail?${query}`;
    };

    const goToAuxiliary = (e: MouseEvent) => {
        e.stopPropagation();
        const targetUrl: string = `${baseUrl}#/tms/create/assistance?type=AUXILIARY_REGISTRATION&pageResource=PORTAL_001`;
        if (userinfo?.hasLogin) {
            window.open(targetUrl);
        } else {
            verifyHandler().then(() => {
                window.open(targetUrl);
            });
        }
    };

    const goTransaction = (e: MouseEvent, url: string) => {
        e.stopPropagation();
        window.open(url);
    };

    const goToRegistry = (e: MouseEvent) => {
        e.stopPropagation();
        const targetUrl: string = `${baseUrl}#/tms/create/self?pageResource=PORTAL_001`;
        if (userinfo?.hasLogin) {
            window.open(targetUrl);
        } else {
            verifyHandler().then(() => {
                window.open(targetUrl);
            });
        }
    };

    const getStatusText = () => {
        const currentStatus = tmsStatus.find(d => d.value === status);
        return currentStatus.text;
    };
    return (
        <div
            className={cx(styles.item)}
            onClick={() => gotoDetail()}
        >
            <div className={styles.content}>
                <img src={iconUrl} />
                <div className={styles.info}>
                    <div className={styles.title}>
                        <div className={styles.name}>
                            {name}
                        </div>
                        <div className={cx(styles.status, styles[status])}>
                            {getStatusText()}
                        </div>
                    </div>
                    <div className={styles.infoContent}>
                        <div className={styles.infoItem}>
                            <div className={styles.infoItemRow}>
                                <div className={styles.label}>注册号：</div>
                                <div className={styles.value}>{registrationNumber || '-'}</div>
                            </div>
                            <div className={styles.infoItemRow}>
                                <div className={styles.label}>商标类别：</div>
                                <div className={styles.value}>{category || '-'}</div>
                            </div>
                            <div className={styles.infoItemRow}>
                                <div className={styles.label}>群组分类：</div>
                                <div className={styles.value}>{(groups || []).join('，') || '-'}</div>
                            </div>
                        </div>
                        <div className={styles.infoItem}>
                            <div className={styles.infoItemRow}>
                                <div className={styles.label}>申请人：</div>
                                <div className={styles.value}>{applicantName || '-'}</div>
                            </div>
                            <div className={styles.infoItemRow}>
                                <div className={styles.label}>初审公告期号：</div>
                                <div className={styles.value}>{announcementIssue || '-'}</div>
                            </div>
                            <div className={styles.infoItemRow}>
                                <div className={styles.label}>初审公告日期：</div>
                                <div className={styles.value}>{announcementDate || '-'}</div>
                            </div>
                        </div>
                        <div className={styles.infoItem}>
                            <div className={styles.infoItemRow}>
                                <div className={styles.label}>申请日期：</div>
                                <div className={styles.value}>{applicationDate || '-'}</div>
                            </div>
                            <div className={styles.infoItemRow}>
                                <div className={styles.label}>注册公告期号：</div>
                                <div className={styles.value}>{registrationIssue || '-'}</div>
                            </div>
                            <div className={styles.infoItemRow}>
                                <div className={styles.label}>注册公告日期：</div>
                                <div className={styles.value}>{registrationDate || '-'}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className={styles.right}>
                    {
                        transactionInfo ? (
                            <React.Fragment>
                                <div className={styles.traModule}>
                                    <span className={styles.traSymol}>￥</span>
                                    <span className={styles.traPrice}>{transactionInfo.price}</span>
                                </div>
                                <div
                                    className={styles.auxiliaryButton}
                                    onClick={e => goTransaction(e, transactionInfo.detailURL)}
                                >
                                    查看详情
                                </div>
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                <div
                                    className={styles.auxiliaryButton}
                                    onClick={e => goToAuxiliary(e)}
                                >
                                    专家辅助
                                </div>
                                <div
                                    className={styles.registryButton}
                                    onClick={e => goToRegistry(e)}
                                >
                                    自助注册
                                </div>
                            </React.Fragment>
                        )
                    }
                </div>
            </div>
            {
                transactionInfo && <div className={styles.badge}></div>
            }
        </div>
    );
};
