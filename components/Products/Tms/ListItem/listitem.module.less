.item {
    position: relative;
    width: 100%;
    border-bottom: 1px solid rgba(34, 34, 34, 0.08);
    background: #fff;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover {
        background: rgba(241, 244, 248, 0.6);
    }
    .content {
        display: flex;
        align-items: center;
        padding: 24px;
        img {
            width: 224px;
            height: 126px;
            margin-right: 44px;
        }
        .info {
            border-right: 1px solid rgba(34, 34, 34, 0.08);
            .title {
                display: flex;
                align-items: center;
                margin-bottom: 12px;
                .name {
                    font-size: 18px;
                    color: #222;
                    font-weight: 500;
                }
                .status {
                    padding: 8px;
                    font-size: 12px;
                    font-weight: 500;
                    margin-left: 8px;
                    border-radius: 2px;
                }
            }
            .infoContent {
                display: flex;
                .infoItem {
                    width: 228px;
                    margin-right: 16px;
                    &:last-child {
                        margin-right: 0;
                    }
                    .infoItemRow {
                        display: flex;
                        line-height: 24px;
                        .label {
                            font-size: 14px;
                            color: #222;
                            opacity: 0.7;
                            flex-wrap: nowrap;
                        }
                        .value {
                            flex: 1;
                            color: #222;
                            font-size: 14px;
                            flex-wrap: wrap;
                        }
                    }
                }
            }
        }
        .right {
            width: 170px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            .auxiliaryButton, .registryButton {
                width: 92px;
                height: 32px;
                border-radius: 4px;
                font-size: 14px;
                text-align: center;
                line-height: 32px;
                cursor: pointer;
            }
            .auxiliaryButton {
                background: #2468f2;
                color: #fff;
                margin-bottom: 20px;
                &:hover {
                    background: #5083F1;
                }
            }
            .registryButton {
                background: #fff;
                border: 1px solid #2468F2;
                color: #2468F2;
                &:hover {
                    border: 1px solid #5083F1;
                    color: #5083F1;
                }
            }
            .traModule {
                display: flex;
                color: #F43E3E;
                font-weight: 500;
                align-items: baseline;
                margin-bottom: 12px;
                .traSymol {
                    font-size: 18px;
                }
                .traPrice {
                    font-size: 32px;
                }
            }
        }
    }
    .REGISTERED {
        background: rgba(48, 191, 19, 0.08);
        color: #30BF13;
    }
    .FIRST_TRAILED {
        background: rgba(36, 104, 242, 0.08);
        color: #2468F2;
    }
    .DEAD, .INVALID {
        background: rgba(21, 27, 38, 0.08);
        color: #151B26;
    }
    .AUDITING {
        background: rgba(255, 147, 38, 0.08);
        color: #FF9326;
    }
    .REJECTED {
        background: rgba(243, 62, 62, 0.08);
        color: #F33E3E;
    }
    .badge {
        width: 56px;
        height: 56px;
        position: absolute;
        top: -6px;
        right: -6px;
        background: url('https://tms-static.bj.bcebos.com/tms-server-portal/res-tra-badge.png') no-repeat;
        background-size: cover;
    }
}

.REGISTEREDBEFORE::before {
    background: #30BF13;
}

.FIRST_TRAILEDBEFORE::before {
    background: #2468F2;
}

.DEADBEFORE::before, .INVALIDBEFORE::before {
    background: #555961;
}

.AUDITINGBEFORE::before {
    background: #FF9326;
}

.REJECTEDBEFORE::before {
    background: #F33E3E;
}