.extend {
    display: flex;
    .left {
        .leftItemWrap{
            position: relative;
            .leftItem {
                width: 236px;
                padding: 32px 16px 32px 0;
                .leftTitle {
                    font-size: 18px;
                    color: #fff;
                    font-weight: 500;
                }
                .leftDes{
                    margin-top: 10px;
                    font-size: 14px;
                    opacity: 0.7;
                    color: #fff;
                    line-height: 24px;
                }
            }
            .mask {
                position: absolute;
                width: 600px;
                height: 100%;
                top: 0;
                left: -364px;
                opacity: 0.22;
                background-image: linear-gradient(270deg, #F8FAFC 0%, rgba(248,250,252,0.00) 100%);
            }
        }
        .hidden {
            display: none;
        }
    }
    .right {
        display: flex;
        // justify-content: space-between;
        flex-wrap: wrap;
    }
    .rightItem {
        width: 300px;
        height: 224px;
        padding: 28px;
        background: #fff;
        border-radius: 4px;
        margin-bottom: 20px;
        margin-right: 20px;
        cursor: pointer;
        &:hover {
            transform: translateY(-12px);
            transition: 100ms;
            box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
        }
        &:nth-of-type(3) {
            margin-right: 0;
        }
        &:nth-of-type(6) {
            margin-right: 0;
        }
    }
    .rightTitle {
        font-size: 18px;
        color: #222;
        font-weight: 500;
        margin-bottom: 16px;
    }
    .rightDes {
        font-size: 14px;
        color: #222;
        line-height: 24px;
        height: 72px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: pre-line;
    }
    .price {
        display: flex;
        align-items: baseline;
        margin-top: 24px;
        .priceSymbol {
            font-size: 18px;
            color: #F33E3E;
            font-weight: 500;
            margin-right: 5px;
        }
        .priceNum {
            font-size: 32px;
            color: #F33E3E;
            font-weight: 700;
        }
        .priceText {
            font-size: 14px;
            color: #222;
            font-weight: 500;
            opacity: 0.7;
        }
    }
}
.extendBg {
    background: url('https://tms-static.cdn.bcebos.com/tms-server-portal/slice-bg.png') no-repeat;
    background-size: cover;
}
.titleColor {
    color: #fff;
}
.desColor {
    color: #fff;
    opacity: 0.7;
}