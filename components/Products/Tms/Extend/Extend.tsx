/**
 * @file TMS商标官网首页
 * @desc 商标延伸服务模块
 */

import {useState} from 'react';
import cx from 'classnames';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {BlockTemplate} from '@baidu/bce-portal-ui';

import {extendLeftData, extendData} from '../defaultModel';

import styles from './extend.module.less';

export const Extend = (props: any) => {
    const {baseUrl} = props;
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const [data, changeData] = useState(extendData.slice(0, 6));
    const [selectKey, changeSelectKey] = useState('1');

    const changeExtendData = (key: string) => {
        changeSelectKey(key);
        if (key === '1') {
            changeData(extendData.slice(0, 6));
        } else if (key === '2') {
            changeData(extendData.slice(6, 11));
        } else {
            changeData(extendData.slice(11));
        }

    };

    const goToConsole = (link?: string, noBaseUrl = false) => {
        if (!link) {
            return;
        }
        const newLink = noBaseUrl ? link : `${baseUrl}${link}`;
        props.sendLog && props.sendLog();
        if (userinfo?.hasLogin) {
            window.open(newLink);
        } else {
            // 未登录时, 弹出登录弹层, 登录成功后进行跳转
            verifyHandler().then(() => {
                window.open(newLink);
            });
        }
    };

    return (
        <BlockTemplate
            title="商标延伸服务"
            des="海量商标即买即用，专业商标确权维护服务，保障品牌权益"
            className={styles.extendBg}
            titleClassName={styles.titleColor}
            desClassName={styles.desColor}
        >
            <div
                className={styles.extend}
            >
                <div className={styles.left}>
                    {
                        extendLeftData.map(d => {
                            return (
                                <div
                                    key={d.key}
                                    className={styles.leftItemWrap}
                                >
                                    <div
                                        className={styles.leftItem}
                                        onMouseOver={() => changeExtendData(d.key)}
                                    >
                                        <div className={styles.leftTitle}>{d.title}</div>
                                        <div className={styles.leftDes}>{d.des}</div>
                                    </div>
                                    <div className={cx(styles.mask, {
                                        [styles.hidden]: d.key !== selectKey,
                                    })}
                                    >
                                    </div>
                                </div>
                            );
                        })
                    }
                </div>
                <div className={styles.right}>
                    {
                        data.map(d => {
                            return (
                                <div
                                    className={styles.rightItem}
                                    key={d.title}
                                    onClick={() => goToConsole(d.link, d.noBaseUrl)}
                                >
                                    <div className={styles.rightTitle}>{d.title}</div>
                                    <div className={styles.rightDes}>{d.des}</div>
                                    <div className={styles.price}>
                                        {
                                            !d.noPrice && <div className={styles.priceSymbol}>￥</div>
                                        }
                                        <div className={styles.priceNum}>{d.price}</div>
                                        {
                                            !d.noPrice && <div className={styles.priceText}>&nbsp;{d.unit || '/ 件'}</div>
                                        }
                                    </div>
                                </div>
                            );
                        })
                    }
                </div>
            </div>
        </BlockTemplate>
    );
};
