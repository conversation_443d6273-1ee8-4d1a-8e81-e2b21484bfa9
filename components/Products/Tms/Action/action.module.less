.actionWrap {
    display: flex;
    justify-content: space-between;
    .actionItem {
        position: relative;
        width: 380px;
        height: 248px;
        background: url('https://tms-static.cdn.bcebos.com/tms-server-portal/action-new.png') no-repeat;
        background-size: 100%;
        border-radius: 4px;
        .title {
            font-size: 18px;
            color: #222;
            font-weight: 500;
            margin-bottom: 24px;
        }
    }
    .content {
        padding: 28px;
        z-index: 111;
        .contentItem {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            &:hover {
                .point {
                    background: #2468f2;
                }
                .arrow {
                    display: block;
                }
                a {
                    color: #2468f2;
                }
            }
        }
        .point {
            width: 4px;
            height: 4px;
            background: #222;
            border-radius: 50%;
            margin-right: 12px;
        }
        a {
            font-size: 14px;
            color: #222;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 24px;
            max-width: calc(100% - 50px);
        }
        .arrow {
            width: 16px;
            height: 16px;
            background: url('https://tms-static.bj.bcebos.com/tms-server-portal/arrow.svg') no-repeat;
            background-size: 100%;
            display: none;
            margin-left: 18px;
        }
    }
}