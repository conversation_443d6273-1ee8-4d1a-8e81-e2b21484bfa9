/**
 * @file TMS商标官网首页
 * @desc 使用指南模块
 */

import {BlockTemplate} from '@baidu/bce-portal-ui';

import {actionData} from '../defaultModel';

import styles from './action.module.less';

export const Action = () => {
    return (
        <BlockTemplate
            title="操作指南"
            des="不会注册商标？这里帮你从0了解商标，安心使用商标服务"
        >
            <div className={styles.actionWrap}>
                {
                    actionData.map(d => {
                        return (
                            <div
                                className={styles.actionItem}
                                key={d.title}
                            >
                                <div className={styles.content}>
                                    <div className={styles.title}>{d.title}</div>
                                    {
                                        d.list.map(v => {
                                            return (
                                                <div
                                                    key={v.link}
                                                    className={styles.contentItem}
                                                >
                                                    <div className={styles.point}></div>
                                                    <a
                                                        href={v.link}
                                                        target="_blank"
                                                    >
                                                        {v.text}
                                                    </a>
                                                    <div className={styles.arrow}></div>
                                                </div>
                                            );
                                        })
                                    }
                                </div>
                            </div>
                        );
                    })
                }
            </div>
        </BlockTemplate>
    );
};
