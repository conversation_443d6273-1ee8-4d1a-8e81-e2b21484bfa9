.couponModal {
    .couponModalMask {
        position: fixed;
        top: 0;
        width: 100%;
        bottom: 0;
        height: 100%;
        z-index: 1000;
        height: 100%;
        background-color: rgba(0,0,0,.45);
    }
    .modalWrap {
        position: fixed;
        top: 0;
        width: 100%;
        bottom: 0;
        height: 100%;
        overflow: auto;
        outline: 0;
        .modalContent {
            padding: 84px 40px 0;
            width: 576px;
            height: 436px;
            background: url('https://tms-static.bj.bcebos.com/tms-server-portal/couponBg.png') no-repeat;
            background-size: cover;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            .modalClose {
                width: 12px;
                height: 12px;
                background: url('https://tms-static.bj.bcebos.com/tms-server-portal/couponClose.png') no-repeat;
                background-size: cover;
                cursor: pointer;
                position: absolute;
                top: 40px;
                right: 40px;
            }
            .modalCouponContent {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                .couponItem1 {
                    background: url('https://tms-static.bj.bcebos.com/tms-server-portal/coupon1.png') no-repeat;
                }
                .couponItem2 {
                    background: url('https://tms-static.bj.bcebos.com/tms-server-portal/coupon2.png') no-repeat;
                }
                .couponItem3 {
                    background: url('https://tms-static.bj.bcebos.com/tms-server-portal/coupon3.png') no-repeat;
                }
                .couponItem4 {
                    background: url('https://tms-static.bj.bcebos.com/tms-server-portal/coupon4.png') no-repeat;
                }
                .couponItem {
                    width: 240px;
                    height: 128px;
                    background-size: cover;
                    margin-bottom: 16px;
                }
            }
            .modalReceiveCoupon {
                width: 170px;
                height: 40px;
                background-image: linear-gradient(90deg, #F75152 0%, #FD7F7F 100%);
                border-radius: 4px;
                font-size: 16px;
                color: #FFFFFF;
                line-height: 40px;
                font-weight: 500;
                text-align: center;
                margin: 0 auto;
                cursor: pointer;
                &:hover {
                    background-image: linear-gradient(90deg, #E9080A 0%, #FE4F4F 100%);
                }
            }
            .hasReceive {
                background-image: linear-gradient(90deg, #FFE2AD 0%, #FEF2DF 100%);
                color: #785742;
                &:hover {
                    background-image: linear-gradient(90deg, #FFE2AD 0%, #FEF2DF 100%);
                }
            }
        }
        z-index: 1001;
    }
}