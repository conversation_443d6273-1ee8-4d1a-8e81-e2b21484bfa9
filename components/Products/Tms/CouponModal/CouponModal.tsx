
import React from 'react';
import cx from 'classnames';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {getLoginUrl} from '@common/helper/page';
import {message} from 'antd';
import {isSandbox} from '@components/Products/Tms/defaultModel';

import styles from './couponmodal.module.less';

export const CouponModal = (props: any) => {
    const {showCoupon, changeShowCoupon, isLogin, changeHasReceive, hasReceive, timeShow} = props;
    const activId = isSandbox() ? 'ACTIV-Rt9qy9vvUfs' : 'ACTIV-HF3bQyjJj9m';
    const receiveCoupon = () => {
        if (isLogin && !hasReceive) {
            netService.get(urlConst.GET_TMS_RECEIVE_COUPON, {
                activId,
            }).then(() => {
                message.success('领取成功');
                changeHasReceive(true);
            });
        } else if (!isLogin) {
            window.open(getLoginUrl(), '_self');
        }
    };
    return (
        <React.Fragment>
            {
                timeShow && showCoupon && (
                    <div className={styles.couponModal}>
                        <div className={styles.couponModalMask}></div>
                        <div className={styles.modalWrap}>
                            <div className={styles.modalContent}>
                                <div
                                    className={styles.modalClose}
                                    onClick={() => {
                                        changeShowCoupon(false);
                                        localStorage.setItem('closeTmsModal', 'true');
                                    }}
                                >
                                </div>
                                <div className={styles.modalCouponContent}>
                                    {
                                        [1, 2, 3, 4].map(d => <div key={d} className={cx(styles.couponItem, styles[`couponItem${d}`])}></div>)
                                    }
                                </div>
                                <div
                                    className={cx(styles.modalReceiveCoupon, {
                                        [styles.hasReceive]: hasReceive,
                                    })}
                                    onClick={receiveCoupon}
                                >
                                    {
                                        hasReceive ? '已领取' : '一键领取'
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                )
            }
        </React.Fragment>
    );
};
