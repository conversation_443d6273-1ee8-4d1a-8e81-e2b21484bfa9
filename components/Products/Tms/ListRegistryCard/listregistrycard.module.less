.registryWrap {
    display: flex;
    justify-content: space-between;
    .self {
        background: url('https://tms-static.cdn.bcebos.com/tms-server-portal/self-reg.png') no-repeat;
    }
    .aux {
        background: url('https://tms-static.cdn.bcebos.com/tms-server-portal/aux-reg.png') no-repeat;
    }
    .gua {
        background: url('https://tms-static.cdn.bcebos.com/tms-server-portal/gua-reg.png') no-repeat;
    }
    .card {
        width: 380px;
        height: 168px;
        position: relative;
        cursor: pointer;
        &:hover {
            .registryImg {
                transform: scale(1.08);
            }
        }
        .imgWrap {
            width: 100%;
            height: 100%;
            overflow: hidden;
            border-radius: 4px;
        }
        .registryImg {
            width: 100%;
            height: 100%;
            transition: all 0.3s linear;
        }
        .content {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            padding: 28px;
        }
        .title {
            font-size: 18px;
            color: #fff;
            font-weight: 500;
            line-height: 28px;
        }
        .des {
            font-size: 14px;
            color: #fff;
            margin: 10px 0 18px;
            line-height: 24px;
            .price {
                font-size: 16px;
                color: #FFD843;
            }
        }
        .apply {
            display: inline-block;
            background: #fff;
            border-radius: 4px;
            width: 92px;
            height: 32px;
            font-size: 14px;
            color: #2468f2;
            text-align: center;
            line-height: 32px;
            cursor: pointer;
            &:hover {
                color: #5083F1;
            }
        }
    }
    .badge {
        background: url('https://tms-static.cdn.bcebos.com/tms-server-portal/badge.png') no-repeat;
        width: 56px;
        height: 56px;
        background-size: 100%;
        position: absolute;
        right: -8px;
        top: -8px;
        display: flex;
        justify-content: center;
        z-index: 2;
        .text {
            font-size: 12px;
            color: #fff;
            transform: rotate(45deg);
            padding-top: 14px;
            font-weight: 500;
        }
    }
}