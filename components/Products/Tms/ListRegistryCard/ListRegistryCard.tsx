/**
 * @file TMS商标官网列表页
 * @desc 商标注册推荐模块
 */

import cx from 'classnames';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';

import {listRegistryData} from '../defaultModel';

import styles from './listregistrycard.module.less';

export const ListRegistryCard = (props: any) => {
    const {baseUrl} = props;
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const goToRegistry = (targetUrl: string) => {
        if (userinfo?.hasLogin) {
            window.open(targetUrl);
        } else {
            verifyHandler().then(() => {
                window.open(targetUrl);
            });
        }
    };
    return (
        <div
            className={styles.registryWrap}
        >
            {
                listRegistryData.map(d => (
                    <div
                        className={cx(styles.card)}
                        key={d.title}
                        onClick={() => goToRegistry(`${baseUrl}${d.link}`)}
                    >
                        <div className={styles.imgWrap}>
                            <img
                                src={d.url}
                                className={styles.registryImg}
                            />
                        </div>
                        <div className={styles.content}>
                            <div className={styles.title}>
                                {d.title}
                            </div>
                            <div className={styles.des}>
                                {d.des}
                                <span className={styles.price}>￥{d.price}&nbsp;</span>
                                起
                            </div>
                            <a
                                className={styles.apply}
                            >
                                立即申请
                            </a>
                        </div>
                        {
                            d.hot && (
                                <div className={styles.badge}>
                                    <div className={styles.text}>
                                        热门
                                    </div>
                                </div>
                            )
                        }
                    </div>
                ))
            }
        </div>
    );
};
