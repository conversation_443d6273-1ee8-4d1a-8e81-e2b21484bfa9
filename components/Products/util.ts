import {Ioc} from '@baidu/bce-decorators';
import {UEnvService} from '@common/services/env';
import moment from 'moment';
import md5 from 'js-md5';
import axios from 'axios';
import {Method, Request} from './Bcd/interface';

class HostMap {
    private isSandbox = Ioc(UEnvService).isProdSandbox;
    private online: any = {
        console: 'console.bce.baidu.com',
        cloud: 'cloud.baidu.com',
    };
    private sandbox: any = {
        console: 'qasandbox.bcetest.baidu.com',
        cloud: 'cloudtest.baidu.com',
    };

    getHost(key: string) {
        return `https://${this.isSandbox ? this.sandbox[key] : this.online[key]}`;
    }
}

export const hostMap = new HostMap();

// 计算timestamp
export const calcTimestamp: (url: string, methods?: string) => string = (url, methods = 'POST') => {
    const time = moment(new Date()).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
    const timestamp = time + '@' + md5(methods + url + time + 'ieq%$jsaf23!@fkjwie');
    return timestamp;
};

// 请求方法
export const request = (params: Request, method: Method): Promise<any> => {
    const {url, data, timestampPath} = params;
    return axios({
        url: url,
        method,
        data,
        headers: {
            timestamp: calcTimestamp(timestampPath, method),
        },
    });
};

// 计算折扣
export const formatFloat = (value: number, n: number) => {
    const f = Math.round(value * Math.pow(100, n)) / Math.pow(10, n);
    let s = f.toString();
    const rs = s.indexOf('.');
    if (rs < 0) {
        s += '.';
    }
    for (let i = s.length - s.indexOf('.'); i <= n; i++) {
        s += '0';
    }
    return s;
};

export const showQifuHeader = (
    url: string = 'https://bce.bdstatic.com/lib/@baiducloud/fe-qifu-common-sdk/@latest/qifu-common.bootstrap.js'
) => {

    const head: HTMLHeadElement = document.getElementsByTagName('head')[0];
    const script: HTMLScriptElement = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    head.appendChild(script);
};

export const showBcdDiscountDialog = () => {
    const isSandbox = /(localhost)|(cloudtest)|(baidu-int)|(qasandbox)|(bcetest)/.test(location.origin);
    const version = isSandbox ? '********' : '@latest';

    const now = new Date();
    now.setSeconds(0);
    now.setMilliseconds(0);

    const link = document.createElement('link');
    link.type = 'text/css';
    link.rel = 'stylesheet';
    link.href = `https://bce.bdstatic.com/lib/@baiducloud/qifu-sdk/${version}/iife/index.css?t=${Math.floor(now.getTime() / 1000)}`;
    document.getElementsByTagName('head')[0].appendChild(link);

    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.charset = 'utf-8';
    script.src = `https://bce.bdstatic.com/lib/@baiducloud/qifu-sdk/${version}/iife/index.js?t=${Math.floor(now.getTime() / 1000)}`;
    document.getElementsByTagName('head')[0].appendChild(script);

};

export const addUsePackage = (
    url: string = 'https://bce.bdstatic.com/portal-server/common/bce-storage.js'
) => {
    const head: HTMLHeadElement = document.getElementsByTagName('head')[0];
    const script: HTMLScriptElement = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    head.appendChild(script);
};

// 域名常见问题
/* eslint-disable */
export const initFaqData = [
    {
        title: '如何选择适合您的域名？',
        link: 'https://cloud.baidu.com/doc/BCD/s/Hjwvymjgx#%E5%A6%82%E4%BD%95%E9%80%89%E6%8B%A9%E5%90%88%E9%80%82%E6%82%A8%E7%9A%84%E5%9F%9F%E5%90%8D%EF%BC%9F',
    },
    {
        title: '如何购买域名？',
        link: 'https://cloud.baidu.com/doc/BCD/s/Hjwvymjgx#%E5%A6%82%E4%BD%95%E8%B4%AD%E4%B9%B0%E5%9F%9F%E5%90%8D%EF%BC%9F',
    },
    {
        title: '在百度注册的域名支持更换DNS地址么？',
        link: 'https://cloud.baidu.com/doc/BCD/s/Mjwvymk7a#%E6%80%8E%E4%B9%88%E4%BF%AE%E6%94%B9%E5%9F%9F%E5%90%8Ddns%E6%9C%8D%E5%8A%A1%E5%99%A8%EF%BC%9F',
    },
    {
        title: '域名要备案吗，如何备案？',
        link: 'https://cloud.baidu.com/doc/BCD/s/Dk2ylkl48',
    },
    {
        title: '从域名到网站，要做哪几步？',
        link: 'https://cloud.baidu.com/doc/BCD/s/sjwvymkii#%E4%BB%8E%E5%9F%9F%E5%90%8D%E5%88%B0%E7%BD%91%E7%AB%99%EF%BC%8C%E9%9C%80%E8%A6%81%E5%93%AA%E5%87%A0%E6%AD%A5%EF%BC%9F',
    },
];