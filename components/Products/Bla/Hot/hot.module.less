.hotWrapper {
    width: 1180px;
    margin: 0 auto;
    padding: 52px 0;
}

.header {
    margin-bottom: 32px;
    text-align: center;
}

.headerTitle {
    font-family: PingFangSC-Semibold;
    font-size: 28px;
    color: #222222;
    letter-spacing: 0;
    text-align: center;
    line-height: 28px;
    font-weight: 600;
}

.hotContainer {
    margin-bottom: 20px;
}

.headerBg {
    width: 100%;
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220506/hot-header-bg.png');
    background-size: cover;
    background-position: center;
    color: #FFF !important;
}

.showDetail {
    margin-top: 8px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.defaultIcon {
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
}

.showDetailText {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #2469F3;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
}

.showDetailIcon {
    width: 16px;
    height: 14px;
    margin-left: 8px;
    transition: margin-left .3s ease;
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220506/detail-icon.png');
}

.cardClassName:hover .showDetailIcon {
    margin-left: 24px;
}

.cardClassName:hover .titleClassName {
    color: #2469F3;
}