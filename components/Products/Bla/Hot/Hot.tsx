/**
 * @file BLA 资质代办官网结果页
 * @description 热门资质推荐模块
 */

import cx from 'classnames';
import {Pricecards} from '@baidu/bce-portal-ui';
import styles from './hot.module.less';

interface ProductsBlaHotProps {
    datasource?: PricecardItem[];
    onConsult?: (item: any) => void;
    onBuy?: (item: any) => void;
    onDetail?: (item: any) => void;
}

export interface PricecardItem {
    title: string;
    desc?: string;
    fixedIcon?: string;
    labels?: LabelItem[];
    tags?: string[];
    price?: string | number;
    originPrice?: string | number;
    unit?: string;
}

interface LabelItem {
    icon?: string;
    item: CustomText[];
}

interface CustomText {
    text: string;
    color?: string;
}

export const ProductsBlaHot = (props: ProductsBlaHotProps) => {
    const {datasource, onConsult, onDetail, onBuy} = props;

    const headerSlot = (params: any) => {
        return (
            <div className={styles.showDetail} onClick={() => onDetail(params)}>
                <div className={styles.showDetailText}>查看详情</div>
                <div className={cx(styles.defaultIcon, styles.showDetailIcon)}></div>
            </div>
        );
    };

    return (
        <div className={styles.hotWrapper}>
            <header className={styles.header}>
                <div className={styles.headerTitle}>热门推荐服务</div>
            </header>
            <div id="recommendService"></div>
            <div className={styles.hotContainer}>
                <Pricecards
                    cardClassName={styles.cardClassName}
                    titleClassName={styles.titleClassName}
                    datasource={datasource}
                    onConsult={onConsult}
                    onBuy={onBuy}
                    headerSolt={headerSlot}
                />
            </div>
        </div>
    );
};
