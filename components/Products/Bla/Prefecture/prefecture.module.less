.PrefectureBg {
    width: 100%;
    height: 380px;
    background-size: cover;
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220506/background-prefecture.png');
}

.PrefectureWrapper {
    width: 1180px;
    margin: 0 auto;
    padding: 52px 0;
    display: flex;
    justify-content: space-between;
}

.introWrapper {
    width: 182px;
}

.introQuote {
    width: 26px;
    height: 24px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220506/quotation.png');
}

.introTitle {
    margin-top: 24px;
    font-family: PingFangSC-Semibold;
    font-size: 28px;
    color: #FFFFFF;
    letter-spacing: 0;
    line-height: 28px;
    font-weight: 600;
}

.introDesc {
    margin-top: 16px;
    opacity: 0.8;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
}

.sliderWrapper {
    width: 960px;
}

.flexCenter {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.arrowIcon {
    width: 14px;
    height: 14px;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    cursor: pointer;
}

.leftArrowIcon {
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220506/left-arrow.png');
}

.rightArrowIcon {
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220506/right-arrow.png');
}

.sliderContent {
    width: 880px;
    overflow: hidden;
}

.sliderItems {
    display: flex;
    padding-top: 12px;
    transform: translateX(0);
    transition: transform 1s ease;
}

.sliderItemBg {
    float: left;
    width: 280px;
    border-radius: 6px;
    margin-right: 20px;
}

.slideRight {
    transform: translateX(-100%);
}

.slideLeft {
    transform: translateX(100%);
}

.sliderDots {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.sliderDotItem {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 12px;
    opacity: 0.6;
    background: #FFFFFF;
    cursor: pointer;
}

.dotActive {
    opacity: 1;
}

.labelBorder {
    border-top: 1px solid #ededed;
    margin: 4px 20px 16px 20px;
}