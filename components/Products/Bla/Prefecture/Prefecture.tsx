/**
 * @file BLA 资质代办官网首页
 * @description 高新企业专区
 */

import cx from 'classnames';
import {MutableRefObject, useCallback, useEffect, useRef, useState} from 'react';
import {Pricecards} from '@baidu/bce-portal-ui';
import {PricecardItem} from '../Recommend/Recommend';
import styles from './prefecture.module.less';

interface ProductsBlaPrefectureProps {
    datasource?: PricecardItem[];
    onConsult?: (item: any) => void;
    onBuy?: (item: any) => void;
}

let timer: any = null;
let animationTimer: any = null;

export const ProductsBlaPrefecture = (props: ProductsBlaPrefectureProps) => {
    const {datasource, onConsult, onBuy} = props;
    const [current, setCurrent] = useState<number>(1);
    const [nextAnimation, setNextAnimation] = useState<string>('next');
    const [total, setTotal] = useState<number>(1);
    const ref: MutableRefObject<any> = useRef(null);

    // 自动播放
    const autoSlide = useCallback((rel?: string) => {
        const totalCount = datasource.length;
        setTotal(totalCount);
        timer && clearTimeout(timer);
        animationTimer && clearTimeout(animationTimer);
        switch (rel) {
            case 'prev':
                setCurrent((current - 1 + totalCount) % totalCount);
                break;
            case 'next':
            default:
                setCurrent((current + 1) % totalCount);
                break;
        }
        setNextAnimation(rel);
    }, [datasource, current]);

    useEffect(() => {
        if (current === total - 1 && nextAnimation === 'prev') {
            ref.current.style.transition = 'transform 0s ease';
            ref.current.style.transform = `translateX(${-300 * total}px)`;
            animationTimer = setTimeout(() => {
                ref.current.style.transition = 'transform .5s ease';
                ref.current.style.transform = `translateX(${-300 * current}px)`;
                clearTimeout(animationTimer);
            }, 0);
        }
        else if (!current && nextAnimation === 'next') {
            ref.current.style.transform = `translateX(${-300 * total}px)`;
            animationTimer = setTimeout(() => {
                ref.current.style.transition = 'transform 0s ease';
                ref.current.style.transform = `translateX(${-300 * current}px)`;
                clearTimeout(animationTimer);
            }, 500);
        }
        else {
            ref.current.style.transition = 'transform .5s ease';
            ref.current.style.transform = `translateX(${-300 * current}px)`;
        }
    }, [current, nextAnimation, total]);

    useEffect(() => {
        timer = setTimeout(() => {
            autoSlide('next');
        }, 3000);
        return () => clearTimeout(timer);
    }, [autoSlide]);

    return (
        <div className={styles.PrefectureBg}>
            <div className={styles.PrefectureWrapper}>
                <div className={styles.introWrapper}>
                    <div className={styles.introQuote}></div>
                    <div className={styles.introTitle}>高新企业专区</div>
                    <div className={styles.introDesc}>全程专业保障，提升项目申报成功率，可申请国家高新补贴与基金</div>
                </div>
                <div className={styles.sliderWrapper}>
                    <div className={cx(styles.flexCenter, styles.sliderContanier)}>
                        <div
                            className={cx(styles.arrowIcon, styles.leftArrowIcon)}
                            onClick={() => autoSlide('prev')}
                        >
                        </div>
                        <div className={cx(styles.sliderContent)}>
                            <div
                                className={styles.sliderItems}
                                style={{width: `${300 * datasource.length - 20}px`}}
                                ref={ref}
                            >
                                {datasource.map(item => (
                                    <div
                                        className={cx(styles.sliderItemBg)}
                                        key={item.title}
                                    >
                                        <Pricecards
                                            headerClassName={styles.headerBg}
                                            datasource={[item]}
                                            onConsult={onConsult}
                                            onBuy={onBuy}
                                            cardWidth="280px"
                                            labelsSolt={() => <div className={styles.labelBorder}></div>}
                                        />
                                    </div>
                                ))}
                                {total > 3 && datasource.slice(0, 3).map(item => (
                                    <div
                                        className={cx(styles.sliderItemBg)}
                                        key={item.title}
                                    >
                                        <Pricecards
                                            headerClassName={styles.headerBg}
                                            datasource={[item]}
                                            onConsult={onConsult}
                                            onBuy={onBuy}
                                            cardWidth="280px"
                                            labelsSolt={() => <div className={styles.labelBorder}></div>}
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div
                            className={cx(styles.arrowIcon, styles.rightArrowIcon)}
                            onClick={() => autoSlide('next')}
                        >
                        </div>
                    </div>
                    <div className={styles.sliderDots}>
                        {datasource.map((item, index) => (
                            <div
                                className={cx(styles.sliderDotItem, current === index ? styles.dotActive : '')}
                                key={item.title}
                                onClick={() => setCurrent(index)}
                            >
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};
