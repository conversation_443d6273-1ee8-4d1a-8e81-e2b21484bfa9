/**
 * @file BLA 资质代办官网结果页
 * @description 资质详情弹窗
 */

import cx from 'classnames';
import {useCallback, useEffect, useState} from 'react';
import {Modal} from 'antd';
import {DetailTabs, scrollToAnchor} from '../config';
import styles from './detail.module.less';

interface ProductsBlaDetailModalProps {
    open?: boolean;
    detail: QualificationDetail;
    onConfirm?: () => void;
    onCancel?: (value: boolean) => void;
}

export interface QualificationDetail {
    title: string;
    supervisor: string;
    approve: string;
    approveLevel: string;
    processingConditions: string;
    processingTerms: string;
    legalBasis: string;
    type?: string;
}

interface TabItem {
    id: string;
    text: string;
}

export const ProductsBlaDetailModal = (props: ProductsBlaDetailModalProps) => {
    const {open, detail, onConfirm, onCancel} = props;
    const [tabs, setTabs] = useState<TabItem[]>([]);
    const [currrent, setCurrent] = useState<string>('');

    const showTab = useCallback((type: string) => {
        switch (type) {
            case 'supervisor': {
                return detail?.supervisor || detail?.approve || detail?.approveLevel;
            }
            case 'processingConditions': {
                return detail?.processingConditions;
            }
            case 'processingTerms': {
                return detail?.processingTerms;
            }
            case 'legalBasis': {
                return detail?.legalBasis;
            }
            default:
                return '';
        }
    }, [detail]);

    const onClose = () => {
        onCancel && onCancel(false);
    };

    useEffect(() => {
        const data = DetailTabs.filter(item => showTab(item.id));
        setTabs(data);
        data.length && setCurrent(data[0].id);
    }, [detail, showTab]);

    return (
        <Modal
            wrapClassName={styles.detailWrapClassName}
            visible={open}
            closable={false}
            footer={null}
            width="680px"
            centered
            onCancel={onClose}
        >
            <div className={styles.header}>
                <div className={styles.title}>{detail?.title}</div>
                <div className={cx(styles.iconBg, styles.closeIcon)} onClick={onClose}></div>
            </div>
            <div className={styles.detailContainer}>
                <div className={styles.steps}>
                    {tabs.map(tab => showTab(tab.id) && (
                        <div
                            key={tab.id}
                            className={cx(styles.tabItem, currrent === tab.id ? styles.activeTab : styles.defaultTab)}
                            onClick={() => {
                                setCurrent(tab.id);
                                scrollToAnchor(tab.id, true);
                            }}
                        >
                            {tab.text}
                        </div>
                    ))}
                </div>
                <div className={cx(styles.infos, detail?.type ? styles.lastStep : '')}>
                    {showTab('supervisor') && (
                        <div className={styles.stepInfo}>
                            <div id="supervisor"></div>
                            <div className={styles.infoTitle}>审批流程</div>
                            <div className={styles.infoText}>
                                {detail?.supervisor && <div>主管部门：{detail?.supervisor}</div>}
                                {detail?.approve && <div>审批部门：{detail?.approve}</div>}
                                {detail?.approveLevel && <div>审批层级：{detail?.approveLevel}</div>}
                            </div>
                        </div>
                    )}
                    {detail?.processingConditions && (
                        <div className={styles.stepInfo}>
                            <div id="processingConditions"></div>
                            <div className={styles.infoTitle}>办理条件</div>
                            <div className={styles.infoText}>{detail?.processingConditions}</div>
                        </div>
                    )}
                    {detail?.processingTerms && (
                        <div className={styles.stepInfo}>
                            <div id="processingTerms"></div>
                            <div className={styles.infoTitle}>申请材料</div>
                            <div className={styles.infoText}>{detail?.processingTerms}</div>
                        </div>
                    )}
                    {detail?.legalBasis && (
                        <div className={styles.stepInfo}>
                            <div id="legalBasis"></div>
                            <div className={styles.infoTitle}>法律依据</div>
                            <div className={styles.infoText}>{detail?.legalBasis}</div>
                        </div>
                    )}
                    {detail?.type && (
                        <div className={styles.fixedBtn}>
                            <div className={styles.defaultBtn} onClick={onConfirm}>立即办理</div>
                        </div>
                    )}
                </div>
            </div>
        </Modal>
    );
};
