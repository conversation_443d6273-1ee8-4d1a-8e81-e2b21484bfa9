.detailWrapClassName {
    :global {
        .ant-modal-mask {
            background: rgba(34, 34, 34, .5);
        }
        .ant-modal-body {
            padding: 0;
        }
        .ant-modal-content {
            border-radius: 6px;
            min-height: 560px;
        }
    }
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
}

.title {
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #222222;
    letter-spacing: 0;
    line-height: 28px;
    font-weight: 500;
}

.iconBg {
    background-repeat: no-repeat;
    background-size: cover;
}

.closeIcon {
    width: 10px;
    height: 10px;
    background-image: url('https://cbs-public.cdn.bcebos.com/portal/20220330/close.png');
    cursor: pointer;
}

.detailContainer {
    display: flex;
    border-top: 1px solid #EDEDED;
    position: relative;
}

.steps {
    width: 104px;
    height: 483px;
    border-right: 1px solid #EDEDED;
}

.tabItem {
    width: 100%;
    height: 48px;
    line-height: 48px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
}

.tabItem:hover {
    background: rgba(36, 105, 243, .12);
}

.defaultTab {
    color: rgba(34, 34, 34, .7);
    font-weight: 400;
}

.activeTab {
    color: #2469F3;
    font-weight: 500;
    background: rgba(36, 105, 243, .12);
}

.infos {
    width: 575px;
    height: 483px;
    padding: 24px;
    overflow-y: scroll;
}

.stepInfo {
    width: 100%;
    padding-bottom: 24px;
}

.lastStep {
    padding-bottom: 100px;
}

.infoTitle {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #222222;
    letter-spacing: 0;
    line-height: 26px;
    font-weight: 500;
    margin-bottom: 8px;
}

.infoText {
    opacity: 0.7;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
    white-space: pre-wrap;
}

.fixedBtn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 575px;
    height: 84px;
    padding: 24px;
    background-color: #FFFFFF;
}

.defaultBtn {
    float: right;
    width: 104px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    background: #2469F3;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    font-weight: 400;
    cursor: pointer;
}
