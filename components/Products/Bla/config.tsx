/**
 * @file BLA官网首页(全局静态数据)
 * /components/Products/Bla/config.tsx
 */

import moment from 'moment';
import md5 from 'js-md5';

export const Link = {
    cloud: 'https://cloud.baidu.com',
    bla: '/product/bla',
    cbs: '/product/cbs',
    list: 'https://console.bce.baidu.com/cbs#/bla/license/list',
    order: 'https://console.bce.baidu.com/cbs#/bla/license/choose',
    park: '/product/cbs#/park?track=zizhisearch&pageResource=zizhisearch',
};

export const SandboxLink = {
    cloud: 'https://cloudtest.baidu.com',
    bla: '/product/bla',
    cbs: '/product/cbs',
    list: 'https://qasandbox.bcetest.baidu.com/cbs/#/bla/license/list',
    order: 'https://qasandbox.bcetest.baidu.com/cbs/#/bla/license/choose',
    park: '/product/cbs#/park?track=zizhisearch&pageResource=zizhisearch',
};

export const addTraceToUrl = (url: '', traceValue: string) => {
    const trace = ['trace', 'pageResource'].map(item => `${item}=${traceValue}`).join('&');
    return `${url}${url.includes('?') ? '&' : '?'}${trace}`;
};

export const CbsBreadcrumbLinks = (link: any) => [
    {
        text: '云官网首页',
        link: addTraceToUrl(link.cloud, 'cbs'),
    },
    {
        text: '工商财税',
        link: addTraceToUrl(link.cbs, 'cbsnav'),
    },
];

export const SearchTabs = [
    {
        label: '资质查询',
        key: 'qualification',
    },
    {
        label: '智能起名',
        key: 'smartname',
    },
    {
        label: '工商核名',
        key: 'checkname',
    },
];

export const Steps = [
    {
        title: '咨询洽谈',
        imgUrl: 'https://cbs-public.bj.bcebos.com/portal/20220506/step-consult.png',
        labels: [
            '7 * 12小时在线',
            '10分钟快速响应',
        ],
    },
    {
        title: '准备资料',
        imgUrl: 'https://cbs-public.bj.bcebos.com/portal/20220506/step-material.png',
        labels: [
            '专业顾问全程辅助',
            '一键递交更高效',
        ],
    },
    {
        title: '提交审核',
        imgUrl: 'https://cbs-public.bj.bcebos.com/portal/20220506/step-submit.png',
        labels: [
            '即传即交 资料不堆积',
            '绿色通道 优先选择',
        ],
    },
    {
        title: '成功拿证',
        imgUrl: 'https://cbs-public.bj.bcebos.com/portal/20220506/step-success.png',
        labels: [
            '随时查询办理进度',
            '部分地区包邮到家',
        ],
    },
];

export const Advantages = {
    left: [
        {
            id: '01',
            icon: 'https://cbs-public.bj.bcebos.com/portal/20220506/advantage-profession.png',
            title: '专业服务',
            desc: '精选资质顾问提供专业的资质解决方案，帮助您选择更适合的资质。',
        },
        {
            id: '02',
            icon: 'https://cbs-public.bj.bcebos.com/portal/20220506/advantage-cover.png',
            title: '全行业覆盖',
            desc: '整合全行业资质种类，并提供在线选购及管理平台，省心省力管理您的资质。',
        },
    ],
    right: [
        {
            id: '03',
            icon: 'https://cbs-public.bj.bcebos.com/portal/20220506/advantage-fast.png',
            title: '急速递交',
            desc: '资质申请材料即传即交，快至1分钟递交至资质审查机构。',
        },
        {
            id: '04',
            icon: 'https://cbs-public.bj.bcebos.com/portal/20220506/advantage-quality.png',
            title: '品质保障',
            desc: '致力于提供比同行更专业的服务，官方品牌更值得信赖，全程为您的服务保驾护航。',
        },
    ],
};

export const Anchors = [
    {
        id: 'searchResult',
        text: '查询结果',
    },
    {
        id: 'submitDemand',
        text: '提交需求',
    },
    {
        id: 'recommendService',
        text: '推荐服务',
    },
];

export const DemandFastLabels = [
    '官方精选资深顾问，免费提供咨询服务',
    '对您推荐更合适公司发展且性价比更高的方案',
    '线上服务流程标准化，可随时查看申报进度',
    '为您提供企业全生命周期的贴心服务',
];

export const DetailTabs = [
    {
        id: 'supervisor',
        text: '审批流程',
    },
    {
        id: 'processingConditions',
        text: '办理条件',
    },
    {
        id: 'processingTerms',
        text: '申请材料',
    },
    {
        id: 'legalBasis',
        text: '法律依据',
    },
];

export const QualificationTypes = [
    {
        key: 'INDUSTRY',
        value: '按行业查询',
    },
    {
        key: 'COMPANY',
        value: '按企业名查询',
    },
    {
        key: 'QUALIFICATION',
        value: '按资质名查询',
    },
];

export const Rules = {
    name: [
        () => ({
            validator(_: any, value: any) {
                if (!value) {
                    return Promise.reject(new Error('请输入您的称呼'));
                }
                return Promise.resolve();
            },
        }),
    ],
    bizType: [
        () => ({
            validator(_: any, value: any) {
                if (!value) {
                    return Promise.reject(new Error('请选择咨询的服务'));
                }
                return Promise.resolve();
            },
        }),
    ],
    phone: [
        () => ({
            validator(_: any, value: any) {
                if (!value) {
                    return Promise.reject(new Error('请输入手机号'));
                }
                if (!/^1\d{10}$/.test(value)) {
                    return Promise.reject(new Error('请输入正确的手机号'));
                }
                return Promise.resolve();
            },
        }),
    ],
    smsCode: [
        () => ({
            validator(_: any, value: any) {
                if (!value) {
                    return Promise.reject(new Error('请输入验证码'));
                }
                if (!/\d{6}$/.test(value)) {
                    return Promise.reject(new Error('请输入正确的验证码'));
                }
                return Promise.resolve();
            },
        }),
    ],
};

// 搜索框参数拼接
export const concatParams = (params: any) => {
    const keys = Object.keys(params);
    return keys.map(key => `${key}=${params[key] || ''}`).join('&');
};

export const contraryReptile = (url: string) => {
    // 通过反爬验证timestamp
    const time = moment(new Date()).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
    return time + '@' + md5('POST' + url + time + 'ieq%$jsaf23!@fkjwie');
};

// 滚动到锚点
export const scrollToAnchor = (anchorName: string, center: boolean = false) => {
    if (anchorName) {
        // 找到锚点
        const anchorElement = document.getElementById(anchorName);
        // 如果对应id的锚点存在，就跳转到锚点
        if (anchorElement) {
            anchorElement.scrollIntoView({
                behavior: 'smooth',
                block: center ? 'center' : 'start',
            });
        }
    }
};

// 将参数转换为数组
const convertParamArrToObj = (arr: string[], obj: any) => {
    obj = obj || {};
    for (const item of arr) {
        const tempArr = item.split('=');
        obj[tempArr[0]] = tempArr[1];
    }
    return obj;
};

// 根据当前所在界面返回默认的追踪字段值
const getDefaultTrackValueByPage = () => {
    let {hash} = window.location;
    if (!hash) {
        return 'bla_portal_index';
    }
    // 去除 #/
    hash = hash.replace(/^#\//, '');
    // 去除 query
    hash = hash.split('?')[0];
    // 替换为下划线分隔
    hash = hash.replace(/\//, '_');
    return `bla_portal_${hash}`;
};

// 获取URL中携带的参数
export const getTrackValueFromUrl = (name?: string) => {
    const trackKeys: string[] = ['trace', 'track', 'pageResource', 'pageSource'];
    let obj = {};
    // 从 search 中获取参数
    const search = window.location.search.substring(1);
    const searchParamArr = search.split('&');
    obj = convertParamArrToObj(searchParamArr, obj);

    // 从 hash 中获取参数
    const hashQueryIndex = window.location.hash.indexOf('?');
    const hashQuery = window.location.hash.substring(hashQueryIndex + 1);
    const hashParamArr = hashQuery.split('&');

    // 按照优先级，返回 url 链接中的追踪参数的字段值
    obj = convertParamArrToObj(hashParamArr, obj);
    for (const key of trackKeys) {
        // @ts-ignore
        if (obj[key]) {
            // @ts-ignore
            return decodeURIComponent(obj[key]);
        }
    }

    // 如果传入的 name 有值，则返回 name
    if (typeof (name) === 'string' && name) {
        return name;
    }

    // 根据当前所在界面返回默认值
    return getDefaultTrackValueByPage();
};
