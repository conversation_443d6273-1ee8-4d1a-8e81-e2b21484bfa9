/**
 * @file BLA 资质代办官网首页
 * @description 表单提交成功弹窗
 */

import cx from 'classnames';
import {Modal} from 'antd';
import styles from './success.module.less';

interface ProductsBlaSuccessModalProps {
    open?: boolean;
    title: string;
    note: string;
    onCancel?: (value: boolean) => void;
}

export const ProductsBlaSuccessModal = (props: ProductsBlaSuccessModalProps) => {
    const {open, title, note, onCancel} = props;
    const bodyStyle = {
        padding: '24px',
    };

    const onClose = () => {
        onCancel && onCancel(false);
    };

    return (
        <Modal
            wrapClassName={styles.successWrapClassName}
            bodyStyle={bodyStyle}
            visible={open}
            closable={false}
            footer={null}
            width="400px"
            centered
            onCancel={onClose}
        >
            <div className={styles.tipWrapper}>
                <div className={cx(styles.iconBg, styles.tipIcon)}></div>
                <div className={styles.tipText}>
                    <div className={styles.title}>{title}</div>
                    <div className={styles.note}>{note}</div>
                </div>
                <div className={cx(styles.iconBg, styles.closeIcon)} onClick={onClose}></div>
                <div className={styles.closeBtn} onClick={onClose}>关闭</div>
            </div>
        </Modal>
    );
};
