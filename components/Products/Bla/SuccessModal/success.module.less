.successWrapClassName {
    :global {
        .ant-modal-mask {
            background: rgba(34, 34, 34, .5);
        }
        .ant-modal-content {
            border-radius: 6px;
            min-height: 182px;
        }
    }
}

.tipWrapper {
    display: flex;
}

.tipIcon {
    width: 18px;
    height: 18px;
    background-image: url('https://cbs-public.cdn.bcebos.com/portal/20220330/success.png');
}

.tipText {
    margin-left: 12px;
}

.title {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    line-height: 14px;
    font-weight: 500;
}

.note {
    margin-top: 16px;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #222222;
    letter-spacing: 0;
    line-height: 12px;
    font-weight: 400;
}

.iconBg {
    background-repeat: no-repeat;
    background-size: cover;
}

.closeIcon {
    position: absolute;
    top: 24px;
    right: 24px;
    width: 10px;
    height: 10px;
    background-image: url('https://cbs-public.cdn.bcebos.com/portal/20220330/close.png');
    cursor: pointer;
}

.closeBtn {
    position: absolute;
    bottom: 24px;
    right: 24px;
    width: 72px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    background: #2469F3;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    font-weight: 400;
    cursor: pointer;
}
