/**
 * @file BLA 资质代办官网首页
 * @description 拿证步骤
 */

import cx from 'classnames';
import {MutableRefObject, useEffect, useRef, useState} from 'react';
import {Steps} from '../config';
import styles from './steps.module.less';

export const ProductsBlaSteps = () => {
    const [current, setCurrent] = useState<number>(0);
    const totalCount = Steps.length - 1;
    const ref: MutableRefObject<any> = useRef(null);

    useEffect(() => {
        const timer = setTimeout(() => {
            setCurrent((current + 1) % (totalCount + 1));
            ref.current.style.transition = current === totalCount ? 'width 0s ease' : 'width 1.5s ease';
        }, 2000);
        return () => clearTimeout(timer);
    }, [current, totalCount]);

    return (
        <div className={styles.stepsWrapper}>
            <header className={cx(styles.bodyCenter, styles.header)}>
                <div className={styles.headerTitle}>在线申请，仅需4步拿证</div>
                <div className={cx(styles.defaultText, styles.headerDesc)}>告别线下递交繁琐流程，专业顾问辅助快速下证</div>
            </header>
            <div id="bla_application_flow"></div>
            <div className={cx(styles.bodyCenter, styles.stepsContainer)}>
                <div className={styles.bar}>
                    <div ref={ref} className={styles.percent} style={{width: `${100 * current / totalCount}%`}}></div>
                </div>
                <div className={styles.flexBetwween}>
                    {Steps.map((step, index) => (
                        <div className={styles.stepItem} key={step.title}>
                            <img className={styles.stepIcon} src={step.imgUrl} alt={step.title} />
                            <div className={cx(styles.title, current > index ? styles.activeTitle : '')}>{step.title}</div>
                            <div className={styles.indexIconContainer}>
                                <div className={cx(styles.indexIcon,
                                    current >= index ? styles.activeIndex : styles.defaultIndex)}
                                >
                                    {index + 1}
                                </div>
                            </div>
                            <div>
                                {step.labels.map(label => (
                                    <div className={styles.labelItem} key={label}>
                                        <div className={styles.labelIcon}></div>
                                        <div className={styles.labelText}>{label}</div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};
