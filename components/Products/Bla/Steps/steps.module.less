.stepsWrapper {
    background: #F6F8FB;
    padding: 52px 0;
}

.bodyCenter {
    width: 1180px;
    margin: 0 auto;
}

.header {
    text-align: center;
}

.headerTitle {
    font-family: PingFangSC-Semibold;
    font-size: 28px;
    color: #222222;
    letter-spacing: 0;
    text-align: center;
    line-height: 28px;
    font-weight: 600;
}

.defaultText {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    text-align: center;
    line-height: 24px;
    font-weight: 400;
}

.headerDesc {
    opacity: 0.7;
    margin-top: 12px;
    margin-bottom: 32px;
}

.stepsContainer {
    margin-top: 32px;
    height: 242px;
    padding: 32px 70px;
    background: #FFFFFF;
    box-shadow: 0 2px 16px 0 rgba(34,34,34,0.10);
    border-radius: 6px;
    position: relative;
}

.flexBetwween {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stepItem {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stepIcon {
    width: 40px;
    height: 40px;
}

.title {
    margin-top: 12px;
    opacity: 0.7;
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #222222;
    letter-spacing: 0;
    line-height: 18px;
    font-weight: 500;
}

.activeTitle {
    opacity: 1;
}

.indexIconContainer {
    position: relative;
    z-index: 3;
    padding: 16px 12px;
    background-color: #FFFFFF;
}

.indexIcon {
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 50%;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #FFFFFF;
    font-weight: 400;
}

.activeIndex {
    background: #2469F3;
}

.defaultIndex {
    opacity: 0.4;
    background: #222222;
}

.labelItem {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 12px;
}

.labelItem:last-child {
    margin-bottom: 0;
}

.labelIcon {
    width: 14px;
    height: 14px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220506/checked.png');
}

.labelText {
    margin-left: 8px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
}

.bar {
    position: absolute;
    z-index: 2;
    top: 126px;
    left: 157px;
    width: 866px;
    height: 2px;
    background: #EDEDED;
    border-radius: 2px;
}

.percent {
    height: 100%;
    background: #2469F3;
    border-radius: 2px;
    transition: width .8s ease;
}