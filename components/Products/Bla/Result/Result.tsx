/**
 * @file BLA 资质代办官网结果页
 * @description 资质结果列表
 */

import cx from 'classnames';
import {useState} from 'react';
import {QualificationDetail} from '../DetailModal/DetailModal';
import styles from './result.module.less';

interface ProductsBlaResultProps {
    qualifications: {
        [propName: string]: Qualification[];
    };
    onDetail?: (detail: QualificationDetail, license?: LicenseItem) => void;
    onConsult?: (value: any) => void;
    onBuy?: (value: any) => void;
    onEmptyConsult?: (value: any) => void;
}

export interface LicenseItem {
    type?: string;
    industryType?: string;
    name: string;
    parentsLicense: string;
    licenseTreeLevel: string;
    categoryName: string;
    itemUrl: string;
    description: string;
    tag?: string;
    children?: LicenseItem[];
}

interface Qualification extends LicenseItem {
    supervisor: string;
    approve: string;
    approveLevel: string;
    legalBasis: string;
    processingConditions: string;
    processingTerms: string;
    superiority: string;
}

interface LicenseBaseItemProps {
    license: LicenseItem;
    extendedData?: any;
}

interface QualificationBaseItemProps {
    qualification: Qualification;
    extendedData?: any;
}

const LicenseBaseItem = ({license, extendedData}: LicenseBaseItemProps) => (
    <div className={cx(styles.flexCenter, styles.licenseItem)}>
        <div className={styles.licenseInfo}>
            <div className={styles.flexAlignCenter}>
                <div className={styles.licenseTitle}>{license.name}</div>
                {license.type && (
                    <div className={cx(styles.imgBg, styles.tagImg, styles.licenseTagText)}>{license.tag}</div>
                )}
            </div>
            <div className={styles.licensenDesc}>{license.description || license.categoryName}</div>
        </div>
        <div className={styles.licenseOpt}>
            <div
                className={cx(styles.licenseBtn, styles.primaryBtn)}
                onClick={() => extendedData.onDetail({
                    ...extendedData.detail,
                    type: license.type,
                }, license)}
            >
                查看详情
            </div>
            {license.type && (
                <div
                    className={cx(styles.licenseBtn, styles.defaultBtn)}
                    onClick={() => extendedData.onBuy(license)}
                >
                    立即办理
                </div>
            )}
            <div
                className={cx(styles.licenseBtn, styles.defaultBtn)}
                onClick={() => extendedData.onConsult(license)}
            >
                立即咨询
            </div>
        </div>
    </div>
);

const QualificationBaseItem = ({qualification, extendedData}: QualificationBaseItemProps) => {
    const children = qualification.children;
    return children.length ? (
        <>
            {children.map(child => (
                <LicenseBaseItem
                    license={{
                        ...child,
                        name: `${qualification.name}-${child.name}`,
                    }}
                    key={child.name}
                    extendedData={extendedData}
                />
            ))}
        </>
    ) : (
        <LicenseBaseItem
            license={qualification}
            extendedData={extendedData}
        />
    );
};

export const ProductsBlaResult = (props: ProductsBlaResultProps) => {
    const {qualifications, onDetail, onConsult, onBuy, onEmptyConsult} = props;
    const types = Object.keys(qualifications);
    const [folds, setFolds] = useState<boolean[]>([false, false, false]);

    return (
        <div className={styles.resultWrapper}>
            <div id="searchResult"></div>
            {!!types && types.map((type, index) => (
                <div className={styles.resultContainer} key={type}>
                    <div className={cx(styles.flexCenter, styles.qualificationHeader)}>
                        <div className={styles.qualificationTitle}>{type}</div>
                        <div
                            className={cx(styles.imgBg, styles.foldIcon, folds[index] ? '' : styles.convert)}
                            onClick={() => {
                                setFolds(folds.map((fold, fIndex) => {
                                    return fIndex === index ? !fold : fold;
                                }));
                            }}
                        >
                        </div>
                    </div>
                    <div className={cx(styles.qualifications, folds[index] ? styles.fold : '')}>
                        {qualifications[type].map(qualification => (
                            <QualificationBaseItem
                                qualification={qualification}
                                key={qualification.name}
                                extendedData={{
                                    detail: {
                                        title: qualification.name,
                                        supervisor: qualification.supervisor,
                                        approve: qualification.approve,
                                        approveLevel: qualification.approveLevel,
                                        processingConditions: qualification.processingConditions,
                                        processingTerms: qualification.processingTerms,
                                        legalBasis: qualification.legalBasis,
                                    },
                                    onDetail,
                                    onConsult,
                                    onBuy,
                                }}
                            />
                        ))}
                    </div>
                </div>
            ))}
            {!types.length && (
                <div className={styles.empty}>
                    <div className={styles.emptyImg}></div>
                    <div className={styles.emptyInfo}>该行业未查询到相关资质，可免费咨询专业顾问人工评估</div>
                    <div
                        className={cx(styles.licenseBtn, styles.primaryBtn, styles.emptyBtn)}
                        onClick={onEmptyConsult}
                    >
                        立即咨询
                    </div>
                </div>
            )}
        </div>
    );
};
