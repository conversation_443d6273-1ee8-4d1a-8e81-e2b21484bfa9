/**
 * @file BLA 资质代办官网首页
 * @description 产品导航模块
 */

import cx from 'classnames';
import {ConsultExtraData} from '../ConsultModal/ConsultModal';
import styles from './nav.module.less';

interface ProductsBlaNavProps {
    navs: NavItem[];
    onConsult?: (params: ConsultExtraData) => void;
}

export interface NavItem {
    icon: string;
    title: string;
    bizType: string;
    trace: string;
    tags: string[];
    items: navItem[];
}

interface navItem {
    name: string;
    link?: string;
}

export const ProductsBlaNav = (props: ProductsBlaNavProps) => {
    const {navs, onConsult} = props;

    const onClickNav = (item: navItem, nav: NavItem) => {
        if (item.link) {
            window.open(`${item.link}${item.link.includes('?') ? '&' : '?'}${nav.trace}`);
            return;
        }
        onConsult && onConsult({
            name: item.name,
            bizType: nav.bizType,
        });
    };

    return (
        <div className={styles.navPosition}>
            <div className={cx(styles.bodyCenter, styles.navWrapper)}>
                {!!navs.length && navs.map(nav => (
                    <div key={nav.title} className={styles.navContainer}>
                        <div className={styles.nav}>
                            <div className={cx(styles.flexAlignCenter, styles.title)}>
                                <img src={nav.icon} className={styles.navIcon} alt={nav.title} />
                                <div className={styles.navTitle}>{nav.title}</div>
                                {nav.tags.length && nav.tags.map(tag => (
                                    <div key={tag} className={styles.navTag}>{tag}</div>
                                ))}
                            </div>
                            <div className={styles.navItemsContainer}>
                                <div className={styles.navItems}>
                                    {nav.items.map(item => (
                                        <span
                                            key={item.name}
                                            className={cx(styles.flexAlignCenter, styles.navItem)}
                                            onClick={() => onClickNav(item, nav)}
                                        >
                                            <span className={styles.navInterval}></span>
                                            <span>{item.name}</span>
                                        </span>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};
