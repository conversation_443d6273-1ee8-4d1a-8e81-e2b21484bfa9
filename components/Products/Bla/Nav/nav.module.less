.bodyCenter {
    width: 1180px;
    height: 196px;
    margin: 0 auto;
}

.navPosition {
    position: absolute;
    top: 338px;
    width: 100%;
}

.navWrapper {
    background: #FFFFFF;
    box-shadow: 0 2px 18px 0 rgba(34,34,34,0.10);
    border-radius: 6px;
    display: flex;
    flex-wrap: wrap;
}

.flexAlignCenter {
    display: flex;
    align-items: center;
}

.navContainer {
    position: relative;
    width: 393.3px;
    height: 98px;
    border-right: 1px solid #EDEDED;
    border-bottom: 1px solid #EDEDED;
}

.nav {
    position: absolute;
    z-index: 5;
    padding: 20px;
}

.nav:hover {
    z-index: 9;
    width: 100%;
    min-height: 58px;
    background: #FFFFFF;
    box-shadow: 0 2px 18px 0 rgba(34,34,34,0.10);
    border-radius: 0 6px 0 0;
}

.nav:hover .navTitle {
    color: #2469F3;
    font-weight: 500;
}

.navIcon {
    width: 18px;
    height: 18px;
}

.navTitle {
    margin-left: 8px;
    font-family: PingFangSC-Regular;
    font-size: 16px;
    color: #222222;
    letter-spacing: 0;
    line-height: 26px;
    font-weight: 400;
}

.navTag {
    margin-left: 8px;
    padding: 0 8px;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #2469F3;
    letter-spacing: 0;
    line-height: 20px;
    font-weight: 400;
    border: 1px solid #2469F3;
    border-radius: 2px;
}

.navItemsContainer {
    margin-top: 11px;
    height: 24px;
    overflow: hidden;
    position: relative;
}

// .navContainer:hover .navItemsContainer {
//     height: auto;
// }

.navItems {
    margin-left: -25px;
}

.navItem {
    float: left;
    opacity: 0.7;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
    cursor: pointer;
}

.navItem:hover {
    opacity: 1;
    color: #2469F3;
}

.navInterval {
    display: inline-block;
    height: 14px;
    border-left: 1px solid #222222;
    margin: 0 12px;
}