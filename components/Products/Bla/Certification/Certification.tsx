/**
 * @file BLA 资质代办官网首页
 * @description 全行业资质
 */

import cx from 'classnames';
import {useEffect, useState} from 'react';
import {Pricecards} from '@baidu/bce-portal-ui';
import {PricecardItem} from '../Recommend/Recommend';
import styles from './certification.module.less';

interface ProductsBlaCertificationProps {
    datasource?: CertificationItem[];
    onConsult?: (item: any) => void;
    onBuy?: (item: any) => void;
    onDetail?: (item: any) => void;
    onBlaConsult?: (item: any) => void;
}

export interface CertificationItem {
    industry: string;
    desc: string;
    qualifications: PricecardItem[];
}

export const ProductsBlaCertification = (props: ProductsBlaCertificationProps) => {
    const {datasource, onConsult, onDetail, onBuy, onBlaConsult} = props;
    const [current, setCurrent] = useState<number>(0);
    const [showMore, setShowMore] = useState<boolean>(false);
    const [currentDatasource, setCurrentDatasource] = useState<PricecardItem[]>();
    const maxLength = 8;
    const kinds = datasource.map(item => ({
        title: item.industry,
        desc: item.desc,
    }));

    useEffect(() => {
        const qualifications = datasource[current]?.qualifications?.length > maxLength && !showMore
            ? datasource[current]?.qualifications?.slice(0, maxLength)
            : datasource[current]?.qualifications;
        setCurrentDatasource(qualifications);
    }, [datasource, current, maxLength, showMore]);

    const headerSlot = (params: any) => {
        return (
            <div className={styles.showDetail} onClick={() => onDetail(params)}>
                <div className={styles.showDetailText}>查看详情</div>
                <div className={cx(styles.defaultIcon, styles.showDetailIcon)}></div>
            </div>
        );
    };

    return (
        <div className={styles.certificationWrapper}>
            <header className={styles.header}>
                <div className={styles.headerTitle}>全行业资质任您挑选</div>
                <div className={cx(styles.defaultText, styles.headerDesc)}>覆盖更全面的资质种类，提供更专业的代办服务</div>
            </header>
            <div className={styles.kinds}>
                {kinds.map(((item, index) => (
                    <div
                        key={item.title}
                        className={cx(styles.kindItemDefault, current === index ? styles.kindItemActive : '')}
                        onClick={() => setCurrent(index)}
                    >
                        <div className={styles.kindItemTitle}>{item.title}</div>
                        <div className={styles.kindItemDesc}>{item.desc}</div>
                    </div>
                )))}
            </div>
            <div className={styles.certificationsContainer}>
                <Pricecards
                    cardClassName={styles.cardClassName}
                    titleClassName={styles.titleClassName}
                    datasource={currentDatasource}
                    onConsult={onConsult}
                    onBuy={onBuy}
                    headerSolt={headerSlot}
                />
            </div>
            <div
                className={cx(
                    styles.certificationsMore,
                    currentDatasource?.length === datasource[current]?.qualifications?.length ? styles.hidden : ''
                )}
                onClick={() => setShowMore(true)}
            >
                <span>查看更多</span>
                <div className={cx(styles.defaultIcon, styles.certificationsMoreIcon)}></div>
            </div>
            <div className={cx(styles.defaultIcon, styles.consultBanner)}>
                <div className={styles.consultIntro}>如您没有找到您想要的资质，请联系客服或您的专家顾问</div>
                <div
                    className={styles.consultBtn}
                    onClick={onBlaConsult}
                >
                    咨询顾问
                </div>
            </div>
        </div>
    );
};
