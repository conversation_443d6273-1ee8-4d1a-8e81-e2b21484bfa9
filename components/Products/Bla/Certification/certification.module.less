.certificationWrapper {
    width: 1180px;
    margin: 0 auto;
    padding: 52px 0;
}

.header {
    text-align: center;
}

.headerTitle {
    font-family: PingFangSC-Semibold;
    font-size: 28px;
    color: #222222;
    letter-spacing: 0;
    text-align: center;
    line-height: 28px;
    font-weight: 600;
}

.defaultText {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    text-align: center;
    line-height: 24px;
    font-weight: 400;
}

.headerDesc {
    opacity: 0.7;
    margin-top: 12px;
    margin-bottom: 32px;
}

.kinds {
    height: 68px;
    background: #F6F8FB;
    border-radius: 6px;
    display: flex;
}

.kindItemDefault {
    width: 196px;
    height: 100%;
    position: relative;
    color: #222222;
    text-align: center;
    cursor: pointer;
}

.kindItemActive {
    background: #2469F3;
    color: #FFFFFF;
    border-radius: 6px;
}

.kindItemActive::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -8px;
    border-color: #2469F3 transparent transparent transparent;
    border-style: solid solid solid solid;
    border-width: 4px;
    height: 0px;
    width: 0px;
}

.kindItemTitle {
    margin-top: 7px;
    font-family: PingFangSC-Regular;
    font-size: 16px;
    line-height: 26px;
    font-weight: 400;
}

.kindItemActive .kindItemTitle {
    font-family: PingFangSC-Medium;
    font-weight: 500;
}

.kindItemDesc {
    margin-top: 4px;
    opacity: 0.8;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    line-height: 24px;
    font-weight: 400;
}

.certificationsContainer {
    margin-top: 32px;
}

.certificationsMore {
    width: 100px;
    margin: 0 auto;
    margin-top: 20px;
    cursor: pointer;
    opacity: 0.7;
    font-family: PingFangSC-Regular;
    font-size: 16px;
    color: #222222;
    letter-spacing: 0;
    line-height: 16px;
    font-weight: 400;
}

.certificationsMore:hover {
    color: #2469F3;
}

.certificationsMore:hover .certificationsMoreIcon {
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220506/more-active.png');
}

.defaultIcon {
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
}

.certificationsMoreIcon {
    display: inline-block;
    margin-left: 8px;
    width: 12px;
    height: 12px;
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220506/more.png');
}

.consultBanner {
    height: 76px;
    margin-top: 32px;
    padding: 0 52px;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220506/background-consult.png');
}

.consultIntro {
    font-family: PingFangSC-Medium;
    font-size: 20px;
    color: #FFFFFF;
    line-height: 20px;
    font-weight: 500;
}

.consultBtn {
    width: 112px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    background: #FFFFFF;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #2469F3;
    letter-spacing: 0;
    font-weight: 400;
    cursor: pointer;
}

.hidden {
    display: none;
}

.showDetail {
    margin-top: 8px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.showDetailText {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #2469F3;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
}

.showDetailIcon {
    width: 16px;
    height: 14px;
    margin-left: 8px;
    transition: margin-left .3s ease;
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220506/detail-icon.png');
}

.cardClassName:hover .showDetailIcon {
    margin-left: 24px;
}

.cardClassName:hover .titleClassName {
    color: #2469F3;
}