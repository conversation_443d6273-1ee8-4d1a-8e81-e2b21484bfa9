.bodyCenter {
    width: 1180px;
    margin: 0 auto;
}

// 搜索框tab切换
.tabs {
    padding-top: 24px;
    display: flex;
    margin-bottom: 12px;
}

.tabItem {
    margin-right: 24px;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 24px;
    cursor: pointer;
}

.tabItem:last-child {
    margin-right: 0;
}

.activeTab {
    font-family: PingFangSC-Medium;
    color: #2469F3;
    font-weight: 500;
}

.defaultTab {
    opacity: 0.7;
    font-family: PingFangSC-Regular;
    color: #222222;
    font-weight: 400;
}

// 搜索框
.searchWrapper {
    display: flex;
}

.interval {
    height: 20px;
    border-left: 1px solid #CCCCCC;
}

.searchboxWrapper {
    width: 100%;
    height: 52px;
    background: #FFFFFF;
    display: flex;
    align-items: center;
}

.industrySelect {
    width: 100%;
}

.searchboxBtn {
    width: 168px;
    min-width: 168px;
    height: 52px;
    line-height: 52px;
    font-family: PingFangSC-Regular;
    font-size: 16px;
    letter-spacing: 0;
    font-weight: 400;
    text-align: center;
    cursor: pointer;
}

.searchBtn {
    color: #FFFFFF;
    background: #2469F3;
    border-radius: 0 6px 6px 0;
}

.consultBtn {
    margin-left: 24px;
    color: #528BFF;
    background: #FFFFFF;
    border-radius: 6px;
}

.hidden {
    display: none;
}

.navs {
    margin-top: 12px;
    display: flex;
    justify-content: space-between;
}

.noticeIcon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}

.activity:last-child .navInterval {
    display: none;
}

.navList {
    opacity: 0.7;
}

.navList a {
    opacity: 0.7;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    text-align: right;
    line-height: 24px;
    font-weight: 400;
}

.navList a:hover {
    opacity: 1;
    color: #2469F3;
}

.flexAlignCenter {
    display: flex;
    align-items: center;
}

.defaultText {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
}

.navInterval {
    height: 14px;
    border-left: 1px solid #222222;
    margin: 0 12px;
}