/**
 * @file BLA 资质代办官网首页
 * @description 搜索模块
 */

import cx from 'classnames';
import {useEffect, useState} from 'react';
import {Searchbox} from '@baidu/bce-portal-ui';
import {SearchTabs, SandboxLink, Link, concatParams} from '../config';
import styles from './search.module.less';

interface SearchboxProps {
    cities: AreaItem[];
    industries: GroupItem[];
    qualifications: GroupItem[];
    qualificationType?: string;
    keyword?: string;
    showActivity?: boolean;
    activities?: ActivityItem[];
    errorTipClassName?: string;
    onSearch?: (params: any) => void;
    onConsult?: () => void;
    onCustom?: () => void;
}

export interface GroupItem {
    groupName: string;
    groupItems: string[];
}

export interface AreaItem {
    groupName: string;
    groupItems: AreaRowItem[];
}

interface AreaRowItem {
    rowName: string;
    rowItems: string[];
}

export interface ActivityItem {
    link: string;
    label: TextItem[];
}

interface TextItem {
    text: string;
    color: string;
}

export const ProductsBlaSearch = (props: SearchboxProps) => {
    const {
        cities,
        industries,
        qualifications,
        qualificationType,
        keyword,
        showActivity,
        activities,
        errorTipClassName,
        onSearch: onChange,
        onConsult,
        onCustom,
    } = props;
    const [currentTab, setCurrentTab] = useState<string>('qualification');
    const [link, setLink] = useState(Link);

    useEffect(() => {
        // 判断是否为沙盒地址
        const isSandbox = () => {
            return /(cloudtest)|(baidu-int)|(qasandbox)|(bcetest)/.test(location.origin);
        };
        setLink(isSandbox() ? SandboxLink : Link);
    }, []);

    const onSearch = (res: any) => {
        switch (currentTab) {
            case 'smartname': {
                window.open(`${link.cbs}#/smartresult?${concatParams(res)}&organizationType=有限公司`);
                break;
            }
            case 'checkname': {
                window.open(`${link.cbs}#/search?${concatParams(res)}`);
                break;
            }
            default: {
                const query = {
                    keywordType: res.qualificationType,
                    keyword: res.keyword,
                };
                if (res.keyword && onChange) {
                    onChange(query);
                    break;
                }
                res.keyword && window.open(`${link.bla}/result.html?${concatParams(query)}`);
                break;
            }
        }
    };

    const onQualiIndustryChange = (res: any) => {
        !showActivity && onChange({
            keywordType: res.qualificationType,
            keyword: res.keyword,
        });
    };

    return (
        <>
            <div className={cx(styles.bodyCenter, styles.tabs)}>
                {SearchTabs.map(item => (
                    <div
                        key={item.key}
                        className={cx(styles.tabItem, currentTab === item.key ? styles.activeTab : styles.defaultTab)}
                        onClick={() => setCurrentTab(item.key)}
                    >
                        {item.label}
                    </div>
                ))}
            </div>
            <div className={styles.bodyCenter}>
                <div className={cx(styles.searchWrapper, currentTab === 'qualification' ? '' : styles.hidden)}>
                    <Searchbox
                        mode="qualification"
                        cityName="北京"
                        qualificationType={qualificationType}
                        industryType={qualificationType === '按行业查询' ? keyword : ''}
                        keyword={qualificationType === '按资质名查询' ? keyword : ''}
                        industryDatasource={qualifications}
                        onChange={onQualiIndustryChange}
                        onSearch={onSearch}
                    />
                    {showActivity && (
                        <div className={cx(styles.searchboxBtn, styles.consultBtn)} onClick={onConsult}>免费咨询</div>
                    )}
                </div>
                <div className={cx(styles.searchWrapper, currentTab === 'smartname' ? '' : styles.hidden)}>
                    <Searchbox
                        mode="smartname"
                        cityName="北京"
                        industryType="网络科技"
                        organizationType="有限公司"
                        areaDatasource={cities}
                        industryDatasource={industries}
                        onSearch={onSearch}
                    />
                    {showActivity && (
                        <div className={cx(styles.searchboxBtn, styles.consultBtn)} onClick={onCustom}>私人定制好名</div>
                    )}
                </div>
                <div className={currentTab === 'checkname' ? '' : styles.hidden}>
                    <Searchbox
                        mode="checkname"
                        cityName="北京"
                        industryType="网络科技"
                        organizationType="有限公司"
                        areaDatasource={cities}
                        industryDatasource={industries}
                        errorTipClassName={errorTipClassName}
                        onSearch={onSearch}
                    />
                </div>
            </div>
            {showActivity && (
                <div className={currentTab === 'qualification' ? '' : styles.hidden}>
                    <div className={cx(styles.bodyCenter, styles.navs)}>
                        <div className={cx(styles.flexAlignCenter, styles.defaultText)}>
                            <img
                                src="https://cbs-public.bj.bcebos.com/bla/20220424/notification-icon.png"
                                className={styles.noticeIcon}
                                alt="最新活动"
                            />
                            {activities.map(item => (
                                <span className={styles.activity} key={item.link}>
                                    <a
                                        href={item.link}
                                        key={item.link}
                                        target="_blank"
                                    >
                                        {item.label.map(({text, color}) => (
                                            <span key={text} style={{color}}>{text}</span>
                                        ))}
                                    </a>
                                    <span className={styles.navInterval}></span>
                                </span>
                            ))}
                        </div>
                        <div className={cx(styles.flexAlignCenter, styles.navList)}>
                            <a
                                className={styles.defaultText}
                                href="https://cloud.baidu.com/doc/CBS/s/Xkt2eukrr"
                                target="_blank"
                            >
                                产品文档
                            </a>
                            <div className={styles.navInterval}></div>
                            <a
                                className={styles.defaultText}
                                href={link.list}
                                target="_blank"
                            >
                                管理列表
                            </a>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};
