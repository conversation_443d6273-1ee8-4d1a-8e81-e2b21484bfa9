/**
 * @file BLA 资质代办官网首页
 * @description 咨询弹窗
 */

import cx from 'classnames';
import {useRef} from 'react';
import {Modal} from 'antd';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {contraryReptile} from '../config';
import {ProductsBlaForm} from '../Form/Form';
import styles from './consult.module.less';

export type ConsultType = 'specificConsult' | 'overallConsult';

interface ProductsBlaConsultModalProps {
    open?: boolean;
    type: ConsultType;
    extraData?: ConsultExtraData;
    onSuccess?: (value: string) => void;
    onCancel?: (value: boolean) => void;
}

interface ProductsBlaCustomModalProps {
    open?: boolean;
    onSuccess?: (value: string) => void;
    onCancel?: (value: boolean) => void;
}

export interface ConsultExtraData {
    name: string;
    bizType: string;
    type?: string;
    area?: string;
}

export const ProductsBlaConsultModal = (props: ProductsBlaConsultModalProps) => {
    const {open, type, extraData, onSuccess, onCancel} = props;
    const childRef = useRef();

    const onClose = () => {
        (childRef.current as any).resetForm();
        onCancel && onCancel(false);
    };

    const onSubmit = (values: any) => {
        const isBLA = values.bizType === 'BLA' || extraData?.bizType === 'BLA';
        const url = isBLA ? urlConst.GET_BLA_SUBMIT_DEMAND : urlConst.GET_CBS_SUBMIT_DEMAND;
        const params = isBLA ? {
            type: extraData?.type || 'ICP',
            area: extraData?.area || 'BEIJING',
            content: values?.description,
            contactNumber: values.phone,
            verificationCode: values.smsCode,
            requestSource: 'bla_portal',
        } : {
            area: (['COMREG', 'BKP'].includes(values.bizType)
                || ['COMREG', 'BKP'].includes(extraData?.bizType)) ? '110100' : 'global',
            bizType: extraData?.bizType,
            ...values,
        };
        return netService.post(
            url,
            params,
            {},
            {headers: {timestamp: contraryReptile('/open/portal/recommend')}}
        ).then(() => {
            onCancel && onCancel(false);
            onSuccess && onSuccess('提交成功！服务专家会在工作时间尽快与您取得联系！');
        });
    };

    return (
        <Modal
            wrapClassName={styles.consultWrapClassName}
            visible={open}
            closable={false}
            footer={null}
            width="552px"
            centered
            onCancel={onClose}
        >
            <div className={styles.consultTitle}>免费咨询</div>
            <div className={cx(styles.iconBg, styles.closeIcon)} onClick={onClose}></div>
            <ProductsBlaForm
                ref={childRef}
                type={type}
                serviceName={extraData?.name}
                bizType={extraData?.bizType}
                onSubmit={onSubmit}
            />
        </Modal>
    );
};

export const ProductsBlaCustomModal = (props: ProductsBlaCustomModalProps) => {
    const {open, onSuccess, onCancel} = props;
    const childRef = useRef();

    const onClose = () => {
        (childRef.current as any).resetForm();
        onCancel && onCancel(false);
    };

    const onSubmit = (values: any) => {
        return netService.post(
            urlConst.GET_CBS_SUBMIT_DEMAND,
            {
                ...values,
                bizType: 'CLOUD_PARK_COMREG',
                area: 'CP0000',
                description: '起名',
            },
            {},
            {headers: {timestamp: contraryReptile('/open/portal/recommend')}}
        ).then(() => {
            onCancel && onCancel(false);
            onSuccess && onSuccess('恭喜您！您已预约成功！');
        });
    };

    return (
        <Modal
            wrapClassName={styles.consultWrapClassName}
            visible={open}
            closable={false}
            footer={null}
            width="440px"
            centered
            onCancel={onClose}
        >
            <div className={styles.customWrapper}>
                <div className={styles.consultTitle}>私人定制公司好名</div>
                <div className={styles.consultDesc}>您也可以选择起名专家为您定制专属企业名</div>
                <div className={cx(styles.iconBg, styles.closeIcon, styles.customCloseIcon)} onClick={onClose}></div>
            </div>
            <ProductsBlaForm
                ref={childRef}
                type="smartname"
                onSubmit={onSubmit}
            />
        </Modal>
    );
};
