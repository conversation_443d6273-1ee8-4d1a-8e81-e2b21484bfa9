/**
 * @file BLA 资质代办官网首页
 * @description 其他产品推荐
 */

import cx from 'classnames';
import styles from './interests.module.less';

interface ProductsBlaInterestsProps {
    interests?: InterestItem[];
}

export interface InterestItem {
    title: string;
    desc: string;
    imgUrl: string;
    link: string;
}

export const ProductsBlaInterests = (props: ProductsBlaInterestsProps) => {
    const {interests} = props;

    return (
        <div className={styles.interestsWrapper}>
            <header className={cx(styles.bodyCenter, styles.header)}>
                <div className={styles.headerTitle}>您可能还对以下产品感兴趣</div>
            </header>
            <div id="bla_interested_product"></div>
            <div className={cx(styles.bodyCenter, styles.interestsContainer)}>
                {interests.map(item => (
                    <a
                        key={item.title}
                        className={cx(styles.interestImg, styles.interestItem)}
                        style={{backgroundImage: `url(${item.imgUrl})`}}
                        href={item.link}
                        target="_blank"
                    >
                        <div className={styles.interestItemTitle}>{item.title}</div>
                        <div className={styles.interestItemDesc}>{item.desc}</div>
                    </a>
                ))}
            </div>
        </div>
    );
};
