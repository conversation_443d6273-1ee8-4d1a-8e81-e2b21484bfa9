/**
 * @file BLA 资质代办官网结果页
 * @description 提交需求模块
 */

import cx from 'classnames';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {ProductsBlaForm} from '../Form/Form';
import {DemandFastLabels, contraryReptile} from '../config';
import styles from './demandfast.module.less';

interface ProductsBlaDemandFastProps {
    onSuccess?: (value: string) => void;
}

export const ProductsBlaDemandFast = (props: ProductsBlaDemandFastProps) => {
    const {onSuccess} = props;
    const onSubmit = (values: any) => {
        const isBLA = values.bizType === 'BLA';
        const url = isBLA ? urlConst.GET_BLA_SUBMIT_DEMAND : urlConst.GET_CBS_SUBMIT_DEMAND;
        const params = isBLA ? {
            type: 'ICP',
            area: 'BEIJING',
            contactNumber: values.phone,
            verificationCode: values.smsCode,
            requestSource: 'bla_portal',
        } : {
            ...values,
            area: '110100',
        };
        return netService.post(
            url,
            params,
            {},
            {headers: {timestamp: contraryReptile('/open/portal/recommend')}}
        ).then(() => {
            onSuccess && onSuccess('恭喜您！成功获取方案和价格！');
        });
    };

    return (
        <div className={styles.demandfastWrapper}>
            <div className={styles.intro}>
                <div className={styles.title}>没找到自己想要的资质？</div>
                <div className={styles.title} id="submitDemand">10秒快速提交需求</div>
                <div className={styles.flexAlignCenter}>
                    <div className={cx(styles.bgImg, styles.demandfastImg)}></div>
                    <div className={styles.labels}>
                        {DemandFastLabels.map(item => (
                            <div className={cx(styles.flexAlignCenter, styles.labelItem)} key={item}>
                                <div className={cx(styles.bgImg, styles.labelIcon)}></div>
                                <div className={styles.labelText}>{item}</div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            <div className={styles.formWrapper}>
                <div className={styles.demandTip}>提交需求后，我们会安排专业顾问10分钟内联系您，请保持电话畅通。</div>
                <ProductsBlaForm
                    type="demand"
                    onSubmit={onSubmit}
                />
            </div>
        </div>
    );
};
