/**
 * @file BLA 面包屑导航
 */

import {useEffect, useState} from 'react';
import {Breadcrumb} from 'antd';
import {CbsBreadcrumbLinks, Link, SandboxLink} from '../config';
import styles from './cbsBreadcrumb.module.less';

interface CbsBreadcrumbProps {
    currentLabel: string;
}

interface LinkItem {
    text: string;
    link?: string;
}

export const CbsBreadcrumb = (props: CbsBreadcrumbProps) => {
    const {currentLabel} = props;
    const [link, setLink] = useState(Link);
    const [items, setItems] = useState<LinkItem[]>([]);

    useEffect(() => {
        // 判断是否为沙盒地址
        const isSandbox = () => {
            return /(cloudtest)|(baidu-int)|(qasandbox)|(bcetest)/.test(location.origin);
        };
        setLink(isSandbox() ? SandboxLink : Link);
    }, []);

    useEffect(() => {
        setItems(CbsBreadcrumbLinks(link));
    }, [link]);

    return (
        <div className={styles.cbsBreadcrumb}>
            <Breadcrumb separator=">">
                {items.map((item: LinkItem) => (
                    <Breadcrumb.Item key={item.link}>
                        <a
                            href={item.link}
                            target="_blank"
                            className={styles.linkItem}
                        >
                            {item.text}
                        </a>
                    </Breadcrumb.Item>
                ))}
                <Breadcrumb.Item>
                    <span className={styles.defaultItem}>{currentLabel}</span>
                </Breadcrumb.Item>
            </Breadcrumb>
        </div>
    );
};
