/**
 * @file BLA 资质代办官网首页
 * @description 爆款资质推荐模块
 */

import cx from 'classnames';
import {Pricecards} from '@baidu/bce-portal-ui';
import styles from './recommend.module.less';

interface ProductsBlaRecommendProps {
    datasource?: PricecardItem[];
    onConsult?: (item: any) => void;
    onBuy?: (item: any) => void;
}

export interface PricecardItem {
    title: string;
    desc?: string;
    fixedIcon?: string;
    labels?: LabelItem[];
    tags?: string[];
    price?: string | number;
    originPrice?: string | number;
    unit?: string;
}

interface LabelItem {
    icon?: string;
    item: CustomText[];
}

interface CustomText {
    text: string;
    color?: string;
}

export const ProductsBlaRecommend = (props: ProductsBlaRecommendProps) => {
    const {datasource, onConsult, onBuy} = props;

    return (
        <div className={styles.recommendWrapper}>
            <header className={styles.header}>
                <div className={styles.headerTitle}>爆款资质推荐</div>
                <div className={cx(styles.defaultText, styles.headerDesc)}>官方精选爆款资质，专人一对一急速办理，支持绿色通道</div>
            </header>
            <div className={cx(styles.hotContainer)}>
                <Pricecards
                    headerClassName={styles.headerBg}
                    labelClassName={styles.labelClassName}
                    datasource={datasource}
                    onConsult={onConsult}
                    onBuy={onBuy}
                />
            </div>
        </div>
    );
};
