/**
 * @file BLA 资质代办官网首页
 * @description 产品优势模块
 */

import cx from 'classnames';
import {Advantages} from '../config';
import styles from './advantage.module.less';

export const ProductsBlaAdvantage = () => {
    return (
        <div className={cx(styles.bodyCenter, styles.advantageWrapper)}>
            <header className={styles.header}>
                <div className={styles.headerTitle}>服务优势</div>
                <div className={cx(styles.defaultText, styles.headerDesc)}>全行业资质覆盖，更优质的资质申请体验</div>
            </header>
            <div id="bla_advantage_service"></div>
            <div className={cx(styles.flexBetwween, styles.advantageContainer)}>
                <div className={cx(styles.flexBetwween, styles.flexColumn)}>
                    {Advantages.left.map(item => (
                        <div
                            key={item.id}
                            className={cx(styles.flexAlignCenter, styles.advantageItem)}
                        >
                            <div className={cx(styles.advantageItemContainer, styles.advantageLeft)}>
                                <div className={styles.flexAlignCenter}>
                                    <img
                                        src={item.icon}
                                        className={styles.advantageItemIcon}
                                        alt={item.title}
                                    />
                                    <div className={styles.advantageItemTitle}>{item.title}</div>
                                </div>
                                <div className={styles.advantageItemDesc}>{item.desc}</div>
                            </div>
                            <div className={styles.advantageItemIdLeft}>{item.id}</div>
                        </div>
                    ))}
                </div>
                <img
                    src="https://bce.bdstatic.com/p3m/common-service/uploads/gif_chanpinyoushi_d4d3694.gif"
                    className={styles.advantageVideo}
                    alt="服务优势"
                />
                <div className={cx(styles.flexBetwween, styles.flexColumn)}>
                    {Advantages.right.map(item => (
                        <div
                            key={item.id}
                            className={cx(styles.flexAlignCenter, styles.advantageItem)}
                        >
                            <div className={cx(styles.advantageItemContainer, styles.advantageRight)}>
                                <div className={styles.flexAlignCenter}>
                                    <img
                                        src={item.icon}
                                        className={styles.advantageItemIcon}
                                        alt={item.title}
                                    />
                                    <div className={styles.advantageItemTitle}>{item.title}</div>
                                </div>
                                <div className={styles.advantageItemDesc}>{item.desc}</div>
                            </div>
                            <div className={styles.advantageItemIdRight}>{item.id}</div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};
