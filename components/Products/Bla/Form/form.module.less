.form {
    margin-top: 20px;
    :global {
        .ant-form-item {
            margin-bottom: 16px;
        }
        .ant-form-item-explain, .ant-form-item-extra {
            height: 0;
            min-height: 0;
            font-size: 12px;
            line-height: 16px;
        }
        .ant-form-item-label > label,
        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
            height: 36px;
        }
        .ant-select-single.ant-select-show-arrow .ant-select-selection-item,
        .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
            line-height: 34px;
        }
        .ant-input {
            height: 36px;
            border-radius: 4px;
        }
        .ant-input:focus {
            border-color: #2469F3;
            box-shadow: none;
        }
        .ant-input:hover {
            border-color: #2469F3;
        }
        .ant-select-selector {
            border-radius: 4px;
        }
        .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
        .ant-select-selector {
            border-color: #2469F3;
            box-shadow: none;
        }
        .ant-select:not(.ant-select-customize-input) .ant-select-selector:hover {
            border-color: #2469F3;
        }
        .ant-input-textarea-show-count::after {
            position: absolute;
            right: 10px;
            bottom: 28px;
        }
    }
}

.select {
    :global {
        .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
            color: #2469F3;
            font-weight: 500;
            background-color: #FFFFFF;
        }
        .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
            background: #F0F5FF;
        }
    }
}

.checkbox {
    :global {
        .ant-checkbox-checked .ant-checkbox-inner {
            background-color: #2469F3;
            border-color: #2469F3;
        }
    }
}

.submitBtn {
    border: none;
    outline: none;
    height: 0;
    width: 0;
    padding: 0;
}
.submitBtn span {
    margin: 0 auto;
    height: 34px;
    background: #2469F3;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #FFFFFF;
    text-align: center;
    line-height: 34px;
    font-weight: 400;
    cursor: pointer;
}

.demandBtn span {
    width: 174px;
}

.disabledBtn span {
    opacity: .7;
}

.consultBtn span {
    width: 174px;
    margin-top: 24px;
    transform: translateX(90%);
}

.customBtn span {
    width: 360px;
    height: 40px;
    line-height: 40px;
    margin-top: 24px;
}

.phoneWrapper {
    position: relative;
}

.phoneWrapper:hover {
    border-color: #2469F3;
}

.smsBtn {
    position: absolute;
    z-index: 5;
    right: 16px;
    top: 6px;
    width: 90px;
    border-left: 1px solid #EDEDED;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #2469F3;
    text-align: right;
    font-weight: 400;
    cursor: pointer;
}

.disabledBtn {
    opacity: .7;
    cursor: default;
}

.serviceName {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    line-height: 36px;
    font-weight: 400;
}

.send {
    width: 108px;
    height: 18px;
    background: #FF5657;
    border-radius: 2px;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    text-align: center;
    color: #FFFFFF;
    letter-spacing: 0;
    line-height: 18px;
    font-weight: 400;
}
.price {
    margin-top: 12px;
    display: flex;
    align-items: flex-end;
}

.free {
    font-family: PingFangSC-Medium;
    font-size: 24px;
    color: #FF5657;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 500;
    margin-right: 8px;
}

.originPrice {
    opacity: 0.4;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    line-height: 14px;
    font-weight: 400;
    text-decoration: line-through;
}

