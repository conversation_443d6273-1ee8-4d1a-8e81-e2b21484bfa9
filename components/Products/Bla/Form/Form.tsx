/**
 * @file BLA 资质代办官网首页
 * @description 表单模块
 */

import cx from 'classnames';
import React, {useEffect, useImperativeHandle, useState} from 'react';
import {Form, Input, Select, Button, Checkbox} from 'antd';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {Rules, contraryReptile, getTrackValueFromUrl} from '../config';
import styles from './form.module.less';

const {Option} = Select;

type FormType = 'demand' | 'specificConsult' | 'overallConsult' | 'smartname';

interface ProductsBlaFormProps {
    type: FormType;
    serviceName?: string;
    bizType?: string;
    onSubmit?: (values: any) => Promise<any>;
}

// eslint-disable-next-line complexity
export const ProductsBlaForm = React.forwardRef((props: ProductsBlaFormProps, ref) => {
    const {type, serviceName, bizType, onSubmit} = props;
    const [form] = Form.useForm();
    const [countdown, setCountdown] = useState<number>(60);
    const [checked, setChecked] = useState<boolean>(false);

    useImperativeHandle(ref, () => ({
        resetForm: () => {
            form.resetFields();
            setChecked(false);
            setCountdown(60);
        },
    }));

    const onFinish = async (values: any) => {
        const trackValue = getTrackValueFromUrl();
        const params = {
            ...values,
            source: trackValue,
        };
        if (serviceName) {
            params.description = `${serviceName}-${params.description}`;
        }
        onSubmit && await onSubmit(params);
        form.resetFields();
        setChecked(false);
        setCountdown(60);
    };

    const onChecked = () => {
        setChecked(!checked);
    };

    const sendVerifyCode = () => {
        const isBLA = (
            ['demand', 'overallConsult'].includes(type) && form.getFieldValue('bizType') === 'BLA'
        ) || bizType === 'BLA';
        const url = isBLA ? urlConst.GET_BLA_SEND_SMSCODE : urlConst.GET_CBS_SEND_SMSCODE;
        const params = isBLA ? {
            contactNumber: form.getFieldValue('phone'),
        } : {
            phone: form.getFieldValue('phone'),
        };
        netService.post(
            url,
            params,
            {},
            {headers: {timestamp: contraryReptile('/open/portal/recommend')}}
        );
    };

    const getSmsCode = async () => {
        await form.validateFields(['phone']);
        if (countdown < 60) {
            return;
        }
        setCountdown(59);
        sendVerifyCode();
    };

    useEffect(() => {
        const timer = setTimeout(() => {
            if (countdown < 60) {
                setCountdown(countdown > 0 ? countdown - 1 : 60);
            }
        }, 1000);
        return () => clearTimeout(timer);
    }, [countdown]);

    return (
        <div className={styles.demandfastWrapper} id="submitDemand">
            <Form
                form={form}
                name="basic"
                autoComplete="off"
                labelAlign="left"
                labelCol={{span: 4}}
                wrapperCol={{span: 20}}
                className={styles.form}
                onFinish={onFinish}
            >
                {type === 'specificConsult' && (
                    <Form.Item
                        label="咨询服务"
                        name="serviceName"
                    >
                        <div className={styles.serviceName}>{serviceName}</div>
                    </Form.Item>
                )}
                {['demand', 'overallConsult'].includes(type) && (
                    <Form.Item
                        label="我想咨询"
                        name="bizType"
                        initialValue="BLA"
                        rules={Rules.bizType}
                    >
                        <Select
                            placeholder="请选择您要咨询的服务"
                            dropdownClassName={styles.select}
                        >
                            <Option value="BLA">资质代办</Option>
                            <Option value="COMREG">工商注册</Option>
                            <Option value="BKP">代理记账</Option>
                        </Select>
                    </Form.Item>
                )}
                {type === 'smartname' && (
                    <Form.Item
                        name="name"
                        wrapperCol={type === 'smartname' && {offset: 0, span: 24}}
                        rules={Rules.name}
                    >
                        <Input placeholder="请输入您的称呼" />
                    </Form.Item>
                )}
                <Form.Item
                    label={type === 'smartname' ? '' : '手机号码'}
                    name="phone"
                    wrapperCol={type === 'smartname' && {offset: 0, span: 24}}
                    rules={Rules.phone}
                >
                    <div className={styles.phoneWrapper}>
                        <Input
                            maxLength={11}
                            placeholder="密码保护中，请放心填写"
                        />
                        <div
                            className={cx(styles.smsBtn, countdown < 60 ? styles.disabledBtn : '')}
                            onClick={() => {
                                getSmsCode();
                            }}
                        >
                            {countdown < 60 ? `重新获取${countdown}s` : '获取验证码'}
                        </div>
                    </div>
                </Form.Item>
                <Form.Item
                    label={type === 'smartname' ? '' : '验证码'}
                    name="smsCode"
                    wrapperCol={type === 'smartname' && {offset: 0, span: 24}}
                    rules={Rules.smsCode}
                >
                    <Input
                        maxLength={6}
                        placeholder="请输入6位数验证码"
                    />
                </Form.Item>
                {['specificConsult', 'overallConsult'].includes(type) && (
                    <Form.Item
                        label="需求备注"
                        name="description"
                    >
                        <Input.TextArea
                            maxLength={1024}
                            showCount
                            autoSize={{minRows: 4, maxRows: 4}}
                            placeholder="请输入备注信息，1024字以内（选填）"
                        />
                    </Form.Item>
                )}
                {/* 提交按钮 */}
                {type === 'demand' && (
                    <Form.Item wrapperCol={{offset: 8, span: 16}}>
                        <Button
                            className={cx(styles.submitBtn, styles.demandBtn)}
                            htmlType="submit"
                        >
                            免费获取方案和价格
                        </Button>
                    </Form.Item>
                )}
                {['specificConsult', 'overallConsult'].includes(type) && (
                    <Form.Item wrapperCol={{offset: 0, span: 24}}>
                        <div className={styles.checkbox}>
                            <Checkbox checked={checked} onChange={onChecked}>
                                同意授权百度智能云工商财税服务商通过该电话联络并提供服务
                            </Checkbox>
                        </div>
                        <Button
                            className={cx(styles.submitBtn, styles.consultBtn,
                                checked ? '' : styles.disabledBtn
                            )}
                            disabled={!checked}
                            htmlType="submit"
                        >
                            提交咨询
                        </Button>
                    </Form.Item>
                )}
                {type === 'smartname' && (
                    <Form.Item wrapperCol={{offset: 0, span: 24}}>
                        <div className={styles.send}>赠送人工核名服务</div>
                        <div className={styles.price}>
                            <div className={styles.free}>限时免费</div>
                            <div className={styles.originPrice}>300元/次</div>
                        </div>
                        <Button
                            className={cx(styles.submitBtn, styles.customBtn)}
                            htmlType="submit"
                        >
                            即刻预约
                        </Button>
                    </Form.Item>
                )}
            </Form>
        </div>
    );
});
