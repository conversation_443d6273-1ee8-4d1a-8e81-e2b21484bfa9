import React, {useEffect, useState, useRef} from 'react';
import Cookies from 'js-cookie';
import {Popover} from 'antd';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import moment from 'moment';
import md5 from 'js-md5';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {hostMap} from '../../util';
import styles from '../recommend.module.less';

interface TranRecommendItem {
    domain: string;
    price: number;
    [index: string]: any;
}
interface IframeParams {
    domains: string[];
}
const DomainTypeMap: any = {
    FIXED_PRICE: '一口价域名',
    AGENT_DEMAND: '委托购买',
    PREMIUM: '白金域名',
};

export const TranRecommend: React.FC<{tranRecommend: TranRecommendItem[]}> = props => {
    const {tranRecommend} = props;
    const [tranRecommendList, setTranRecommendList] = useState(tranRecommend);
    const deepCacheTranList = useRef([]);
    const [, , verifyHandler] = useCheckActivateUserInfo({});
    const dispose = (id: string) => {
        const iframe = document.getElementById(id);
        if (iframe) {
            iframe.parentNode.removeChild(iframe);
        }
    };
    const setData = (key: string, value: IframeParams) => {
        return new Promise((resolve, reject) => {
            const FRAME_ID = '__PORTAL_STORAGE_KEY__';
            try {
                let domain = 'baidu.com';
                if (document.domain === 'localhost') {
                    domain = 'localhost';
                }
                document.domain = domain;
            }
            catch (ex) {reject(ex);}
            let iframe: any = document.getElementById(FRAME_ID);
            if (!iframe) {
                iframe = document.createElement('iframe');
                iframe.id = '__PORTAL_STORAGE_KEY__';
                iframe.style.cssText = 'position:absolute;z-index:10000;left:-9999px';
            }
            let origin = `https://${location.host}`;
            if (location.hostname === 'localhost') {
                origin = `http://${location.host}`;
            }
            iframe.src = `${origin}/helper/relay.html?_=${new Date().getTime()}`;
            iframe.onload = () => {
                iframe.contentWindow.set(key, value);
                resolve({key, value});
                dispose(FRAME_ID);
            };
            document.body.appendChild(iframe);
            setTimeout(() => {
                dispose(FRAME_ID);
            }, 5000);
        });
    };

    // 统一displayTag
    const calcDisplayTag: (displayTag: string) => string = displayTag => {
        if (['PREMIUM', '白金域名'].includes(displayTag)) {
            return 'PREMIUM';
        } else if (['FIXED_PRICE', '一口价域名'].includes(displayTag)) {
            return 'FIXED_PRICE';
        } else {
            return 'AGENT_DEMAND';
        }
    };
    // 跳转
    const calcJump: (domainInfo: any) => void = domainInfo => {
        const {tradeType, domainName, price} = domainInfo;
        if (!price) {
            return;
        }

        verifyHandler().then(code => {
            if (code === 0) {
                switch (calcDisplayTag(tradeType)) {
                    case 'AGENT_DEMAND':
                        window.open(`${hostMap.getHost('console')}/bcd/?track=BCD_PORTAL&clientFrom=CLOUD#/bcd/entrust/create~domain=${domainName}`);
                        break;
                    case 'FIXED_PRICE':
                        window.open(`${hostMap.getHost('cloud')}/product/bcd/search.html#/fixed/detail?domain=${domainName}`);
                        break;
                    case 'PREMIUM':
                        setData(`search_purchase_data_${Cookies.get('accountId') || Cookies.get('bce-session')}`, {domains: [domainInfo]})
                            .then(() => {
                                window.open(`${hostMap.getHost('console')}/bcd/
                                    ?track=BCD_PORTAL&clientFrom=CLOUD#/bcd/domain/register~campaignId=search_purchase_data`);
                            });
                        break;
                }
            } else if (code === 201) {
                calcJump(domainInfo); // 已激活，再次判断是否需要实名
            }
        }).catch(err => console.log(err));
    };
    const contraryReptile = (url: string) => {
        // 通过反爬验证timestamp
        const time = moment(new Date()).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        return time + '@' + md5('POST' + url + time + 'ieq%$jsaf23!@fkjwie');
    };
    // 拉取详情 更新列表
    const upDateTranList = (e: React.MouseEvent<HTMLSpanElement, MouseEvent>, domainName: string, index: number) => {
        e.stopPropagation();
        const tmpList = deepCacheTranList.current;
        tmpList[index].data.isOverTime = false;
        deepCacheTranList.current = tmpList;
        setTranRecommendList(JSON.parse(JSON.stringify(deepCacheTranList.current)));

        const domainSplitArr = domainName?.split('.');
        const tld = domainSplitArr.pop();
        const domain = domainSplitArr.join('.');
        netService.post(
            urlConst.GET_BCD_PORTAL_DETAIL,
            {label: domain, tld: tld},
            {},
            {headers: {timestamp: contraryReptile('/open/domain/detail')}}
        ).then((detail: any) => {
            const tmpList = deepCacheTranList.current;

            if (detail.price) {
                detail.isOvertime = false;
                detail.displayTag = calcDisplayTag(detail.tradeType);
                detail.isSuccess = true;
            } else {
                detail.isOvertime = true;
            }

            tmpList[index].data = detail;
            deepCacheTranList.current = tmpList;
            setTranRecommendList(JSON.parse(JSON.stringify(deepCacheTranList.current)));
        });
    };
    useEffect(() => {
        tranRecommendList.forEach(item => {
            if (item?.data?.price) {
                item.data.isSuccess = true;
                item.data.isOvertime = false;
            } else {
                item.data.isSuccess = false;
                item.data.isOvertime = true;
            }
        });
        deepCacheTranList.current = tranRecommendList;
        setTranRecommendList(JSON.parse(JSON.stringify(deepCacheTranList.current)));
    }, []); // eslint-disable-line


    return (
        <div className={styles['right-module-item']}>
            <div className={styles['right-item-title']}>域名交易推荐</div>
            {
                tranRecommendList.slice(0, 5).map((item: TranRecommendItem, i: number) => {
                    const {domainName, tradeType, price, isSuccess, isOvertime} = item?.data;

                    return (
                        <div
                            className={styles['right-item-desc']}
                            key={domainName}
                            onClick={() => calcJump(item.data)}
                        >
                            <a
                                className={`${styles['desc-left']} ${styles['desc-left-fixed']}`}
                            >
                                <span className={styles[calcDisplayTag(tradeType)]}>
                                    {DomainTypeMap[calcDisplayTag(tradeType)]}
                                </span>
                                <span className={styles.domain}>
                                    <Popover content={domainName} trigger="hover">
                                        {domainName}
                                    </Popover>
                                </span>
                            </a>
                            {
                                isSuccess ? (
                                    <span className={styles['desc-price']}>
                                        {calcDisplayTag(tradeType) === 'AGENT_DEMAND' ? '限时免费咨询' : `￥${price || '--'}`}
                                    </span>
                                ) : (
                                    <span
                                        className={styles['overtime-btn']}
                                        onClick={e => upDateTranList(e, domainName, i)}
                                    >
                                        {isOvertime ? '超时重试' : '加载中..'}
                                    </span>
                                )
                            }
                        </div>
                    );
                })
            }
        </div>
    );
};
