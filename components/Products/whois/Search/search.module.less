::-webkit-input-placeholder {
    opacity: 0.4;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222;
}

.search {
    width: 980px;
    margin: 0 auto;
    .search-box {
        width: 980px;
        height: 52px;
        position: relative;
        background: #FFF;
        border-radius: 6px;
        overflow: hidden;
        display: flex;
        align-items: center;
        margin-top: 40px;

        input {
            width: 800px;
            height: 52px;
            box-sizing: border-box;
            border: none;
            outline: none;
            padding-left: 20px;
            font-size: 14px;
            line-height: 24px;
            color: #191A24;
        }

        .btn {
            width: 180px;
            height: 52px;
            background: #2468F2;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: PingFangSC-Regular;
            font-size: 16px;
            color: #FFF;
            letter-spacing: 0;
            text-align: center;
            font-weight: 400;
            cursor: pointer;
        }

        .btn:hover {
            background: #528EFF;
        }
    }

    .error-tip {
        color: #F6654D;
    }

    .is-whois-detail {
        width: 806px;
        margin: auto 0;
        box-sizing: border-box;
        border: 1px solid rgba(34,34,34,0.16);

        input {
            width: 626px;
        }
    }
}
