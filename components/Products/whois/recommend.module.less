.right-module-item {
    box-sizing: border-box;
    padding: 24px 24px 10px;

    .right-item-title {
        font-family: PingFangSC-Semibold;
        font-size: 18px;
        color: #222;
        letter-spacing: 0;
        line-height: 28px;
        font-weight: 600;
        margin-bottom: 14px;
    }

    .right-item-desc {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #222;
        line-height: 24px;
        font-weight: 400;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .overtime-btn {
            width: 84px;
            height: 18px;
            border: 1px solid #CCC;
            border-radius: 8px;
            display: flex;
            font-size: 12px;
            justify-content: center;
            align-items: center;
            color: #888;
            cursor: pointer;
        }

        a {
            color: #222;

            .FIXED_PRICE {
                color: #F33E3E;
                line-height: 18px;
                background: rgba(233, 118, 118, 0.14);
                margin-right: 8px;
                font-family: PingFangSC-Medium;
                font-size: 11px;
                padding: 0 4px;
                font-weight: 500;
                border-radius: 2px;
            }

            .PREMIUM, .AGENT_DEMAND {
                color: #2468F2;
                line-height: 18px;
                background: rgba(129, 173, 240, 0.14);
                margin-right: 8px;
                font-family: PingFangSC-Medium;
                font-size: 11px;
                padding: 0 4px;
                font-weight: 500;
                border-radius: 2px;
            }
        }

        .desc-left {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
            position: relative;

            span:nth-child(1) {
                flex-shrink: 0;
            }

            .visited-all {
                position: absolute;
                left: 0;
                top: 20px;
                height: 18px;
                font-size: 14px;
                color: #222;
                opacity: .7;
                display: none;
                line-height: 18px;
                transition: 2s all ease-in-out;
            }

            .domain {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .desc-left:hover .visited-all {
            display: block;
        }

        .desc-left-fixed {
            width: 60%;
        }

        .desc-left:hover {
            color: #2468F2;
        }

        .desc-price {
            width: 40%;
            color: #F6654D;
            text-align: right;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
        }

        .desc-description {
            opacity: 0.4;
            text-align: right;
        }
    }
}