import React from 'react';
import styles from '../recommend.module.less';

interface TldRecommendItem {
    suffix: string;
    description: number;
    [index: string]: any;
}
export const TldRecommend: React.FC<{tldRecommend: TldRecommendItem[]}> = props => {
    const {tldRecommend} = props;

    return (
        <div className={styles['right-module-item']}>
            <div className={styles['right-item-title']}>域名后缀推荐</div>
            {
                tldRecommend.slice(0, 5).map(item => (
                    <div
                        className={styles['right-item-desc']}
                        key={item.suffix}
                    >
                        <a
                            className={styles['desc-left']}
                            title={item.suffix}
                            href={`https://cloud.baidu.com/product/bcd/search.html?root=${item.suffix}`}
                        >
                            {item.suffix}
                        </a>
                        <span className={styles['desc-description']}>{item.description}</span>
                    </div>
                ))
            }
        </div>
    );
};
