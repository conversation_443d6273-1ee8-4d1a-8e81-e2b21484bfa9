import React from 'react';
import styles from '../recommend.module.less';

interface DomainRecommendItem {
    domain: string;
    price: number;
    [index: string]: any;
}
export const UnregisterDomain: React.FC<{domainRecommend: DomainRecommendItem[]}> = props => {
    const {domainRecommend} = props;

    return (
        <div className={styles['right-module-item']}>
            <div className={styles['right-item-title']}>未注册域名推荐</div>
            {
                domainRecommend.slice(0, 5).map(item => (
                    <div
                        className={styles['right-item-desc']}
                        key={item.domain}
                    >
                        <a
                            className={styles['desc-left']}
                            title={item.domain}
                            href={`https://cloud.baidu.com/product/bcd/search.html?keyword=${item.domain}`}
                        >
                            {item.domain}
                        </a>
                        <span className={styles['desc-price']}>￥ {item.price}</span>
                    </div>
                ))
            }
        </div>
    );
};
