import React, {useEffect, useState} from 'react';
import styles from './simpleverify.module.less';

interface SimpleVerifyConfig {
    width?: number;
    height?: number;
    borderColor?: string;
    bgColor?: string;
    borderRadius?: number;
    tips?: string;
    barBackground?: string;
    movedColor?: string;
    successTips?: string;
    successIcon?: string;
    success?: () => void;
}

const defaultConfig: SimpleVerifyConfig = {
    width: 368,
    height: 46,
    borderColor: '#EEE',
    bgColor: '#EEE',
    borderRadius: 0,
    tips: '请按住滑块，拖动到最右边',
    movedColor: '#0BC280',
    successTips: '验证通过',
};
const BCD_WHOIS_URL = 'https://bcd-public.cdn.bcebos.com/whois';
export const SimpleVerify: React.FC<SimpleVerifyConfig> = props => {

    const config: SimpleVerifyConfig = {
        ...defaultConfig,
        ...props,
    };
    const initData = {
        max: config.width - 58,
        style: {
            width: config.width,
            height: config.height,
            border: `${config.borderColor} 1px solid`,
            backgroundColor: config.bgColor,
            borderRadius: config.borderRadius,
        },
        slideBoxStyle: {
            borderRadius: config.borderRadius,
        },
        iconStyle: {
            background: `url(${config.successIcon}) no-repeat`,
        },
    };
    // 是否开始滑动
    const [isMouseEnter, setIsMouseEnter] = useState<boolean>(false);
    // 有效滑动距离
    const [diff, setDiff] = useState<number>(0);
    // initData
    const [start, setStart] = useState<number>(0);
    const [curr, setCurr] = useState<number>(0);
    const [isSuccess, setIsSuccess] = useState<boolean>(false);
    const [isMousedown, setIsMousedown] = useState<boolean>(false);

    // 鼠标移入
    const mouseenter = () => {
        if (isSuccess) {
            return;
        }
        setIsMouseEnter(true);
    };
    // 鼠标离开
    const mouseleave = () => {
        if (isSuccess || isMousedown) {
            return;
        }
        setIsMouseEnter(false);
    };
    // 鼠标按下
    const mousedown = (e: any) => {
        if (isSuccess) {
            return;
        }
        setDiff(0);
        setIsMousedown(true);
        setStart(e.nativeEvent.x);
    };
    // 鼠标移动
    const mousemove = (e: any) => {
        if (!isMousedown || isSuccess) {
            return;
        }
        e.preventDefault();
        e.stopPropagation();
        setCurr(e.x);
        const diff = curr - start;
        if (diff < 0) {
            return;
        }
        if (diff >= initData.max) {
            setDiff(initData.max);
            setIsSuccess(true);
            config.success && config.success();
            return;
        }
        setDiff(diff);
    };
    // 鼠标松开
    const mouseup = () => {
        if (isSuccess) {
            return;
        }
        setIsMousedown(false);
        setDiff(0);
        setIsMouseEnter(false);
    };

    useEffect(() => {
        document.body.addEventListener('mousemove', mousemove);
        document.body.addEventListener('mouseup', mouseup);
        return () => {
            document.body.removeEventListener('mousemove', mousemove);
            document.body.removeEventListener('mouseup', mouseup);
        };
    }, [isMousedown, isMouseEnter, diff, start, curr, isSuccess]); // eslint-disable-line

    /** 滑条样式 */
    const slideStyle = {
        borderRadius: config.borderRadius,
        background: config.movedColor,
        left: 58 - config.width,
        opacity: isMouseEnter ? 1 : 0,
        transitionDuration: !isMouseEnter || !isMousedown ? '.3s' : '0s',
        transform: `translateX(${diff}px)`,
    };
    /** 滑块样式 */
    const barStyle = {
        background: config.barBackground,
        transitionDuration: !isMouseEnter || !isMousedown ? '.3s' : '0s',
        transform: `translateX(${diff}px)`,
    };
    /** 成功文本样式 */
    const textStyle = {
        opacity: isSuccess ? 1 : 0,
        transitionDuration: !isMouseEnter || !isMousedown ? '.3s' : '0s',
    };

    return (
        <div style={initData.style} className={styles['simple-verify']}>
            <div className={styles['verify-tips']}>{config.tips}</div>
            <div style={initData.slideBoxStyle} className={styles['verify-box']}>
                <div style={slideStyle} className={styles['veriry-slide']} />
            </div>
            <div
                className={styles['verify-bar']}
                onMouseEnter={() => mouseenter()}
                onMouseLeave={() => mouseleave()}
                onMouseDown={e => mousedown(e)}
            >
                <div style={barStyle} className={styles.icon}>
                    {
                        isSuccess ? (
                            <img src={`${BCD_WHOIS_URL}/success.svg`} alt="成功" title="成功" />
                        ) : (
                            <>
                                <img src={`${BCD_WHOIS_URL}/arrow-right.svg`} alt="箭头" title="箭头" />
                                <img src={`${BCD_WHOIS_URL}/arrow-right.svg`} style={{marginLeft: '-8px'}} alt="箭头" title="箭头" />
                            </>
                        )
                    }
                </div>
            </div>
            <div style={textStyle} className={styles['verify-success-tips']}>
                {config.successTips}
            </div>
        </div>
    );
};
