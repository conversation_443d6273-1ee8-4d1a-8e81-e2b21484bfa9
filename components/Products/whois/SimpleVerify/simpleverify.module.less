.simple-verify {
    box-sizing: border-box;
    line-height: 1;
    position: relative;
    margin: 0 auto;
    -webkit-user-select: none;

    .verify-tips {
        widows: 100%;
        height: 100%;
        color: #9CA0A7;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        pointer-events: none;
    }

    .verify-box {
        position: absolute;
        left: -1px;
        top: -1px;
        width: calc(100% + 2px);
        height: calc(100% + 2px);
        overflow: hidden;
    }

    .veriry-slide {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity .1s linear, transform .3s ease;
    }

    .verify-bar {
        position: absolute;
        left: 0;
        top: 0;
        width: 56px;
        height: 100%;
        cursor: pointer;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        z-index: 1;
        transition: transform .3s ease;
        touch-action: none;

        .icon {
            width: 52px;
            height: 38px;
            box-shadow: rgba(#717277, .3) 0 3px 10px;
            background-color: #FFF;
            display: flex;
            align-items: center;
            justify-content: center;

        }
    }

    .verify-success-tips {
        position: absolute;
        left: -1px;
        top: -1px;
        width: calc(100% + 2px);
        height: calc(100% + 2px);
        color: #FFF;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-weight: bold;
        opacity: 0;
        transition: opacity .1s linear;
        pointer-events: none;

        span {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }
    }
}