import React from 'react';
import styles from '../recommend.module.less';

interface FaqDataItem {
    title: string;
    link: string;
}
export const FaqAbout: React.FC<{initFaqData: FaqDataItem[]}> = props => {
    const {initFaqData} = props;

    return (
        <div className={styles['right-module-item']}>
            <div className={styles['right-item-title']}>域名常见问题</div>
            {
                initFaqData.map(item => (
                    <div
                        className={styles['right-item-desc']}
                        key={item.link}
                    >
                        <a
                            className={styles['desc-left']}
                            href={item.link}
                            target="_blank"
                            title={item.title}
                        >
                            {item.title}
                        </a>
                    </div>
                ))
            }
        </div>
    );
};
