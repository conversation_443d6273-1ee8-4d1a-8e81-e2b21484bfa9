export interface TldRecommendItem {
    suffix: string;
    description: number;
    [index: string]: any;
}

export interface NoticeListItem {
    noticeId: string;
    title: string;
    summary: string;
    createTime: string;
    [index: string]: any;
}

export interface Request {
    url: string;
    data?: { [index: string]: any };
    timestampPath: string;
}

export interface NoticeDetail {
    title: string;
    summary: string;
    detail: string;
    createTime: string;
    [index: string]: any;
}

export type Method = 'GET' | 'POST';
