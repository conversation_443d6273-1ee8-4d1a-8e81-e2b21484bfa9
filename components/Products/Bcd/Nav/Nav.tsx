/**
 * @file BCD域名官网首页
 * @desc 导航栏模块
 */

import React from 'react';
import {LogObject} from '@common/hooks/page/useQifuLog';
import {defaultProductsBcdNavData} from '../defaultModel';
import styles from './nav.module.less';

const {navData} = defaultProductsBcdNavData;
interface InterNavItem {
    id: number;
    lists: InterNavElemItem[];
}
interface InterNavElemItem {
    imgSrc: string;
    title: string;
    link?: string;
    titleActive: string;
    bio: string;
    style: string;
    itemLink?: InterNavDomItem[];
}
interface InterNavDomItem {
    id?: string;
    text?: string;
    rightLine?: boolean;
    link?: string;
}

const navDomItems = (item: InterNavItem, h: LogObject) => {
    const handleClickNavItem = (item: InterNavDomItem) => {
        switch (item.id) {
            case 'WHOIS':
                h.log(356602);
                h.log(393258);
                break;
            case 'DNS':
                h.log(356602);
                h.log(612667);
                break;
            case 'TOOL':
                h.log(356602);
                h.log(243863);
                break;
            default:
                break;
        }
    };
    return (
        <>
            {
                item.lists.map((elem: InterNavElemItem) => {
                    if (elem.style === 'normal') {
                        return (
                            <a
                                className={styles['item-normal']}
                                key={elem.imgSrc}
                                href={elem.link}
                                target="_blank"
                                title={elem.title}
                            >
                                <span
                                    className={styles.title}
                                >
                                    <img
                                        src={elem.imgSrc}
                                        width={22}
                                        height={22}
                                        alt={elem.title}
                                        title={elem.title}
                                    />
                                    <span className={styles['title-text']}>{elem.title}</span>
                                    <span className={styles['title-active']}>{elem.titleActive}</span>
                                </span>
                                <div className={styles.bio}>{elem.bio}</div>
                            </a>
                        );
                    }
                    return (
                        <div className={styles['item-long']} key={elem.title}>
                            <div className={styles.title}>
                                <span>{elem.title}</span>
                            </div>
                            <div className={styles.tool}>
                                {
                                    elem.itemLink.map((dom: InterNavDomItem, index: number) => (
                                        <React.Fragment key={dom.link}>
                                            <a
                                                className={styles['tool-link']}
                                                href={dom.link}
                                                target="_blank"
                                                title={dom.text}
                                                onClick={() => handleClickNavItem(dom)}
                                            >
                                                {dom.text}
                                            </a>
                                            <span className={styles['tool-line']}>
                                                {index === elem.itemLink.length - 1 ? '' : '|'}
                                            </span>
                                        </React.Fragment>
                                    ))
                                }
                            </div>
                        </div>
                    );
                })
            }
        </>
    );
};
export const ProductsBcdNav = ({h}: {h: LogObject}) => {
    return (
        <div className={styles['nav-container']}>
            {
                navData.map(item => {
                    return (
                        <div className={styles['nav-item']} key={item.id}>
                            {
                                navDomItems(item, h)
                            }
                        </div>
                    );
                })
            }
        </div>
    );
};
