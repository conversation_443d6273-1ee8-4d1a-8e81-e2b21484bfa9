.active-column {
    width: 100%;
    background: url('https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/active-bg.png') no-repeat center center;
    background-size: cover;
    box-sizing: border-box;
    padding: 60px 0;
    background-color: #ccc;

    h2 {
        font-family: PingFangSC-Medium;
        font-size: 28px;
        color: #FFF;
        text-align: center;
        line-height: 40px;
        font-weight: 500;
        margin-bottom: 32px;
    }

    .active-container {
        width: 1180px;
        height: 226px;
        margin: 0 auto;
        box-sizing: border-box;
        position: relative;

        .over-hidden {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;

            ul {
                display: flex;
                width: 1180px;
                justify-content: space-between;
                position: absolute;
                left: 0;
                transition: .4s all cubic-bezier(0, 0, 0.42, 1);

                .img-box {
                    width: 380px;
                    height: 226px;
                    flex-shrink: 0;
                    box-sizing: border-box;
                    transition: .2s all ease-in;
                    overflow: hidden;
                    border-radius: 6px;
                    margin-right: 20px;

                    div {
                        width: 380px;
                        height: 226px;
                    }

                    img {
                        transition: .2s all ease-in;
                    }
                }
                .dotActive {
                    opacity: 1;
                }

                .img-box:hover img {
                    transform: scale(1.08);
                }
            }
        }
        .left-btn {
            width: 52px;
            height: 52px;
            background-color: #FFF;
            background: url('https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/left.png') center center no-repeat;
            background-color: #FFF;
            background-size: 52px;
            border-radius: 50%;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translate(-50%, -50%);
            z-index: 99;
            cursor: pointer;
        }
        .right-btn {
            width: 52px;
            height: 52px;
            background: url('https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/right.png') center center no-repeat;
            background-color: #FFF;
            background-size: 52px;
            border-radius: 50%;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translate(50%, -50%);
            z-index: 99;
            cursor: pointer;
        }
    }
}