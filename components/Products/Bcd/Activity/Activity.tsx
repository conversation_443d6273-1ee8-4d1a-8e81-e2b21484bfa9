/**
 * @file BCD域名官网首页
 * @desc 活动轮播模块
 */

import {useRef, useState, useEffect, useCallback, MutableRefObject} from 'react';
import {defaultProductsBcdActive} from '../defaultModel';
import styles from './activity.module.less';

const {bannerData} = defaultProductsBcdActive;
let timer: any = null;
let animationTimer: any = null;
let dataSource: any[] = bannerData;
export const ProductsBcdActive = () => {

    dataSource = dataSource.concat(bannerData);
    const [current, setCurrent] = useState<number>(1);
    const [nextAnimation, setNextAnimation] = useState<string>('next');
    const [total, setTotal] = useState<number>(1);
    const ref: MutableRefObject<any> = useRef(null);
    const slideDomWidth = Number(ref.current?.children[0]?.offsetWidth) + 20;

    const autoSlide = useCallback((rel?: string) => {
        const totalCount = dataSource.length;
        setTotal(totalCount);
        timer && clearTimeout(timer);
        animationTimer && clearTimeout(animationTimer);
        switch (rel) {
            case 'prev':
                setCurrent((current - 1 + totalCount) % totalCount);
                break;
            case 'next':
            default:
                setCurrent((current + 1) % totalCount);
                break;
        }
        setNextAnimation(rel);
    }, [current]);

    useEffect(() => {
        if (current === total - 1 && nextAnimation === 'prev') {
            ref.current.style.transition = 'transform 0s ease';
            animationTimer = setTimeout(() => {
                ref.current.style.transition = 'transform .1s ease';
                ref.current.style.transform = `translateX(${-slideDomWidth * current}px)`;
                clearTimeout(animationTimer);
            }, 0);
        }
        else if (!current && nextAnimation === 'next') {
            ref.current.style.transform = `translateX(${-slideDomWidth * total}px)`;
            animationTimer = setTimeout(() => {
                ref.current.style.transition = 'transform 0s ease';
                ref.current.style.transform = `translateX(${-slideDomWidth * current}px)`;
                clearTimeout(animationTimer);
            }, 500);
        }
        else {
            ref.current.style.transition = 'transform .5s ease';
            ref.current.style.transform = `translateX(${-slideDomWidth * current}px)`;
        }
    }, [current, nextAnimation, total, slideDomWidth]);

    useEffect(() => {
        timer = setTimeout(() => {
            autoSlide('next');
        }, 3000);
        return () => clearTimeout(timer);
    }, [autoSlide]);

    return (
        <div className={styles['active-column']}>
            <h2>域名活动</h2>
            <div className={styles['active-container']}>
                <div className={styles['over-hidden']}>
                    <ul ref={ref}>
                        {
                            dataSource.map((item, index) => (
                                <li
                                    className={styles['img-box']}
                                    // eslint-disable-next-line react/no-array-index-key
                                    key={index}
                                >
                                    <a href={item.link} target="_blank" title="域名活动">
                                        <img src={item.imgSrc} width={380} height={226} alt="域名活动" title="域名活动" />
                                    </a>
                                </li>
                            ))
                        }
                    </ul>
                </div>
                <div className={styles['left-btn']} onClick={() => autoSlide('prev')}></div>
                <div className={styles['right-btn']} onClick={() => autoSlide('next')}></div>
            </div>
        </div>
    );
};
