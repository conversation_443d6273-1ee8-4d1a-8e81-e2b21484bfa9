/**
 * @file BCD官网首页(全局静态数据)
 * /components/Products/Bcd/defaultModel.tsx
 */
import styles from './Search/search.module.less';

const cloudHost = 'https://cloud.baidu.com';
const consoleHost = 'https://console.bce.baidu.com';

export const defaultProductsBcdSearchData = {
    navLeftData: {
        domainActive: [
            {
                domain: '.com',
                price: '￥33',
                text: '（新客专享）',
                link: '/product/bcd/search.html?tld=.com&track=BCD_PORTAL&clientFrom=CLOUD',
            },
            {
                domain: '.top',
                price: '￥15',
                text: '起',
                class: styles.cn,
                link: '/product/bcd/search.html?tld=.top&track=BCD_PORTAL&clientFrom=CLOUD',
            },
            {
                domain: '专业域名代运维服务',
                price: '￥100',
                text: '起',
                class: styles.cn,
                link: 'https://cloud.baidu.com/survey/ctsservice.html?track=BCD_PORTAL',
            },
        ],
        priceAll: {
            text: '价格总览',
            link: '/product/bcd/price.html?track=BCD_PORTAL&clientFrom=CLOUD',
        },
    },
    navRightData: [
        {
            text: '域名公告',
            link: '/product/bcd/notice/list.html?track=BCD_PORTAL&clientFrom=CLOUD',
        },
        {
            text: '批量注册',
            link: '/product/bcd/search.html?keyword=&purchaseType=multiple&track=BCD_PORTAL&clientFrom=CLOUD',
        },
        {
            text: '委托购买',
            link: '/product/bcd/land/#/domain?track=BCD_PORTAL&clientFrom=CLOUD',
        },
        {
            text: '域名交易',
            link: '/product/bcd/land/#/fixed?track=BCD_PORTAL&clientFrom=CLOUD',
        },
        {
            text: '域名购买指南',
            link: `${cloudHost}/doc/BCD/s/Wjwvymgtu?track=BCD_PORTAL&clientFrom=CLOUD`,
        },
    ],
    urlSearch: '/product/bcd/search.html',
};

export const defaultProductsBcdNavData = {
    navData: [
        {
            id: 1,
            lists: [
                {
                    imgSrc: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/weituo.svg',
                    title: '域名',
                    titleActive: '委托购买',
                    bio: '千万域名资源，帮您回购心动域名',
                    link: '/product/bcd/land/#/domain?track=BCD_PORTAL&clientFrom=CLOUD',
                    style: 'normal',
                },
                {
                    imgSrc: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/baijin.svg',
                    title: '白金域名',
                    link: '/product/bcd/search.html#/bjDomain?track=BCD_PORTAL&clientFrom=CLOUD',
                    titleActive: '',
                    bio: '抢注精选域名，塑造品牌形象',
                    style: 'normal',
                },
                {
                    imgSrc: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/youhui.svg',
                    title: '优惠',
                    link: '/product/bcd/resourcePkg.html?track=BCD_PORTAL&clientFrom=CLOUD#/bcd/package/purchase',
                    titleActive: '资源包',
                    bio: 'com后缀专属优惠包7.5折起',
                    style: 'normal',
                },
                {
                    imgSrc: '1',
                    title: '域名管理',
                    titleActive: '',
                    bio: '',
                    style: 'long',
                    itemLink: [
                        {
                            text: '域名转入',
                            rightLine: true,
                            link: `${consoleHost}/bcd/?track=BCD_PORTAL&clientFrom=CLOUD#/bcd/transferin/list`,
                        },
                        {
                            text: '域名续费',
                            rightLine: true,
                            link: `${consoleHost}/bcd/?track=BCD_PORTAL&clientFrom=CLOUD#/bcd/manage/list`,
                        },
                        {
                            text: '云解析DNS',
                            rightLine: true,
                            link: `${consoleHost}/bcd/?track=BCD_PORTAL&clientFrom=CLOUD#/bcd/manage/list`,
                        },
                        {
                            text: '模板认证',
                            rightLine: false,
                            link: `${consoleHost}/bcd/?track=BCD_PORTAL&clientFrom=CLOUD#/bcd/contacts/list`,
                        },
                    ],
                },
            ],
        },
        {
            id: 2,
            lists: [
                {
                    imgSrc: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/yikoujia.svg',
                    title: '域名',
                    titleActive: '交易',
                    link: '/product/bcd/search.html#/fixed',
                    bio: '上万个优质域名，一口价交易',
                    style: 'normal',
                },
                {
                    imgSrc: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20250320/aipagegray.png',
                    title: '',
                    titleActive: '大模型AI建站',
                    link: 'https://cloud.baidu.com/product/aipage.html?trace=bcd_portal_qifuheader&pageResource=bcd_portal_qifuheader&select=2',
                    bio: '拥抱智能，一键生成专属网站！',
                    style: 'normal',
                },
                {
                    imgSrc:
                        'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/zhuanchang.svg',
                    title: 'SSL证书服务',
                    link: '/product/ssl.html',
                    titleActive: '',
                    bio: '精选知名品牌，288元起',
                    style: 'normal',
                },
                {
                    imgSrc: '2',
                    title: '域名工具',
                    titleActive: '',
                    bio: '',
                    style: 'long',
                    itemLink: [
                        {
                            id: 'WHOIS',
                            text: 'whois查询',
                            rightLine: true,
                            link: '/product/bcd/toolPack.html?pageTitle=whois',
                        },
                        {
                            id: 'DNS',
                            text: '解析查询',
                            rightLine: true,
                            link: '/product/bcd/toolPack.html?pageTitle=dns',
                        },
                        {
                            id: 'TOOL',
                            text: '域名检测工具',
                            rightLine: true,
                            link: '/product/bcd/toolPack.html?pageTitle=tool',
                        },
                        {
                            text: 'SSL证书',
                            rightLine: false,
                            link: '/product/ssl.html',
                        },
                    ],
                },
            ],
        },
    ],
};

export const defaultProductsBcdAbout = {
    aboutData: [
        {
            title: '品牌信誉',
            des1: '域名注册服务商',
            des2: '为千万级用户提供服务',
            class: 'l-t',
        },
        {
            title: 'DNS云解析',
            des1: '专业DNS智能云解析',
            des2: '免费6G解析高防，精准可靠',
            class: 'r-t',
        },
        {
            title: '服务保障',
            des1: '提供国内外百余种域名后缀',
            des2: '管理功能丰富安全，为客户保驾护航',
            class: 'l-b',
        },
        {
            title: '1V1大客户支持',
            des1: '一对一大客户服务支持对接',
            des2: '提供批量注册，高效能对接',
            class: 'r-b',
        },
    ],
};
export const defaultProductsBcdActive = {
    bannerData: [
        {
            imgSrc: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/banner-1.png',
            link: '/product/bcd/land/#/fixed?track=BCD_PORTAL&clientFrom=CLOUD',
        },
        {
            imgSrc: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/banner-3.png',
            link: '/product/bcd/land/#/domain?track=BCD_PORTAL&clientFrom=CLOUD',
        },
    ],
};

export const defaultProductsBcdFooter = {
    guideData: [
        {
            imgSrc: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/huaban.svg',
            title: '购买域名',
            lists: [
                {
                    text: '域名选购指南',
                    link: `${cloudHost}/doc/BCD/s/Wjwvymgtu?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '注册域名',
                    link: `${cloudHost}/doc/BCD/s/Gkdpmex6c?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '信息模板',
                    link: `${cloudHost}/doc/BCD/s/Pkdplyg9c?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '域名实名认证',
                    link: `${cloudHost}/doc/BCD/s/ekds6bd3g?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
            ],
        },
        {
            imgSrc: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/huaban.svg',
            title: '域名管理',
            lists: [
                {
                    text: '域名解析',
                    link: `${cloudHost}/doc/BCD/s/bkdsfohrx?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '域名续费',
                    link: `${cloudHost}/doc/BCD/s/5kds9a5hh?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '域名转入',
                    link: `${cloudHost}/doc/BCD/s/yjwvyme3t?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '变更域名所有者',
                    link: `${cloudHost}/doc/BCD/s/Yjwvymg44?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '域名备案',
                    link: `${cloudHost}/doc/BCD/s/Ujwvymesk?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '域名证书下载',
                    link: `${cloudHost}/doc/BCD/s/Fkds8dclf?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '修改DNS服务器',
                    link: `${cloudHost}/doc/BCD/s/gjwvymddo?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '域名转至其他账号',
                    link: `${cloudHost}/doc/BCD/s/Hjwvymfgs?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
            ],
        },
        {
            imgSrc: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/huaban.svg',
            title: '域名交易',
            lists: [
                {
                    text: '域名委托购买',
                    link: `${cloudHost}/doc/BCD/s/3kmwugb5c?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '一口价（优选）',
                    link: `${cloudHost}/doc/BCD/s/Wknha1o24?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '域名优惠资源包',
                    link: `${cloudHost}/doc/BCD/s/2jwvymlfz?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
            ],
        },
        {
            imgSrc: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/huaban.svg',
            title: '常见问题',
            lists: [
                {
                    text: '域名注册及解析问题',
                    link: `${cloudHost}/doc/BCD/s/Hjwvymjgx?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '域名流程及使用问题',
                    link: `${cloudHost}/doc/BCD/s/sjwvymkii?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '域名DNS问题',
                    link: `${cloudHost}/doc/BCD/s/Mjwvymk7a?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
                {
                    text: '域名转入问题',
                    link: `${cloudHost}/doc/BCD/s/1jwvymjtj?track=BCD_PORTAL&clientFrom=CLOUD`,
                },
            ],
        },
    ],
    cooperativePartner: [
        'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/xinwang.png',
        'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/juming.png',
        'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/shangwu.png',
        'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/bangning.png',
        'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/icann.png',
        'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/yiming.png',
    ],
    goRegisterLink: '/product/bcd/search.html?track=BCD_PORTAL&clientFrom=CLOUD',
};

export const defaultProductsBcdProcess = {
    processData: [
        {
            title: '选购域名',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_1.png',
            detail: '作为互联网服务的入口，在网上“安家立户”，您首先需要先拥有一个域名',
            linkArray: [
                {
                    name: '新域名注册',
                    link: 'https://cloud.baidu.com/product/bcd/search.html',
                },
                {
                    name: '域名委托购买',
                    link: 'https://cloud.baidu.com/product/bcd/land/#/domain?track=BCD_PORTAL&clientFrom=CLOUD',
                },
                {
                    name: '二手域名交易',
                    link: 'https://cloud.baidu.com/product/bcd/land/#/fixed?select=1',
                },
            ],
        },
        {
            title: '制作发布网站',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_2.png',
            detail: '您可以在线自行设计制作，也可以委托我们为您量身定做一个网站',
            linkArray: [
                {
                    name: '智能门户建站',
                    link: 'https://aipage.baidu.com/',
                },
                {
                    name: '云服务器',
                    link: 'https://cloud.baidu.com/product/bcc.html',
                },
            ],
        },
        {
            title: 'ICP备案',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_3.png',
            detail: '根据工信部要求，您的域名需在完成ICP备案后，方可开通服务',
            linkArray: [
                {
                    name: '网站ICP备案',
                    link: 'https://cloud.baidu.com/beian/index.html',
                },
                {
                    name: 'ICP许可证办理',
                    link: 'https://cloud.baidu.com/product/bla?linkFrom=bcd',
                },
            ],
        },
        {
            title: '网站优化加固',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_4.png',
            detail: '如何给用户提供速度更快、更安全、更稳定的网站？可以完成以下优化方案：',
            linkArray: [
                {
                    name: '智能解析调度',
                    link: 'https://cloud.baidu.com/product/dns.html',
                },
                {
                    name: '网站证书加密',
                    link: 'https://cloud.baidu.com/product/ssl.html',
                },
                {
                    name: 'CDN网站加速',
                    link: 'https://cloud.baidu.com/product/cdn.html',
                },
                {
                    name: 'BOS对象存储',
                    link: 'https://cloud.baidu.com/product/bos.html',
                },
            ],
        },
        {
            title: '数字营销推广',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_5.png',
            detail: '网站建设完备后，就可以启动互联网和全方位的数字化营销推广工作了，您可以进行：',
            linkArray: [
                {
                    name: '消息服务',
                    link: 'https://cloud.baidu.com/product/sms.html',
                },
                {
                    name: '智能推荐引擎',
                    link: 'https://cloud.baidu.com/product/ai-rec.html',
                },
            ],
        },
        {
            title: '企业品牌保护',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_6.png',
            detail: '数字化时代，作为核心数字资产的企业自有品牌、知识产权需要高度重视并合理利用',
            linkArray: [
                {
                    name: '为公司品牌注册商标',
                    link: 'https://cloud.baidu.com/product/tms.html',
                },
                {
                    name: '品牌中文域名直达',
                    link: 'https://cloud.baidu.com/product/bcd/search.html/#/land?track=BCD_PORTAL&clientFrom=CLOUD',
                },
                {
                    name: '直接选购已有商标',
                    link: 'https://cloud.baidu.com/product/tms/transaction?linkFrom=bcd',
                },
            ],
        },
        {
            title: '运营提效',
            img: 'https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_7.png',
            detail: '前端营销体系运营启动后，后端办公与运营效率的提升就显得尤为重要。您可以',
            linkArray: [
                {
                    name: '可视化经营分析 ',
                    link: 'https://cloud.baidu.com/product/sugar.html',
                },
                {
                    name: '企业网盘协同办公',
                    link: 'https://cloud.baidu.com/product/bnd-e.html',
                },
                {
                    name: '电子合同在线签署',
                    link: 'https://cloud.baidu.com/product/xuperevid.html',
                },
            ],
        },
    ],
};

export const Rules = {
    domain: [
        () => ({
            validator(_: any, value: any) {
                if (!value) {
                    return Promise.reject(new Error('请输入您要购买的域名'));
                }
                if (!/^(([-\u4E00-\u9FA5a-z0-9]{1,63})\.)+([\u4E00-\u9FA5a-z]{2,63})\.?$/.test(value)) {
                    return Promise.reject(new Error('域名格式不正确，请输入正确的域名格式'));
                }
                return Promise.resolve();
            },
        }),
    ],
    budget: [
        () => ({
            validator(_: any, value: any) {
                if (!value) {
                    return Promise.reject(new Error('请输入您的购买预算'));
                }
                if (!/^([1-9]\d{4,}|[2-9]\d{3,})$/.test(value) || value < 2000) {
                    return Promise.reject(new Error('购买预算只支持大于等于2000的正整数'));
                }
                return Promise.resolve();
            },
        }),
    ],
    contactName: [
        () => ({
            validator(_: any, value: any) {
                if (!value) {
                    return Promise.reject(new Error('请输入联系人姓名'));
                }
                if (!/^[\u2E80-\u9FFF]{1,8}$/.test(value)) {
                    return Promise.reject(new Error('请输入8位以内的中文'));
                }
                return Promise.resolve();
            },
        }),
    ],
    contactPhone: [
        () => ({
            validator(_: any, value: any) {
                if (!value) {
                    return Promise.reject(new Error('请输入手机号'));
                }
                if (!/^1\d{10}$/.test(value)) {
                    return Promise.reject(new Error('请输入正确的手机号'));
                }
                return Promise.resolve();
            },
        }),
    ],
    verifyCode: [
        () => ({
            validator(_: any, value: any) {
                if (!value) {
                    return Promise.reject(new Error('请输入验证码'));
                }
                if (!/^\d{6}$/.test(value)) {
                    return Promise.reject(new Error('请输入正确的验证码'));
                }
                return Promise.resolve();
            },
        }),
    ],
};

// 添加百度统计
export const addBcdBaiduStatistics = () => {
    if (/(cloud|qifu)\.baidu\.com/.test(window.location.host)) {
        const hm = document.createElement('script');
        hm.src = 'https://hm.baidu.com/hm.js?3b03c9d056632472ecdfed82c7b9034e';
        const s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(hm, s);
    }
};
