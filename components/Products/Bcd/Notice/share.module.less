.notice-share {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #7A8599;
    line-height: 32px;
    margin: 20px 0 0 0;

    .notice-share-content {
        display: flex;
        align-items: center;
    }

    .notice-share-icon {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 34px;
        height: 34px;
        font-size: 20px;
        border-radius: 50%;
        line-height: 34px;
        border: 1px solid #666;
        color: #666;
        transition: background-color 0.6s ease-out 0s;
        text-decoration: none;
        margin: 4px;
    }

    .icon-weibo {
        color: #FF763b;
        border-color: #FF763b;

        &:hover {
            color: #FFF;
            background-color: #FF763b;
        }
    }

    .icon-qq {
        color: #56B6E7;
        border-color: #56B6E7;

        &:hover {
            color: #FFF;
            background-color: #56B6E7;
        }
    }

    .icon-wx {
        color: #7BC549;
        border-color: #7BC549;

        &:hover {
            color: #FFF;
            background-color: #7BC549;
        }
    }
    
    .icon-douban {
        color: #33B045;
        font-size: 16px;
        font-weight: 600;
        border-color: #33B045;

        &:hover {
            color: #FFF;
            background-color: #33B045;
        }
    }

    .icon-qzone {
        color: #FDBE3D;
        border-color: #FDBE3D;

        &:hover {
            color: #FFF;
            background-color: #FDBE3D;

            .icon-qzone-svg {
                fill: #FFF;
            }
        }

        &-svg {
            fill: #FDBE3D;
        }
    }
}
