/* eslint-disable max-len */
/**
 * @file BCD域名官网公告页
 * @desc 分享模块
 */

import {QqOutlined, WechatOutlined, WeiboOutlined} from '@ant-design/icons';
import React from 'react';
import QRCode from 'qrcode.react';
import {Popover} from 'antd';
import {useRouter} from 'next/router';
import {hostMap} from '../../../../components/Products/util';
import styles from './share.module.less';

interface Props {
    title?: string;
    summary?: string;
}

export const NoticeShare = (props: Props) => {
    const {title = '', summary = ''} = props;

    const router = useRouter();

    const url = `${hostMap.getHost('cloud')}${encodeURIComponent(router.asPath)}`;

    const weiboHref = `https://service.weibo.com/share/share.php?url=${url}&title=${title}&appkey=`;

    const qqHref = `https://connect.qq.com/widget/shareqq/index.html?url=${url}&title=${title}`;

    const wechatContent = (
        <>
            <QRCode value={decodeURIComponent(url)} size={100} />
            <div>微信里点“发现”，扫一下</div>
            <div>二维码便可将本文分享至朋友圈。</div>
        </>
    );

    const doubanHref = `http://shuo.douban.com/!service/share?href=${url}&name=${title}&text=${summary}&starid=0&aid=0&style=11`;

    const qzoneHref = `https://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url=${url}&title=${title}&summary=${summary}`;

    return (
        <div className={styles['notice-share']}>
            分享：
            <div className={styles['notice-share-content']}>
                <a href={weiboHref} target="_blank" title="微博分享" className={`${styles['icon-weibo']} ${styles['notice-share-icon']}`}>
                    <WeiboOutlined />
                </a>
                <a href={qqHref} target="_blank" title="QQ分享" className={`${styles['icon-qq']} ${styles['notice-share-icon']}`}>
                    <QqOutlined />
                </a>
                <Popover content={wechatContent} title="微信扫一扫：分享" zIndex={9999} overlayStyle={{textAlign: 'center', fontSize: '12px'}}>
                    <a href="###" onClick={e => {e.preventDefault();}} title="微信分享" className={`${styles['icon-wx']} ${styles['notice-share-icon']}`}>
                        <WechatOutlined />
                    </a>
                </Popover>
                <a href={doubanHref} target="_blank" title="豆瓣分享" className={`${styles['icon-douban']} ${styles['notice-share-icon']}`}>豆</a>
                <a href={qzoneHref} target="_blank" title="QQ空间分享" className={`${styles['icon-qzone']} ${styles['notice-share-icon']}`}>
                    <svg
                        className={styles['icon-qzone-svg']}
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="990"
                        xmlnsXlink="http://www.w3.org/1999/xlink"
                        width="20"
                        height="20"
                    >
                        <path
                            d="M505.22 54.71c-4.89 2.01-18.63 16.62-18.63 19.95 0 1.11-21.51 46.77-47.88 101.31S389.72 278.15 388.4 281.93c-1.11 3.54-3.12 7.77-3.99 9.09-1.11 1.32-7.32 13.29-13.95 26.61-7.08 14.64-14.64 26.82-18.84 30.15-10.2 8.64-3.33 7.77-207.48 30.6C51.92 388.79 41.72 390.35 36.2 395.45c-3.99 3.54-5.55 16.41-2.67 23.94 0.9 1.98 40.56 39.69 88.23 83.79 150.72 139.2 140.97 129.45 140.97 139.86 0 5.1-3.09 23.49-6.66 41.22-3.78 17.73-12.18 62.07-19.05 98.64s-14.85 78.48-17.73 93.09c-15.3 78.03-15.51 80.25-7.53 88.68 7.53 7.98 16.17 7.08 39-4.44 21.27-10.86 23.28-11.97 65.16-36.57 15.3-8.88 35.25-20.4 44.34-25.5s25.92-15.06 37.23-21.93 21.06-12.42 21.72-12.42c0.45 0 7.32-3.99 15.06-8.88s14.85-8.85 15.75-8.85c0.9 0 8.88-4.44 17.73-9.96 21.06-13.08 39.24-19.74 49.65-17.73 4.44 0.9 15.06 5.1 23.49 9.75 22.17 11.76 161.82 92.22 205.26 118.14 37.02 22.17 47.88 26.37 60.51 23.04 8.43-2.01 10.86-14.19 7.98-38.34-1.32-10.86-3.33-23.07-4.44-27.27-1.77-7.08-9.09-46.32-26.82-144.75-8.19-46.77-8.19-47.67 10.2-54.54 25.5-9.54 37.47-17.94 32.82-22.62-1.56-1.56-15.75 0.24-45.21 5.55-54.75 9.96-66.06 11.52-123.9 16.86-65.82 6.21-126.57 7.53-207.72 4.65-63.39-2.22-138.75-7.77-142.98-10.41-3.33-2.22-1.77-9.09 3.33-13.53 2.67-2.43 36.36-26.61 74.7-53.85 120.15-85.11 198.18-141.42 204.6-147.42 11.07-10.41 10.2-10.86-35.91-16.86-72.93-9.3-91.98-10.86-210.81-16.41-39.69-1.77-75.6-3.99-79.8-4.65-43.44-7.32 177.99-27.27 271.53-24.39 61.41 1.77 133.68 6.87 176.22 12.42 39.69 5.31 42.12 5.76 42.12 10.2 0 3.54-4.2 6.66-97.53 70.92-38.34 26.61-79.14 54.75-90.66 62.73-11.31 7.98-23.04 16.17-25.71 17.94-12.42 8.43-54.09 37.47-65.61 45.87-7.32 5.31-13.08 11.07-13.08 12.87 0 4.2 10.86 6.87 44.34 10.86 88.89 10.41 254.25 16.17 261.57 8.88 1.56-1.56 1.32-6.42-1.11-16.62-1.77-7.77-3.33-15.09-3.33-16.17 0-3.12 37.68-40.8 89.76-89.55 137.64-129 140.76-132.12 140.76-142.32 0-8.88-7.32-16.86-18.18-19.5-5.31-1.32-37.02-4.89-70.5-8.19-33.48-3.12-70.5-6.66-82.02-7.77-11.52-1.32-35.91-3.78-54.3-5.76-70.26-7.08-91.77-9.75-96.87-12.18-9.75-4.44-18.84-22.62-86.01-173.79C536.21 74 532.01 65.36 525.35 59.39c-5.52-5.1-14.16-7.08-20.16-4.65z"
                            p-id="991"
                        >
                        </path>
                    </svg>
                </a>
            </div>
        </div>
    );
};
