import {defaultProductsBcdProcess} from '../defaultModel';
import styles from './process.module.less';

const {processData} = defaultProductsBcdProcess;

export const ProductsBcdProcess = () => {
    return (
        <>
            <div className={styles.process}>
                <div className={styles.container}>
                    <h1 className={styles.header}>做生意从域名开始</h1>
                    <span className={styles.introduce}>提供选购域名、网站发布、安全、营销等全方位服务，为生意保驾护航</span>
                    <div className={styles.box}>
                        {
                            processData.map((item, index) => (
                                <div
                                    key={index.toString()}
                                    className={styles.card}
                                >
                                    <img src={item.img} alt={item.title} title={item.title} className={styles['card-img']} />
                                    <h3 className={styles['card-title']}>{item.title}</h3>
                                    <p className={styles['card-detail']}>{item.detail}</p>
                                    <div className={styles['card-link-box']}>
                                        {
                                            item.linkArray.map((item, aindex) => (
                                                <a
                                                    href={item.link}
                                                    key={aindex.toString()}
                                                    className={styles['link-item']}
                                                    target="_blank"
                                                    title={`产品名称${item.name}`}
                                                    data-track-category="域名首页流程中心"
                                                    data-track-name={`产品名称${item.name}`}
                                                    data-track-value={item.link}
                                                >
                                                    <span>{item.name}</span>
                                                    <img
                                                        src="https://bcd-public.bj.bcebos.com/bcd_portal/20230301/arrow_up.png"
                                                        alt="箭头"
                                                        title="箭头"
                                                    />
                                                </a>
                                            ))
                                        }
                                    </div>
                                    <img
                                        src="https://bcd-public.bj.bcebos.com/bcd_portal/20230301/process_card_bg.png"
                                        alt="背景"
                                        title="背景"
                                        className={styles['card-bg']}
                                    />
                                </div>
                            ))
                        }
                    </div>
                </div>
            </div>
        </>
    );
};

