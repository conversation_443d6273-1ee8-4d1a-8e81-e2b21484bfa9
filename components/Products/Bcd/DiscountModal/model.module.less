.discountModel {
    :global {
        .ant-modal-content {
            background: url('https://bcd-public.cdn.bcebos.com/bcd_portal/20241104/bg.png') no-repeat center center;
            border-radius: 6px;
            background-color: #fff;
            background-size: 100% 100%;

            .ant-modal-body {
                padding: 0;
            }
        }
    }
    .content {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 50px 0 32px 0px;
        h3 {
            font-family: PingFangSC-Semibold;
            font-size: 28px;
            color: #000000;
            line-height: 38px;
            font-weight: 600;
            i {
                font-weight: 600;
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                background-image: linear-gradient(90deg,#4dcecb 10%,#2468f2 30%,#e45d45 70%, red);
            }
        }
        p {
            font-family: PingFangSC-Regular;
            font-size: 16px;
            color: rgba(21, 27, 38, 0.7);
            line-height: 22px;
            font-weight: 400;
            margin-top: 16px;
            display: flex;
            justify-content: center;
            align-items: baseline;
            i {
                font-family: DINAlternate-Bold;
                font-size: 23px;
                color: #000000;
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                font-weight: 700;
                margin: 0 2px 0 4px;
                position: relative;
                top: 1px;
                &:nth-child(1),
                &:nth-child(3),
                &:nth-child(4) {
                    background-image: linear-gradient(135deg, #6AFFDF,#62D5FE,#2468f2 50%);
                }
                &:nth-child(2) {
                    background-image: linear-gradient(135deg,#3EE1DD,#2468f2,#2468f2,#e45d45 90%);
                }
                &:nth-child(2),
                &:nth-child(3) {
                    margin-left: 6px;
                    margin-right: 4px;
                }
            }
        }

        .discountListWrap {
            width: 600px;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-top: 20px;

            .discountItem {
                width: 290px;
                margin-bottom: 10px;
                display: flex;
                padding: 12px 10px;
                background-color: #ffffff; /* 卡片背景颜色 */
                border-radius: 10px; /* 卡片边框圆角 */
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 卡片阴影效果 */

                &:nth-of-type(2n+1) {
                    margin-right: 20px;
                }

                img {
                    width: 30px;
                    height: 30px;
                    margin-right: 10px;
                }

                h4 {
                    font-size: 18px;
                    color: rgb(34, 34, 34);
                    line-height: 28px;
                    font-family: PingFangSC-Semibold;
                    font-weight: 600;
                }

                p {
                    opacity: 0.7;
                    font-size: 14px;
                    color: rgb(34, 34, 34);
                    line-height: 24px;
                    margin-top: 3px;
                    font-family: PingFangSC-Regular;
                    justify-content: flex-start;
                }

                .priceWrap {
                    display: flex;
                    align-items: flex-end;
                    color: gray;

                    .price {
                        font-size: 20px;
                        color: #e45d45;
                        font-weight: 800;
                        line-height: 28px;
                        height: 30px;
                    }

                    > span {
                        display: flex;
                        align-items: flex-end;
                    }

                    .unit {
                        color: gray;
                    }
                }

                .originPrice {
                    margin-left: 4px;
                    color: gray;
                    text-decoration: line-through;
                }
            }
        }
        .btn {
            background-image: linear-gradient(108deg, #faad14 2%, #e45d45 84%, red 100%), linear-gradient(-77deg, #16E1A2 0%, #2EC0FD 18%, #2B97E5 45%, #2468F0 100%);
            border-radius: 4px;
            width: 200px;
            height: 40px;
            line-height: 40px;
            font-family: PingFangSC-Semibold;
            font-size: 16px;
            color: #FFFFFF;
            text-align: center;
            font-weight: 600;
            cursor: pointer;
            margin-top: 24px;
            &:hover {
                opacity: .8;
            }
        }
    }
}

.discountButton {
    position: fixed;
    right: 8px;
    bottom: 50%;
    z-index: 1000;
    color: #fff;
    background-image: linear-gradient(108deg, #faad14 2%, #e45d45 84%, red 100%);
    border-radius: 8px;
    box-shadow: 0 8px 42px 0 rgba(9, 18, 33, .08);
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    padding: 20px 0;
    transition: opacity .3s ease-out;
    width: 40px;
    align-items: center;
    cursor: pointer;

    span {
        font-family: PingFangSC-Medium;
        font-size: 14px;
        font-weight: 500;      
    }
}