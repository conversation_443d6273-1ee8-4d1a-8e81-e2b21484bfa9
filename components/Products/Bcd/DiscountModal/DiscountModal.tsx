import {useEffect, useState} from 'react';
import useQifuLog from '@common/hooks/page/useQifuLog';
import {Modal} from 'antd';
import styles from './model.module.less';

const discountDataList = [
    {
        icon: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20241104/aisuda.png',
        title: 'com域名',
        desc: '新用户享33元',
        originPrice: '81',
        price: '68',
    },
    {
        icon: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20241104/signed.png',
        title: 'cn域名',
        desc: '中国国家顶级域名',
        originPrice: '32',
        price: '30',
    },
    {
        icon: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20241104/store.png',
        title: 'top域名',
        desc: 'top 代表着卓越、创新、巅峰',
        originPrice: '15',
        price: '13',
    },
    {
        icon: 'https://bcd-public.cdn.bcebos.com/bcd_portal/20241104/tms.png',
        title: 'net域名',
        desc: '网络服务经典域名',
        originPrice: '93',
        price: '86',
    },
];

export default function DiscountModal() {
    const [visible, setVisible] = useState(false);
    const [showDiscountLabel, setShowDiscountLabel] = useState(false);
    const [h] = useQifuLog(464055);

    const openModal = () => {
        setVisible(true);
        localStorage.setItem('hideDiscountModal', String(new Date().getTime()));
    };

    const showModal = () => {
        h.log(695295, {path: window.location.origin + window.location.pathname});
        openModal();
    };

    useEffect(() => {
        // 根据配置文件判断是否显示优惠活动弹窗
        const now = new Date();
        now.setSeconds(0);
        now.setMilliseconds(0);
        fetch('https://cms-store.cdn.bcebos.com/conf/bcd-discount.json?t=' + Math.floor(now.getTime() / 1000)).then(res => {
            res.json().then(data => {
                if (data.hideDecDiscountModal) {
                    setShowDiscountLabel(false);
                } else {
                    setShowDiscountLabel(true);
                    const hideDiscountModal = localStorage.getItem('hideDiscountModal');
                    const currentTime = new Date().getTime();
                    if (hideDiscountModal) {
                        // 3天不显示优惠活动弹窗
                        if (currentTime - (Number(hideDiscountModal) || 0) > 1000 * 60 * 60 * 24 * 3) {
                            openModal();
                        }
                    } else {
                        openModal();
                    }
                }
            });
        }).catch(e => {
            console.warn(e);
        });
    }, []);

    const closeModal = () => {
        setVisible(false);
    };

    return (
        <>
            <Modal
                className={styles.discountModel}
                open={visible}
                destroyOnClose
                centered
                width={750}
                footer={null}
                maskClosable={false}
                onCancel={closeModal}
            >
                <div className={styles.content}>
                    <h3>
                        域名双12超省季
                        <i>感恩大回馈</i>
                    </h3>
                    <p>
                        为答谢新老客户，com、cn、top、net注册<i>惊喜直降</i>
                    </p>

                    <div className={styles.discountListWrap}>
                        {discountDataList.map(discountData => (
                            <div key={discountData.title} className={styles.discountItem}>
                                <img src={discountData.icon} alt="" />
                                <div>
                                    <h4>{discountData.title}</h4>
                                    <p>{discountData.desc}</p>
                                    <div className={styles.priceWrap}>
                                        <span className={styles.price}>{discountData.price}元</span>
                                        <span className={styles.unit}>/首年</span>
                                        <span className={styles.originPrice}>{discountData.originPrice}元/首年</span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    <div className={styles.btn} onClick={closeModal}>
                        马上抢
                    </div>
                </div>
            </Modal>
            {showDiscountLabel && (
                <div className={styles.discountButton} onClick={showModal}>
                    <span>双</span>
                    <span>12</span>
                    <span>促</span>
                    <span>销</span>
                </div>
            )}
        </>
    );
}
