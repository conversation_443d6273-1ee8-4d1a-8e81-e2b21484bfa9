.hot-recommend {
    width: 100%;
    padding: 160px 0 40px;

    h2 {
        font-family: PingFangSC-Medium;
        font-size: 28px;
        color: #222;
        line-height: 40px;
        text-align: center;
        font-weight: 500;
    }

    .bio {
        margin: 8px auto 0;
        opacity: 0.7;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #222;
        text-align: center;
        line-height: 22px;
        font-weight: 400;

        .more {
            color: #2468F2;
            margin-left: 16px;
        }
    }

    ul {
        width: 1180px;
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        margin-top: 32px;
        justify-content: space-between;
        align-content: flex-start;

        li {
            width: 280px;
            height: 206px;
            box-sizing: border-box;
            border: 1px solid rgba(34,34,34,0.08);
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: .2s all ease-in;

            .hot-title {
                font-family: PingFangSC-Medium;
                font-size: 36px;
                line-height: 36px;
                font-weight: 500;
                display: flex;
                align-items: center;

                .spot {
                    color: #FB5037;
                }

                .domain {
                    color: #222;
                }

                .displayTag {
                    padding: 0 8px;
                    height: 20px;
                    font-size: 12px;
                    color: #FFF;
                    line-height: 20px;
                    font-weight: 600;
                    margin-left: 12px;
                    border-radius: 2px;
                    position: relative;
                }

                .displayTag::before {
                    content: '';
                    display: 'block';
                    width: 0;
                    position: absolute;
                    left: -10px;
                    top: 50%;
                    transform: translateY(-50%);
                    height: 0;
                    border-width: 5px;
                    border-style: solid;
                }

                .displayTag-bg-HOT {
                    background-image: linear-gradient(270deg, #FA7070 0%, #F33D3D 100%);
                }

                .displayTag-bg-HOT::before {
                    border-color: transparent #F33D3D transparent transparent;
                }

                .displayTag-bg-NEW {
                    background-image: linear-gradient(270deg, #4AA1FA 0%, #2468F2 100%);
                }

                .displayTag-bg-NEW::before {
                    border-color: transparent #2468F2 transparent transparent;
                }

                .displayTag-bg-other {
                    background-image: linear-gradient(270deg, #FFA74E 0%, #FF6E26 100%);
                }

                .displayTag-bg-other::before {
                    border-color: transparent #FF6E26 transparent transparent;
                }
            }

            .hot-bio {
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: rgba(34,34,34,0.40);
                text-align: justify;
                line-height: 22px;
                font-weight: 400;
                margin-top: 12px;
            }

            .hot-active {
                margin-top: 16px;
                display: flex;
                height: 20px;

                .hot-active-btn {
                    border: 1px solid #FF6E26;
                    border-radius: 2px;
                    height: 20px;
                    padding: 0 8px;
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: #FF6E26;
                    margin-right: 8px;
                    display: flex;
                    align-items: center;
                }
            }

            .hot-price {
                margin-top: 24px;
                display: flex;
                align-items: flex-end;

                .hot-price-origin {
                    display: flex;
                    align-items: flex-end;
                    font-family: PingFangSC-Medium;
                    color: #FB5037;
                    line-height: 28px;
                    font-weight: 500;

                    .price-logo {
                        font-size: 18px;
                        line-height: 20px;
                    }

                    .price-num {
                        font-size: 28px;
                    }

                    .price-text {
                        font-family: PingFangSC-Regular;
                        font-size: 14px;
                        color: #222;
                        letter-spacing: 0;
                        line-height: 22px;
                        font-weight: 400;
                    }
                }

                .hot-price-curr {
                    margin-left: 8px;
                    opacity: 0.4;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: #222;
                    line-height: 22px;
                    font-weight: 400;
                    text-decoration:line-through
                }
            }
        }

        li:hover {
            transform: translateY(-12px);
            box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
        }
    }

    .hot-recommend-banner {
        margin: 32px auto;
        width: 1180px;
        height: 88px;

        img {
            height: 100%;
            width: 100%;
        }
    }

}
