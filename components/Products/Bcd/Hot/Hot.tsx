/**
 * @file BCD域名官网首页
 * @desc 热门推荐模块
 */

import styles from './hot.module.less';

interface HotRecommend {
    totalCount?: number;
    result: HotRecommendItem[];
}
interface HotRecommendItem {
    tld: string;
    description?: string;
    displayTag?: string;
    displayOrder?: number;
    originalPrice?: string;
    registerPrice?: string;
    activityTagList?: ActivityTag[];
}
interface ActivityTag {
    activityName: string;
    activityType: string;
}

export const ProductsBcdHot = ({hotRecommendList}: {hotRecommendList: HotRecommend}) => {
    const displayTagArr = ['NEW', 'HOT'];

    const jumpDomainDetail = (tld: string): any => {
        tld && window.open(`https://cloud.baidu.com/product/bcd/search.html?tld=.${tld}&track=BCD_PORTAL&clientFrom=CLOUD`);
    };

    const isShowDisplayTag = (displayTag: string): any => {
        const bgColor = displayTagArr.includes(displayTag)
            ? `displayTag-bg-${displayTag}`
            : 'displayTag-bg-other';
        if (displayTag) {
            return <div className={`${styles.displayTag} ${styles[bgColor]}`}>{displayTag}</div>;
        }
        return '';
    };

    return (
        <div className={styles['hot-recommend']}>
            <h2>热门推荐</h2>
            <ul>
                {
                    hotRecommendList.result.slice(0, 8).map(item => {
                        return (
                            <li key={item.tld} onClick={() => jumpDomainDetail(item.tld)}>
                                <div className={styles['hot-title']}>
                                    <span className={styles.spot}>.</span>
                                    <span className={styles.domain}>{item.tld}</span>
                                    {
                                        isShowDisplayTag(item.displayTag)
                                    }
                                </div>
                                <div className={styles['hot-bio']}>
                                    {item.description}
                                </div>
                                <div className={styles['hot-active']}>
                                    {
                                        item.activityTagList?.slice(0, 2)?.map(elem => (
                                            <div key={elem.activityName} className={styles['hot-active-btn']}>{elem.activityName}</div>
                                        ))
                                    }
                                </div>
                                <div className={styles['hot-price']}>
                                    <div className={styles['hot-price-origin']}>
                                        <span className={styles['price-logo']}>￥</span>
                                        <span className={styles['price-num']}>{item.registerPrice.split('.')[0]}</span>
                                        <span className={styles['price-text']}>/首年起</span>
                                    </div>
                                    <div className={styles['hot-price-curr']}>{`原价￥${item.originalPrice.split('.')[0]}/年`}</div>
                                </div>
                            </li>
                        );
                    })
                }
            </ul>
        </div>
    );
};
