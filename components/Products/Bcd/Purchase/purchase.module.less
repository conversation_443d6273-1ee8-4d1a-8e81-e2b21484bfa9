.purchase {
    width: 100%;
    background: url('https://bcd-public.bj.bcebos.com/bcd_portal/20230309/purchase_bj.png') no-repeat 50%/cover;
    .container {
        width: 1180px;
        padding: 60px 0;
        margin: 0 auto;
        .title {
            width: 100%;
            font-size: 28px;
            color: #222222;
            text-align: center;
            font-weight: 500;
            margin-bottom: 32px;
        }
        .purchase-form {
            display: grid;
            grid-template-columns: repeat(3, 380px);
            grid-column-gap: 20px;
            :global {
                .ant-form-item {
                    margin-bottom: 24px;
                }
                .ant-form-item-has-error {
                    margin-bottom: 32px;
                }
                .ant-form-item-label {
                    margin: 0;
                    padding: 0;
                }
                .ant-form-item-label > label {
                    font-size: 14px;
                    color: rgba(34,34,34,0.70);
                    line-height: 24px;
                    font-weight: 400;
                    margin-bottom: 8px;
                }
                .ant-form-item-explain, .ant-form-item-extra {
                    height: 0;
                    min-height: 0;
                    font-size: 12px;
                    line-height: 16px;
                }
                .ant-input {
                    height: 40px;
                    border-radius: 4px;
                    font-size: 14px;
                    color: #222222;
                    line-height: 24px;
                    &::placeholder {
                        color: rgba(34,34,34,0.70);
                    }
                }
                .ant-input:focus {
                    border-color: #2468F2;
                    box-shadow: none;
                }
                .ant-input:hover {
                    border-color: #2468F2;
                }
                .ant-input-textarea-show-count::after {
                    position: absolute;
                    right: 10px;
                    bottom: 28px;
                }
                .ant-btn {
                    width: 260px;
                    height: 40px;
                    background: #2468F2;
                    border: none;
                    border-radius: 4px;
                    text-shadow: none;
                    box-shadow: none;
                    &:hover {
                        background-color: #528eff;
                    }
                }
                .ant-btn > span {
                    font-size: 16px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    text-align: center;
                    font-weight: 500;
                }
                .ant-input-status-success {
                    background-color: #FFFFFF;
                }
                .ant-form-item-explain-error {
                    font-size: 14px;
                    color: #F33E3E;
                    line-height: 24px;
                    font-weight: 400;
                }
            }
            .code-wrapper {
                position: relative;
            }
            
            .code-wrapper:hover {
                border-color: #2468F2;
            }
            .sms-btn {
                position: absolute;
                z-index: 5;
                right: 12px;
                top: 8px;
                width: 70px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #2468F2;
                text-align: right;
                font-weight: 400;
                line-height: 24px;
                cursor: pointer;
            }
            .disabledBtn {
                width: 90px;
                color: #222222;
                opacity: .4;
                cursor: default;
            }
        }
        .attention {
            display: flex;
            margin-top: 32px;
        }
        .attention-box {
            position: absolute;
            width: 118px;
            height: 40px;
            box-sizing: border-box;
            padding: 8px 0;
            text-align: center;
            border: 1px solid rgba(34,34,34,0.16);
            background-color: rgba(255,255,255,0);
            border-radius: 4px;
            cursor: pointer;
            transition: 0.2s all ease;
            span {
                font-size: 14px;
                color: rgba(34,34,34,0.70);
                line-height: 24px;
                font-weight: 500;
            }
            &::before {
                display: inline-block;
                content: '';
                width: 24px;
                height: 24px;
                vertical-align: middle;
                background: url(https://bcd-public.bj.bcebos.com/bcd_portal/20230309/weixin_g.png) no-repeat;
                background-size: 24px 24px;
                margin-right: 8px;
            }
            .attention-qrcode {
                position: relative;
                left: -27px;
                top: 8px;
                width: 172px;
                height: 179px;
                visibility: hidden;
                img {
                    width: 172px;
                    height: 179px;
                }
            }
        }
        .attention-box:hover {
            border: 1px solid #2468F2;
            span {
                color: #2468F2;
            }
            &::before {
                background:url(https://bcd-public.bj.bcebos.com/bcd_portal/20230309/weixin_b.png) no-repeat 50% 50%/cover;
            }
            .attention-qrcode {
                transition: 0.4s all ease;
                visibility: visible;
                transform: translateY(3px);
                z-index: 1111;
            }
        }
        .attention-tips {
            position: relative;
            top: -2px;
            left: 130px;
            height: 44px;
            p {
                font-size: 14px;
                color: rgba(34,34,34,0.70);
                letter-spacing: 0;
                text-align: justify;
                line-height: 22px;
                font-weight: 400;
            }
        }
        .purchase-form > div:last-child {
            grid-column-start: span 3;
            text-align: center;
            margin: 16px 0 0;
        }
        .wx-icon {
            margin-right: 8px;
        }
    }
}