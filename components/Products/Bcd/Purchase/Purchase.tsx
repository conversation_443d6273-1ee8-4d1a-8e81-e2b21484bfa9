import cx from 'classnames';
import React, {useEffect, useState} from 'react';
import moment from 'moment';
import md5 from 'js-md5';
import {Form, Input, Button, message} from 'antd';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {Rules} from '../defaultModel';
import styles from './purchase.module.less';

export const ProductsBcdPurchase = () => {
    const [form] = Form.useForm();
    const [countdown, setCountdown] = useState<number>(60);
    const contraryReptile = (url: string) => {
        // 通过反爬验证timestamp
        const time = moment(new Date()).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        return time + '@' + md5('POST' + url + time + 'ieq%$jsaf23!@fkjwie');
    };
    const sendVerifyCode = () => {
        const url = urlConst.GET_BCD_SEND_SMSCODE;
        const params = {
            phone: form.getFieldValue('contactPhone'),
        };
        netService.post(
            url,
            params,
            {},
            {headers: {timestamp: contraryReptile('/open/portal/recommend')}}
        );
    };
    const getSmsCode = async () => {
        await form.validateFields(['contactPhone']);
        if (countdown < 60) {
            return;
        }
        setCountdown(59);
        sendVerifyCode();
    };

    const [messageApi, contextHolder] = message.useMessage();
    const successToast = () => {
        messageApi.open({
            type: 'success',
            content: '委托购买需求发布成功',
            className: 'custom-class',
            style: {
                marginTop: '80px',
                borderRadius: '4px',
            },
        });
    };

    const errorToast = (message: string) => {
        messageApi.open({
            type: 'error',
            content: message || '委托购买需求发布失败',
            className: 'custom-class',
            style: {
                marginTop: '80px',
            },
        });
    };

    const onFinish = async (values: any) => {
        const url = urlConst.GET_BCD_PURCHASE_DATA;
        const params = {
            ...values,
        };
        try {
            await netService.post(url, params);
            successToast();
            form.resetFields();
            setCountdown(60);
        } catch (error: any) {
            errorToast(error?.message?.global);
        }
    };

    useEffect(() => {
        const timer = setTimeout(() => {
            if (countdown < 60) {
                setCountdown(countdown > 0 ? countdown - 1 : 60);
            }
        }, 1000);
        return () => clearTimeout(timer);
    }, [countdown]);

    return (
        <>
            <div className={styles.purchase}>
                <div className={styles.container}>
                    <h1 className={styles.title}>心仪的域名已被注册？委托专业域名经纪人购买</h1>
                    <div>
                        <div>
                            {contextHolder}
                            <Form
                                form={form}
                                name="basic"
                                layout="vertical"
                                className={styles['purchase-form']}
                                onFinish={onFinish}
                            >
                                <Form.Item
                                    label="购买域名"
                                    name="domain"
                                    validateTrigger="onBlur"
                                    rules={Rules.domain}
                                >
                                    <Input placeholder="请输入购买域名" size="large" autoComplete="off" />
                                </Form.Item>
                                <Form.Item
                                    label="购买预算"
                                    name="budget"
                                    validateTrigger="onBlur"
                                    rules={Rules.budget}
                                >
                                    <Input placeholder="请输入购买预算" size="large" autoComplete="off" />
                                </Form.Item>
                                <Form.Item
                                    label="联系人"
                                    name="contactName"
                                    validateTrigger="onBlur"
                                    rules={Rules.contactName}
                                >
                                    <Input placeholder="请输入联系人" size="large" autoComplete="off" />
                                </Form.Item>
                                <Form.Item
                                    label="手机号码"
                                    name="contactPhone"
                                    validateTrigger="onBlur"
                                    rules={Rules.contactPhone}
                                >
                                    <Input placeholder="请输入手机号码" size="large" autoComplete="off" />
                                </Form.Item>
                                <Form.Item
                                    label="验证码"
                                    name="verifyCode"
                                    validateTrigger="onBlur"
                                    rules={Rules.verifyCode}
                                >
                                    <div className={styles['code-wrapper']}>
                                        <Input placeholder="请输入验证码" size="large" className={styles['code-wrapper']} autoComplete="off" />
                                        <div
                                            className={cx(styles['sms-btn'], countdown < 60 ? styles.disabledBtn : '')}
                                            onClick={() => {
                                                getSmsCode();
                                            }}
                                        >
                                            {countdown < 60 ? `重新获取${countdown}s` : '获取验证码'}
                                        </div>
                                    </div>
                                </Form.Item>
                                <div className={styles.attention}>
                                    <div className={styles['attention-box']}>
                                        <span>关注我们</span>
                                        <div className={styles['attention-qrcode']}>
                                            <img
                                                src="https://bcd-public.bj.bcebos.com/bcd_portal/20230309/cloud_qrcode.png"
                                                alt="二维码"
                                                title="二维码"
                                            />
                                        </div>
                                    </div>
                                    <div className={styles['attention-tips']}>
                                        <p>关注企业服务公众号，了解更多信息。</p>
                                        <p>还有不定期发放福利哦</p>
                                    </div>
                                </div>
                                <Form.Item>
                                    <Button type="primary" htmlType="submit">
                                        发布委托购买需求
                                    </Button>
                                </Form.Item>
                            </Form>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

