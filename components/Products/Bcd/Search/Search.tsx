/**
 * @file BCD域名官网首页
 * @desc 搜索框模块
 */

import {useState, useEffect, useRef} from 'react';
import {LogObject} from '@common/hooks/page/useQifuLog';
import {defaultProductsBcdSearchData} from '../defaultModel';

import styles from './search.module.less';

const {navLeftData, navRightData, urlSearch} = defaultProductsBcdSearchData;
interface tatolDomainList {
    totalCount?: number;
    result: DomainInfo[];
}
interface DomainInfo {
    tld?: string;
    displayTag?: string | null;
}

export function getUrlParams() {
    const urlStr = window.location.search.split('?')[1];
    const urlSearchParams = new URLSearchParams(urlStr);
    const result = Object.fromEntries(urlSearchParams.entries());
    let resultStr = '';
    Object.keys(result).forEach((key: string) => {
        if (result[key]) {
            if (resultStr) {
                resultStr += `&${key}=${result[key]}`;
            } else {
                resultStr = `${key}=${result[key]}`;
            }
        }
    });
    return resultStr;
}

export const ProductsBcdSearch = ({tldsAll, h}: {tldsAll: tatolDomainList, h: LogObject}) => {
    const [searchVal, setSearchVal] = useState('');
    const [isAutoEntry, setIsAutoEntry] = useState(false);
    const [switchDomain, setSwitchDomain] = useState(false);
    const [selectDomain, setSelectDomain] = useState('.com');
    const interval: any = useRef();

    const domainSearch = (e?: any, keyDown?: string) => {
        if (keyDown && e.keyCode !== 13) {
            return;
        }
        h.log(552954);
        if (isAutoEntry) {
            return;
        }
        let keyword: string = '';
        const tldArr: string[] = tldsAll.result.map(item => item.tld);
        const searchValSplitArr: string[] = searchVal.split('.');
        if (!searchVal) {
            return;
        }
        if (searchValSplitArr.length > 1 && tldArr.includes(searchValSplitArr.pop())) {
            keyword = searchVal;
        } else {
            keyword = `${searchVal}${selectDomain}`;
        }
        tldArr.includes(searchVal.split('.').pop());
        // 携带参数跳转
        const params = getUrlParams();
        window.open(`${urlSearch}?keyword=${keyword}${params ? `&${params}` : ''}`);
    };

    // 输入框自动输入效果
    const autoEntryInp = () => {
        setIsAutoEntry(true);
        let flag = 0;
        let num = 0;
        let speedStart = 0;
        let speedLast = 0;

        const domainlists = ['baidu'];
        interval.current = setInterval(() => {
            const domainLong = domainlists[flag].length;
            if (num < domainLong) {

                if (speedStart++ > 5) {
                    setSearchVal(domainlists[flag].substr(0, num) + '|');
                    num++;
                }
            } else if (num < 2 * domainLong + 1) {
                speedStart = 0;
                setSearchVal(domainlists[flag].substr(0, 2 * domainLong - num) + '|');
                if (speedLast++ > 5) {
                    num++;
                }
            } else {
                speedLast = 0;
                flag++;
                num = 0;
                if (flag >= domainlists.length) {
                    flag = 0;
                }
            }
        }, 300);
    };

    useEffect(() => {
        autoEntryInp();
        return () => {
            setIsAutoEntry(false);
            clearInterval(interval.current);
        };
    }, []);

    return (
        <>
            <div className={styles.search}>
                <input
                    type="text"
                    placeholder="请输入要查询的域名，如：baidu"
                    value={searchVal}
                    onChange={e => {
                        setSearchVal(e.target.value);
                    }}
                    onFocus={() => {
                        setSearchVal('');
                        setIsAutoEntry(false);
                        clearInterval(interval.current);
                    }}
                    onKeyDown={e => domainSearch(e, 'keyDown')}
                />
                <div
                    className={`${styles.select} ${switchDomain ? styles.active : ''}`}
                    onClick={() => {
                        setSwitchDomain(!switchDomain);
                    }}
                >
                    <div>{selectDomain}</div>
                    <img
                        src={'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/arrow-down.svg'}
                        width={28}
                        height={20}
                        alt="箭头"
                        title="箭头"
                    />
                    <div
                        className={styles['select-container']}
                        onMouseLeave={() => {
                            setSwitchDomain(false);
                        }}
                    >
                        {
                            tldsAll.result.map(item => {
                                return (
                                    <div
                                        key={item.tld}
                                        className={styles['domain-item']}
                                        onClick={() => {
                                            setSelectDomain(`.${item.tld}`);
                                        }}
                                    >
                                        {`.${item.tld}`}
                                        <span className={styles.displayTag}>{item.displayTag}</span>
                                    </div>
                                );
                            })
                        }
                    </div>
                </div>
                <div className={styles.btn} onClick={domainSearch}>域名查询</div>
            </div>
            <div className={styles.nav}>
                <div className={styles.left}>
                    {
                        navLeftData.domainActive.map(item => {
                            return (
                                <a
                                    key={item.link}
                                    className={item.class}
                                    href={item.link}
                                    target="_blank"
                                >
                                    {item.domain}<span>{item.price}</span>{item.text}
                                </a>
                            );
                        })
                    }
                    <a
                        className={styles.priceAll}
                        href={navLeftData.priceAll.link}
                        target="_blank"
                    >
                        {navLeftData.priceAll.text}
                        <div className={styles.arrowRight}></div>
                    </a>
                </div>
                <div className={styles.right}>
                    {
                        navRightData.map(item => {
                            if (item.text === '域名公告') {
                                return (
                                    <div className={styles.notice}>
                                        <img
                                            src={'https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/notice.svg'}
                                            width={14}
                                            height={14}
                                            alt="域名公告"
                                            title="域名公告"
                                        />
                                        <a
                                            key={item.link}
                                            href={item.link}
                                            target="_blank"
                                            title={item.text}
                                        >
                                            {item.text}
                                        </a>
                                    </div>
                                );
                            } else {
                                return (
                                    <a
                                        key={item.link}
                                        href={item.link}
                                        target="_blank"
                                        title={item.text}
                                    >
                                        {item.text}
                                    </a>
                                );
                            }
                        })
                    }
                </div>
            </div>
        </>
    );
};
