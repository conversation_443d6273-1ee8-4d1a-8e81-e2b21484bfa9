::-webkit-input-placeholder {
    opacity: 0.4;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222;
}
.search {
    position: relative;
    width: 980px;
    height: 52px;
    margin: 30px auto 0;
    background: #FFF;
    border-radius: 6px;
    display: flex;
    align-items: center;

    input {
        width: 660px;
        height: 52px;
        box-sizing: border-box;
        background: #FFF;
        border-radius: 6px 0 0 6px;
        border: none;
        outline: none;
        padding-left: 20px;
        font-size: 14px;
        color: #191A24;
    }

    .select {
        flex: 1;
        height: 22px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-left: 1px solid #ccc;
        line-height: 14px;
        padding: 10px;
        box-sizing: border-box;
        cursor: pointer;
        div {
            width: 80px;
            display: flex;
            justify-content: center;
            font-family: PingFangSC-Regular;
            font-size: 16px;
            color: #222;
            line-height: 22px;
        }

        .down-img {
            width: 4px;
            display: block;
            opacity: 0.4;
            flex-shrink: 0;
            transition: .2s all ease-out;
            transform: translateY(1px);
        }

        .select-container {
            width: 800px;
            height: 0;
            padding: 0;
            background: #FFF;
            box-shadow: 0 4px 6px 0 rgba(34,34,34,0.08);
            border-radius: 6px;
            position: absolute;
            top: 53px;
            right: 180px;
            z-index: 99;
            overflow-y: scroll;
            opacity: 1;
            transition: .2s all ease-in;
            box-sizing: border-box;
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;

            .domain-item {
                width: 110px;
                height: 34px;
                display: flex;
                box-sizing: border-box;
                padding: 20px;
                justify-content: flex-start;
                align-items: center;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                font-size: 14px;

                .displayTag {
                    font-size: 12px;
                    color: #F33E3E;
                    letter-spacing: 0;
                    line-height: 22px;
                    font-weight: 500;
                    margin-left: 4px;
                }
            }
        }
        .select-container::-webkit-scrollbar {
            -webkit-appearance: none;
            width: 7px;
        }
        .select-container::-webkit-scrollbar-thumb {
            border-radius: 4px;
            background-color: #ccc;
        }
    }
    .active {
        img {
            transform: rotate(180deg);
        }
        .select-container {
            height: 260px;
        }
    }
    .btn {
        background: #2468F2;
        border-radius: 0 6px 6px 0;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: #FFF;
        height: 52px;
        width: 180px;
        letter-spacing: 0;
        text-align: center;
        line-height: 52px;
        cursor: pointer;
        display: block;
    }

    .btn:hover {
        background-color: #528eff;
    }
}

.nav {
    width: 980px;
    height: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 12px auto 0;

    .left {
        flex: 3;
        flex-wrap: nowrap;
        display: flex;
        align-items: center;
        a {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #222;
            cursor: pointer;

            span {
                color: #FB5037;
            }
        }

        a:hover {
            color: #2468F2;
        }
        a:hover span {
            color: #2468F2;
        }

        .com {
            border-right: 1px solid #ccc;
        }

        .cn {
            margin-left: 9px;
        }

        .priceAll {
            margin-left: 24px;
            opacity: 0.7;
            display: flex;
            align-items: center;

            .arrowRight {
                display: block;
                width: 16px;
                height: 16px;
                background: url('https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/arrow-right.svg') no-repeat;
            }

        }

        .priceAll:hover {
            opacity: 1;
            color: #2468F2;
        }

        .priceAll:hover .arrowRight {
            background: url('https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/arrow-hover.svg') no-repeat;
        }
    }

    .right {
        flex: 2;
        display: flex;
        justify-content: flex-end;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #222;
        line-height: 24px;
        font-weight: 400;

        .notice {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        a {
            padding-left: 8px;
            color: rgba(34,34,34,.7);
        }

        a:hover {
            opacity: 1;
            color: #2468F2;
        }
    }
}
