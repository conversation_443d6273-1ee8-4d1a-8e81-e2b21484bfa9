/**
 * @file BCD域名官网首页
 * @desc 底部介绍模块
 */

import {ProductsBcdPurchase} from '@components/Products/Bcd/Purchase/Purchase';
import {defaultProductsBcdFooter} from '../defaultModel';
import styles from './footer.module.less';

const {guideData, cooperativePartner, goRegisterLink} = defaultProductsBcdFooter;
export const ProductsBcdFooter = () => {

    return (
        <>
            <div className={styles.guide}>
                <div className={styles.container}>
                    <h3>使用指南</h3>
                    <ul className={styles['list-container']}>
                        {
                            guideData.map(item => {
                                return (
                                    <li key={item.title} className={styles['list-item']}>
                                        <div className={styles.left}>
                                            <img src={item.imgSrc} width={26} height={22} alt={item.title} title={item.title} />
                                            <span>{item.title}</span>
                                        </div>
                                        <div className={styles.right}>
                                            {
                                                item.lists?.map(elem => {
                                                    return (
                                                        <a
                                                            target="_blank"
                                                            href={elem.link}
                                                            className={styles['do-link']}
                                                            key={elem.link}
                                                            title={elem.text}
                                                        >
                                                            {elem.text}
                                                        </a>
                                                    );
                                                })
                                            }
                                        </div>
                                    </li>
                                );
                            })
                        }
                    </ul>
                </div>
            </div>
            <ProductsBcdPurchase />
            <div className={styles['cooperative-partner']}>
                <div className={styles.container}>
                    <h3>合作伙伴</h3>
                    <div className={styles.bio}>百度智能云联合优秀合作伙伴，完善域名建设，共同加速域名发展</div>
                    <div className={styles['img-box']}>
                        {
                            cooperativePartner.map(item => {
                                return <img key={item} src={item} width={182} height={93} alt="合作伙伴" title="合作伙伴" />;
                            })
                        }
                    </div>
                </div>
            </div>
            <div className={styles['footer-banner']}>
                <div className={styles['footer-container']}>
                    <div className={styles['content-container']}>
                        <div>
                            <div className={styles['footer-title']}>一个域名，成就一个梦想</div>
                            <a className={styles['reg-btn']} href={goRegisterLink} target="_blank" title="注册域名">现在注册</a>
                        </div>

                        <video
                            playsInline
                            webkit-playsinline
                            style={
                                {
                                    height: '374px',
                                    position: 'absolute',
                                    left: '50%',
                                    bottom: '0',
                                    transform: 'translate(-50%, 0)',
                                }
                            }
                            autoPlay
                            loop
                            muted
                            src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/blue_bg.mp4"
                        />
                    </div>
                </div>
            </div>
        </>
    );
};
