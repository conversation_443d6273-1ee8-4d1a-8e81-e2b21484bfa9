.tranfer {
    width: 100%;
    padding: 60px 0 40px;
    background: #F6F8FB;

    h2 {
        font-family: PingFangSC-Medium;
        font-size: 28px;
        color: #222;
        line-height: 40px;
        text-align: center;
        font-weight: 500;
    }

    .bio {
        margin: 8px auto 0;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        text-align: center;
        line-height: 22px;
        font-weight: 400;

        .content {
            color: #222;
            opacity: 0.7;
        }

        .more {
            color: #2468F2;
            margin-left: 14px;
        }
    }

    ul {
        width: 1180px;
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        margin-top: 32px;
        justify-content: space-between;
        align-content: flex-start;

        li {
            width: 220px;
            height: 202px;
            background: #FFF;
            border-radius: 3px;
            box-sizing: border-box;
            padding: 20px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: .2s all ease-in;

            .title {
                font-family: PingFangSC-Medium;
                font-size: 26px;
                color: #222;
                letter-spacing: 0;
                line-height: 36px;
                font-weight: 500;
                overflow: hidden;
                white-space: normal;
                text-overflow: ellipsis;

                .spot {
                    color: #F33D3D;
                }
            }

            .time {
                height: 22px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: rgba(34,34,34,0.40);
                text-align: justify;
                line-height: 22px;
                font-weight: 400;
                margin: 12px 0 16px;
            }

            .type {
                height: 20px;
                border-radius: 2px;
                margin-bottom: 24px;
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #FFF;
                line-height: 20px;
                box-sizing: border-box;
                padding: 0 8px;
                display: inline-block;
            }

            .type-PREMIUM {
                background-image: linear-gradient(270deg, #4AA1FA 0%, #2468F2 100%);
            }

            .type-AGENT_DEMAND {
                background-image: linear-gradient(270deg, #4AA1FA 0%, #2468F2 100%);
            }

            .type-FIXED_PRICE {
                background-image: linear-gradient(270deg, #FA7070 0%, #F33D3D 100%);
            }

            .overtime-btn {
                width: 120px;
                height: 36px;
                border: 1px solid #CCC;
                border-radius: 18px;
                margin: 0 auto;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 14px;
                color: #222;
            }

            .overtime-btn:hover {
                background: #2468F2;
                color: #FFF;
                border: 0;
            }

            .price {
                font-family: PingFangSC-Medium;
                font-size: 28px;
                color: #FB5037;
                line-height: 28px;
                font-weight: 500;

                .logo {
                    font-size: 18px;
                }
            }
        }

        li:hover {
            transform: translateY(-12px);
            box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
        }
    }
}