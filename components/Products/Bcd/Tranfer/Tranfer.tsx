/**
 * @file BCD域名官网首页
 * @desc 域名交易模块
 */

import {useEffect, useState, useRef} from 'react';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import moment from 'moment';
import md5 from 'js-md5';
import Cookies from 'js-cookie';
import styles from './tranfer.module.less';

interface IframeParams {
    domains: string[];
}

interface SandboxMap {
    FIXED_PRICE: string;
    AGENT_DEMAND: string;
    PREMIUM: string;
}

interface DomainInfoItem {
    domain?: string;
    type?: string;
    price?: string;
    [key: string]: any;
}

const DomainTypeMap: any = {
    FIXED_PRICE: '一口价域名',
    AGENT_DEMAND: '委托购买',
    PREMIUM: '白金域名',
};

export const ProductsBcdTranfer = () => {

    const [tranRecommendList, setTranRecommendList] = useState([]);
    const [tranJumpLinkMap, setTranJumpLinkMap] = useState<SandboxMap>({FIXED_PRICE: '', AGENT_DEMAND: '', PREMIUM: ''});

    const deepCacheTranList = useRef([]);
    useEffect(() => {
        // 判断是否为沙盒地址
        const isSandbox = () => {
            return /(cloudtest)|(baidu-int)|(qasandbox)|(bcetest)/.test(location.origin);
        };

        const map = {
            FIXED_PRICE: `https://${isSandbox() ? 'cloudtest' : 'cloud'}.baidu.com/product/bcd/search.html#/fixed`,
            AGENT_DEMAND: `https://${isSandbox()
                ? 'qasandbox.bcetest' : 'console.bce'}.baidu.com/bcd/?track=BCD_PORTAL&clientFrom=CLOUD#/bcd/entrust`,
            PREMIUM: `https://${isSandbox() ? 'qasandbox.bcetest' : 'console.bce'}.baidu.com/bcd/?track=BCD_PORTAL&clientFrom=CLOUD#/bcd`,
        };
        setTranJumpLinkMap(map);
    }, []);

    const contraryReptile = (url: string) => {
        // 通过反爬验证timestamp
        const time = moment(new Date()).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        return time + '@' + md5('POST' + url + time + 'ieq%$jsaf23!@fkjwie');
    };

    // 统一域名类型字段
    const unifiedDomainType = (displayTag: string) => {
        if (['PREMIUM', '白金域名'].includes(displayTag)) {
            return 'PREMIUM';
        } else if (['FIXED_PRICE', '一口价域名'].includes(displayTag)) {
            return 'FIXED_PRICE';
        } else {
            return 'AGENT_DEMAND';
        }
    };

    // 拉取详情 更新列表
    const upDateTranList = (domainName: string, index: number, banTime?: string) => {
        const domainSplitArr = domainName?.split('.');
        const tld = domainSplitArr.pop();
        const domain = domainSplitArr.join('.');
        netService.post(
            urlConst.GET_BCD_PORTAL_DETAIL,
            {label: domain, tld: tld},
            {},
            {headers: {timestamp: contraryReptile('/open/domain/detail')}}
        ).then((detail: any) => {
            const tmpList = deepCacheTranList.current;
            detail.domain = detail.domainName;
            if (banTime) {
                detail.banTime = banTime;
            }

            if (detail.price) {
                detail.isOvertime = false;
                detail.displayTag = unifiedDomainType(detail.tradeType);
                detail.resquestResult = true;
            } else {
                detail.isOvertime = true;
            }

            tmpList[index] = {...tmpList[index], ...detail};
            deepCacheTranList.current = tmpList;

            setTranRecommendList(JSON.parse(JSON.stringify(deepCacheTranList.current)));
        });
    };
    useEffect(() => {
        (async () => {
            const res: any = await netService.post(
                urlConst.GET_BCD_PORTAL_TRAN,
                {},
                {},
                {headers: {timestamp: contraryReptile('/open/portal/recommend')}}
            );
            deepCacheTranList.current = JSON.parse(JSON.stringify(res.result));
            deepCacheTranList.current.forEach((detail: DomainInfoItem, index: number) => {
                if (detail.price) {
                    detail.isOvertime = false;
                    detail.displayTag = unifiedDomainType(detail.type);
                    detail.resquestResult = true;
                }
                upDateTranList(detail.domain, index, detail.banTime);
            });
            setTranRecommendList(JSON.parse(JSON.stringify(deepCacheTranList.current)));
        })();
    }, []); // eslint-disable-line

    // 拆分域名
    const splitDomain = (domain: string, tld?: boolean): string => {
        const splitDomainArr = domain?.split('.');
        if (tld) {
            return splitDomainArr?.pop();
        }
        splitDomainArr?.pop();
        return splitDomainArr?.join('.');
    };

    const isSandbox = () => {
        return /(localhost)|(qifu-sandbox)|(cloudtest)|(baidu-int)|(qasandbox)|(bcetest)/.test(location.origin);
    };

    const setData = (key: string, value: IframeParams) => {
        return new Promise(resolve => {
            const env = isSandbox() ? 'SANDBOX' : 'ONLINE'; // 环境变量
            bceStorage.init(env);
            bceStorage.set({
                key,
                payload: {
                    ...value,
                    timestamp: new Date().getTime() + 24 * 60 * 60 * 1000,
                },
                storage: 'localStorage',
            }).then(() => {
                resolve({key, value});
            });
        });
    };


    // 点击交易跳转
    const jumpTranDetail = (domainInfo: any, domainType: string) => {
        if (!domainInfo.resquestResult) {
            return;
        }
        const {type = '', domain = ''} = domainInfo;
        domainInfo.domainName = domain;
        domainInfo.tradeType = domainType || type;

        switch (domainType) {
            case 'FIXED_PRICE':
                window.open(`${tranJumpLinkMap.FIXED_PRICE}/detail?domain=${domainInfo.domain}&track=BCD_PORTAL&clientFrom=CLOUD`);
                break;
            case 'AGENT_DEMAND':
                window.open(`${tranJumpLinkMap.AGENT_DEMAND}/create~domain=${domainInfo.domain}`);
                break;
            case 'PREMIUM':
                setData(`search_purchase_data_${Cookies.get('accountId') || Cookies.get('bce-session').slice(0, 32)}`, {domains: [domainInfo]})
                    .then(() => {
                        window.open(`${tranJumpLinkMap.PREMIUM}/domain/register~campaignId=search_purchase_data`);
                    });
                break;
            default:
                window.open(`${tranJumpLinkMap.FIXED_PRICE}/detail?domain=${domainInfo.domain}&track=BCD_PORTAL&clientFrom=CLOUD`);
                break;
        }
    };

    // 超时请求
    const overtimeRequest = (info: any, index: number) => {
        if (!info.isOvertime) {
            return;
        }

        // 变更超时提示
        const tmpList = deepCacheTranList.current;
        tmpList[index].isOvertime = false;
        deepCacheTranList.current = tmpList;

        setTranRecommendList(JSON.parse(JSON.stringify(deepCacheTranList.current)));

        upDateTranList(info.domain, index);
    };

    // 显示时间
    const calcBanTime = (time: string) => {
        const expireTime = moment(time);
        const targetTime = moment('2050-01-01 00:00:00');

        if (time && expireTime.diff(targetTime, 'days') < 0) {
            return `出售到期时间：${time.split(' ')[0]}`;
        }
        return '出售到期时间：长期';
    };

    return (
        <div className={styles.tranfer}>
            <h2>域名交易</h2>
            <div className={styles.bio}>
                <span className={styles.content}>为买卖双方提供丰富、安全、高效、便捷的域名交易服务</span>
                <a
                    className={styles.more}
                    href={tranJumpLinkMap.FIXED_PRICE}
                    target="_blank"
                    title="查看更多"
                >
                    {'查看更多 >'}
                </a>
            </div>
            <ul>
                {
                    tranRecommendList.slice(0, 10).map((item, index) => {
                        const tranTypeBg = `type-${item?.displayTag}`;
                        return (
                            <li key={item?.domain} onClick={() => jumpTranDetail(item, item?.displayTag)}>
                                <div className={styles.title}>
                                    <span>{splitDomain(item?.domain)}</span>
                                    <span className={styles.spot}>.</span>
                                    <span>{splitDomain(item?.domain, true)}</span>
                                </div>

                                <div className={styles.time}>
                                    {
                                        item?.displayTag === 'FIXED_PRICE' ? calcBanTime(item?.banTime) : ''
                                    }
                                </div>
                                <div className={`${styles.type} ${styles[tranTypeBg]}`}>{DomainTypeMap[item?.displayTag]}</div>
                                {
                                    item?.resquestResult ? (
                                        <div className={styles.price}>
                                            <span className={styles.logo}>
                                                {
                                                    item?.displayTag === 'AGENT_DEMAND' ? '限时免费咨询' : '￥'
                                                }
                                            </span>
                                            <span className={styles.num}>
                                                {
                                                    item?.displayTag === 'AGENT_DEMAND' ? '' : item?.price || '--'
                                                }
                                            </span>
                                        </div>
                                    ) : (
                                        <div onClick={() => overtimeRequest(item, index)} className={styles['overtime-btn']}>
                                            {item?.isOvertime ? '超时重试' : '请求中...'}
                                        </div>
                                    )
                                }
                            </li>
                        );
                    })
                }
            </ul>
        </div>
    );
};

