import {useEffect, useRef} from 'react';
import {useOnMount} from '@baidu/bce-hooks';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import moment from 'moment';
import md5 from 'js-md5';
import {message} from 'antd';
import {hasLogin} from '@common/helper/page';
import styles from './casDiscountModal.module.less';

message.config({top: 180});

export default function CasDiscountModal() {
    const [messageApi, contextHolder] = message.useMessage();
    const hasLoginRef = useRef(false);

    const contraryReptile = (url: string) => {
        // 通过反爬验证timestamp
        const time = moment(new Date()).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
        return time + '@' + md5('POST' + url + time + 'ieq%$jsaf23!@fkjwie');
    };

    const getQueryValue = (name: string) => {
        const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
        const r = window.location.search.substr(1).match(reg)
            || window.location.hash.replace(/.*\?/, '').substr(0).match(reg);
        if (r != null) {
            return decodeURIComponent(r[2]);
        }
        return null;
    };

    const getQueryStr = (ori: string) => {
        const keys = ['trace', 'track', 'pageResource', 'pageSource', '_frm'];
        let result = '';
        for (const key of keys) {
            const val = getQueryValue(key);
            if (val) {
                result += `&${key}=${val}`;
            }
        }
        result += `&ori=${ori}`;
        return result.slice(1);
    };

    const jumpToBCDSearch = () => {
        window.open(`${window.location.origin}/product/bcd/search.html?${getQueryStr('dbp')}`, '_blank');
    };

    // 领取代金券
    const handleReceive = async (data: {id: string, url?: string}) => {
        try {
            const res = await netService.post(
                urlConst.POST_BCD_REVICE_CAS_COUPON,
                {activityUuid: 'ACT-2025-07-28-QIFU'},
                {},
                {
                    headers: {
                        timestamp: contraryReptile('/issue-coupon'),
                        'X-Request-By': 'RestClient',
                    },
                }
            );
            console.log(res);
            if (res.success) {
                /* eslint-disable */
                data && data.showSuccessTip && data.showSuccessTip(data.id, 'dbp');
                /* eslint-disable */
            }
        } catch (error) {
            console.log(error);
            if (error?.message?.redirect) {
                localStorage.removeItem('hideDiscountModal');
                window.location.href = error.message.redirect;
                return;
            }
            messageApi.open({
                type: 'error',
                content: error?.message?.global || '系统错误，请稍后重试',
                icon: <img
                    style={{width: '16px', height: '16px', margin: '0 8px 2px 0'}}
                    src="https://bce.bdstatic.com/p3m/common-service/uploads/toast_fail_31e6d1f.png"
                />,
            });
        }
    };

    // 获取活动信息
    const handleGetActivityInfo = async (data: {id: string, url?: string}) => {
        try {
            const res = await netService.get(
                urlConst.GET_BCD_ACTIVITY_INFO,
                {activityUuid: 'ACT-2025-07-28-QIFU'},
                {},
                {
                    headers: {
                        timestamp: contraryReptile('/info'),
                        'X-Request-By': 'RestClient',
                    }
                }
            );
            console.log(res);
            if (res.success) {
                // 活动进行中
                if (res?.result?.active) {
                    /* eslint-disable */
                    // 已领取过
                    if (res?.result?.couponInfoList?.[0]?.received) {
                        data && data.showSuccessTip && data.showSuccessTip(data.id, 'dbp');
                    } else {
                        handleReceive(data);
                    }
                    /* eslint-disable */
                } else {
                    messageApi.open({
                        type: 'warning',
                        content: '活动已结束',
                        icon: <img
                            style={{width: '16px', height: '16px', margin: '0 8px 2px 0'}}
                            src="https://bce.bdstatic.com/p3m/common-service/uploads/toast_fail_31e6d1f.png"
                        />,
                    });
                }
            }
        } catch (error) {
            console.log(error);
            if (error?.message?.redirect) {
                localStorage.removeItem('hideDiscountModal');
                window.location.href = error.message.redirect;
                return;
            }
            messageApi.open({
                type: 'error',
                content: error?.message?.global || '系统错误，请稍后重试',
                icon: <img
                    style={{width: '16px', height: '16px', margin: '0 8px 2px 0'}}
                    src="https://bce.bdstatic.com/p3m/common-service/uploads/toast_fail_31e6d1f.png"
                />,
            });
        }
    };

    // 点击域名卡片
    const handleClickBCD = async (data: {id: string, url?: string}) => {
        try {
            const res = await netService.post(
                urlConst.POST_BCD_IS_NEW_USER,
                {serviceTypeList: ['BCD']},
                {},
                {
                    headers: {
                        timestamp: contraryReptile('/isNewUser'),
                        'X-Request-By': 'RestClient',
                    }
                }
            );
            if (res.success) {
                if (res?.result?.isNewUser) {
                    jumpToBCDSearch();
                }
                else {
                    messageApi.open({
                        type: 'warning',
                        content: '此项优惠仅限新用户参与',
                        icon: <img
                            style={{width: '16px', height: '16px', margin: '0 8px 2px 0'}}
                            src="https://bce.bdstatic.com/p3m/common-service/uploads/toast_fail_31e6d1f.png"
                        />,
                    });
                }
            }
        } catch (error) {
            console.log(error);
            if (error?.message?.redirect) {
                localStorage.removeItem('hideDiscountModal');
                window.location.href = error.message.redirect;
                return;
            }
            messageApi.open({
                type: 'error',
                content: error?.message?.global || '系统错误，请稍后重试',
                icon: <img
                    style={{width: '16px', height: '16px', margin: '0 8px 2px 0'}}
                    src="https://bce.bdstatic.com/p3m/common-service/uploads/toast_fail_31e6d1f.png"
                />,
            });
        }
    };

    const isOnline = () => {
        return !/(localhost)|(qifu-sandbox)|(test)|(cloudtest)|(baidu-int)|(qasandbox)|(bcetest)/.test(
            window.location.origin,
        );
    };

    const loginLink = () => {
        return isOnline() ? 'https://login.bce.baidu.com/' : 'https://login.bcetest.baidu.com';
    };

    const handleClick = (data: {id: string, url?: string}) => {
        console.log(hasLoginRef.current);
        if (hasLoginRef.current) {
            if (['BUY', 'SSL_DV', 'SSL_OV_PRO', 'SSL_EV_PRO'].includes(data.id)) {
                handleGetActivityInfo(data);
            }
            else if (data.id === 'BCD_COM') {
                handleClickBCD(data);
            }
        } else {
            localStorage.removeItem('hideDiscountModal');
            const url = window.location.href;
            window.location.href = `${loginLink()}?redirect=${encodeURIComponent(url)}`;
        }
    };

    useEffect(() => {
        hasLogin().then((res) => {
            console.log('logined', res);
            hasLoginRef.current = true;
        }).catch((error) => {
            console.log('nologin', error);
            hasLoginRef.current = false;
        });
    }, []);

    useOnMount(() => {
        const isSandbox = /(localhost)|(cloudtest)|(baidu-int)|(qasandbox)|(bcetest)/.test(location.origin);
        const version = isSandbox ? '********' : '@latest';

        const now = new Date();
        now.setSeconds(0);
        now.setMilliseconds(0);

        const link = document.createElement('link');
        link.type = 'text/css';
        link.rel = 'stylesheet';
        link.href = `https://bce.bdstatic.com/lib/@baiducloud/qifu-sdk/${version}/iife/index.css?t=${Math.floor(now.getTime() / 1000)}`;
        document.getElementsByTagName('head')[0].appendChild(link);

        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.charset = 'utf-8';
        script.src = `https://bce.bdstatic.com/lib/@baiducloud/qifu-sdk/${version}/iife/index.js?t=${Math.floor(now.getTime() / 1000)}`;
        document.getElementsByTagName('head')[0].appendChild(script);
        script.onload = () => {
            if ((window as any).openQifuDiscountModal) {
                (window as any).openQifuDiscountModal({
                    isSandbox,
                    pageType: 'bcdPortal',
                    onActClick: handleClick,
                });
            }
        };
    });

    return <div className={styles['cas-discount-modal']}>{contextHolder}</div>;
}
