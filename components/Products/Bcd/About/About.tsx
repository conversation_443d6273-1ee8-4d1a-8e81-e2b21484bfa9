/**
 * @file BCD域名官网首页
 * @desc 关于我们模块
 */


import {defaultProductsBcdAbout} from '../defaultModel';
import styles from './about.module.less';

const {aboutData} = defaultProductsBcdAbout;

export const ProductsBcdAbout = () => {
    return (
        <div className={styles.about}>
            <h2>为什么选择我们？</h2>
            <div className={styles.bio}>为买卖双方提供丰富、安全、高效、便捷的域名交易服务</div>
            <div className={styles['about-container']}>
                {
                    aboutData.map(item => (
                        <div key={item.class}>
                            <div className={styles['item-' + item.class]} key={item.class}>
                                <div className={styles.title}>{item.title}</div>
                                <div className={styles.des}>
                                    {item.des1}
                                </div>
                                <div className={styles.des}>
                                    {item.des2}
                                </div>
                            </div>
                            <div className={styles['line-' + item.class]}></div>
                        </div>
                    ))
                }
                <div className={styles['img-box']}>
                    {
                        <video
                            playsInline
                            webkit-playsinline
                            style={
                                {
                                    width: '886px',
                                    position: 'absolute',
                                    left: '50%',
                                    top: '50%',
                                    transform: 'translate(-50%, -50%)',
                                }
                            }
                            autoPlay
                            loop
                            muted
                            src="https://bcd-public.cdn.bcebos.com/bcd_portal/20220211/server-func.mp4"
                        >

                        </video>
                    }
                </div>
            </div>
        </div>
    );
};
