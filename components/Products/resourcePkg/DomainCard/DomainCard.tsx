/**
 * @file BCD资源包card
 * /componets/Products/resourcePkg/DomainCard
 */

import React from 'react';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {formatFloat, hostMap} from '../../util';
import styles from './domainCard.module.less';


interface PkgDomain {
    packageId?: string;
    price?: number | string;
    validityPeriod?: number | string;
    [key: string]: any;
}

export default function PkgDomainCard(props: PkgDomain) {
    const {info} = props;
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const jumpPay: (pkgId: string) => void = pkgId => {
        const targetUrl = `${hostMap.getHost('console')}/bcd/?pageResource=resource_package/#/bcd/package/purchase~packageId=${pkgId}`;
        if (!userinfo?.hasLogin) {
            verifyHandler()
                .then(() => window.open(targetUrl))
                .catch(err => console.log(err));
        } else {
            window.open(targetUrl);
        }
    };

    return (
        <div className={styles.card}>
            <div className={styles.content}>
                <div className={styles.titleLine}>{info.title}</div>
                <div className={styles.descLine}>{info.description.split('-')[0]}</div>
                <div className={styles.timeLine}>
                    <span className={styles.successLogo}></span>
                    <span>有效期{info.validityPeriod}个月</span>
                </div>
                <div className={styles.discountLine}>
                    {formatFloat(info.price / ((info.price as number) + (info.savingPrice as number)), 1)}折
                </div>
                <div className={styles.priceLine}>
                    <div className={styles.priceLogo}>￥</div>
                    <div className={styles.price}>{info.price}</div>
                    <div className={styles.priceText}>元</div>
                    <div className={styles.saveLogo}>省</div>
                    <div className={styles.priceSave}>{info.savingPrice}元</div>
                </div>
            </div>
            <div className={styles.subBtn} onClick={() => jumpPay(info.packageId)}>立即抢购</div>
        </div>
    );
}
