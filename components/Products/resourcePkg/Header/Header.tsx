/**
 * @file BCD资源包Header
 * /componets/Products/resourcePkg/Header
 */

import React from 'react';
import styles from './header.module.less';

export default function PkgHeader() {
    return (
        <div className={styles.header}>
            <div className={styles.headerContainer}>
                <div className={styles.title}>域名专属优惠包重磅来袭</div>
                <div className={styles.desc}>针对域名主流后缀 多款品牌保护场景组合推出不同版本优惠包</div>
                <div className={styles.activeType}>优惠资源包</div>
                <div className={styles.activeDesc}>活动说明：用户一次性购买“优惠资源包”（仅限英文前缀），可以在有效期内多次使用，需在有效期内使用完,过期则无法继续使用.优惠资源包目前不支持代金券、折扣或返点支付,且一经售出,不退不换.</div>
            </div>
        </div>
    );
}
