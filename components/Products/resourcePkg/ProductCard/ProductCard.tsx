/**
 * @file BCD资源包card
 * /componets/Products/resourcePkg/ProductCard
 */

import React from 'react';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {formatFloat, hostMap} from '../../util';
import styles from './productCard.module.less';

interface calcTip {
    title: string;
    [key: string]: string;
}
interface PkgProduct {
    title?: string;
    description?: string;
    packageId?: string;
    price?: number | string;
    validityPeriod?: number | string;
    [key: string]: any;
}

export default function PkgProductCard(props: PkgProduct) {
    const {info} = props;
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const calcTip = (items: calcTip[]) => {
        return items.map(item => item.title.split('域名')[0]).join(' / ');
    };

    const jumpPay: (pkgId: string) => void = pkgId => {
        const targetUrl = `${hostMap.getHost('console')}/bcd/?pageResource=resource_package/#/bcd/package/purchase~packageId=${pkgId}`;
        if (!userinfo?.hasLogin) {
            verifyHandler()
                .then(() => window.open(targetUrl))
                .catch(err => console.log(err));
        } else {
            window.open(targetUrl);
        }
    };

    return (
        <div className={styles.card}>
            <div className={styles.pkgType}>{info.title}</div>
            <div className={styles.content}>
                <div className={styles.titleRow}>
                    <div className={styles.titleLine}>{info.description.split('-')[0]}</div>
                    <div className={styles.titleTip}>
                        <span style={{opacity: 0.7}}>
                            {calcTip(info.items)}
                        </span>
                    </div>
                </div>
                <div className={styles.descLine}>{info.description.split('-')[1]}</div>
                <div className={styles.timeLine}>
                    <span className={styles.successLogo}></span>
                    <span>有效期{info.validityPeriod}个月</span>
                </div>
                <div className={styles.discountLine}>{formatFloat(info.price / ((info.price as number) + (info.savingPrice as number)), 1)}折</div>
                <div className={styles.priceLine}>
                    <div className={styles.price}>{info.price}</div>
                    <div className={styles.priceText}>元</div>
                    <div className={styles.saveLogo}>省</div>
                    <div className={styles.priceSave}>{info.savingPrice}元</div>
                </div>
            </div>
            <div className={styles.subBtn} onClick={() => jumpPay(info.packageId)}>立即抢购</div>
        </div>
    );
}
