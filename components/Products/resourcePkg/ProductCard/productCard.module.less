.card {
    width: 280px;
    height: 266px;
    border-radius: 2px;
    position: relative;

    .pkgType {
        height: 20px;
        min-width: 100px;
        position: absolute;
        top: 0;
        right: 0;
        background: url('https://bcd-public.cdn.bcebos.com/bcd_portal/20220419/typeBg.png') no-repeat top right;
        background-size: 100px 20px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        color: #FFF;
        font-size: 12px;
        text-align: right;
        padding-right: 4px;
    }

    .content {
        height: 222px;
        background: url('https://bcd-public.cdn.bcebos.com/bcd_portal/20220419/mask.png') no-repeat top right;
        background-color: #FFF;
        background-size: 280px auto;
        padding: 26px 20px 0;
        border-radius: 2px 2px 0 0;

        .titleRow {
            margin-bottom: 8px;
            position: relative;
        }

        .titleRow:hover .titleTip {
            visibility: visible;
            opacity: 1;
        }

        .titleLine {
            color: #222;
            font-family: PingFang SC;
            font-size: 18px;
            line-height: 28px;
            letter-spacing: 0px;
            text-align: left;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .titleLine:hover {
            color: #2468F2;
        }

        .titleTip {
            position: absolute;
            z-index: 9;
            width: 290px;
            padding: 12px 16px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            background-color: #FFF;
            box-shadow: 0px 0px 6px 0px #dedede;
            color: #000;
            font-size: 14px;
            line-height: 20px;
            border-radius: 2px;
            transform: translate(-25px, 6px);
            visibility: hidden;
            opacity: 0;

            >span {
                width: 100%;
                text-align: start;
                word-wrap: break-word;
            }

            >span::after {
                content: '';
                display: block;
                width: 0;
                height: 0;
                border-top: 8px solid transparent;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-bottom: 6px solid #FFF;
                position: absolute;
                top: -14px;
                left: 50%;
                transform: translateX(-50%);
            }
        }

        .descLine {
            color: #222;
            font-family: PingFang SC;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0px;
            text-align: left;
            margin-bottom: 26px;
            opacity: 0.7;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .timeLine {
            color: #222;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 14px;
            line-height: normal;
            opacity: .7;
            letter-spacing: 0px;
            text-align: left;
            display: flex;
            align-items: center;
            margin-bottom: 24px;

            .successLogo {
                display: block;
                width: 14px;
                height: 10px;
                background: url('https://bcd-public.cdn.bcebos.com/bcd_portal/20220510/vector.png') no-repeat center/cover;
                margin-right: 6px;
            }
        }

        .discountLine {
            width: 40px;
            height: 20px;
            border: 1px solid #FF4A4A;
            border-radius: 2px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #FF4A4A;
            font-family: PingFang SC;
            font-size: 12px;
            margin-bottom: 12px;
        }

        .priceLine {
            display: flex;
            align-items: baseline;
            height: 32px;
            vertical-align: baseline;

            .priceLogo {
                color: #FF4A4A;
                font-family: PingFang SC;
                font-weight: 700;
                font-size: 14px;
            }

            .price {
                color: #FF4A4A;
                font-family: PingFang SC;
                font-size: 28px;
                font-weight: 500;
            }

            .priceText {
                color: #222;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 12px;
                letter-spacing: 0px;
                text-align: left;
                margin: 0 2px;

            }

            .saveLogo {
                display: block;
                width: 18px;
                height: 18px;
                background: #FF4A4A;
                color: #FFF;
                font-size: 12px;
                line-height: 18px;
                border-radius: 50%;
                text-align: center;
                margin: 0 4px;
            }

            .priceSave {
                opacity: 0.4;
                color: #222;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 12px;
                line-height: 22px;
            }
        }
    }

    .subBtn {
        height: 44px;
        background: linear-gradient(90.19deg, #FF8B58 0%, #FF3E47 100%);
        color: #FFF;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        border-radius: 0 0 2px 2px;
    }
}