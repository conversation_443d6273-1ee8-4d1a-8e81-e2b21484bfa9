/**
 * @file BLA 资质数字藏品活动页
 * @description 静态展示模块
 */

import {useState} from 'react';
import cx from 'classnames';
import {
    StaticComplianceReason,
    StaticQualification,
    StaticScene,
} from '../BlaDigitalConfig';
import styles from './static.module.less';

export const ProductsBlaDigitalStatic = () => {
    // const {datasource} = props;
    const [currentTab, setCurrentTab] = useState<number>(0);

    return (
        <div className={styles.StaticWrapper}>
            <section className={styles.SectionWrapper}>
                <header className={styles.header}>
                    <div className={styles.headerTitle}>NFT数藏业务为什么要合规运营</div>
                </header>
                <div id="bla_operation"></div>
                <div className={cx(styles.bodyCenter, styles.justifyBetween)}>
                    {StaticComplianceReason.map(item => (
                        <div
                            key={item.title}
                            className={cx(styles.bgImg, styles.transformItem, styles.complianceItem)}
                            style={{backgroundImage: `url(${item.imgUrl})`}}
                        >
                            <div className={styles.titleText}>{item.title}</div>
                            <div className={cx(styles.descText, styles.complianceItemDesc)}>{item.desc}</div>
                        </div>
                    ))}
                </div>
            </section>
            <section className={cx(styles.SectionWrapper, styles.gapBg)}>
                <header className={styles.header}>
                    <div className={styles.headerTitle}>NFT数藏平台合规运营需要哪些资质</div>
                </header>
                <div id="bla_qualification"></div>
                <div className={cx(styles.bodyCenter, styles.justifyBetween, styles.qualificationWrapper)}>
                    <div className={cx(styles.qualificationTabWrapper)}>
                        {StaticQualification.map((item, index) => (
                            <div
                                key={item.kind}
                                className={cx(styles.qualificationTabItem,
                                    currentTab === index ? styles.activeTab : styles.defaultTab
                                )}
                                onClick={() => setCurrentTab(index)}
                            >
                                {item.kind}
                            </div>
                        ))}
                    </div>
                    <div className={styles.qualificationContent}>
                        {StaticQualification[currentTab].qualifications.map(item => (
                            <div
                                key={item.title}
                                className={cx(styles.qualificationItem)}
                            >
                                <div className={cx(styles.titleText)}>{item.title}</div>
                                <div className={cx(styles.qualificationLabelWrapper)}>
                                    {item.labels.map(labelItem => (
                                        <div
                                            key={labelItem.name}
                                            className={cx(styles.qualificationLabelItem)}
                                            style={{width: labelItem.width}}
                                        >
                                            <div className={cx(styles.alignCenter)}>
                                                <div className={cx(styles.iconImg, styles.qualificationLabelNameIcon)}></div>
                                                <div className={cx(styles.titleText, styles.qualificationLabelNameText)}>{labelItem.name}</div>
                                            </div>
                                            <div className={cx(styles.descText, styles.qualificationLabelDesc)}>{labelItem.desc}</div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>
            <section className={styles.SectionWrapper}>
                <header className={styles.header}>
                    <div className={styles.headerTitle}>NFT数藏平台合规解决方案</div>
                </header>
                <div id="bla_solution"></div>
                <div className={cx(styles.bodyCenter, styles.bgImg, styles.solution)}></div>
            </section>
            <section className={cx(styles.SectionWrapper, styles.gapBg)}>
                <header className={styles.header}>
                    <div className={styles.headerTitle}>NFT数藏平台资质适用场景</div>
                </header>
                <div id="bla_applicable_scene"></div>
                <div className={cx(styles.bodyCenter, styles.justifyBetween)}>
                    {StaticScene.map(item => (
                        <div
                            key={item.title}
                            className={cx(styles.transformItem, styles.sceneItem)}
                        >
                            <div
                                className={cx(styles.iconImg, styles.sceneLogo)}
                                style={{backgroundImage: `url(${item.imgUrl})`}}
                            >
                            </div>
                            <div className={styles.titleText}>{item.title}</div>
                        </div>
                    ))}
                </div>
            </section>
        </div>
    );
};
