.SectionWrapper {
    padding: 52px 0;
}

.bodyCenter {
    width: 1180px;
    margin: 0 auto;
}

.justifyBetween {
    display: flex;
    justify-content: space-between;
}

.bgImg {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.iconImg {
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
}

.transformItem {
    transform: translateY(0);
    transition: transform .3s ease;
}

.transformItem:hover {
    transform: translateY(-12px);
}

.titleText {
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #222222;
    letter-spacing: 0;
    line-height: 28px;
    font-weight: 500;
}

.descText {
    opacity: 0.7;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
}

.gapBg {
    background: #F6F8FB;
}

.alignCenter {
    display: flex;
    align-items: center;
}

.header {
    margin-bottom: 32px;
    text-align: center;
}

.headerTitle {
    font-family: PingFangSC-Semibold;
    font-size: 28px;
    color: #222222;
    letter-spacing: 0;
    text-align: center;
    line-height: 28px;
    font-weight: 600;
}

// 运营合规
.complianceItem {
    width: 380px;
    height: 200px;
    padding: 28px;
}

.complianceItemTitle {
    margin-top: 16px;
}

// 资质类型
.qualificationWrapper {
    align-items: flex-start;
}

.qualificationTabWrapper {
    width: 220px;
}

.qualificationTabItem {
    width: 100%;
    height: 52px;
    line-height: 52px;
    border-radius: 4px;
    margin-bottom: 16px;
    padding-left: 40px;
    font-family: PingFangSC-Regular;
    font-size: 18px;
    font-weight: 400;
    cursor: pointer;
}

.activeTab {
    background: #2469F2;
    color: #FFFFFF;
}

.defaultTab {
    background: #E6EEFF;
    color: #2469F2;
}

.qualificationContent {
    width: 940px;
}

.qualificationItem {
    padding: 24px;
    margin-bottom: 20px;
    background: #FFFFFF;
    border-radius: 4px;
}

.qualificationItem:last-child {
    margin-bottom: 0;
}

.qualificationLabelWrapper {
    display: flex;
    flex-wrap: wrap;
}

.qualificationLabelItem {
    margin-top: 16px;
}

.qualificationLabelNameIcon {
    width: 14px;
    height: 14px;
    background-image: url('https://cbs-public.bj.bcebos.com/portal/20220617/label-checked.png');
}

.qualificationLabelNameText {
    font-size: 14px;
    line-height: 24px;
    margin-left: 8px;
}

.qualificationLabelDesc {
    margin-top: 8px;
}

.solution {
    height: 590px;
    background-image: url('https://cbs-public.cdn.bcebos.com/bla/20220831/solution.png');
}

// 适用场景
.sceneItem {
    width: 280px;
    height: 196px;
    background: #FFFFFF;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.sceneLogo {
    width: 88px;
    height: 88px;
    margin-bottom: 16px;
}