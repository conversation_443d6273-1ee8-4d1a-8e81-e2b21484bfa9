/**
 * @file BLA 资质数字藏品活动页
 * @description 表单咨询弹窗
 */

import cx from 'classnames';
import {useRef} from 'react';
import {Modal} from 'antd';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {contraryReptile} from '@components/Products/Bla/config';
import {ProductsBlaDigitalForm} from '../BlaDigitalForm/BlaDigitalForm';
import styles from './consult.module.less';

interface ProductsBlaDigitalConsultModalProps {
    open?: boolean;
    onSuccess?: (value: string) => void;
    onCancel?: (value: boolean) => void;
}

export const ProductsBlaDigitalConsultModal = (props: ProductsBlaDigitalConsultModalProps) => {
    const {open, onSuccess, onCancel} = props;
    const childRef = useRef();

    const onClose = () => {
        (childRef.current as any).resetForm();
        onCancel && onCancel(false);
    };

    const onSubmit = (params: any) => {
        const url = urlConst.GET_BLA_SUBMIT_DEMAND;
        return netService.post(
            url,
            params,
            {},
            {headers: {timestamp: contraryReptile('/open/portal/recommend')}}
        ).then(() => {
            onCancel && onCancel(false);
            onSuccess && onSuccess('提交成功！服务专家会在工作时间尽快与您取得联系！');
        });
    };

    return (
        <Modal
            wrapClassName={styles.consultWrapClassName}
            visible={open}
            closable={false}
            footer={null}
            width="472px"
            centered
            onCancel={onClose}
        >
            <div className={styles.consultTitle}>咨询资质办理</div>
            <div className={cx(styles.iconBg, styles.closeIcon)} onClick={onClose}></div>
            <ProductsBlaDigitalForm
                ref={childRef}
                onSubmit={onSubmit}
                onCancel={onClose}
                showLabel
            />
        </Modal>
    );
};
