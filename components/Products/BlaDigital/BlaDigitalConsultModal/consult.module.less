.consultWrapClassName {
    :global {
        .ant-modal-mask {
            background: rgba(34, 34, 34, .5);
        }
        .ant-modal-content {
            border-radius: 6px;
        }
        .ant-modal-body {
            padding: 24px 28px;
        }
    }
}

.consultTitle {
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #222222;
    letter-spacing: 0;
    line-height: 28px;
    font-weight: 500;
    margin-bottom: 4px;
}

.iconBg {
    background-repeat: no-repeat;
    background-size: cover;
}

.closeIcon {
    position: absolute;
    top: 32px;
    right: 28px;
    width: 10px;
    height: 10px;
    background-image: url('https://cbs-public.cdn.bcebos.com/portal/20220330/close.png');
    cursor: pointer;
}

.customCloseIcon {
    top: 16px;
    right: 16px;
}

.consultDesc {
    margin-top: 16px;
    opacity: 0.7;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    line-height: 14px;
    font-weight: 400;
    text-align: center;
}
