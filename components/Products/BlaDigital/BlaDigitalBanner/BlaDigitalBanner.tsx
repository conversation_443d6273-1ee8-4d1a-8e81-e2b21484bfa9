/**
 * @file BLA 资质数字藏品活动页
 * @description Banner轮播
 */

import cx from 'classnames';
import {useCallback, useEffect, useState} from 'react';
import styles from './banner.module.less';

export interface BannerItem {
    imgUrl: string;
    link?: string;
}

interface BannerProps {
    datasource?: BannerItem[];
}

let timer: any = null;

export const ProductsBlaDigitalBanner = (props: BannerProps) => {
    const {datasource} = props;
    const [current, setCurrent] = useState<number>(1);

    // 自动播放
    const autoSlide = useCallback(() => {
        const totalCount = datasource.length;
        timer && clearTimeout(timer);
        setCurrent((current + 1) % totalCount);
    }, [datasource, current]);

    useEffect(() => {
        timer = setTimeout(() => {
            autoSlide();
        }, 3000);
        return () => clearTimeout(timer);
    }, [autoSlide]);

    return (
        <div className={styles.BannerWrapper}>
            <div className={cx(styles.BannerContent, styles.defaultBanner)}>
                {datasource.map((item, index) => (
                    <a
                        className={cx(styles.BannerItem)}
                        key={item.imgUrl}
                        style={{
                            backgroundImage: `url(${item.imgUrl})`,
                            display: current === index ? 'block' : 'none',
                        }}
                        href={item.link}
                        target="_blank"
                    >
                    </a>
                ))}
            </div>
            <div className={styles.BannerPagers}>
                {datasource.length > 1 && datasource.map((item, index) => (
                    <div
                        className={cx(styles.BannerPagerItem, current === index ? styles.pagerActive : '')}
                        key={item.imgUrl}
                        onClick={() => setCurrent(index)}
                    >
                    </div>
                ))}
            </div>
        </div>
    );
};
