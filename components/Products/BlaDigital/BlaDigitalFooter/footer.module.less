.FooterWrapper {
    width: 100%;
    height: 240px;
    background-image: url('https://cbs-public.cdn.bcebos.com/bla/20220826/footer-bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.title {
    padding-top: 60px;
    font-family: PingFangSC-Semibold;
    font-size: 28px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 38px;
    font-weight: 600;
}

.buttonWrapper {
    margin-top: 40px;
    display: flex;
    justify-content: center;
}

.defaultButton {
    width: 168px;
    height: 40px;
    line-height: 38px;
    border: 1px solid #FFFFFF;
    border-radius: 4px;
    margin-right: 40px;
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #FFFFFF;
    text-align: center;
    font-weight: 500;
    cursor: pointer;
}

.defaultButton:hover {
    background: #FFFFFF;
    color: #2469F3;
}

.defaultButton:last-child {
    margin-right: 0;
}