/**
 * @file BLA 资质数字藏品活动页
 * @description Footer背景留咨
 */

import cx from 'classnames';
import styles from './footer.module.less';

interface FooterProps {
    toConsult: () => void;
}

export const ProductsBlaDigitalFooter = (props: FooterProps) => {
    const {toConsult} = props;

    return (
        <div className={styles.FooterWrapper}>
            <div className={styles.title}>如何快速构建数字藏品/权益电商平台</div>
            <div className={styles.buttonWrapper}>
                <div
                    className={cx(styles.defaultButton)}
                    onClick={() => toConsult()}
                >
                    资质办理咨询
                </div>
                <a
                    className={cx(styles.defaultButton)}
                    href="https://juxiao.baidu.com/"
                    target="_blank"
                >
                    平台建设方案详情
                </a>
            </div>
        </div>
    );
};
