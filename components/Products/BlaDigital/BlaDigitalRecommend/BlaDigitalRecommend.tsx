/**
 * @file BLA 资质数字藏品活动页
 * @description 数字藏品业务资质相关产品
 */

import cx from 'classnames';
import {Pricecards} from '@baidu/bce-portal-ui';
import {PricecardItem} from '@components/Products/Bla/Recommend/Recommend';
import styles from './recommend.module.less';

interface ProductsBlaRecommendProps {
    datasource?: PricecardItem[];
    link?: any;
    onConsult?: (item: any) => void;
    onBuy?: (item: any) => void;
}


export const ProductsBlaDigitalRecommend = (props: ProductsBlaRecommendProps) => {
    const {datasource, link, onConsult, onBuy} = props;

    return (
        <div className={styles.recommendWrapper}>
            <header className={styles.header}>
                <div className={styles.headerTitle}>数字藏品业务资质相关产品</div>
                <a
                    className={cx(styles.defaultText, styles.linkText)}
                    href={link.bla}
                    target="_blank"
                >
                    查看全部资质
                </a>
            </header>
            <div id="bla_related_products"></div>
            <div className={cx(styles.qualificationContainer)}>
                <Pricecards
                    cardClassName={styles.cardItem}
                    headerClassName={styles.headerBg}
                    labelClassName={styles.labelClassName}
                    datasource={datasource}
                    labelIcon="https://cbs-public.bj.bcebos.com/portal/20220617/label-checked.png"
                    onConsult={onConsult}
                    onBuy={onBuy}
                />
            </div>
        </div>
    );
};
