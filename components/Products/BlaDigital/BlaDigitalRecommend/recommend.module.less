.recommendWrapper {
    width: 1180px;
    margin: 0 auto;
    padding: 52px 0;
}

.header {
    text-align: center;
}

.headerTitle {
    font-family: PingFangSC-Semibold;
    font-size: 28px;
    color: #222222;
    text-align: center;
    line-height: 28px;
    font-weight: 600;
}

.defaultText {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    letter-spacing: 0;
    text-align: center;
    line-height: 24px;
    font-weight: 400;
}

.linkText {
    color: #2469F2;
    margin-top: 12px;
    margin-bottom: 32px;
    cursor: pointer;
}

.cardItem {
    background-image: url('https://cbs-public.cdn.bcebos.com/bla/20220831/carditem-bg.png');
    background-size: cover;
    background-position: right top;
    border: none;
    border-radius: 4px;
}

.headerBg {
    width: 100%;
    min-height: 0;
    padding-bottom: 8px;
}

.labelClassName {
    border-top: none !important;
}