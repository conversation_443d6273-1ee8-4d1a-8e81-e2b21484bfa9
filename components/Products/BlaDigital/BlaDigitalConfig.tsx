/**
 * @file BLA数藏活动页(全局静态数据)
 * /components/Products/BlaDigital/config.tsx
 */

export const Rules = {
    phone: [
        () => ({
            validator(_: any, value: any) {
                if (!value) {
                    return Promise.reject(new Error('请输入手机号'));
                }
                if (!/^1\d{10}$/.test(value)) {
                    return Promise.reject(new Error('请输入正确的手机号'));
                }
                return Promise.resolve();
            },
        }),
    ],
    smsCode: [
        () => ({
            validator(_: any, value: any) {
                if (!value) {
                    return Promise.reject(new Error('请输入验证码'));
                }
                if (!/\d{6}$/.test(value)) {
                    return Promise.reject(new Error('请输入正确的验证码'));
                }
                return Promise.resolve();
            },
        }),
    ],
};

export const FormType = [
    {
        title: '咨询资质办理',
        key: 'BLOCK_CHAIN',
    },
    {
        title: '咨询数藏平台搭建',
        key: 'NFT_PLAT',
    },
];

export const FormConfig = {
    phone: {
        label: '手机号',
        wrapperCol: {},
        placeholder: '请填写您的手机号',
    },
    smsCode: {
        label: '验证码',
        wrapperCol: {},
        placeholder: '请填写您的验证码',
    },
    content: {
        label: '备注',
        wrapperCol: {},
        placeholder: '请填写您的备注',
    },
};

export const FormNoLabelConfig = {
    phone: {
        label: '',
        wrapperCol: {offset: 0, span: 24},
        placeholder: '手机号',
    },
    smsCode: {
        label: '',
        wrapperCol: {offset: 0, span: 24},
        placeholder: '验证码',
    },
    content: {
        label: '',
        wrapperCol: {offset: 0, span: 24},
        placeholder: '备注',
    },
};

export const StaticComplianceReason = [
    {
        title: '藏品质量问题',
        desc: '平台藏品质量低劣，内容可能存在剽窃，缺乏原创性，容易造成版权纠纷，影响平台业务。',
        imgUrl: 'https://cbs-public.cdn.bcebos.com/bla/20220826/reason-1.png',
    },
    {
        title: '二级市场炒作乱象',
        desc: '二级市场允许多次转卖，一定程度上导致藏品价格飙升，容易被市场或政府监管。',
        imgUrl: 'https://cbs-public.cdn.bcebos.com/bla/20220826/reason-2.png',
    },
    {
        title: '数藏产业合规要求',
        desc: '数字藏品市场迅速爆发，侵权案件频发，合规化已是必然趋势，国家对该产业也更为关注。',
        imgUrl: 'https://cbs-public.cdn.bcebos.com/bla/20220826/reason-3.png',
    },
];

export const StaticQualification = [
    {
        kind: '区块链备案',
        qualifications: [
            {
                title: '区块链信息服务备案',
                labels: [
                    {
                        name: '涉及业务模式',
                        desc: '数字藏品平台都是应用了区块链技术，需要办理区块链备案。',
                        width: '100%',
                    },
                    {
                        name: '主管部门',
                        desc: '国家互联网信息办公室',
                        width: '33.3%',
                    },
                    {
                        name: '申请时间',
                        desc: '开展业务之日起10个人工作日内',
                        width: '33.3%',
                    },
                    {
                        name: '备注',
                        desc: '必须',
                        width: '33.3%',
                    },
                ],
            },
        ],
    },
    {
        kind: '经营许可证',
        qualifications: [
            {
                title: '网络文化经营许可证',
                labels: [
                    {
                        name: '涉及业务模式',
                        desc: 'NFT平台涉及一些博物馆藏品和著名艺术家作品，以及音乐、绘画等，通常需要具有网络文化经营许可证。',
                        width: '100%',
                    },
                    {
                        name: '主管部门',
                        desc: '文化和旅游厅',
                        width: '33.3%',
                    },
                    {
                        name: '申请时间',
                        desc: '开展业务之前',
                        width: '33.3%',
                    },
                    {
                        name: '备注',
                        desc: '必须',
                        width: '33.3%',
                    },
                ],
            },
            {
                title: '增值电信业务经营许可证',
                labels: [
                    {
                        name: '涉及业务模式',
                        desc: '平台代替用户作者发布藏品，作者发布藏品，属于互联网信息服务，需要办理增值电信业务许可证ICP。平台上的作品涉及第三方用户挂售，需要办理EDI许可证。',
                        width: '100%',
                    },
                    {
                        name: '主管部门',
                        desc: '省级通信管理局',
                        width: '33.3%',
                    },
                    {
                        name: '申请时间',
                        desc: '开展业务之前',
                        width: '33.3%',
                    },
                    {
                        name: '备注',
                        desc: '必须',
                        width: '33.3%',
                    },
                ],
            },
        ],
    },
    {
        kind: '备案相关',
        qualifications: [
            {
                title: '艺术品经营活动备案',
                labels: [
                    {
                        name: '涉及业务模式',
                        desc: '企业从事艺术品销售等，需要办理艺术品备案。',
                        width: '100%',
                    },
                    {
                        name: '主管部门',
                        desc: '区文化和旅游局',
                        width: '33.3%',
                    },
                    {
                        name: '申请时间',
                        desc: '开展涉艺术品NFT业务之前',
                        width: '33.3%',
                    },
                    {
                        name: '备注',
                        desc: '必须',
                        width: '33.3%',
                    },
                ],
            },
            {
                title: '等保二级或等保三级备案',
                labels: [
                    {
                        name: '涉及业务模式',
                        desc: '等保是网络安全测评级别，避免平台泄露用户隐私信息。达到一定用户数量的平台获取用户的身份证实名信息才需要办理。',
                        width: '100%',
                    },
                    {
                        name: '主管部门',
                        desc: '公司所在地网安大队',
                        width: '33.3%',
                    },
                    {
                        name: '申请时间',
                        desc: '销售NFT之前',
                        width: '33.3%',
                    },
                    {
                        name: '备注',
                        desc: '非必须',
                        width: '33.3%',
                    },
                ],
            },
        ],
    },
    {
        kind: '拍卖许可证',
        qualifications: [
            {
                title: '拍卖经营批准证书',
                labels: [
                    {
                        name: '涉及业务模式',
                        desc: '如果平台上的藏品有拍卖形式销售的，就需要办理该证书。',
                        width: '100%',
                    },
                    {
                        name: '主管部门',
                        desc: '省级商务厅',
                        width: '33.3%',
                    },
                    {
                        name: '申请时间',
                        desc: '开展拍卖业务之前',
                        width: '33.3%',
                    },
                    {
                        name: '备注',
                        desc: '非必须',
                        width: '33.3%',
                    },
                ],
            },
        ],
    },
];

export const StaticScene = [
    {
        title: 'IP/藏品确权',
        imgUrl: 'https://cbs-public.cdn.bcebos.com/bla/20220826/scene-1.png',
    },
    {
        title: '铸造服务、nft/nfr/nfg上链',
        imgUrl: 'https://cbs-public.cdn.bcebos.com/bla/20220826/scene-2.png',
    },
    {
        title: '发行业务、ios/安卓上架',
        imgUrl: 'https://cbs-public.cdn.bcebos.com/bla/20220826/scene-3.png',
    },
    {
        title: '数字藏品拍卖业务',
        imgUrl: 'https://cbs-public.cdn.bcebos.com/bla/20220826/scene-4.png',
    },
];

export const TopAnchors = [
    {
        label: '合规运营',
        id: 'bla_operation',
    },
    {
        label: '哪些资质',
        id: 'bla_qualification',
    },
    {
        label: '解决方案',
        id: 'bla_solution',
    },
    {
        label: '适用场景',
        id: 'bla_applicable_scene',
    },
    {
        label: '相关产品',
        id: 'bla_related_products',
    },
    {
        label: '申请流程',
        id: 'bla_application_flow',
    },
    {
        label: '服务优势',
        id: 'bla_advantage_service',
    },
    {
        label: '感兴趣产品',
        id: 'bla_interested_product',
    },
];
