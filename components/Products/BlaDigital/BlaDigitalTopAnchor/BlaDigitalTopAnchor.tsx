/**
 * @file BLA 资质数字藏品活动页
 * @description 吸顶锚点
 */

import cx from 'classnames';
import {throttle} from 'lodash';
import {useCallback, useEffect, useState} from 'react';
import {scrollToAnchor} from '../../Bla/config';
import styles from './topanchor.module.less';

interface TopAnchorItem {
    label: string;
    id: string;
}

export interface TopAnchorProps {
    anchors: TopAnchorItem[];
    firstAnchor: string;
    rightBtnText?: string;
    onConsult?: () => void;
}

export const ProductsBlaDigitalTopAnchor = (props: TopAnchorProps) => {
    const {anchors, firstAnchor, rightBtnText, onConsult} = props;
    const [show, setShow] = useState<boolean>(false);
    const [activeAnchor, setActiveAnchor] = useState<string>(firstAnchor);

    const onClickRightBtn = () => {
        onConsult && onConsult();
    };

    const onAnchor = (id: string) => {
        scrollToAnchor(id, true);
    };

    const onScrollChange = useCallback(() => {
        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
        const clientHeight = document.documentElement.clientHeight || document.body.clientHeight;

        if (scrollTop > clientHeight) {
            setShow(true);

            let activeItem = firstAnchor;
            anchors.forEach(item => {
                const dom = document.getElementById(item.id);
                if (scrollTop + (clientHeight / 2) >= dom.offsetTop) {
                    activeItem = item.id;
                }
            });
            setActiveAnchor(activeItem);
        }
        else {
            setShow(false);
        }

    }, [anchors, firstAnchor]);

    useEffect(() => {
        // 滚动条滚动时触发
        window.addEventListener('scroll', throttle(onScrollChange, 200), true);
        onScrollChange();
        return () => {
            window.removeEventListener('scroll', onScrollChange, false);
        };
    }, [onScrollChange]);

    return (
        <div className={cx(styles.topAnchorWrapper, show ? styles.showTopAnchor : '')}>
            <div className={cx(styles.bodyCenter, styles.alignCenter)}>
                {anchors.map(item => (
                    <div
                        key={item.id}
                        className={cx(styles.defaultText, styles.anchorItem,
                            item.id === activeAnchor ? styles.activeAnchor : styles.defaultAnchor
                        )}
                        onClick={() => onAnchor(item.id)}
                    >
                        {item.label}
                    </div>
                ))}
                <div
                    className={cx(styles.leftAuto, styles.defaultText, styles.consultBtn)}
                    onClick={() => onClickRightBtn()}
                >
                    {rightBtnText}
                </div>
            </div>
        </div>
    );
};
