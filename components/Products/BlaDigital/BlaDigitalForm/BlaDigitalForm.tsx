/**
 * @file BLA 资质数字藏品活动页
 * @description 留咨表单
 */

import React, {useEffect, useImperativeHandle, useState} from 'react';
import cx from 'classnames';
import {Form, Input, Button, Checkbox} from 'antd';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {contraryReptile, getTrackValueFromUrl} from '@components/Products/Bla/config';
import {FormConfig, FormNoLabelConfig, FormType, Rules} from '../BlaDigitalConfig';
import styles from './form.module.less';

interface ProductsBlaDigitalFormProps {
    showLabel: boolean;
    onSuccess?: (msg: string) => void;
    onCancel?: () => void;
    onSubmit?: (values: any) => Promise<any>;
}

export const ProductsBlaDigitalForm = React.forwardRef((props: ProductsBlaDigitalFormProps, ref) => {
    const {showLabel, onSuccess, onCancel, onSubmit} = props;
    const [form] = Form.useForm();
    const [countdown, setCountdown] = useState<number>(60);
    const [checked, setChecked] = useState<boolean>(false);
    const [formTab, setFormTab] = useState<string>('BLOCK_CHAIN');
    const formConfig = showLabel ? FormConfig : FormNoLabelConfig;

    useImperativeHandle(ref, () => ({
        resetForm: () => {
            form.resetFields();
            setChecked(false);
            setCountdown(60);
        },
    }));

    const onFinish = async (values: any) => {
        const url = urlConst.GET_BLA_SUBMIT_DEMAND;
        const trackValue = getTrackValueFromUrl();
        const params = {
            type: showLabel ? 'BLOCK_CHAIN' : formTab,
            area: 'QUANGUO',
            content: values?.content,
            contactNumber: values.phone,
            verificationCode: values.smsCode,
            requestSource: 'bla_nft_portal',
            source: trackValue,
        };
        if (onSubmit) {
            await onSubmit(params);
        }
        else {
            await netService.post(
                url,
                params,
                {},
                {headers: {timestamp: contraryReptile('/open/portal/recommend')}}
            );
            onSuccess && onSuccess('提交成功！服务专家会在工作时间尽快与您取得联系！');
        }
        form.resetFields();
        setChecked(false);
        setCountdown(60);
    };

    const onChecked = () => {
        setChecked(!checked);
    };

    const sendVerifyCode = () => {
        const url = urlConst.GET_BLA_SEND_SMSCODE;
        const params = {
            contactNumber: form.getFieldValue('phone'),
        };
        netService.post(
            url,
            params,
            {},
            {headers: {timestamp: contraryReptile('/open/portal/recommend')}}
        );
    };

    const getSmsCode = async () => {
        await form.validateFields(['phone']);
        if (countdown < 60) {
            return;
        }
        setCountdown(59);
        sendVerifyCode();
    };

    useEffect(() => {
        const timer = setTimeout(() => {
            if (countdown < 60) {
                setCountdown(countdown > 0 ? countdown - 1 : 60);
            }
        }, 1000);
        return () => clearTimeout(timer);
    }, [countdown]);

    return (
        <div className={cx(showLabel ? styles.NoLabelFormWrapper : styles.FormWrapper)}>
            {!showLabel && (
                <div className={cx(styles.justifyBetween)}>
                    {FormType.map(item => (
                        <div
                            className={cx(styles.FormTab, formTab === item.key ? styles.FormTabItemActive : styles.FormTabItem)}
                            key={item.key}
                            onClick={() => setFormTab(item.key)}
                        >
                            {item.title}
                        </div>
                    ))}
                </div>
            )}
            <Form
                form={form}
                name="basic"
                autoComplete="off"
                labelAlign="left"
                labelCol={{span: 4}}
                wrapperCol={{span: 20}}
                className={styles.form}
                onFinish={onFinish}
            >
                <Form.Item
                    name="phone"
                    label={formConfig.phone.label}
                    wrapperCol={formConfig.phone.wrapperCol}
                    rules={Rules.phone}
                    required
                >
                    <div className={styles.phoneWrapper}>
                        <Input
                            maxLength={11}
                            placeholder={formConfig.phone.placeholder}
                        />
                        <div
                            className={cx(styles.smsBtn, countdown < 60 ? styles.disabledBtn : '')}
                            onClick={() => {
                                getSmsCode();
                            }}
                        >
                            {countdown < 60 ? `重新获取${countdown}s` : '获取验证码'}
                        </div>
                    </div>
                </Form.Item>
                <Form.Item
                    name="smsCode"
                    label={formConfig.smsCode.label}
                    wrapperCol={formConfig.smsCode.wrapperCol}
                    rules={Rules.smsCode}
                    required
                >
                    <Input
                        maxLength={6}
                        placeholder={formConfig.smsCode.placeholder}
                    />
                </Form.Item>
                <Form.Item
                    name="content"
                    label={formConfig.content.label}
                    wrapperCol={formConfig.content.wrapperCol}
                >
                    <Input placeholder={formConfig.content.placeholder} />
                </Form.Item>
                <Form.Item wrapperCol={{offset: 0, span: 24}}>
                    <div className={styles.checkbox}>
                        <Checkbox checked={checked} onChange={onChecked}>
                            我已阅读并同意百度智能云
                        </Checkbox>
                        <a
                            href="https://cloud.baidu.com/doc/Agreements/s/Kjwvy245m"
                            target="_blank"
                            className={styles.protocolText}
                        >
                            《隐私保护政策》
                        </a>
                    </div>
                    {showLabel ? (
                        <div className={styles.justifyEnd}>
                            <Button
                                className={cx(styles.submitBtn, styles.showLabelBtn, styles.cancelBtn)}
                                onClick={() => onCancel()}
                            >
                                取消
                            </Button>
                            <Button
                                className={cx(styles.submitBtn, styles.showLabelBtn, styles.primaryBtn)}
                                disabled={!checked}
                                htmlType="submit"
                            >
                                咨询
                            </Button>
                        </div>
                    ) : (
                        <Button
                            className={cx(styles.submitBtn, styles.noLabelBtn, styles.primaryBtn)}
                            disabled={!checked}
                            htmlType="submit"
                        >
                            提交咨询
                        </Button>
                    )}
                </Form.Item>
            </Form>
        </div>
    );
});
