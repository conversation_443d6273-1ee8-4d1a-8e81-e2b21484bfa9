.FormWrapper {
    width: 320px;
    height: 388px;
    padding: 24px;
    background-image: linear-gradient(180deg, #F2F7FF 0%, #FFFFFF 100%);
    border: 1px solid #FFFFFF;
    box-shadow: 0 4px 16px 0 rgba(34,34,34,0.10);
    border-radius: 4px;
}

.justifyBetween {
    display: flex;
    justify-content: space-between;
}

.justifyEnd {
    display: flex;
    justify-content: flex-end;
}

.FormTab {
    font-size: 18px;
    color: #222222;
    letter-spacing: 0;
    line-height: 28px;
    padding-bottom: 8px;
    cursor: pointer;
}

.FormTabItem {
    opacity: 0.7;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    border-bottom: 2px solid transparent;
}

.FormTabItemActive {
    font-family: PingFangSC-Medium;
    font-weight: 500;
    border-bottom: 2px solid #2469F2;
}

.form {
    margin-top: 24px;
    :global {
        .ant-form-item {
            margin-bottom: 20px;
        }
        .ant-form-item:last-child {
            margin-bottom: 0;
        }
        .ant-form-item-explain, .ant-form-item-extra {
            height: 0;
            min-height: 0;
            font-size: 12px;
            line-height: 16px;
        }
        .ant-form-item-label > label {
            height: 36px;
        }
        .ant-input {
            height: 36px;
            border-radius: 4px;
        }
        .ant-input:focus {
            border-color: #2469F3;
            box-shadow: none;
        }
        .ant-input:hover {
            border-color: #2469F3;
        }

        .ant-btn[disabled],
        .ant-btn[disabled]:hover,
        .ant-btn[disabled]:focus,
        .ant-btn[disabled]:active {
            opacity: .7;
            background-color: #2469F3;
            border-color: #2469F2;
            color: #FFFFFF;
        }
        [ant-click-animating-without-extra-node='true']::after, .ant-click-animating-node {
            box-shadow: none;
            animation: none;
        }
    }
}

.checkbox {
    :global {
        .ant-checkbox-checked .ant-checkbox-inner {
            background-color: #2469F3;
            border-color: #2469F3;
        }
        .ant-checkbox + span {
            padding-right: 0;
        }
    }
}

.phoneWrapper {
    position: relative;
}

.phoneWrapper:hover {
    border-color: #2469F3;
}

.smsBtn {
    position: absolute;
    z-index: 5;
    right: 16px;
    top: 8px;
    width: 90px;
    border-left: 1px solid #EDEDED;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #2469F3;
    text-align: right;
    font-weight: 400;
    cursor: pointer;
}

.protocolText,
.protocolText:hover {
    color: #2469F3;
}

.submitBtn {
    border: none;
    outline: none;
    height: 0;
    width: 0;
    padding: 0;
    margin: 24px 0 36px 0;
}

.submitBtn span {
    width: 100%;
    height: 36px;
    line-height: 34px;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    cursor: pointer;
}

.primaryBtn span {
    background: #2469F3;
    border: 1px solid #2469F3;
    color: #FFFFFF;
}

.noLabelBtn {
    width: 272px;
}

.showLabelBtn {
    width: 84px;
}

.cancelBtn {
    margin-right: 16px;
}

.cancelBtn span {
    color: #222222;
    background: #FFFFFF;
    border: 1px solid #E6E6E6;
}