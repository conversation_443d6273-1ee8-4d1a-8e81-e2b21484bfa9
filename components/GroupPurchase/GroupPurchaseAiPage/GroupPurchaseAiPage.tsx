import {useRef, useEffect} from 'react';
import {
    GetGroupPurchaseProductsReqParams,
    GroupProductComponentProps,
    GroupPurchaseProductObj,
    GroupPurchaseProductPriceParams,
    GroupPurchaseTabSelectProps,
} from '@common/interface/groupPurchase';
import {Form, Input} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {useHttp, useOnMount} from '@baidu/bce-hooks';
import {PageResponseObj, netService} from '@baidu/bce-services';
import {EnumProductServiceType} from '@common/interface/common';
import {urlConst} from '@common/constant/urlConst';
import {genPriceParamsByProductObj, getProductByFields, setProductFieldVal} from '@common/helper/groupPurchase';
import {ProductPriceDetailObj} from '@common/interface/page';
import {GroupPurchaseDurationSelect} from '@common/components/page/GroupPurchaseDurationSelect/GroupPurchaseDurationSelect';
import {UDebounce} from '@common/helper/UDebounce';
import style from './GroupPurchaseAiPage.module.less';

type FormValue = {
    type: TypeEnum;
    version: VersionEnum;
    siteName: string;
    duration: string;
};

enum TypeEnum {
    'site' = 'site',
    'miniprogram' = 'miniprogram',
}

enum VersionEnum {
    'miniprogram_2' = 'miniprogram_2',
    'miniprogram_3' = 'miniprogram_3',
    'miniprogram_4' = 'miniprogram_4',
}

const typeSelArr: GroupPurchaseTabSelectProps['data'] = [
    {
        label: '全网门户',
        value: TypeEnum.site,
        desc: 'PC网站+手机网站+百度小程序+微信小程序+支付宝小程序+微信公众号+百家号+微博号 八合一',
    },
    {
        label: '小程序门户',
        value: TypeEnum.miniprogram,
        desc: '百度小程序+微信小程序+支付宝小程序 三合一',
    },
];

const versionSelArr: GroupPurchaseTabSelectProps['data'] = [
    {
        label: '展示版',
        value: VersionEnum.miniprogram_2,
    },
    {
        label: '官网版',
        value: VersionEnum.miniprogram_3,
    },
    {
        label: '营销版',
        value: VersionEnum.miniprogram_4,
    },
];

export function GroupPurchaseAiPage(props: GroupProductComponentProps) {
    const debounce = useRef(new UDebounce<FormValue>(100));
    const [form] = useForm();

    const [productData] = useHttp<GetGroupPurchaseProductsReqParams, PageResponseObj<GroupPurchaseProductObj>>({
        url: urlConst.GET_GROUP_PURCHASE_PRODUCTS,
        methods: 'get',
        params: {
            serviceType: EnumProductServiceType.AIPAGE,
            scene: 'mergePurchase',
        },
        defaultValue: {
            order: '',
            orderBy: 'default',
            pageNo: 0,
            pageSize: 0,
            result: [],
        },
    }, true);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
    });

    // 赋默认值后主动触发一次询价
    useEffect(() => {
        if (productData.result?.length) {
            debounce.current.next(form.getFieldsValue());
        }
    }, [productData.result, form]);

    debounce.current.subscribe((allVal: FormValue) => {
        const comboName = allVal.type === TypeEnum.site ? `${allVal.type}_${allVal.version}` : allVal.version;
        const target = getProductByFields(productData.result, [{
            name: comboName,
            isFlavor: true,
            val: null,
        }]);
        const paramsDuration = GroupPurchaseDurationSelect.getDurationValue(allVal.duration);
        if (target) {
            const params = genPriceParamsByProductObj(
                setProductFieldVal(target, comboName, false, true, '1'),
                allVal
            );
            const configList = [{
                name: '产品系列',
                value: typeSelArr.find(item => item.value === allVal.type).label,
            }, {
                name: '产品版本',
                value: versionSelArr.find(item => item.value === allVal.version)?.label,
            }, {
                name: '时长',
                value: paramsDuration.label,
            }];
            props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });
            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {data: [params]}
            ).then(res => {
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    // 下单参数
                    orderParams: {
                        ...params,
                        serviceId: '',
                        serviceGroupId: '',
                        serviceType: 'AIPAGE',
                        parentServiceType: 'AIPAGE',
                        region: 'global',
                        duration: paramsDuration.durationInt,
                        timeUnit: paramsDuration.timeUnit,
                        count: 1,
                        // 下单参数
                        extraConfig: JSON.stringify({
                            comboName,
                            name: allVal.siteName,
                        }),
                    },
                });
            });
        }
    });

    return (
        <Form
            form={form}
            initialValues={props.value}
            className={style['group-purchase-aipage-form']}
            onValuesChange={(e, allVal) => {
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="产品系列：">
                <Form.Item
                    name="type"
                >
                    <GroupPurchaseTabSelect
                        data={typeSelArr}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="产品版本：" hasBorder>
                <Form.Item
                    name="version"
                >
                    <GroupPurchaseTabSelect
                        data={versionSelArr}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="站点名称：" hasBorder>
                <Form.Item
                    name="siteName"
                    rules={
                        [{
                            required: true,
                            message: '请输入站点名称',
                        }]
                    }
                >
                    <Input
                        className={style['site-name']}
                        placeholder="请填写站点名称"
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="时长：" hasBorder>
                <Form.Item
                    name="duration"
                >
                    <GroupPurchaseDurationSelect
                        options={['1Y', '2Y', '3Y']}
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
