import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {
    GetGroupPurchaseProductsReqParams,
    GroupProductComponentProps,
    GroupPurchaseProductObj,
    GroupPurchaseProductPriceParams,
    GroupPurchaseTabSelectPropsData,
} from '@common/interface/groupPurchase';
import {Form} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {useEffect, useMemo, useState, useRef} from 'react';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {useHttp, useOnMount} from '@baidu/bce-hooks';
import {PageResponseObj, netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {EnumProductServiceType} from '@common/interface/common';
import {genPriceParamsByProductObj, getProductByFields, getProductFieldVal, getZhFromFlavorDisplayName} from '@common/helper/groupPurchase';
import {UInput} from '@common/components/UInput/UInput';
import {UDebounce} from '@common/helper/UDebounce';
import {ProductPriceDetailObj} from '@common/interface/page';
import {GroupPurchaseDurationSelect} from '@common/components/page/GroupPurchaseDurationSelect/GroupPurchaseDurationSelect';
import style from './GroupPurchaseNat.module.less';

type NatGatewayScale = 'little' | 'medium' | 'large';

type FormValue = {
    region: string;
    natGatewayScale: NatGatewayScale;
    name: string;
    duration: string;
};

const NAT_GATEWAY_SCALE_MAP: {
    [p in NatGatewayScale]: string;
} = {
    'little': '小',
    'medium': '中',
    'large': '大',
};

export function GroupPurchaseNat(props: GroupProductComponentProps) {
    const debounce = useRef(new UDebounce<FormValue>(100));
    const [form] = useForm();
    const [formValue, setFormValue] = useState<FormValue>(props.value);
    const [productData] = useHttp<GetGroupPurchaseProductsReqParams, PageResponseObj<GroupPurchaseProductObj>>({
        url: urlConst.GET_GROUP_PURCHASE_PRODUCTS,
        methods: 'get',
        params: {
            serviceType: EnumProductServiceType.NAT,
            scene: 'mergePurchase',
        },
        defaultValue: {
            order: '',
            orderBy: 'default',
            pageNo: 0,
            pageSize: 0,
            result: [],
        },
    }, true);

    // 获取资源默认分组的groupId
    const [groupId] = useHttp<never, string, any>({
        url: urlConst.GET_RESOURCE_GROUP_ID,
        methods: 'get',
        defaultValue: '',
        transform: e => {
            const data: any[] = e?.groups || [];
            const defaultId = data.find(item => item.name === '默认分组')?.groupId || '';
            return defaultId;
        },
    }, true);

    // 获取VPC id
    const [vpcId] = useHttp<{region: string}, string, any>({
        url: urlConst.GET_VPC_LIST,
        params: {
            region: 'bj',
        },
        methods: 'post',
        defaultValue: '',
        transform: e => {
            const data: any[] = e?.vpc || [];
            const defaultId = data.find(item => item.defaultVpc === true)?.vpcId || '';
            return defaultId;
        },
    }, true);

    const regionData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productData.result.forEach(item => {
            if (!res.find(i => i.value === item.region)) {
                res.push({
                    label: getZhFromFlavorDisplayName(item.flavorDisplayName, 'region') || item.region,
                    value: item.region,
                });
            }
        });
        return res;
    }, [productData]);
    const natGatewayScaleData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [null, null, null];
        // 按 小中大 的顺序排列
        const indexMap = {little: 0, medium: 1, large: 2};
        productData.result.forEach(item => {
            const natGatewayScale = getProductFieldVal(item, 'nat_gateway_scale', false, true) as NatGatewayScale;
            if (
                !res.find(i => i?.value === natGatewayScale)
                && item.region === formValue.region
            ) {
                res[indexMap[natGatewayScale]] = {
                    label: NAT_GATEWAY_SCALE_MAP[natGatewayScale],
                    value: natGatewayScale,
                };
            }
        });
        return res.filter(Boolean);
    }, [formValue.region, productData.result]);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
    });

    debounce.current.subscribe((allVal: FormValue) => {
        const paramsDuration = GroupPurchaseDurationSelect.getDurationValue(allVal.duration);
        const target = getProductByFields(productData.result, [{
            name: 'region',
            isFlavor: false,
            val: allVal.region,
        }, {
            name: 'nat_gateway_scale',
            isFlavor: true,
            isScale: false,
            val: allVal.natGatewayScale,
        }]);
        if (target) {
            const params = genPriceParamsByProductObj(target, allVal);
            const configList = [{
                name: '地域',
                value: regionData.find(item => item.value === allVal.region).label,
            }, {
                name: '类型',
                value: natGatewayScaleData.find(item => item.value === allVal.natGatewayScale)?.label,
            }, {
                name: '购买时长',
                value: paramsDuration.label,
            }];
            props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });
            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {data: [params]}
            ).then(res => {
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    orderParams: {
                        serviceType: 'NAT',
                        parentServiceType: 'NAT',
                        serviceId: '',
                        serviceGroupId: '',
                        displayName: 'NAT实例',
                        region: allVal.region,
                        subServiceType: getProductFieldVal(target, 'subServiceType', false, true),
                        duration: paramsDuration.durationInt,
                        timeUnit: paramsDuration.timeUnit,
                        count: 1,
                        flavor: params.flavor,
                        specs: params.flavor.flavorItems,
                        extraConfig: JSON.stringify({
                            name: allVal.name,
                            vpcId,
                            reservationLength: paramsDuration.durationInt,
                            reservationTimeUnit: paramsDuration.timeUnit,
                            resourceGroupId: groupId,
                        }),
                    },
                });
            });
        }
    });

    // NAT 表单的初始值要在这里赋上
    useEffect(() => {
        if (groupId && vpcId && productData.result) {
            debounce.current.next(form.getFieldsValue());
        }
    }, [groupId, vpcId, form, productData.result]);

    return (
        <Form
            form={form}
            initialValues={formValue}
            className={style['group-purchase-nat-form']}
            onValuesChange={(e, allVal) => {
                setFormValue(allVal);
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="区域：" hasBorder>
                <Form.Item
                    name="region"
                >
                    <GroupPurchaseTabSelect
                        data={regionData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="类型：">
                <Form.Item
                    name="natGatewayScale"
                >
                    <GroupPurchaseTabSelect
                        data={natGatewayScaleData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="网关名称：">
                <Form.Item
                    name="name"
                    className={style['name']}
                    rules={
                        [{
                            required: true,
                            message: '请输入NAT网关名称',
                        }]
                    }
                >
                    <UInput placeholder="请输入NAT网关名称" />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="购买时长：" hasBorder>
                <Form.Item
                    name="duration"
                >
                    <GroupPurchaseDurationSelect
                        options={['1M', '2M', '3M', '4M', '5M', '6M', '1Y', '2Y', '3Y']}
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
