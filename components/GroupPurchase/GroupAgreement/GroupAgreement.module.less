@import '@styles/variables.less';

.purchase-agreement-wrapper {
    padding: 24px;
    background: @CW;
    border-radius: 4px;

    :global {
        .ant-form-item-control-input {
            min-height: unset;
        }
    }

    .purchase-agreement {
        display: flex;
        align-items: center;
    
        label {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            cursor: pointer;
    
            input[type="checkbox"] {
                display: none;
    
                &:checked + i {
                    border-color: @CB;
                    background: @CB url('https://bce.bdstatic.com/portal-cloud-server/images/index2022/merge_purchace/icon_checkbox.svg') no-repeat 50%/12px 12px;
                }
            }
    
            i {
                display: block;
                width: 100%;
                height: 100%;
                border: 1px solid @CLB;
                border-radius: 2px;
            }
        }

        p {
            color: @C0;
            font-size: 12px;
            font-family: PingFangSC-Regular;
            line-height: 16px;
            
            a {
                line-height: 16px;
                color: @CB;
    
                &:hover {
                    color: @CLB;
                }
            }
        }
    }
} 
