import {Form} from 'antd';
import {groupBuyFormRules} from '@components/groupBuy/groupBuyFromRules';
import styles from './GroupAgreement.module.less';

function Agreement(props: {value?: boolean, onChange?: (value: boolean) => void, id?: string}) {
    const {value, onChange, id} = props;
    return (
        <div className={styles['purchase-agreement']} id={id}>
            <label>
                <input
                    type="checkbox"
                    checked={value}
                    onChange={e => onChange(e.target.checked)}
                />
                <i />
            </label>
            <p>
                我已阅读理解并同意
                <a href="https://console.bce.baidu.com/iam/agreement-v2.html" target="_blank">《百度智能云线上订购协议》</a>
            </p>
        </div>
    );
}

export function GroupAgreement() {
    return (
        <Form.Item
            name="agreement"
            rules={groupBuyFormRules.AGREEMENTCHECK}
            className={styles['purchase-agreement-wrapper']}
        >
            <Agreement />
        </Form.Item>
    );
}
