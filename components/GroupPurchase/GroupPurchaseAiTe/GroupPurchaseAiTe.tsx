import {Form} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {useMemo, useRef, useState} from 'react';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {
    GetGroupPurchaseProductsReqParams,
    GroupProductComponentProps,
    GroupPurchaseProductObj,
    GroupPurchaseTabSelectPropsData,
    GroupPurchaseProductPriceParams,
} from '@common/interface/groupPurchase';
import {useHttp, useOnMount} from '@baidu/bce-hooks';
import {PageResponseObj, netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {EnumProductServiceType, ProductApiInfo} from '@common/interface/common';
import {
    genPriceParamsByProductObj,
    getAiCountSplitRange,
    getApiNameAndIdByValueDisplayName,
    getProductByFields,
    getProductFieldVal,
    getZhFromFlavorDisplayName,
} from '@common/helper/groupPurchase';
import {USelect} from '@common/components/USelect/USelect';
import {isArray, uniqBy} from 'lodash';
import {ProductPriceDetailObj} from '@common/interface/page';
import {UDebounce} from '@common/helper/UDebounce';
import style from './GroupPurchaseAiTe.module.less';


type FormValue = {
    region: string;
    apiId: string;
    version: string;
    duration: string;
};

const durationData = [
    {
        label: '12个月',
        value: '12M',
    },
];

export function GroupPurchaseAiTe(props: GroupProductComponentProps) {
    const [form] = useForm();
    const debounce = useRef(new UDebounce<FormValue>(100));
    const [formValue, setFormValue] = useState<FormValue>(props.value);
    const [currSubServiceType, setCurrSubServiceType] = useState(null);
    const [apiInfo] = useHttp<null, ProductApiInfo[], ProductApiInfo[], {product: string}>({
        url: urlConst.GET_PRODUCT_API_ID,
        methods: 'get',
        query: {
            product: 'ai_te',
        },
        defaultValue: [] as any,
    }, true);
    const [productData] = useHttp<GetGroupPurchaseProductsReqParams, PageResponseObj<GroupPurchaseProductObj>>({
        url: urlConst.GET_GROUP_PURCHASE_PRODUCTS,
        methods: 'get',
        params: {
            serviceType: EnumProductServiceType.AI_TE,
            scene: 'mergePurchase',
        },
        defaultValue: {
            order: '',
            orderBy: 'default',
            pageNo: 0,
            pageSize: 0,
            result: [],
        },
    }, true);
    const regionData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productData.result.forEach(item => {
            if (!res.find(i => i.value === item.region)) {
                res.push({
                    label: getZhFromFlavorDisplayName(item.flavorDisplayName, 'region') || item.region,
                    value: item.region,
                });
            }
        });
        return res;
    }, [productData]);

    const subServiceTypeData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productData.result.forEach(item => {
            if (item.region === formValue.region) {
                const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
                const apiTarget = getApiNameAndIdByValueDisplayName(
                    getZhFromFlavorDisplayName(item.flavorDisplayName, 'subServiceType'),
                    apiInfo
                );
                if (isArray(apiTarget.api_name)) {
                    apiTarget.api_name.forEach(api => {
                        res.push({
                            label: api.api_name,
                            value: api.api_id,
                            extr: subServiceType,
                        });
                    });
                } else {
                    res.push({
                        label: apiTarget.api_name,
                        value: apiTarget.api_id,
                        extr: subServiceType,
                    });
                }
            }
        });
        return uniqBy(res, 'value');
    }, [apiInfo, formValue.region, productData.result]);

    const versionData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productData.result.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            const version = getProductFieldVal(item, 'Version_0', false, true) as string;
            if (
                !res.find(i => i.value === version)
                && item.region === formValue.region
                && subServiceType === currSubServiceType
            ) {
                res.push({
                    label: `${getAiCountSplitRange(version)}次`,
                    value: version,
                });
            }
        });
        return res;
    }, [formValue.region, currSubServiceType, productData.result]);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
    });
    debounce.current.subscribe((allVal: FormValue) => {
        const fields = [{
            name: 'region',
            isFlavor: false,
            val: allVal.region,
        }, {
            name: 'subServiceType',
            isFlavor: true,
            isScale: false,
            val: currSubServiceType,
        }, {
            name: 'Version_0',
            isFlavor: true,
            isScale: false,
            val: allVal.version,
        }];
        const aiTeProduct = getProductByFields(productData.result, fields);
        if (aiTeProduct) {
            const params = genPriceParamsByProductObj(aiTeProduct, allVal);
            const configList = [{
                name: '区域',
                value: regionData.find(item => item.value === allVal.region).label,
            }, {
                name: '接口名称',
                value: subServiceTypeData.find(item => item.value === allVal.apiId).label,
            }, {
                name: '版本',
                value: versionData.find(item => item.value === allVal.version).label,
            }, {
                name: '时间周期',
                value: '12个月',
            }];
            props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });
            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {data: [params]}
            ).then(res => {
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    // 下单参数
                    orderParams: {
                        ...params,
                        serviceType: 'MACHINETRANSLATION',
                        parentServiceType: 'MACHINETRANSLATION',
                        apiId: allVal.apiId,
                        specs: params.flavor.flavorItems,
                        displayName: subServiceTypeData.find(item => item.value === allVal.apiId).label,
                    },
                });
            });
        }
    });

    return (
        <Form
            form={form}
            className={style['group-purchase-ai-te-form']}
            initialValues={formValue}
            onValuesChange={(e, allVal) => {
                setFormValue(allVal);
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="区域：" hasBorder>
                <Form.Item
                    name="region"
                >
                    <GroupPurchaseTabSelect
                        data={regionData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="接口名称：">
                <Form.Item
                    name="apiId"
                >
                    <USelect
                        className={style['subServiceType-select']}
                        options={subServiceTypeData}
                        triggerChangeDefault
                        onChange={(e, ee) => {
                            setCurrSubServiceType((ee as any).extr);
                        }}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem
                label="版本："
            >
                <Form.Item
                    name="version"
                >
                    <GroupPurchaseTabSelect
                        data={versionData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="时间周期：" hasBorder>
                <Form.Item
                    name="duration"
                >
                    <GroupPurchaseTabSelect
                        data={durationData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
