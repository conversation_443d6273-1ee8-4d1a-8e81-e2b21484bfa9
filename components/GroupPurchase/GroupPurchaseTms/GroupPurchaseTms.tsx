import {useRef} from 'react';
import sha256 from 'crypto-js/sha256';
import Base64 from 'crypto-js/enc-base64';
import {GroupBuyTabSelect} from '@common/components/page/GroupBuyTabSelect/GroupBuyTabSelect';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupBuyTmsItemObj} from '@common/interface/groupBuy';
import {Form, Input} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {GroupProductComponentProps} from '@common/interface/groupPurchase';
import {useOnMount} from '@baidu/bce-hooks';
import {urlConst} from '@common/constant/urlConst';
import {netService} from '@baidu/bce-services';
import {UDebounce} from '@common/helper/UDebounce';
import {ProductPriceDetailObj} from '@common/interface/page';
import {PRODUCT_PRICE_SHA_KEY} from '@common/constant/variableConst';
import {utc} from 'moment';
import style from './GroupPurchaseTms.module.less';

export enum DisplayNameTmsEumn {
    '专家辅助注册' = '专家辅助注册',
    '至尊无忧注册' = '至尊无忧注册',
}

export const displayNameTmsArr: Array<{
    text: string;
    value: DisplayNameTmsEumn;
    desc?: string;
}> = [{
    text: '专家辅助注册',
    value: DisplayNameTmsEumn['专家辅助注册'],
}, {
    text: '至尊无忧注册',
    value: DisplayNameTmsEumn['至尊无忧注册'],
}];

export enum DurationTmsEnum {
    'one' = '1',
}

export const productAllTms: GroupBuyTmsItemObj[] = [
    {
        serviceType: 'TMS',
        personRole: DisplayNameTmsEumn['专家辅助注册'],
        duration: DurationTmsEnum.one,
        timeUnit: 'DAY',
    },
    {
        serviceType: 'TMS',
        personRole: DisplayNameTmsEumn['至尊无忧注册'],
        duration: DurationTmsEnum.one,
        timeUnit: 'DAY',
    },
];

type FormValue = {
    displayName: DisplayNameTmsEumn;
    brandName: string;
};

export const GroupPurchaseTms = (props: GroupProductComponentProps) => {
    const debounce = useRef(new UDebounce<FormValue>(100));
    const [form] = useForm();

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);

        // TMS 不用请求商品库, 初始化的时候直接询价就行了
        debounce.current.next(form.getFieldsValue());
    });

    debounce.current.subscribe((allVal: FormValue) => {
        const {displayName} = allVal;
        const flavorItems = displayName === DisplayNameTmsEumn['专家辅助注册']
            ? [{
                name: 'tmsServiceType',
                value: 'AUXILIARY_REGISTRATION',
                scale: 1,
            }, {
                name: 'AUXILIARY_REGISTRATION',
                value: '1',
                scale: 1,
            }, {
                name: 'subServiceType',
                value: 'trademark registration',
                scale: 1,
            }]
            : [{
                name: 'subServiceType',
                value: 'trademark registration',
                scale: 1,
            }, {
                name: 'GUARANTEE_REGISTRATION',
                value: '1',
                scale: 1,
            }, {
                name: 'tmsServiceType',
                value: 'GUARANTEE_REGISTRATION',
                scale: 1,
            }];
        const params = {
            serviceType: 'TMS',
            parentServiceType: 'TMS',
            displayName: '商标注册',
            serviceId: '',
            serviceGroupId: '',
            timeUnit: 'DAY',
            chargeItemName: 'project',
            region: 'global',
            duration: 1,
            count: 1,
            flavor: {
                flavorItems,
            },
            time: {
                startTime: utc(new Date()).format(),
                period: 'P1D',
            },
            signAuth: '',
        };
        params.signAuth = Base64.stringify(sha256(`TMSglobal1day${JSON.stringify(flavorItems)}${PRODUCT_PRICE_SHA_KEY}`));
        const configList = [{
            name: '注册类型',
            value: allVal.displayName,
        }, {
            name: '商标名称',
            value: allVal.brandName,
        }];
        props.onChange({
            priceList: null,
            configList,
            loading: true,
            orderParams: null,
        });
        netService.post<{data: any}, ProductPriceDetailObj[]>(
            urlConst.POST_MERGE_PRODUCT_PRICE,
            {data: [params]}
        ).then(res => {
            props.onChange && props.onChange({
                priceList: res.result,
                configList,
                loading: false,
                orderParams: {
                    ...params,
                    // 下单参数
                    extraConfig: JSON.stringify({
                        trademarkName: allVal.brandName,
                    }),
                },
            });
        });
    });

    return (
        <Form
            form={form}
            initialValues={props.value}
            className={style['group-purchase-tms-form']}
            onValuesChange={(e, allVal) => {
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="注册类型：" hasBorder>
                <Form.Item name="displayName">
                    <GroupBuyTabSelect
                        data={displayNameTmsArr}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="商标名称：" hasBorder>
                <Form.Item
                    name="brandName"
                    className={style['brandName']}
                    rules={
                        [{
                            required: true,
                            message: '请输入商标名称',
                        }]
                    }
                >
                    <Input
                        placeholder="请输入商标名称"
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
};
