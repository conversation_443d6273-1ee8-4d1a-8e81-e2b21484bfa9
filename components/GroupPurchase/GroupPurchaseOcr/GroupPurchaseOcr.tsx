/* eslint-disable @typescript-eslint/no-unused-vars */
import {Form, message} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {useMemo, useRef, useState} from 'react';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {
    GroupProductComponentProps,
    GroupPurchaseProductObj,
    GroupPurchaseTabSelectPropsData,
    GroupPurchaseProductPriceParams,
} from '@common/interface/groupPurchase';
import {useHttp, useOnMount, useStateRef} from '@baidu/bce-hooks';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {EnumProductServiceType, ProductApiInfo} from '@common/interface/common';
import {
    genPriceParamsByProductObj,
    getAiCountSplitRange,
    getApiNameAndIdByValueDisplayName,
    getProductByFields,
    getProductFieldVal,
    getZhFromFlavorDisplayName,
} from '@common/helper/groupPurchase';
import {USelect} from '@common/components/USelect/USelect';
import {filter, isArray, omit, uniqBy} from 'lodash';
import {ProductPriceDetailObj} from '@common/interface/page';
import {UDebounce} from '@common/helper/UDebounce';
import style from './GroupPurchaseOcr.module.less';

type FormValue = {
    region: string;
    apiId: string;
    count: number;
    specification: number;
    duration: string;
};
type ConfigList = {
    name: string;
    value: string;
};

const durationData = [
    {
        label: '12个月',
        value: '12M',
    },
];
export function GroupPurchaseOcr(props: GroupProductComponentProps) {
    const [form] = useForm();
    const debounce = useRef(new UDebounce<FormValue>(100));
    const [formValue, setFormValue] = useState<FormValue>(props.value);
    const [productData, setProductData] = useState<GroupPurchaseProductObj[]>([]);
    const [currService, setCurrService, currServiceRef] = useStateRef<{
        subServiceType: string;
        serviceType: string;
    }>(null);

    const [apiInfo] = useHttp<null, ProductApiInfo[], ProductApiInfo[], {product: string}>({
        url: urlConst.GET_PRODUCT_API_ID,
        methods: 'get',
        query: {
            product: 'ocr',
        },
        defaultValue: [] as any,
    }, true);

    const regionData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productData.forEach(item => {
            if (!res.find(i => i.value === item.region)) {
                res.push({
                    label: getZhFromFlavorDisplayName(item.flavorDisplayName, 'region') || item.region,
                    value: item.region,
                });
            }
        });
        return res;
    }, [productData]);

    const subServiceTypeData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        if (productData.length > 0 && apiInfo.length > 0) {
            productData.forEach(item => {
                if (item.region === formValue.region) {
                    const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
                    const apiTarget = getApiNameAndIdByValueDisplayName(
                        getZhFromFlavorDisplayName(item.flavorDisplayName, 'subServiceType'),
                        apiInfo
                    );
                    if (isArray(apiTarget.api_name)) {
                        apiTarget.api_name.forEach(api => {
                            res.push({
                                label: api.api_name,
                                value: api.api_id,
                                extr: {
                                    subServiceType,
                                    serviceType: item.serviceType,
                                },
                            });
                        });
                    } else {
                        res.push({
                            label: apiTarget.api_name,
                            value: apiTarget.api_id,
                            extr: {
                                subServiceType,
                                serviceType: item.serviceType,
                            },
                        });
                    }
                }
            });
        }
        return uniqBy(res, 'value');
    }, [apiInfo, formValue.region, productData]);

    const countData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productData.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            const count = getProductFieldVal(item, 'count', false, true) as string;
            if (
                !res.find(i => i.value === count)
                && item.region === formValue.region
                && subServiceType === currService?.subServiceType
            ) {
                res.push({
                    label: `${getAiCountSplitRange(count)}次`,
                    value: count,
                });
            }
        });
        return res;
    }, [currService?.subServiceType, formValue.region, productData]);

    const specificationData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productData.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            const specification = getProductFieldVal(item, 'Specification_0', false, true) as string;
            if (
                !res.find(i => i.value === specification)
                && item.region === formValue.region
                && subServiceType === currService?.subServiceType
            ) {
                res.push({
                    label: `${getAiCountSplitRange(specification)}次`,
                    value: specification,
                });
            }
        });
        return res;
    }, [productData, formValue.region, currService?.subServiceType]);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);

        const ocrDataReq = [
            EnumProductServiceType.OCR,
            EnumProductServiceType.WEBIMAGE_OCR,
            EnumProductServiceType.LOCATION_OCR,
            EnumProductServiceType.BCN_OCR,
            EnumProductServiceType.ID_OCR,
            EnumProductServiceType.GENERAL_OCR,
        ].map(serviceType => netService.get(urlConst.GET_GROUP_PURCHASE_PRODUCTS, {
            serviceType,
            scene: 'mergePurchase',
        }));
        Promise.all(ocrDataReq)
            .then(resList => {
                if (!resList.every(res => res.success)) {
                    throw new Error('OCR 产品数据获取失败');
                }
                setProductData(resList.reduce((data, curRes) => [...data, ...curRes.page.result], []));
            })
            .catch(err => {
                message.error('OCR 产品数据获取失败');
                console.error(err);
            });
    });

    debounce.current.subscribe((allVal: FormValue) => {
        const allFields = [{
            name: 'region',
            isFlavor: false,
            val: allVal.region,
        }, {
            name: 'serviceType',
            isFlavor: false,
            isScale: false,
            val: currServiceRef.current?.serviceType,
        }, {
            name: 'subServiceType',
            isFlavor: true,
            isScale: false,
            val: currServiceRef.current?.subServiceType,
        }, {
            name: 'count',
            isFlavor: true,
            isScale: false,
            val: allVal.count,
        }, {
            name: 'Specification_0',
            isFlavor: true,
            isScale: false,
            val: allVal.specification,
        }];
        const fields = allVal.count ? filter(allFields, o => o.name !== 'Specification_0') : filter(allFields, o => o.name !== 'count');
        const ocrProduct = getProductByFields(productData, fields);
        if (ocrProduct) {
            const params = genPriceParamsByProductObj(ocrProduct, omit(allVal, 'count'));
            const list: ConfigList[] = [{
                name: '区域',
                value: regionData.find(item => item.value === allVal.region).label,
            }, {
                name: '服务类型',
                value: subServiceTypeData.find(item => item.value === allVal.apiId).label,
            }, {
                name: '预付次数包',
                value: countData.find(item => item.value === allVal.count).label,
            }, {
                name: '规格',
                value: specificationData.find(item => item.value === allVal.specification).label,
            }, {
                name: '时间周期',
                value: '12个月',
            }];
            const configList = allVal.count
                ? filter(list, o => o.name !== '规格')
                : filter(list, o => o.name !== '预付次数包');
            props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });
            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {data: [params]}
            ).then(res => {
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    // 下单参数
                    orderParams: {
                        ...params,
                        apiId: allVal.apiId,
                        // 下单都叫OCR
                        serviceType: 'OCR',
                        parentServiceType: 'OCR',
                        displayName: subServiceTypeData.find(item => item.value === allVal.apiId).label,
                    },
                });
            });
        }
    });

    return (
        <Form
            form={form}
            initialValues={formValue}
            className={style['group-purchase-ocr-form']}
            onValuesChange={(e, allVal) => {
                setFormValue(allVal);
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="区域：" hasBorder>
                <Form.Item
                    name="region"
                >
                    <GroupPurchaseTabSelect
                        data={regionData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="服务类型：">
                <Form.Item
                    name="apiId"
                >
                    <USelect
                        className={style['subServiceType-select']}
                        options={subServiceTypeData}
                        triggerChangeDefault
                        onChange={(e, ee) => {
                            setCurrService((ee as any).extr);
                        }}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem
                label="调用量："
                className={formValue.count ? style['show'] : style['hide']}
            >
                <Form.Item
                    name="count"
                >
                    <GroupPurchaseTabSelect
                        isOrder
                        data={countData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem
                label="规格/次："
                className={formValue.specification ? style['show'] : style['hide']}
            >
                <Form.Item
                    name="specification"
                >
                    <GroupPurchaseTabSelect
                        isOrder
                        data={specificationData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="时间周期：" hasBorder>
                <Form.Item
                    name="duration"
                >
                    <GroupPurchaseTabSelect
                        data={durationData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
