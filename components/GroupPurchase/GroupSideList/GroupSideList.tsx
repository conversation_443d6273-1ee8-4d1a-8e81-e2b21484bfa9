import {useMemo} from 'react';
import {Popover, Button} from 'antd';
import Big from 'big.js';
import {calcTotalPrice, formatPriceNumber, productNameMap} from '@components/GroupPurchase/utils';
import {ProductConfigItem} from '@common/interface/groupBuy';
import PriceDetail from '@components/GroupPurchase/PriceDetail/PriceDetail';
import styles from './GroupSideList.module.less';

interface GroupBuyListProps {
    configList: ProductConfigItem[];
    purchaseLoading: boolean;
    onPurchase: () => void;
}

export default function GroupSideList(props: GroupBuyListProps) {
    const {configList, onPurchase, purchaseLoading} = props;

    const selectedConfigList = useMemo(() => configList.filter(item => item.selected), [configList]);

    // 有任何一项在计算中 (price 为空值) 则 total price 为计算中状态
    const totalPrice = selectedConfigList.some(config => !config.price)
        ? null
        : selectedConfigList
            .map(config => calcTotalPrice(config.price))
            .reduce((sum, cur) => sum.add(cur), new Big('0'));

    return (
        <div className={styles['group-purchase-list']}>
            <div className={styles['group-side-list']}>
                {selectedConfigList.map(productConfig => (
                    <div
                        key={(productConfig.serviceType) + Math.random().toString()}
                        className={styles['product-config']}
                    >
                        <h3>{productNameMap[productConfig.serviceType]}</h3>
                        {productConfig.price ? (
                            <div className={styles.price}>
                                <div className={styles['current-price']}>
                                    <span className={styles['price-sign']}>￥</span>
                                    <span className={styles['price-num']}>
                                        {formatPriceNumber(calcTotalPrice(productConfig.price))}
                                    </span>
                                </div>
                                {productConfig.price.length > 1 && (
                                    <Popover
                                        content={<PriceDetail price={productConfig.price} />}
                                        placement="right"
                                    >
                                        <i className={styles['price-detail-icon']} />
                                    </Popover>
                                )}
                            </div>
                        ) : (
                            <div className={styles['price-loading']}>计算中</div>
                        )}
                        <table>
                            <tbody>
                                {productConfig.config.map(({name, value}) => (
                                    <tr key={value}>
                                        <td className={styles['config-name']}>{name}</td>
                                        <td className={styles['config-value']}>{value}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                ))}
            </div>
            <footer className={styles['group-side-footer']}>
                <div className={styles['group-side-footer-title']}>总费用</div>
                {totalPrice ? (
                    <div className={styles.price}>
                        <span className={styles['price-sign']}>￥</span>
                        <span className={styles['price-num']}>{formatPriceNumber(totalPrice)}</span>
                    </div>
                ) : (
                    <div className={styles['price-loading']}>计算中</div>
                )}
                <Button
                    type="primary"
                    className={styles['purchase-btn']}
                    onClick={onPurchase}
                    disabled={purchaseLoading || !totalPrice || !selectedConfigList.length}
                    loading={purchaseLoading}
                >
                    立即购买
                </Button>
            </footer>
        </div>
    );
}
