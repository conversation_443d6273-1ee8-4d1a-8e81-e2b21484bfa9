@import '@styles/variables.less';

.group-purchase-list {
    position: relative;

    .group-side-list {
        max-height: calc(100vh - 293px);
        padding: 0 20px;
        background: @CW;
        border-radius: 4px 4px 0 0;
        overflow-y: auto;
        overscroll-behavior: contain;

        .product-config {
            padding: 20px 0;

            &:nth-child(n+2) {
                border-top: 1px solid rgba(@C0, .04);
            }

            h3 {
                margin-bottom: 4px;
                font-family: PingFangSC-Semibold;
                font-size: 16px;
                color: @C0;
                line-height: 26px;
                font-weight: 600;
            }

            .price-loading,
            .price {
                margin-bottom: 12px;
            }

            .price-loading {
                font-family: PingFangSC-Medium;
                font-weight: 500;
                font-size: 16px;
                color: @CR;
                line-height: 20px;
            }

            .price {
                display: flex;
                align-items: center;
                height: 28px;

                .current-price {
                    color: @CR;

                    .price-sign {
                        margin-right: 2px;
                        font-family: PingFangSC-Medium;
                        font-size: 14px;
                        letter-spacing: 0;
                        line-height: 16px;
                        font-weight: 500;
                    }

                    .price-num {
                        font-family: PingFangSC-Medium;
                        font-size: 20px;
                        line-height: 20px;
                        font-weight: 500;
                    }
                }

                .price-detail-icon {
                    width: 16px;
                    height: 16px;
                    margin-left: 8px;
                    opacity: .4;
                    background: url('https://bce.bdstatic.com/p3m/common-service/uploads/questionInfo_5961941.svg') center / cover;
                    cursor: pointer;
                }
            }

            table {
                .config-name {
                    padding: 0 12px 8px 0;
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: rgba(@C0, .7);
                    vertical-align: top;
                    white-space: nowrap;
                    line-height: 20px;
                    font-weight: 400;
                }

                .config-value {
                    vertical-align: top;
                    padding-bottom: 8px;
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: @C0;
                    line-height: 20px;
                    font-weight: 400;
                }

                tr:last-child {
                    .config-name,
                    .config-value {
                        padding-bottom: 0;
                    }
                }
            }
        }
    }

    .group-side-footer {
        position: sticky;
        bottom: 16px;
        padding: 20px;
        background: @CW;
        border-radius: 0 0 4px 4px;
        box-shadow: 0 -2px 16px 2px rgba(7, 12, 20, .04);

        .group-side-footer-title {
            margin-bottom: 8px;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: rgba(@C0, .7);
            line-height: 20px;
            font-weight: 400;
        }

        .price-loading {
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: 24px;
            color: @CR;
            line-height: 32px;
        }

        .price {
            color: @CR;

            .price-sign {
                font-family: PingFangSC-Medium;
                font-size: 18px;
                letter-spacing: 0;
                text-align: center;
                line-height: 20px;
                font-weight: 500;
            }

            .price-num {
                font-family: PingFangSC-Medium;
                font-size: 32px;
                color: @CR;
                line-height: 32px;
                font-weight: 500;
            }
        }

        .purchase-btn {
            margin-top: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 240px;
            height: 42px;
            background: @CB;
            border: 1px solid @CB;
            border-radius: 4px;
            font-family: PingFangSC-Medium;
            font-size: 16px;
            color: @CW;
            letter-spacing: 0;
            text-align: justify;
            line-height: 26px;
            font-weight: 500;
        }
    }
}
