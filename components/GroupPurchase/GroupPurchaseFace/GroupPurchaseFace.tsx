import {Form} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {useMemo, useRef, useState} from 'react';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {
    GetGroupPurchaseProductsReqParams,
    GroupProductComponentProps,
    GroupPurchaseProductObj,
    GroupPurchaseTabSelectPropsData,
    GroupPurchaseProductPriceParams,
} from '@common/interface/groupPurchase';
import {useHttp, useOnMount} from '@baidu/bce-hooks';
import {PageResponseObj, netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {EnumProductServiceType, ProductApiInfo} from '@common/interface/common';
import {
    getAiCountSplitRange,
    genPriceParamsByProductObj,
    getApiNameAndIdByValueDisplayName,
    getProductByFields,
    getProductFieldVal,
    getZhFromFlavorDisplayName,
} from '@common/helper/groupPurchase';
import {USelect} from '@common/components/USelect/USelect';
import {filter, omit} from 'lodash';
import {ProductPriceDetailObj} from '@common/interface/page';
import {UDebounce} from '@common/helper/UDebounce';
import style from './GroupPurchaseFace.module.less';

type FormValue = {
    region: string;
    subServiceType: string;
    count: number;
    specification: number;
    duration: string;
};
type ConfigList = {
    name: string;
    value: string;
};
const durationData = [
    {
        label: '12个月',
        value: '12M',
    },
];

export function GroupPurchaseFace(props: GroupProductComponentProps) {
    const [form] = useForm();
    const debounce = useRef(new UDebounce<FormValue>(100));
    const [formValue, setFormValue] = useState<FormValue>(props.value);
    const [productFaceData] = useHttp<GetGroupPurchaseProductsReqParams, PageResponseObj<GroupPurchaseProductObj>>({
        url: urlConst.GET_GROUP_PURCHASE_PRODUCTS,
        methods: 'get',
        params: {
            serviceType: EnumProductServiceType.FACE,
            scene: 'mergePurchase',
        },
        defaultValue: {
            order: '',
            orderBy: 'default',
            pageNo: 0,
            pageSize: 0,
            result: [],
        },
    }, true);
    const [apiInfo] = useHttp<null, ProductApiInfo[], ProductApiInfo[], {product: string}>({
        url: urlConst.GET_PRODUCT_API_ID,
        methods: 'get',
        query: {
            product: 'face',
        },
        defaultValue: [] as any,
    }, true);
    const apiSelRef = useRef<GroupPurchaseTabSelectPropsData[0]>(null);
    const regionData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productFaceData.result.forEach(item => {
            if (!res.find(i => i.value === item.region)) {
                res.push({
                    label: getZhFromFlavorDisplayName(item.flavorDisplayName, 'region') || item.region,
                    value: item.region,
                });
            }
        });
        return res;
    }, [productFaceData]);

    const subServiceTypeData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        if (productFaceData.result.length > 0 && apiInfo.length > 0) {
            productFaceData.result.forEach(item => {
                const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
                if (
                    !res.find(i => i.value === subServiceType)
                    && item.region === formValue.region
                ) {
                    const apiTarget = getApiNameAndIdByValueDisplayName(
                        getZhFromFlavorDisplayName(item.flavorDisplayName, 'subServiceType'),
                        apiInfo
                    );
                    res.push({
                        label: apiTarget.api_name as string,
                        value: subServiceType,
                        extr: apiTarget.api_id,
                    });
                }
            });
        }
        return res;
    }, [apiInfo, formValue.region, productFaceData.result]);

    const countData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productFaceData.result.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            const count = getProductFieldVal(item, 'count', false, true) as string;
            if (
                !res.find(i => i.value === count)
                && item.region === formValue.region
                && subServiceType === formValue.subServiceType
            ) {
                res.push({
                    label: `${getAiCountSplitRange(count)}次`,
                    value: count,
                });
            }
        });
        return res.sort((a: any, b: any) => a.value - b.value);
    }, [formValue.region, formValue.subServiceType, productFaceData.result]);

    const specificationData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productFaceData.result.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            const specification = getProductFieldVal(item, 'Specification_0', false, true) as string;
            if (
                !res.find(i => i.value === specification)
                && item.region === formValue.region
                && subServiceType === formValue.subServiceType
            ) {
                res.push({
                    label: `${getAiCountSplitRange(specification)}次`,
                    value: specification,
                });
            }
        });
        return res.sort((a: any, b: any) => a.value - b.value);
    }, [formValue.region, formValue.subServiceType, productFaceData.result]);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
    });

    debounce.current.subscribe((allVal: FormValue) => {
        const allFields = [{
            name: 'region',
            isFlavor: false,
            val: allVal.region,
        }, {
            name: 'subServiceType',
            isFlavor: true,
            isScale: false,
            val: allVal.subServiceType,
        }, {
            name: 'count',
            isFlavor: true,
            isScale: false,
            val: allVal.count,
        }, {
            name: 'Specification_0',
            isFlavor: true,
            isScale: false,
            val: allVal.specification,
        }];
        const fields = allVal.count ? filter(allFields, o => o.name !== 'Specification_0') : filter(allFields, o => o.name !== 'count');
        const faceProduct = getProductByFields(productFaceData.result, fields);
        if (faceProduct) {
            const params = genPriceParamsByProductObj(faceProduct, omit(allVal, 'count'));
            const list: ConfigList[] = [{
                name: '区域',
                value: regionData.find(item => item.value === allVal.region).label,
            }, {
                name: '接口名称',
                value: subServiceTypeData.find(item => item.value === allVal.subServiceType).label,
            }, {
                name: '调用量',
                value: countData.find(item => item.value === allVal.count).label,
            }, {
                name: '规格',
                value: specificationData.find(item => item.value === allVal.specification).label,
            }, {
                name: '时间周期',
                value: '12个月',
            }];
            const configList = allVal.count
                ? filter(list, o => o.name !== '规格')
                : filter(list, o => o.name !== '调用量');
            props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });
            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {data: [params]}
            ).then(res => {
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    // 下单参数
                    orderParams: {
                        ...params,
                        apiId: apiSelRef.current.extr,
                        displayName: apiSelRef.current.label,
                        parentServiceType: 'FACE',
                    },
                });
            });
        }
    });

    return (
        <Form
            form={form}
            initialValues={formValue}
            className={style['group-purchase-face-form']}
            onValuesChange={(e, allVal) => {
                setFormValue(allVal);
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="区域：" hasBorder>
                <Form.Item
                    name="region"
                >
                    <GroupPurchaseTabSelect
                        data={regionData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="接口名称：">
                <Form.Item
                    name="subServiceType"
                >
                    <USelect
                        className={style['subServiceType-select']}
                        options={subServiceTypeData}
                        triggerChangeDefault
                        onChange={(e, ee) => {
                            apiSelRef.current = ee as any;
                        }}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem
                label="调用量："
                className={formValue.count ? style['show'] : style['hide']}
            >
                <Form.Item
                    name="count"
                >
                    <GroupPurchaseTabSelect
                        data={countData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem
                label="规格："
                className={formValue.specification ? style['show'] : style['hide']}
            >
                <Form.Item
                    name="specification"
                >
                    <GroupPurchaseTabSelect
                        data={specificationData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="时间周期：" hasBorder>
                <Form.Item
                    name="duration"
                >
                    <GroupPurchaseTabSelect
                        data={durationData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
