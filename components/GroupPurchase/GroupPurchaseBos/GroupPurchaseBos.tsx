/* eslint-disable @typescript-eslint/no-unused-vars */
import {Form, Radio} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {useMemo, useRef, useState} from 'react';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {
    GetGroupPurchaseProductsReqParams,
    GroupProductComponentProps,
    GroupPurchaseProductObj,
    GroupPurchaseTabSelectPropsData,
    GroupPurchaseProductPriceParams,
    GroupPurchaseDurationSelectProps,
} from '@common/interface/groupPurchase';
import {useHttp, useOnMount} from '@baidu/bce-hooks';
import {PageResponseObj, netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {EnumProductServiceType} from '@common/interface/common';
import {
    genPriceParamsByProductObj,
    getProductByFields,
    getProductFieldVal,
    getZhFromFlavorDisplayName,
    period2month,
    sortCapacityOptions,
} from '@common/helper/groupPurchase';
import {USelect} from '@common/components/USelect/USelect';
import {UDatePicker} from '@common/components/UDatePicker/UDatePicker';
import cn from 'classnames';
import {cloneDeep} from 'lodash';
import {NumberInput} from '@common/components/NumberInput/NumberInput';
import moment, {Moment} from 'moment';
import {ProductPriceDetailObj} from '@common/interface/page';
import {GroupPurchaseDurationSelect} from '@common/components/page/GroupPurchaseDurationSelect/GroupPurchaseDurationSelect';
import {UDebounce} from '@common/helper/UDebounce';
import style from './GroupPurchaseBos.module.less';

type FormValue = {
    region: string;
    subServiceType: string;
    capacity: string;
    duration: string;
    count: number;
    isCustomize: boolean;
    activeTime: Moment;
};

export function GroupPurchaseBos(props: GroupProductComponentProps) {
    const [form] = useForm();
    const debounce = useRef(new UDebounce<FormValue>(100));
    const [formValue, setFormValue] = useState<FormValue>({...props.value, activeTime: moment()});
    const [durationData, setDurationData] = useState<GroupPurchaseDurationSelectProps['options']>(['6M', '12M']);
    const [productBosData] = useHttp<GetGroupPurchaseProductsReqParams, PageResponseObj<GroupPurchaseProductObj>>({
        url: urlConst.GET_GROUP_PURCHASE_PRODUCTS,
        methods: 'get',
        params: {
            serviceType: EnumProductServiceType.BOS,
            scene: 'mergePurchase',
        },
        defaultValue: {
            order: '',
            orderBy: 'default',
            pageNo: 0,
            pageSize: 0,
            result: [],
        },
    }, true);
    const regionData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productBosData.result.forEach(item => {
            if (!res.find(i => i.value === item.region)) {
                res.push({
                    label: getZhFromFlavorDisplayName(item.flavorDisplayName, 'region') || item.region,
                    value: item.region,
                });
            }
        });
        return res;
    }, [productBosData]);

    const subServiceTypeData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productBosData.result.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            if (
                !res.find(i => i.value === subServiceType)
                && item.region === formValue.region
            ) {
                res.push({
                    label: getZhFromFlavorDisplayName(item.flavorDisplayName, 'subServiceType') || item.subServiceType,
                    value: subServiceType,
                });
            }
        });
        return res;
    }, [formValue.region, productBosData.result]);

    const capacityData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productBosData.result.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            const capacity = getProductFieldVal(item, 'capacity', false, true) as string;
            if (
                !res.find(i => i.value === capacity)
                && item.region === formValue.region
                && subServiceType === formValue.subServiceType
            ) {
                res.push({
                    label: capacity.replace('g', 'GB').replace('t', 'TB').replace('p', 'PB'),
                    value: capacity,
                });
            }
        });
        sortCapacityOptions(res);
        return res;
    }, [formValue.region, formValue.subServiceType, productBosData.result]);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
    });

    debounce.current.subscribe((allVal: FormValue) => {
        const bosProduct = getProductByFields(productBosData.result, [{
            name: 'region',
            isFlavor: false,
            val: allVal.region,
        }, {
            name: 'subServiceType',
            isFlavor: true,
            isScale: false,
            val: allVal.subServiceType,
        }, {
            name: 'capacity',
            isFlavor: true,
            isScale: false,
            val: allVal.capacity,
        }]);

        if (bosProduct) {
            const paramsDuration = GroupPurchaseDurationSelect.getDurationValue(allVal.duration);
            let params = genPriceParamsByProductObj(bosProduct, allVal);
            const configList = [{
                name: '区域',
                value: regionData.find(item => item.value === allVal.region).label,
            }, {
                name: '资源包类型',
                value: subServiceTypeData.find(item => item.value === allVal.subServiceType).label,
            }, {
                name: '规格',
                value: capacityData.find(item => item.value === allVal.capacity).label,
            }, {
                name: '购买时长',
                value: paramsDuration.label,
            }, {
                name: '购买数量',
                value: allVal.count.toString(),
            }];
            props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });

            // 下行流量包的 capacity 需要乘以月份数
            if (allVal.subServiceType === 'ReadBytes') {
                params = cloneDeep(params);
                const monthCount = period2month(params.time.period);
                const capacityFlavor = params.flavor.flavorItems.find(item => item.name === 'capacity');
                const capacityNum = parseInt(capacityFlavor.value as string, 10);
                capacityFlavor.value = (capacityFlavor.value as string).replace(/\d+/, (capacityNum * monthCount).toString());
            }

            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {data: [params]}
            ).then(res => {
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    orderParams: {
                        serviceId: '',
                        serviceGroupId: '',
                        serviceType: 'BOS',
                        parentServiceType: 'BOS',
                        subServiceType: allVal.subServiceType,
                        region: allVal.region,
                        duration: paramsDuration.durationInt,
                        timeUnit: paramsDuration.timeUnit,
                        count: allVal.count,
                        resourceActiveTime: moment(allVal.activeTime).utc().format(),
                        flavor: params.flavor,
                        specs: params.flavor.flavorItems,
                    },
                });
            });
        }
    });
    return (
        <Form
            form={form}
            initialValues={formValue}
            className={style['group-purchase-bos-form']}
            onValuesChange={(e, allVal: FormValue) => {
                setFormValue(allVal);
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="区域：" hasBorder>
                <Form.Item
                    name="region"
                >
                    <GroupPurchaseTabSelect
                        data={regionData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="资源包类型：">
                <Form.Item
                    name="subServiceType"
                >
                    <USelect
                        className={style['subServiceType-select']}
                        options={subServiceTypeData}
                        onChange={e => {
                            const isCDN1ReadBytes = e === 'CDN1ReadBytes';
                            setDurationData(isCDN1ReadBytes ? ['1M'] : ['6M', '12M']);
                        }}
                        triggerChangeDefault
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="规格：" hasBorder>
                <Form.Item
                    name="capacity"
                >
                    <GroupPurchaseTabSelect
                        data={capacityData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="时长：" hasBorder>
                <Form.Item
                    name="duration"
                >
                    <GroupPurchaseDurationSelect
                        options={durationData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem className={style['customize-radio']} label="开通时间选择：">
                <Form.Item
                    name="isCustomize"
                >
                    <Radio.Group>
                        <Radio value={false}>支付后立即生效</Radio>
                        <Radio value>指定时间生效</Radio>
                    </Radio.Group>
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem
                className={cn(style['active-time'], formValue.isCustomize ? style['show'] : style['hide'])}
                label="选择时间"
                hasBorder
            >
                <Form.Item
                    name="activeTime"
                    rules={[{required: formValue.isCustomize, message: '请选择生效时间'}]}
                >
                    <UDatePicker
                        showTime
                        disabledDate={current => current && current < moment().startOf('day')}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="购买数量：" hasBorder>
                <Form.Item
                    name="count"
                >
                    <NumberInput
                        max={50}
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
