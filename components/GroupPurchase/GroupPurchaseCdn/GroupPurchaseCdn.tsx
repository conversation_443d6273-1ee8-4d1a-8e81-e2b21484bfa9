import {Form} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {useMemo, useState, useRef, useEffect} from 'react';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {
    GetGroupPurchaseProductsReqParams,
    GroupProductComponentProps,
    GroupPurchaseProductObj,
    GroupPurchaseTabSelectPropsData,
    GroupPurchaseProductPriceParams,
} from '@common/interface/groupPurchase';
import {useHttp, useOnMount} from '@baidu/bce-hooks';
import {PageResponseObj, netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {EnumProductServiceType} from '@common/interface/common';
import {genPriceParamsByProductObj, getProductByFields, getProductFieldVal, sortCapacityOptions} from '@common/helper/groupPurchase';
import {NumberInput} from '@common/components/NumberInput/NumberInput';
import {ProductPriceDetailObj} from '@common/interface/page';
import {GroupPurchaseDurationSelect} from '@common/components/page/GroupPurchaseDurationSelect/GroupPurchaseDurationSelect';
import {UDebounce} from '@common/helper/UDebounce';
import style from './GroupPurchaseCdn.module.less';

type FormValue = {
    subServiceType: string;
    spec: string;
    duration: string;
    count: number;
};

const subServiceTypeLabel = {
    'default': '流量包',
    'HTTPRequestNum': '动态HTTP协议PV包',
    'HTTPSRequestNum': '动态HTTPS协议PV包',
};

export function GroupPurchaseCdn(props: GroupProductComponentProps) {
    const debounce = useRef(new UDebounce<FormValue>(100));
    const [form] = useForm();
    const [formValue, setFormValue] = useState<FormValue>(props.value);
    const [productCdnData] = useHttp<GetGroupPurchaseProductsReqParams, PageResponseObj<GroupPurchaseProductObj>>({
        url: urlConst.GET_GROUP_PURCHASE_PRODUCTS,
        methods: 'get',
        params: {
            serviceType: EnumProductServiceType.CDN,
            scene: 'mergePurchase',
        },
        defaultValue: {
            order: '',
            orderBy: 'default',
            pageNo: 0,
            pageSize: 0,
            result: [],
        },
    }, true);

    const subServiceTypeData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productCdnData.result.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            if (
                !res.find(i => i.value === subServiceType)
            ) {
                res.push({
                    label: subServiceTypeLabel[subServiceType as keyof typeof subServiceTypeLabel] || subServiceType,
                    value: subServiceType,
                });
            }
        });
        return res;
    }, [productCdnData.result]);
    // 规格
    const specData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productCdnData.result.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            const field = subServiceType === 'default' ? 'capacity' : 'count';
            const val = getProductFieldVal(item, field, false, true) as string;
            if (
                !res.find(i => i.value === val)
                && subServiceType === formValue.subServiceType
            ) {
                res.push({
                    label: val.replace('g', 'GB').replace('t', 'TB').replace('p', 'PB'),
                    value: val,
                } as any);
            }
        });
        sortCapacityOptions(res);
        return res;
    }, [formValue.subServiceType, productCdnData.result]);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
    });

    // 赋默认值后主动触发一次询价
    useEffect(() => {
        if (productCdnData.result?.length) {
            debounce.current.next(form.getFieldsValue());
        }
    }, [productCdnData.result, form]);

    debounce.current.subscribe((allVal: FormValue) => {
        const field = allVal.subServiceType === 'default' ? 'capacity' : 'count';
        const paramsDuration = GroupPurchaseDurationSelect.getDurationValue(allVal.duration);
        const cdnProduct = getProductByFields(productCdnData.result, [{
            name: 'subServiceType',
            isFlavor: true,
            isScale: false,
            val: allVal.subServiceType,
        }, {
            name: field,
            isFlavor: true,
            isScale: false,
            val: allVal.spec,
        }]);
        if (cdnProduct) {
            const params = genPriceParamsByProductObj(cdnProduct, allVal);
            const configList = [{
                name: '资源包类型',
                value: subServiceTypeData.find(item => item.value === allVal.subServiceType).label,
            }, {
                name: '规格',
                value: specData.find(item => item.value === allVal.spec).label,
            }, {
                name: '有效期',
                value: paramsDuration.label,
            }, {
                name: '购买数量',
                value: allVal.count.toString(),
            }];
            props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });
            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {data: [params]}
            ).then(res => {
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    orderParams: {
                        'serviceType': 'CDN',
                        'parentServiceType': 'CDN',
                        'displayName': subServiceTypeLabel[allVal.subServiceType as keyof typeof subServiceTypeLabel],
                        'serviceId': '',
                        'serviceGroupId': '',
                        'subServiceType': allVal.subServiceType,
                        'region': 'global',
                        'duration': paramsDuration.durationInt,
                        'timeUnit': paramsDuration.timeUnit,
                        'count': allVal.count,
                        'flavor': params.flavor,
                        'specs': params.flavor.flavorItems,
                        'signatureId': '',
                    },
                });
            });
        }
    });
    return (
        <Form
            form={form}
            initialValues={formValue}
            className={style['group-purchase-cdn-form']}
            onValuesChange={(e, allVal) => {
                setFormValue(allVal);
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="资源包类型：">
                <Form.Item
                    name="subServiceType"
                >
                    <GroupPurchaseTabSelect
                        data={subServiceTypeData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="规格：" hasBorder>
                <Form.Item
                    name="spec"
                >
                    <GroupPurchaseTabSelect
                        data={specData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="有效期：" hasBorder>
                <Form.Item
                    name="duration"
                >
                    <GroupPurchaseDurationSelect
                        options={['3M', '12M']}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="购买数量：" hasBorder>
                <Form.Item
                    name="count"
                >
                    <NumberInput
                        max={50}
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
