import {Form} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {useRef, useState} from 'react';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {
    GroupProductComponentProps,
} from '@common/interface/groupPurchase';
import {NumberInput} from '@common/components/NumberInput/NumberInput';
import moment from 'moment';
import {useOnMount} from '@baidu/bce-hooks';
import {USelect} from '@common/components/USelect/USelect';
import {UDebounce} from '@common/helper/UDebounce';
import style from './GroupPurchaseSms.module.less';

const timeGranularityData = [
    {
        label: '24个月',
        value: 'P24M',
    },
];

const unitArr = [{
    count: '0.5万条',
    unitPrice: '0.045元/条',
    priceSum: '225.00',
}, {
    count: '1.5万条',
    unitPrice: '0.044元/条',
    priceSum: '660.00',
}, {
    count: '5万条',
    unitPrice: '0.040元/条',
    priceSum: '2000.00',
}, {
    count: '10万条',
    unitPrice: '0.039元/条',
    priceSum: '3900.00',
}, {
    count: '50万条',
    unitPrice: '0.038元/条',
    priceSum: '19000.00',
}, {
    count: '100万条',
    unitPrice: '0.037元/条',
    priceSum: '37000.00',
}, {
    count: '300万条',
    unitPrice: '0.036元/条',
    priceSum: '108000.00',
}];

type FormValue = {
    unit: string;
    purchaseCount: number;
};

export function GroupPurchaseSms(props: GroupProductComponentProps) {
    const [form] = useForm();
    const debounce = useRef(new UDebounce<FormValue>(100));
    const [formValue, setFormValue] = useState<FormValue>(props.value);

    debounce.current.subscribe((allVal: FormValue) => {
        const target = unitArr.find(item => item.count === allVal.unit);
        if (target) {
            const configList = [{
                name: '规格',
                value: `${target.count} ${target.unitPrice}`,
            }, {
                name: '购买数量',
                value: `${allVal.purchaseCount}`,
            }, {
                name: '购买时长',
                value: '24个月',
            }];
            props.onChange && props.onChange({
                priceList: [{
                    serviceType: 'SMS',
                    catalogPrice: +target.priceSum,
                    price: +target.priceSum * allVal.purchaseCount,
                    campaignInfoList: null,
                }],
                configList,
                loading: false,
                orderParams: {
                    serviceId: '',
                    serviceGroupId: '',
                    serviceType: 'SMS',
                    parentServiceType: 'SMS',
                    displayName: '简单消息服务(SMS)',
                    subServiceType: 'domestic',
                    region: 'global',
                    duration: 24,
                    count: allVal.purchaseCount,
                    timeUnit: 'MONTH',
                    resourceActiveTime: moment().utc().format(),
                    flavor: {
                        flavorItems: [
                            {
                                name: 'subServiceType',
                                value: 'domestic',
                                scale: 1,
                            },
                            {
                                name: 'count',
                                value: (parseFloat(allVal.unit) * 10000).toString(),
                                scale: 1,
                            },
                            {
                                name: 'deductPolicy',
                                value: 'DomesticMultiItemPackage',
                                scale: 1,
                            },
                        ],
                    },
                },
            });
        }
    });

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
    });

    return (
        <Form
            form={form}
            initialValues={formValue}
            className={style['group-purchase-sms-form']}
            onValuesChange={(e, allVal) => {
                setFormValue(allVal);
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="规格：" hasBorder>
                <Form.Item
                    name="unit"
                >
                    {/* TabSelect 放不下, 换成用 USelect 展示了 */}
                    {/* <GroupPurchaseTabSelect
                        data={unitArr.map(item => ({
                            label: `${item.count} ${item.unitPrice}`,
                            value: item.count,
                        }))}
                    /> */}
                    <USelect
                        className={style['subServiceType-select']}
                        options={unitArr.map(item => ({
                            label: `${item.count} ${item.unitPrice}`,
                            value: item.count,
                        }))}
                        triggerChangeDefault
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="购买数量：" hasBorder>
                <Form.Item
                    name="purchaseCount"
                >
                    <NumberInput
                        max={50}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="购买时长：" hasBorder>
                <Form.Item
                    name="timeGranularity"
                >
                    <GroupPurchaseTabSelect
                        data={timeGranularityData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
