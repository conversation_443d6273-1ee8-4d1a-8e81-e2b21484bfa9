/* eslint-disable no-console */
import {Form} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {
    GetGroupPurchaseProductsReqParams,
    GroupProductComponentProps,
    GroupPurchaseProductObj,
    GroupPurchaseProductPriceParams,
    GroupPurchaseTabSelectPropsData,
} from '@common/interface/groupPurchase';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {
    useEffect,
    useRef,
    useState,
} from 'react';
import {omit} from 'lodash';
import {USelect, USelectProps} from '@common/components/USelect/USelect';
import {useHttp, useOnMount} from '@baidu/bce-hooks';
import {urlConst} from '@common/constant/urlConst';
import {PageResponseObj, netService} from '@baidu/bce-services';
import {EnumProductServiceType} from '@common/interface/common';
import {ProductPriceDetailObj} from '@common/interface/page';
import {genPriceParamsByProductObj} from '@common/helper/groupPurchase';
import {DefaultOptionType} from 'antd/lib/select';
import {UDebounce} from '@common/helper/UDebounce';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import style from './GroupPurchaseCbs.module.less';


enum SubServiceTypeEnum {
    'bookkeeping' = 'bookkeeping',
    'Business Registration' = 'Business Registration',
    // 'Package1' = 'Package1',
    // 'Package2' = 'Package2',
}

enum TypeEnum {
    'dailijizhang' = 'dailijizhang',
    'gongshangzhuce' = 'gongshangzhuce',
}

const TypeMap = {
    [TypeEnum.dailijizhang]: '代理记账服务',
    [TypeEnum.gongshangzhuce]: '工商注册服务',
};

// const SubServiceTypeSuffixMap = {
//     [SubServiceTypeEnum.bookkeeping]: 'bkprys',
//     [SubServiceTypeEnum['Business Registration']]: '',
//     [SubServiceTypeEnum.Package1]: 'pkg1',
//     [SubServiceTypeEnum.Package2]: 'pkg2',
// };

const SubServiceTypeMap = {
    [SubServiceTypeEnum.bookkeeping]: '代理记账',
    [SubServiceTypeEnum['Business Registration']]: '公司注册',
    // [SubServiceTypeEnum.Package1]: '公司注册超值套餐',
    // [SubServiceTypeEnum.Package2]: '公司注册无忧套餐',
};

enum TaxpayerEnum {
    '小规模纳税人' = '小规模纳税人',
    '一般纳税人' = '一般纳税人',
}

enum DeclarationMethodEnum {
    '零申报' = '零申报',
    '非零申报' = '非零申报',
}

enum ServiceDurationEnum {
    '一年' = '一年'
}

enum CompanyTypeEnum {
    '有限公司' = '有限公司',
    '股份有限公司' = '股份有限公司',
    '集团公司' = '集团公司',
}

type FormValue = {
    // 类别
    type: TypeEnum;
    // 纳税人类型
    taxpayerType: TaxpayerEnum;
    // 申报方式
    declarationMethod: DeclarationMethodEnum;
    // 服务时长
    serviceDuration: ServiceDurationEnum;
    // 公司性质
    companyType: CompanyTypeEnum;
    // 服务类型
    serviceType: SubServiceTypeEnum;
    // 服务区域
    area: string;
};

// 只有这些城市是可以成功下单的
// eslint-disable-next-line max-len
const availableCities = ['北京', '长沙', '常州', '成都', '重庆', '佛山', '广州', '贵阳', '海口', '杭州', '合肥', '济南', '南昌', '南京', '南通', '青岛', '上海', '深圳', '苏州', '天津', '温州', '武汉', '无锡', '厦门', '西安', '郑州', '中山'];

const objTransformTabSelData: <T = any>(item: T, isEnum?: boolean) => GroupPurchaseTabSelectPropsData = (obj, isEnum = true) => {
    return Object.keys(obj).map(item => ({
        label: isEnum ? item as any : obj[item as unknown as keyof typeof obj],
        value: item,
    }));
};

export const GroupPurchaseCbs = (props: GroupProductComponentProps) => {
    const [form] = useForm();
    const debounce = useRef(new UDebounce<FormValue>(300));
    const [formValue, setFormValue] = useState<FormValue>(() => {
        const {
            type,
            taxpayerType,
            area,
            declarationMethod = DeclarationMethodEnum['零申报'],
            serviceDuration = ServiceDurationEnum['一年'],
            companyType = CompanyTypeEnum['有限公司'],
            serviceType = SubServiceTypeEnum.bookkeeping,
        } = props.value;
        return {type, taxpayerType, area, declarationMethod, serviceDuration, companyType, serviceType};
    });
    const [citySelOpts, setCitySelOpts] = useState<USelectProps['options']>([]);
    const currentCitySelOpts = useRef<DefaultOptionType>(null);
    const [cities] = useHttp<never, any>({
        url: urlConst.GET_CBS_REGION,
        methods: 'get',
        defaultValue: {},
    }, true);
    const [productData] = useHttp<GetGroupPurchaseProductsReqParams, PageResponseObj<GroupPurchaseProductObj>>({
        url: urlConst.GET_GROUP_PURCHASE_PRODUCTS,
        methods: 'get',
        params: {
            serviceType: EnumProductServiceType.CBS,
            scene: 'mergePurchase',
        },
        defaultValue: {
            order: '',
            orderBy: 'default',
            pageNo: 0,
            pageSize: 0,
            result: [],
        },
    }, true);
    const getCompareRule = (currFormValue: FormValue) => {
        if (currFormValue.type === TypeEnum.dailijizhang) {
            return `${currFormValue.taxpayerType}-${currFormValue.declarationMethod === DeclarationMethodEnum['非零申报']
                ? '小额'
                : currFormValue.declarationMethod}`;
        } else {
            return currFormValue.companyType === CompanyTypeEnum['集团公司'] && currFormValue.serviceType === SubServiceTypeEnum['Business Registration']
                ? currFormValue.companyType
                : `${currFormValue.companyType}-${currFormValue.taxpayerType}`;
        }
    };
    // 筛选地区
    useEffect(() => {
        const cityAvailableList: USelectProps['options'] = [];
        const compareRule = getCompareRule(formValue);
        productData.result.forEach(item => {
            const flagArr: boolean[] = [false, false];
            item.flavor.forEach(flavorItem => {
                if (flavorItem.name === 'subServiceType') {
                    flagArr[0] = (
                        flavorItem.value === (
                            formValue.type === TypeEnum.dailijizhang
                                ? SubServiceTypeEnum.bookkeeping
                                : formValue.serviceType
                        )
                    );
                } else {
                    flagArr[1] = flavorItem.value === compareRule;
                }
            });
            if (!flagArr.includes(false)) {
                const areaTargetFlavor = item.flavorDisplayName.find(item => item.value === compareRule);
                cityAvailableList.push({
                    value: areaTargetFlavor.flavor.split('_')[0],
                    label: areaTargetFlavor.flavorDisplayName,
                });
            }
        });
        const newCityOptions = availableCities.map(city => cityAvailableList.find(item => item.label === city)).filter(Boolean);
        setCitySelOpts(newCityOptions);
    }, [formValue, productData.result]);

    // 服务区域变化， 如果当前选择的区域不在服务区域列表中，则重置为列表第一项
    useEffect(() => {
        if (citySelOpts.length && form) {
            const target = citySelOpts.find(item => item.value === formValue.area);
            form.setFieldValue('area', target ? target.value : citySelOpts[0].value);
            debounce.current.next(form.getFieldsValue());
        }
    }, [citySelOpts, form, formValue.area]);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
    });

    debounce.current.subscribe((allVal: FormValue) => {
        // 修正一下 serviceType
        const isDailijizhang = allVal.type === TypeEnum.dailijizhang;
        if (isDailijizhang) {
            allVal.serviceType = SubServiceTypeEnum.bookkeeping;
        }
        const target = productData.result.find(item => {
            const flag: any[] = [false, false];
            item.flavor.forEach(flavorItem => {
                if (flavorItem.name === 'subServiceType') {
                    flag[0] = flavorItem.value === allVal.serviceType;
                    flag[3] = flavorItem.value;
                } else {
                    flag[1] = (
                        flavorItem.name.split('_')[0] === allVal.area
                        && flavorItem.value === getCompareRule(allVal)
                    );
                }
            });
            return !flag.includes(false);
        });
        if (target) {
            const params = genPriceParamsByProductObj(target, {
                ...allVal,
                duration: allVal.type === TypeEnum.dailijizhang ? '1Y' : '1D',
            });
            let configList = [{
                name: '类别',
                value: TypeMap[allVal.type],
            }, {
                name: '纳税人类型',
                value: allVal.taxpayerType,
            }, {
                name: '服务区域',
                value: currentCitySelOpts.current?.label as string,
            }];
            const configListDailijizhang = [{
                name: '年开票额',
                value: allVal.declarationMethod,
            }, {
                name: '服务时长',
                value: allVal.serviceDuration,
            }];
            const configListGongshangzhuce = [{
                name: '公司性质',
                value: allVal.companyType,
            }, {
                name: '服务类型',
                value: SubServiceTypeMap[allVal.serviceType],
            }];
            configList = configList.concat(isDailijizhang ? configListDailijizhang : configListGongshangzhuce);
            props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });

            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {data: [params]}
            ).then(res => {
                const areaCode = cities.nameToCode[currentCitySelOpts.current?.label as any];
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    // 代理记账的地域需要添加 'bkprys' 后缀
                    orderParams: isDailijizhang ? {
                        serviceType: 'CBS',
                        parentServiceType: 'CBS',
                        displayName: TypeMap[TypeEnum.dailijizhang],
                        serviceId: '',
                        serviceGroupId: '',
                        region: 'global',
                        timeUnit: 'YEAR',
                        duration: '1',
                        count: 1,
                        subServiceType: SubServiceTypeEnum.bookkeeping,
                        flavor: {
                            flavorItems: [
                                {
                                    name: `${allVal.area}_bkprys`,
                                    value: getCompareRule(allVal),
                                    scale: 1,
                                }, {
                                    name: 'subServiceType',
                                    value: 'bookkeeping',
                                    scale: 1,
                                }, {
                                    name: 'area',
                                    value: areaCode,
                                    scale: null,
                                }, {
                                    name: 'productType',
                                    value: 'BKP',
                                    scale: null,
                                }, {
                                    name: 'taxpayerType',
                                    value: allVal.taxpayerType,
                                    scale: null,
                                }, {
                                    name: 'invoiceAmount',
                                    value: allVal.declarationMethod,
                                    scale: null,
                                },
                            ],
                        },
                        extraConfig: JSON.stringify({
                            bizType: 'BKP',
                            area: areaCode,
                            taxpayerType: allVal.taxpayerType,
                            invoiceAmount: 'ZERO',
                        }),
                    } : {
                        serviceType: 'CBS',
                        parentServiceType: 'CBS',
                        displayName: TypeMap[TypeEnum.gongshangzhuce],
                        serviceId: '',
                        serviceGroupId: '',
                        region: 'global',
                        timeUnit: 'DAY',
                        duration: '1',
                        count: 1,
                        subServiceType: SubServiceTypeEnum['Business Registration'],
                        flavor: {
                            flavorItems: [
                                {
                                    name: 'comType',
                                    value: allVal.companyType,
                                    scale: null,
                                },
                                {
                                    name: 'subServiceType',
                                    value: allVal.serviceType,
                                    scale: 1,
                                },
                                {
                                    name: 'taxpayerType',
                                    value: allVal.taxpayerType,
                                    scale: null,
                                },
                                {
                                    name: 'area',
                                    value: areaCode,
                                    scale: null,
                                },
                                {
                                    name: allVal.area,
                                    value: getCompareRule(allVal),
                                    scale: 1,
                                },
                                {
                                    name: 'productType',
                                    value: 'CRS',
                                    scale: null,
                                },
                            ],
                        },
                        extraConfig: JSON.stringify({
                            bizType: 'COMREG',
                            area: areaCode,
                            comType: allVal.companyType,
                            taxpayerType: allVal.taxpayerType,
                            packageType: null,
                        }),
                    },
                });
            });
        }
    });

    return (
        <Form
            form={form}
            initialValues={formValue}
            className={style['group-purchase-cbs-form']}
            onValuesChange={(e, allVal) => {
                setFormValue(allVal);
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="类别：">
                <Form.Item
                    name="type"
                >
                    <GroupPurchaseTabSelect data={objTransformTabSelData(TypeMap, false)} />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="纳税人类型：">
                <Form.Item
                    name="taxpayerType"
                >
                    <GroupPurchaseTabSelect data={objTransformTabSelData(TaxpayerEnum)} />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="服务区域：">
                <Form.Item
                    name="area"
                >
                    <USelect
                        className={style['area-sel']}
                        options={citySelOpts}
                        onChange={(e, opt) => {currentCitySelOpts.current = opt as any;}}
                        triggerChangeDefault
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <div className={formValue.type === TypeEnum.gongshangzhuce ? style['hide'] : style['show']}>
                <GroupBuyRowItem label="年开票额：">
                    <Form.Item
                        name="declarationMethod"
                    >
                        <GroupPurchaseTabSelect data={objTransformTabSelData(DeclarationMethodEnum, false)} />
                    </Form.Item>
                </GroupBuyRowItem>
                <GroupBuyRowItem label="服务时长：">
                    <Form.Item
                        name="serviceDuration"
                    >
                        <GroupPurchaseTabSelect data={objTransformTabSelData(ServiceDurationEnum)} />
                    </Form.Item>
                </GroupBuyRowItem>
            </div>
            <div className={formValue.type === TypeEnum.dailijizhang ? style['hide'] : style['show']}>
                <GroupBuyRowItem label="公司性质：">
                    <Form.Item
                        name="companyType"
                    >
                        <GroupPurchaseTabSelect data={objTransformTabSelData(CompanyTypeEnum)} />
                    </Form.Item>
                </GroupBuyRowItem>
                <GroupBuyRowItem label="服务类型：">
                    <Form.Item
                        name="serviceType"
                    >
                        <GroupPurchaseTabSelect
                            data={
                                Object.keys(omit(SubServiceTypeMap, SubServiceTypeEnum.bookkeeping)).map(item => ({
                                    label: SubServiceTypeMap[item as unknown as keyof typeof SubServiceTypeMap],
                                    value: item,
                                }))
                            }
                        />
                    </Form.Item>
                </GroupBuyRowItem>
            </div>
        </Form>
    );
};
