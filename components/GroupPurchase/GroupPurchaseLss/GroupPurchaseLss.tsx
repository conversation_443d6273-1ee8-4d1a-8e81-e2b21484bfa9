import {Form} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {useMemo, useState, useEffect, useRef} from 'react';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {
    GetGroupPurchaseProductsReqParams,
    GroupProductComponentProps,
    GroupPurchaseProductObj,
    GroupPurchaseTabSelectPropsData,
    GroupPurchaseProductPriceParams,
    GroupPurchaseTabSelectProps,
} from '@common/interface/groupPurchase';
import {useHttp, useOnMount} from '@baidu/bce-hooks';
import {PageResponseObj, netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {EnumProductServiceType} from '@common/interface/common';
import {
    genPriceParamsByProductObj,
    getProductByFields,
    getProductFieldVal,
    getZhFromFlavorDisplayName,
    sortCapacityOptions,
} from '@common/helper/groupPurchase';
import {NumberInput} from '@common/components/NumberInput/NumberInput';
import {ProductPriceDetailObj} from '@common/interface/page';
import {USelect} from '@common/components/USelect/USelect';
import {UDebounce} from '@common/helper/UDebounce';
import {GroupPurchaseDurationSelect} from '@common/components/page/GroupPurchaseDurationSelect/GroupPurchaseDurationSelect';
import style from './GroupPurchaseLss.module.less';

type FormValue = {
    type: 'package' | 'transcode';
    // 转码标准
    subServiceType: string;
    // 流量规格
    capacity: string;
    // 转码规格
    spec: string;
    // 购买数量
    count: number;
    duration: string;
};

const typeData: GroupPurchaseTabSelectProps['data'] = [
    {
        label: '流量包',
        value: 'package',
    },
    // {
    //     label: '转码包',
    //     value: 'transcode',
    // },
];

export function GroupPurchaseLss(props: GroupProductComponentProps) {
    const debounce = useRef(new UDebounce<FormValue>(100));
    const [form] = useForm();
    const [formValue, setFormValue] = useState<FormValue>(props.value);
    const isPackage = useMemo(() => formValue.type === 'package', [formValue.type]);
    const [productLssData] = useHttp<GetGroupPurchaseProductsReqParams, PageResponseObj<GroupPurchaseProductObj>>({
        url: urlConst.GET_GROUP_PURCHASE_PRODUCTS,
        methods: 'get',
        params: {
            serviceType: EnumProductServiceType.LSS,
            scene: 'mergePurchase',
        },
        defaultValue: {
            order: '',
            orderBy: 'default',
            pageNo: 0,
            pageSize: 0,
            result: [],
        },
    }, true);

    const capacityData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        if (!isPackage) {
            return [];
        }
        const res: GroupPurchaseTabSelectPropsData = [];
        productLssData.result.forEach(item => {
            const capacity = getProductFieldVal(item, 'capacity', false, true) as string;
            if (
                capacity
                && !res.find(i => i.value === capacity)
            ) {
                res.push({
                    label: capacity.replace('g', 'GB').replace('t', 'TB').replace('p', 'PB'),
                    value: capacity,
                });
            }
        });
        sortCapacityOptions(res);
        return res;
    }, [isPackage, productLssData.result]);

    const subServiceTypeData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        if (isPackage) {
            return [];
        }
        const res: GroupPurchaseTabSelectPropsData = [];
        productLssData.result.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            if (
                subServiceType
                && !res.find(i => i.value === subServiceType)
            ) {
                res.push({
                    label: getZhFromFlavorDisplayName(item.flavorDisplayName, 'subServiceType') || item.subServiceType,
                    value: subServiceType,
                });
            }
        });
        return res;
    }, [isPackage, productLssData.result]);

    const specData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        if (isPackage) {
            return [];
        }
        const res: GroupPurchaseTabSelectPropsData = [];
        productLssData.result.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            const count = getProductFieldVal(item, 'count', false, true) as string;
            if (
                count
                && !res.find(i => i.value === count)
                && subServiceType === formValue.subServiceType
            ) {
                res.push({
                    label: count,
                    value: count,
                });
            }
        });
        return res;
    }, [formValue.subServiceType, isPackage, productLssData.result]);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
    });

    // 赋默认值后主动触发一次询价
    useEffect(() => {
        if (productLssData.result?.length) {
            debounce.current.next(form.getFieldsValue());
        }
    }, [productLssData.result, form]);

    debounce.current.subscribe((allVal: FormValue) => {
        // 流量包
        const isPackage = allVal.type === 'package';
        const lssProduct = getProductByFields(
            productLssData.result,
            isPackage ? [{
                name: 'capacity',
                isFlavor: true,
                isScale: false,
                val: allVal.capacity,
            }] : [{
                name: 'subServiceType',
                isFlavor: true,
                isScale: false,
                val: allVal.subServiceType,
            }, {
                name: 'count',
                isFlavor: true,
                isScale: false,
                val: allVal.spec,
            }]
        );
        if (lssProduct) {
            const paramsDuration = GroupPurchaseDurationSelect.getDurationValue(allVal.duration);
            const params = genPriceParamsByProductObj(
                lssProduct,
                allVal
            );
            const configList = [{
                name: '类型',
                value: typeData.find(item => item.value === allVal.type).label,
            }, {
                name: '有效期',
                value: paramsDuration.label,
            }, {
                name: '购买数量',
                value: allVal.count.toString(),
            }];
            props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });
            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {data: [params]}
            ).then(res => {
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    orderParams: {
                        serviceType: 'LSS',
                        parentServiceType: 'LSS',
                        serviceId: '',
                        serviceGroupId: '',
                        subServiceType: getProductFieldVal(lssProduct, 'subServiceType', false, true),
                        displayName: 'LSS（音视频直播）',
                        region: 'global',
                        duration: paramsDuration.durationInt,
                        timeUnit: paramsDuration.timeUnit,
                        count: allVal.count,
                        flavor: params.flavor,
                        specs: params.flavor.flavorItems,
                    },
                });
            });
        }
    });

    return (
        <Form
            form={form}
            initialValues={formValue}
            className={style['group-purchase-lss-form']}
            onValuesChange={(e, allVal) => {
                setFormValue(allVal);
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="类型：">
                <Form.Item
                    name="type"
                >
                    <GroupPurchaseTabSelect
                        data={typeData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem
                label="流量规格："
                hasBorder
                className={formValue.type === 'package' ? style['row-show'] : style['row-hide']}
            >
                <Form.Item
                    name="capacity"
                >
                    <GroupPurchaseTabSelect
                        data={capacityData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="转码标准：" className={formValue.type === 'transcode' ? style['row-show'] : style['row-hide']}>
                <Form.Item
                    name="subServiceType"
                >
                    <USelect
                        className={style['subServiceType-sel']}
                        options={subServiceTypeData}
                        triggerChangeDefault
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem
                label="转码规格："
                hasBorder
                className={formValue.type === 'transcode' ? style['row-show'] : style['row-hide']}
            >
                {specData.length <= 0
                    ? <p>暂无规格，请更改转码标准</p>
                    : (
                        <Form.Item
                            name="spec"
                            className={style['spec-form-item']}
                        >
                            <GroupPurchaseTabSelect
                                className={specData.length <= 0 ? style['row-hide'] : style['row-show']}
                                data={specData}
                            />
                        </Form.Item>
                    )
                }
            </GroupBuyRowItem>
            <GroupBuyRowItem label="有效期：" hasBorder>
                <Form.Item
                    name="duration"
                >
                    <GroupPurchaseDurationSelect
                        options={['12M']}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="购买数量：" hasBorder>
                <Form.Item
                    name="count"
                >
                    <NumberInput
                        max={50}
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
