import {Form, message} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {useMemo, useRef, useState} from 'react';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {
    GetGroupPurchaseProductsReqParams,
    GroupProductComponentProps,
    GroupPurchaseProductObj,
    GroupPurchaseTabSelectPropsData,
    GroupPurchaseProductPriceParams,
} from '@common/interface/groupPurchase';
import {useHttp, useOnMount} from '@baidu/bce-hooks';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {EnumProductServiceType, ProductApiInfo} from '@common/interface/common';
import {
    genPriceParamsByProductObj,
    getAiCountSplitRange,
    getApiNameAndIdByValueDisplayName,
    getProductByFields,
    getProductFieldVal,
    getZhFromFlavorDisplayName,
    setProductFieldVal,
} from '@common/helper/groupPurchase';
import {USelect} from '@common/components/USelect/USelect';
import {UDebounce} from '@common/helper/UDebounce';
import {filter, isArray, omit, uniqBy} from 'lodash';
import {ProductPriceDetailObj} from '@common/interface/page';
import style from './GroupPurchaseSpeech.module.less';

type FormValue = {
    region: string;
    apiId: string;
    count: string;
    specification: string;
    duration: string;
};
type ConfigList = {
    name: string;
    value: string;
    id?: string;
};
const durationData = [
    {
        label: '12个月',
        value: '12M',
    },
];
const unitMap: {
    [p in ProductApiInfo['console_unit']]: string
} = {
    'minute': '分钟',
    'hour': '小时',
    'character': '字符',
};

function sortSpecOptions(options: GroupPurchaseTabSelectPropsData) {
    return options.sort((a, b) => {
        const numA = parseInt(a.value as string, 10);
        const numB = parseInt(b.value as string, 10);
        return numA - numB;
    });
}

export function GroupPurchaseSpeech(props: GroupProductComponentProps) {
    const debounce = useRef(new UDebounce<FormValue>(100));
    const [form] = useForm();
    const [formValue, setFormValue] = useState<FormValue>(props.value);
    const [currProductApi, setCurrProductApi] = useState<ProductApiInfo & {subServiceType: string}>(null);
    const [apiInfo] = useHttp<null, ProductApiInfo[], ProductApiInfo[], {product: string}>({
        url: urlConst.GET_PRODUCT_API_ID,
        methods: 'get',
        query: {
            product: 'speech',
        },
        defaultValue: [] as any,
    }, true);
    const [productSpeechData, setProductSpeechData] = useState<GroupPurchaseProductObj[]>([]);

    const regionData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productSpeechData.forEach(item => {
            if (!res.find(i => i.value === item.region)) {
                res.push({
                    label: getZhFromFlavorDisplayName(item.flavorDisplayName, 'region') || item.region,
                    value: item.region,
                });
            }
        });
        return res;
    }, [productSpeechData]);

    const subServiceTypeData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        if (productSpeechData.length > 0 && apiInfo.length > 0) {
            productSpeechData.forEach(item => {
                if (item.region === formValue.region) {
                    const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
                    const apiTarget = getApiNameAndIdByValueDisplayName(
                        getZhFromFlavorDisplayName(item.flavorDisplayName, 'subServiceType'),
                        apiInfo
                    );
                    if (isArray(apiTarget.api_name)) {
                        apiTarget.api_name.forEach(api => {
                            res.push({
                                label: api.api_name,
                                value: api.api_id,
                                extr: {
                                    ...api,
                                    subServiceType,
                                },
                            });
                        });
                    } else {
                        res.push({
                            label: apiTarget.api_name,
                            value: apiTarget.api_id,
                            extr: {
                                ...apiTarget,
                                subServiceType,
                            },
                        });
                    }
                }
            });
        }
        return uniqBy(res, 'value');
    }, [apiInfo, formValue.region, productSpeechData]);

    const isMinuteToHour = useMemo(() => {
        return currProductApi?.p3m_unit === 'minute' && currProductApi?.console_unit === 'hour';
    }, [currProductApi?.console_unit, currProductApi?.p3m_unit]);
    const countData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productSpeechData.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            let count = getProductFieldVal(item, 'count', false, true) as string;
            count = isMinuteToHour
                ? `${+count / 60}`
                : count;
            if (
                +count > 0
                && !res.find(i => i.value === count)
                && item.region === formValue.region
                && subServiceType === currProductApi?.subServiceType
            ) {
                res.push({
                    label: `${getAiCountSplitRange(count)}${unitMap[currProductApi?.console_unit] || '次'}`,
                    value: count,
                });
            }
        });
        return sortSpecOptions(res);
    }, [currProductApi?.console_unit, currProductApi?.subServiceType, formValue.region, isMinuteToHour, productSpeechData]);

    const specificationData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productSpeechData.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            let specification = getProductFieldVal(item, 'Specification_0', false, true) as string;
            specification = isMinuteToHour
                ? `${+specification / 60}`
                : specification;
            if (
                +specification > 0
                && !res.find(i => i.value === specification)
                && item.region === formValue.region
                && subServiceType === currProductApi?.subServiceType
            ) {
                res.push({
                    label: `${getAiCountSplitRange(specification)}${unitMap[currProductApi?.console_unit] || '次'}`,
                    value: specification,
                });
            }
        });
        return sortSpecOptions(res);
    }, [currProductApi?.console_unit, currProductApi?.subServiceType, formValue.region, isMinuteToHour, productSpeechData]);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
        netService.get<GetGroupPurchaseProductsReqParams, GroupPurchaseProductObj>(
            urlConst.GET_GROUP_PURCHASE_PRODUCTS,
            {
                serviceType: EnumProductServiceType.SPEECH,
                scene: 'mergePurchase',
            }
        ).then(res => {
            // 剔除短语音审核 (subServiceType: Short_Speechcensoring_Count)
            setProductSpeechData(res.page.result.filter(i => i.subServiceType !== 'Short_Speechcensoring_Count'));
        }).catch(err => {
            message.error('获取语音识别产品信息失败');
            console.error('获取语音识别产品信息失败', err);
        });
    });

    debounce.current.subscribe((allVal: FormValue) => {
        const isCount = allVal.count && countData.length > 0;
        const allFields = [{
            name: 'region',
            isFlavor: false,
            val: allVal.region,
        }, {
            name: 'subServiceType',
            isFlavor: true,
            isScale: false,
            val: currProductApi?.subServiceType,
        }, {
            name: 'count',
            isFlavor: true,
            isScale: false,
            val: isMinuteToHour ? `${+allVal.count * 60}` : allVal.count,
        }, {
            name: 'Specification_0',
            isFlavor: true,
            isScale: false,
            val: isMinuteToHour ? `${+allVal.specification * 60}` : allVal.specification,
        }];
        const fields = isCount
            ? filter(allFields, o => o.name !== 'Specification_0')
            : filter(allFields, o => o.name !== 'count');
        const speechProduct = getProductByFields(productSpeechData, fields);
        if (speechProduct) {
            const list: ConfigList[] = [{
                name: '区域',
                value: regionData.find(item => item.value === allVal.region).label,
            }, {
                name: '接口名称',
                value: subServiceTypeData.find(item => item.value === allVal.apiId).label,
            }, {
                name: '规格',
                value: countData.find(item => item.value === allVal.count)?.label,
                id: 'count',
            }, {
                name: '规格',
                value: specificationData.find(item => item.value === allVal.specification)?.label,
                id: 'Specification_0',
            }, {
                name: '时间周期',
                value: '12个月',
            }];
            const configList = isCount
                ? filter(list, o => o.id !== 'Specification_0')
                : filter(list, o => o.id !== 'count');
            props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });
            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {
                    data: [
                        genPriceParamsByProductObj(
                            speechProduct,
                            omit(allVal, 'count')
                        ),
                    ],
                }
            ).then(res => {
                // 下单的时候 小时对应的需要count 需要除以60处理
                const orderSpeechProduct = isMinuteToHour ? (
                    isCount
                        ? setProductFieldVal(speechProduct, 'count', false, true, allVal.count)
                        : setProductFieldVal(speechProduct, 'Specification_0', false, true, allVal.specification)
                ) : speechProduct;
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    // 下单参数
                    orderParams: {
                        ...genPriceParamsByProductObj(
                            orderSpeechProduct,
                            omit(allVal, 'count')
                        ),
                        apiId: allVal.apiId,
                        displayName: subServiceTypeData.find(item => item.value === allVal.apiId).label,
                        parentServiceType: 'SPEECH',
                    },
                });
            });
        }
    });

    return (
        <Form
            form={form}
            initialValues={formValue}
            className={style['group-purchase-speech-form']}
            onValuesChange={(e, allVal) => {
                setFormValue(allVal);
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="区域：" hasBorder>
                <Form.Item
                    name="region"
                >
                    <GroupPurchaseTabSelect
                        data={regionData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="接口名称：">
                <Form.Item
                    name="apiId"
                >
                    <USelect
                        className={style['subServiceType-select']}
                        options={subServiceTypeData}
                        triggerChangeDefault
                        onChange={(e, ee: any) => {
                            setCurrProductApi({...ee.extr});
                        }}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem
                label="规格："
                className={formValue.count && countData.length ? style['show'] : style['hide']}
            >
                <Form.Item
                    name="count"
                >
                    <GroupPurchaseTabSelect
                        data={countData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem
                label="规格："
                className={formValue.specification && specificationData.length ? style['show'] : style['hide']}
            >
                <Form.Item
                    name="specification"
                >
                    <GroupPurchaseTabSelect
                        data={specificationData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="时间周期：" hasBorder>
                <Form.Item
                    name="duration"
                >
                    <GroupPurchaseTabSelect
                        data={durationData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
