/* eslint-disable @typescript-eslint/no-unused-vars */
import {ProductPriceDetailObj} from '@common/interface/page';
import {formatPriceNumber} from '../utils';
import styles from './PriceDetail.module.less';

// 多个 price 时 popover 组件的内容
export default function PriceDetail(props: {price: ProductPriceDetailObj[]}) {
    const {price} = props;
    return (
        <>
            {price.map(item => (
                <div key={item.serviceType}>
                    <span className={styles.label}>{item.serviceType}</span>
                    <span className={styles.price}>￥{formatPriceNumber(item.price)}</span>
                </div>
            ))}
        </>
    );
}
