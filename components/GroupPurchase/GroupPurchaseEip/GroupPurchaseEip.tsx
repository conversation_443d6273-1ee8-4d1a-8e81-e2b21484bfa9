import {Form} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {useMemo, useRef, useState, useEffect} from 'react';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {
    GetGroupPurchaseProductsReqParams,
    GroupProductComponentProps,
    GroupPurchaseProductObj,
    GroupPurchaseTabSelectPropsData,
    GroupPurchaseProductPriceParams,
} from '@common/interface/groupPurchase';
import {useHttp, useOnMount} from '@baidu/bce-hooks';
import {PageResponseObj, netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {EnumProductServiceType} from '@common/interface/common';
import {genPriceParamsByProductObj, getProductByFields, getProductFieldVal, getZhFromFlavorDisplayName} from '@common/helper/groupPurchase';
import {NumberInput} from '@common/components/NumberInput/NumberInput';
import {ProductPriceDetailObj} from '@common/interface/page';
import USliderInput from '@common/components/USliderInput/USliderInput';
import {UDebounce} from '@common/helper/UDebounce';
import {GroupPurchaseDurationSelect} from '@common/components/page/GroupPurchaseDurationSelect/GroupPurchaseDurationSelect';
import style from './GroupPurchaseEip.module.less';

const sizeUnit = 'Mbps';

type FormValue = {
    region: string;
    subServiceType: string;
    bandwidth: number;
    duration: string;
    count: number;
};


export function GroupPurchaseEip(props: GroupProductComponentProps) {
    const [form] = useForm();
    const debounce = useRef(new UDebounce<FormValue>(300));
    const [formValue, setFormValue] = useState<FormValue>(props.value);
    const productDataRef = useRef(null);
    const [productData] = useHttp<GetGroupPurchaseProductsReqParams, PageResponseObj<GroupPurchaseProductObj>>({
        url: urlConst.GET_GROUP_PURCHASE_PRODUCTS,
        methods: 'get',
        params: {
            serviceType: EnumProductServiceType.EIP,
            scene: 'mergePurchase',
        },
        defaultValue: {
            order: '',
            orderBy: 'default',
            pageNo: 0,
            pageSize: 0,
            result: [],
        },
        succCallBack: e => {
            productDataRef.current = e;
        },
    }, true);
    // 获取资源默认分组的groupId
    const [groupId] = useHttp<never, string, any>({
        url: urlConst.GET_RESOURCE_GROUP_ID,
        methods: 'get',
        defaultValue: '',
        transform: e => {
            const data: any[] = e?.groups || [];
            const defaultId = data.find(item => item.name === '默认分组')?.groupId || '';
            return defaultId;
        },
    }, true);
    const regionData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productData.result.forEach(item => {
            if (!res.find(i => i.value === item.region)) {
                res.push({
                    label: getZhFromFlavorDisplayName(item.flavorDisplayName, 'region') || item.region,
                    value: item.region,
                });
            }
        });
        return res;
    }, [productData]);

    const subServiceTypeData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productData.result.forEach(item => {
            const subServiceType = getProductFieldVal(item, 'subServiceType', false, true) as string;
            if (
                !res.find(i => i.value === subServiceType)
                && item.region === formValue.region
            ) {
                const label = getZhFromFlavorDisplayName(item.flavorDisplayName, 'subServiceType') || item.subServiceType;
                res.push({
                    label: label === 'default' ? '标准型BGP' : label,
                    value: subServiceType,
                });
            }
        });
        return res;
    }, [formValue.region, productData.result]);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
    });

    // 赋默认值后主动触发一次询价
    useEffect(() => {
        if (productData.result?.length) {
            debounce.current.next(form.getFieldsValue());
        }
    }, [productData.result, form]);

    debounce.current.subscribe((allVal: FormValue) => {
        const target = getProductByFields(productData.result, [{
            name: 'region',
            isFlavor: false,
            val: allVal.region,
        }, {
            name: 'subServiceType',
            isFlavor: true,
            isScale: false,
            val: allVal.subServiceType,
        }]);
        if (target) {
            const paramsDuration = GroupPurchaseDurationSelect.getDurationValue(allVal.duration);
            const params = genPriceParamsByProductObj(target, allVal);
            const configList = [{
                name: '地域',
                value: regionData.find(item => item.value === allVal.region).label,
            }, {
                name: '线路类型',
                value: subServiceTypeData.find(item => item.value === allVal.subServiceType).label,
            }, {
                name: '购买时长',
                value: paramsDuration.label,
            }, {
                name: '公网带宽',
                value: `${allVal.bandwidth}${sizeUnit}`,
            }, {
                name: '购买数量',
                value: allVal.count.toString(),
            }];
            props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });
            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {data: [params]}
            ).then(res => {
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    orderParams: {
                        serviceType: 'EIP',
                        parentServiceType: 'EIP',
                        serviceId: '',
                        serviceGroupId: '',
                        region: allVal.region,
                        duration: paramsDuration.durationInt,
                        timeUnit: paramsDuration.timeUnit,
                        count: allVal.count,
                        subServiceType: allVal.subServiceType,
                        flavor: params.flavor,
                        specs: params.flavor.flavorItems,
                        extraConfig: JSON.stringify({
                            resourceGroupId: groupId,
                            bandWidth: allVal.bandwidth,
                        }),
                        signatureId: '',
                    },
                });
            });
        }
    });

    return (
        <Form
            form={form}
            initialValues={formValue}
            className={style['group-purchase-eip-form']}
            onValuesChange={(e, allVal) => {
                setFormValue(allVal);
                debounce.current.next(allVal);
            }}
        >
            <GroupBuyRowItem label="区域：" hasBorder>
                <Form.Item
                    name="region"
                >
                    <GroupPurchaseTabSelect
                        data={regionData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="线路类型：">
                <Form.Item
                    name="subServiceType"
                >
                    <GroupPurchaseTabSelect
                        // 监听值的变化，若变化则重置公网带宽值
                        onChange={(e, val) => {
                            if (val.label !== formValue.subServiceType) {
                                form.setFieldsValue({
                                    bandwidth: val.value === 'default' ? 1 : 100,
                                });
                                debounce.current.next(form.getFieldsValue());
                            }
                        }}
                        data={subServiceTypeData}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="时长：" hasBorder>
                <Form.Item
                    name="duration"
                >
                    <GroupPurchaseDurationSelect
                        options={['1M', '2M', '3M', '4M', '5M', '6M', '1Y', '2Y', '3Y']}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="公网带宽：" hasBorder>
                <Form.Item
                    name="bandwidth"
                >
                    <USliderInput
                        min={formValue.subServiceType === 'default' ? 1 : 100}
                        max={formValue.subServiceType === 'default' ? 2048 : 5000}
                        unit={sizeUnit}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="购买数量：" hasBorder>
                <Form.Item
                    name="count"
                >
                    <NumberInput
                        max={50}
                    />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
