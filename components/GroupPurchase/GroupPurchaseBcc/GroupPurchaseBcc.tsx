/* eslint-disable max-len */
import {Ioc} from '@baidu/bce-decorators';
import {getCookie, removeFromArrayByCondition} from '@baidu/bce-helper';
import {useHttp, useOnMount, useStateRef} from '@baidu/bce-hooks';
import {PageResponseObj, netService} from '@baidu/bce-services';
import USliderInput from '@common/components/USliderInput/USliderInput';
import {GroupBuyRowItem} from '@common/components/page/GroupBuyRowItem/GroupBuyRowItem';
import {GroupPurchaseTabSelect} from '@common/components/page/GroupPurchaseTabSelect/GroupPurchaseTabSelect';
import {urlConst} from '@common/constant/urlConst';
import {getProductFieldVal, getProductByFields, getZhFromFlavorDisplayName, genPriceParamsByProductObj} from '@common/helper/groupPurchase';
import {BCCImagesObj, BCCSysObj} from '@common/interface/groupBuy';
import {
    GroupProductComponentProps,
    GetGroupPurchaseProductsReqParams,
    GroupPurchaseProductObj,
    GroupPurchaseProductPriceParams,
    GroupPurchaseTabSelectPropsData,
    GroupPurchaseProductFlavorObj,
} from '@common/interface/groupPurchase';
import {UEnvService} from '@common/services/env';
import {Form, Select} from 'antd';
import {useForm} from 'antd/lib/form/Form';
import {DefaultOptionType} from 'antd/lib/select';
import {useEffect, useMemo, useState, useRef} from 'react';
import {utc} from 'moment';
import {ProductPriceDetailObj} from '@common/interface/page';
import {PRODUCT_PRICE_SHA_KEY} from '@common/constant/variableConst';
import {EnumProductServiceType} from '@common/interface/common';
import {getLogicalZone} from '@common/helper/purchase';
import {cloneDeep} from 'lodash';
import sha256 from 'crypto-js/sha256';
import Base64 from 'crypto-js/enc-base64';
import {UDebounce} from '@common/helper/UDebounce';
import {GroupPurchaseDurationSelect} from '@common/components/page/GroupPurchaseDurationSelect/GroupPurchaseDurationSelect';
import selectStyles from '@common/components/USelect/main.module.less';
import style from './GroupPurchaseBcc.module.less';

const isClient = Ioc(UEnvService).isClient;
// 处理异步触发表单change防抖  lodash的防抖需要的是同一个函数

const sizeUnit = 'G';
const bandwidthUnit = 'M';

type VersionOptObj = DefaultOptionType & {data: BCCImagesObj};

type FormValue = {
    region: string;
    spec: string;
    cpu: number;
    memory: number;
    osType: string;
    osVersion: string;
    bandwidth: number;
    size: number;
    duration: string;
};

// 镜像
function versionOptAssemble(data: BCCSysObj, spec: string): VersionOptObj[] {
    const res: VersionOptObj[] = [];
    const specArr = spec.split('.');
    const visiableData: string[] = data.visible[`${specArr[0]}.${specArr[1]}`];
    visiableData && visiableData.forEach(id => {
        const bccImage = data.images.find(item => item.imageId === id);
        const osNameOpt = res.find(item => item.value === bccImage.osName);
        if (osNameOpt) {
            const children = osNameOpt.children;
            // 去重 + 去除不需要的
            if (
                !children.find(item => item.value === bccImage.osVersion)
                && bccImage.osArch !== 'aarch64 (64bit)'
            ) {
                children.push({
                    label: bccImage.osVersion,
                    value: bccImage.osVersion,
                    data: bccImage,
                    // className: style['sel-options'],
                });
            }
        } else {
            res.push({
                label: bccImage.osName,
                value: bccImage.osName,
                data: bccImage,
                // className: style['sel-options'],
                children: [{
                    label: bccImage.osVersion,
                    value: bccImage.osVersion,
                    data: bccImage,
                    // className: style['sel-options'],
                }],
            });
        }
    });
    return res;
}

// 以 label 为比较对象, 对 options 降序
function sortOptions(options: Array<{label: string}>) {
    return options.sort((a, b) => {
        if (a.label < b.label) {
            return 1;
        } else if (a.label > b.label) {
            return -1;
        } else {
            return 0;
        }
    });
}

export function GroupPurchaseBcc(props: GroupProductComponentProps) {
    const debounce = useRef(new UDebounce<null>(300));
    const [form] = useForm<FormValue>();
    const [formValue, setFormValue] = useState<FormValue>(props.value);
    const formValueLabel = useRef<{
        [p in keyof FormValue]?: string;
    }>({});
    const [versionOpt, setVersionOpt, versionOptRef] = useStateRef<VersionOptObj[]>([]);
    const [imageOpt, setImageOpt] = useState<VersionOptObj[]>([]);
    const [minDiskSize, setMinDiskSize] = useState<number>(20);
    // 注意：当前选中的规格和镜像数据有关系
    // const [specActiveTabSelectItem, setSpecActiveTabSelectItem] = useState<GroupPurchaseTabSelectPropsData[0]>({} as any);

    const [productBccData] = useHttp<GetGroupPurchaseProductsReqParams, PageResponseObj<GroupPurchaseProductObj>>({
        url: urlConst.GET_GROUP_PURCHASE_PRODUCTS,
        methods: 'get',
        params: {
            serviceType: EnumProductServiceType.BCC,
            scene: 'mergePurchase',
        },
        defaultValue: {
            order: '',
            orderBy: 'default',
            pageNo: 0,
            pageSize: 0,
            result: [],
        },
    }, true);

    const [imagesRes, , imageHttp] = useHttp<{product: string}, BCCSysObj>({
        url: urlConst.POST_GROUP_BUY_BCC_IMAGES,
        methods: 'post',
        defaultValue: {images: [], visible: {}},
        config: {
            headers: {
                'X-Region': 'bj',
                csrftoken: isClient ? getCookie('bce-user-info') : '',
            },
        },
        params: {
            product: 'BCC',
        },
    }, false);

    const regionData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productBccData.result.forEach(item => {
            if (!res.find(i => i.value === item.region)) {
                res.push({
                    label: getZhFromFlavorDisplayName(item.flavorDisplayName, 'region') || item.region,
                    value: item.region,
                    desc: '不同地域云产品之间内网不互通；选择最靠近您客户的地域，可降低访问延时，创建成功后不支持更换地域。',
                });
            }
        });
        return res;
    }, [productBccData]);

    const specData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        const region = formValue.region;
        productBccData.result.forEach(item => {
            const spec = getProductFieldVal(item, 'spec', false, true) as string;
            const specPartition = spec.split('.')[1];
            // TODO: extr 应该不需要了
            if (
                item.region === region
                && !res.find(i => i.extr === specPartition)
            ) {
                res.push({
                    label: getZhFromFlavorDisplayName(item.flavorDisplayName, 'specId') || spec,
                    value: specPartition,
                    extr: specPartition,
                });
            }
        });
        return res;
    }, [formValue.region, productBccData.result]);

    const cpuData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productBccData.result.forEach(item => {
            const spec = getProductFieldVal(item, 'spec', false, true) as string;
            const cpu = getProductFieldVal(item, 'cpu', true, true) as number;
            const specPartition = spec.split('.')[1];
            if (
                !res.find(i => +i.value === +cpu)
                && item.region === formValue.region
                && formValue.spec
                && specPartition === formValue.spec
            ) {
                res.push({
                    label: `${cpu}核`,
                    value: cpu,
                });
            }
        });
        return res;
    }, [formValue.region, formValue.spec, productBccData.result]);

    const memoryData = useMemo<GroupPurchaseTabSelectPropsData>(() => {
        const res: GroupPurchaseTabSelectPropsData = [];
        productBccData.result.forEach(item => {
            const spec = getProductFieldVal(item, 'spec', false, true) as string;
            const cpu = getProductFieldVal(item, 'cpu', true, true) as number;
            const memory = getProductFieldVal(item, 'memory', true, true) as number;
            const specPartition = spec.split('.')[1];
            if (
                !res.find(i => i.value === memory)
                && item.region === formValue.region
                && formValue.spec
                && specPartition === formValue.spec
                && cpu === formValue.cpu
            ) {
                res.push({
                    label: `${memory}G`,
                    value: memory,
                });
            }
        });
        return res;
    }, [formValue.cpu, formValue.region, formValue.spec, productBccData.result]);


    const resetMinDiskSize = (osName: string, osVersion: string) => {
        let validMinDiskSize = 40;
        if (osName !== 'Windows Server'
            && !osVersion.toLowerCase().includes('cuda')
            && osVersion !== '基于CentOS 6.5（64位）的WordPress环境'
            && osVersion !== 'VCPE V1.4.2基于CentOS 7.5（64位）'
        ) {
            validMinDiskSize = 20;
        }
        if (validMinDiskSize !== minDiskSize) {
            setMinDiskSize(validMinDiskSize);

            const currentSize = form.getFieldValue('size');
            if (currentSize < validMinDiskSize) {
                form.setFieldValue('size', validMinDiskSize);
            }
        }
    };

    // 镜像数据和 region有关系
    useEffect(() => {
        if (formValue.region) {
            imageHttp({
                product: 'BCC',
            }, {
                config: {
                    headers: {
                        csrftoken: isClient ? getCookie('bce-user-info') : '',
                        'X-Region': formValue.region,
                    },
                },
            });
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formValue.region]);

    // 镜像和规格有关系 规格变化 需要重新设置镜像
    useEffect(() => {
        if (imagesRes?.images?.length > 0 && formValue.spec && formValue.cpu && formValue.memory) {
            const spec = `bcc.${formValue?.spec}.c${formValue.cpu}m${formValue.memory}`;

            const opts = versionOptAssemble(imagesRes, spec);
            if (opts.length) {
                const curOsType = form.getFieldValue('osType');
                const curOsVersion = form.getFieldValue('osVersion');

                setImageOpt(opts);
                const osTypeOpt = opts.find(i => i.value === curOsType).children || opts[0].children;
                setVersionOpt(sortOptions(osTypeOpt as any) as any);

                if (!opts.find(i => i.value === curOsType)) {
                    form.setFieldsValue({
                        osType: opts[0].value as string,
                        osVersion: osTypeOpt[0].value,
                    });
                    debounce.current.next(null);
                } else if (!osTypeOpt.find(i => i.value === curOsVersion)) {
                    form.setFieldsValue({
                        osVersion: opts[0].children[0].value,
                    });
                    debounce.current.next(null);
                }
            }
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [form, formValue.cpu, formValue.memory, formValue.spec, imagesRes, setVersionOpt]);

    useOnMount(() => {
        props.onInited && props.onInited<FormValue>(form);
        // minDiskSize 默认是 20, 如果是一些特殊镜像, 需要调整 minDiskSize
        resetMinDiskSize(form.getFieldValue('osType'), form.getFieldValue('osVersion'));
    });

    // 赋默认值后主动触发一次询价
    useEffect(() => {
        if (productBccData.result?.length) {
            debounce.current.next(null);
        }
    }, [productBccData.result, form]);

    debounce.current.subscribe(() => {
        let val = form.getFieldsValue();
        const bccProduct = getProductByFields(productBccData.result, [{
            name: 'region',
            isFlavor: false,
            val: val.region,
        }, {
            name: 'spec',
            isFlavor: true,
            isScale: false,
            val: `bcc.${val?.spec}.c${val.cpu}m${val.memory}`,
        }, {
            name: 'cpu',
            isFlavor: true,
            isScale: true,
            val: val.cpu,
        }, {
            name: 'memory',
            isFlavor: true,
            isScale: true,
            val: val.memory,
        }]);

        if (val.osType && val.osVersion && bccProduct) {
            const paramsDuration = GroupPurchaseDurationSelect.getDurationValue(val.duration);
            const signDuration = val.duration.replace('Y', 'year').replace('M', 'month').replace('D', 'day');
            const bccParams = genPriceParamsByProductObj(bccProduct, val);
            const eipParams: GroupPurchaseProductPriceParams = {
                serviceType: EnumProductServiceType.EIP,
                accountId: '',
                agentAccountId: '',
                chargeItemName: 'Cpt2',
                count: 1,
                flavor: {
                    flavorItems: [{
                        name: 'bandwidth', value: `${val.bandwidth}m`, scale: 1,
                    }],
                },
                region: val.region,
                scene: 'NEW',
                subServiceType: 'default',
                time: {
                    period: `P${val.duration}`,
                    startTime: utc(new Date()).format(),
                },
            };
            eipParams.signAuth = Base64.stringify(sha256(
                // eslint-disable-next-line max-len
                `${eipParams.serviceType}${eipParams.region}${signDuration}${JSON.stringify(eipParams.flavor.flavorItems)}${PRODUCT_PRICE_SHA_KEY}`
            ));
            const cdsParams: GroupPurchaseProductPriceParams = {
                serviceType: EnumProductServiceType.CDS,
                accountId: '',
                agentAccountId: '',
                chargeItemName: 'Cpt2',
                count: 1,
                flavor: {
                    flavorItems: [{
                        name: 'subServiceType',
                        value: 'enhanced_ssd_pl1',
                        scale: 1,
                    }, {
                        name: 'size',
                        value: `${val.size}g`,
                        scale: 1,
                    }],
                },
                region: val.region,
                scene: 'NEW',
                subServiceType: 'default',
                time: {
                    period: `P${val.duration}`,
                    startTime: utc(new Date()).format(),
                },
            };
            cdsParams.signAuth = Base64.stringify(sha256(
                // eslint-disable-next-line max-len
                `${cdsParams.serviceType}${cdsParams.region}${signDuration}${JSON.stringify(cdsParams.flavor.flavorItems)}${PRODUCT_PRICE_SHA_KEY}`
            ));

            const regionLabel = regionData.find(i => i.value === val.region)?.label;
            const specLabel = specData.find(i => i.value === val.spec)?.label;
            const cpuLabel = cpuData.find(i => i.value === val.cpu)?.label;
            const memoryLabel = memoryData.find(i => i.value === val.memory)?.label;
            const configList = [{
                name: '区域',
                value: regionLabel,
            }, {
                name: '规格',
                // eslint-disable-next-line max-len
                // value: `bcc.${formValueLabel.current.spec}.c${formValueLabel.current.cpu}m${formValueLabel.current.memory}|${formValueLabel.current.cpu}|${formValueLabel.current.memory}|${formValue.bandwidth}${bandwidthUnit}|${formValue.size}${sizeUnit}`,
                value: `${specLabel} | ${cpuLabel} | ${memoryLabel} | ${formValue.bandwidth}${bandwidthUnit} | ${formValue.size}${sizeUnit}`,
            }, {
                name: '镜像',
                value: val.osType,
            }, {
                name: '',
                value: val.osVersion,
            }, {
                name: '购买时长',
                value: paramsDuration.label,
            }, {
                name: '购买数量',
                value: '1',
            }];
            props.onChange && props.onChange({
                priceList: null,
                configList,
                loading: true,
                orderParams: null,
            });
            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_MERGE_PRODUCT_PRICE,
                {
                    data: [
                        bccParams,
                        eipParams,
                        cdsParams,
                    ],
                }
            ).then(res => {
                props.onChange && props.onChange({
                    priceList: res.result,
                    configList,
                    loading: false,
                    orderParams: null,
                });
                val = form.getFieldsValue();
                getLogicalZone(
                    val.region,
                    bccParams.flavor.flavorItems.find(item => item.name === 'spec').value as string
                ).then(logicalZone => {
                    const currImage = versionOptRef.current.find(item => item.value === val.osVersion).data;
                    // 下单参数
                    const orderParams = [
                        {
                            serviceId: '',
                            serviceGroupId: '',
                            serviceType: 'BCC',
                            parentServiceType: 'BCC',
                            duration: paramsDuration.durationInt,
                            timeUnit: paramsDuration.timeUnit,
                            region: val.region,
                            logicalZone,
                            count: 1,
                            subServiceType: bccParams.flavor.flavorItems.find(item => item.name === 'subServiceType').value,
                            signAuth: '',
                            flavor: bccParams.flavor,
                            specs: [
                                ...removeFromArrayByCondition(
                                    cloneDeep(bccParams.flavor.flavorItems),
                                    (item: GroupPurchaseProductFlavorObj) => item.name === 'subServiceType' || item.name === 'physicalZone'
                                ),
                                {
                                    name: 'imageId',
                                    value: currImage.id,
                                    scale: 1,
                                },
                                {
                                    name: 'imageType',
                                    value: currImage.imageType,
                                    scale: 1,
                                },
                                {
                                    name: 'osType',
                                    value: val.osType,
                                    scale: 1,
                                },
                                {
                                    name: 'osVersion',
                                    value: val.osVersion,
                                    scale: 1,
                                },
                            ],
                        },
                        {
                            serviceId: '',
                            serviceGroupId: '',
                            serviceType: 'CDS',
                            parentServiceType: 'BCC',
                            duration: paramsDuration.durationInt,
                            timeUnit: paramsDuration.timeUnit,
                            region: val.region,
                            logicalZone,
                            count: 1,
                            subServiceType: cdsParams.flavor.flavorItems.find(item => item.name === 'subServiceType').value,
                            signAuth: '',
                            flavor: cdsParams.flavor,
                            specs: [
                                ...removeFromArrayByCondition(
                                    cloneDeep(cdsParams.flavor.flavorItems),
                                    (item: GroupPurchaseProductFlavorObj) => item.name === 'subServiceType'
                                ),
                            ],
                        },
                        {
                            serviceId: '',
                            serviceGroupId: '',
                            serviceType: 'EIP',
                            parentServiceType: 'BCC',
                            duration: paramsDuration.durationInt,
                            timeUnit: paramsDuration.timeUnit,
                            region: val.region,
                            logicalZone,
                            count: 1,
                            subServiceType: eipParams.subServiceType,
                            signAuth: '',
                            flavor: eipParams.flavor,
                            specs: eipParams.flavor.flavorItems,
                        },
                    ];

                    props.onChange && props.onChange({
                        priceList: res.result,
                        configList,
                        loading: false,
                        orderParams,
                    });
                });
            });
        }
    });

    return (
        <Form
            form={form}
            initialValues={formValue}
            className={style['group-purchase-bcc-form']}
            onValuesChange={(e, allVal) => {
                setFormValue(allVal);
                debounce.current.next(null);
            }}
        >
            <GroupBuyRowItem label="区域：" hasBorder>
                <Form.Item
                    name="region"
                >
                    <GroupPurchaseTabSelect
                        data={regionData}
                        onChange={(e, opt) => {formValueLabel.current.region = opt.label;}}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="规格：">
                <Form.Item
                    name="spec"
                >
                    <GroupPurchaseTabSelect
                        data={specData}
                        onChange={(e, opt) => {formValueLabel.current.spec = opt.label;}}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="CPU：">
                <Form.Item
                    name="cpu"
                >
                    <GroupPurchaseTabSelect
                        isOrder
                        data={cpuData}
                        onChange={(e, opt) => {formValueLabel.current.cpu = opt.label;}}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="内存：">
                <Form.Item
                    name="memory"
                >
                    <GroupPurchaseTabSelect
                        data={memoryData}
                        onChange={(e, opt) => {formValueLabel.current.memory = opt.label;}}
                    />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="系统盘：">
                <Form.Item
                    name="size"
                >
                    <USliderInput min={minDiskSize} max={2048} unit={sizeUnit} />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="带宽：">
                <Form.Item
                    name="bandwidth"
                >
                    <USliderInput min={1} max={500} unit={bandwidthUnit} />
                </Form.Item>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="镜像：" hasBorder>
                <div className={style['os-wrapper']}>
                    <Form.Item
                        name="osType"
                    >
                        <Select
                            className={selectStyles['u-select']}
                            popupClassName={selectStyles['u-select-dropdown']}
                            suffixIcon={<span className={selectStyles.icon}></span>}
                            options={imageOpt}
                            onChange={(e, opt: any) => {
                                resetMinDiskSize(e, opt.children[0].value);
                                form.setFieldValue('osVersion', opt.children[0].value);
                                setVersionOpt(sortOptions(opt.children) as VersionOptObj[]);
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        name="osVersion"
                    >
                        <Select
                            className={selectStyles['u-select']}
                            popupClassName={selectStyles['u-select-dropdown']}
                            suffixIcon={<span className={selectStyles.icon}></span>}
                            options={versionOpt}
                            onChange={osVersion => {
                                resetMinDiskSize(form.getFieldValue('osType'), osVersion);
                            }}
                        />
                    </Form.Item>
                </div>
            </GroupBuyRowItem>
            <GroupBuyRowItem label="时长：" hasBorder>
                <Form.Item
                    name="duration"
                >
                    <GroupPurchaseDurationSelect options={['1M', '3M', '6M', '1Y']} />
                </Form.Item>
            </GroupBuyRowItem>
        </Form>
    );
}
