import {useState} from 'react';
import {UPhoneVerify} from '@common/components/UPhoneVerify/UPhoneVerify';
import {Form} from 'antd';
import {groupBuyFormRules} from '@components/groupBuy/groupBuyFromRules';
import {PhoneVerifyValue} from '@common/interface/groupPurchase';
import styles from './GroupPhoneVerify.module.less';

const PhoneVerify = (props: {
    value?: boolean;
    onChange?: (v: boolean) => void;
    onVerifySuccess: (val: PhoneVerifyValue) => void;
    id?: string;
}) => {
    const {value = false, onChange, onVerifySuccess, id} = props;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [phoneNumberChecked, setPhoneNumberChecked] = useState<boolean>(value || false);
    const handlePhoneNumberChecked = (val: PhoneVerifyValue) => {
        setPhoneNumberChecked(true);
        onChange?.(true);
        onVerifySuccess(val);
    };

    return (
        <div id={id}>
            <UPhoneVerify
                productName="CBS"
                verifySuccessCallBack={handlePhoneNumberChecked}
                verifyFailCallBack={() => onChange?.(false)}
            />
            <p
                id="PHONENUMBERCHECK"
            >
                您购买的部分产品需要百度智能云提供服务。请准确填写您希望接受服务的手机号并完成验证码校验，在您下单后专业顾问会在工作日尽快与您联系。
                百度智能云会保护您的个人信息，仅有百度智能云及其授权委托机构可以看到您提交的信息，请放心提供。
            </p>
        </div>
    );
};

export const GroupPhoneVerify = (props: {
    selectedServiceType: string[];
    onChange: (val: PhoneVerifyValue) => void;
    id?: string;
}) => {
    const {selectedServiceType, onChange, id} = props;
    // 当前选中的serviceTypes中 有CBS，TMS 才会展示该模块
    const allNeedItServiceTypes = ['CBS', 'TMS'];
    const currentServiceTypes = selectedServiceType.filter(i => allNeedItServiceTypes.includes(i));

    return (
        <>
            {
                currentServiceTypes && currentServiceTypes.length > 0
                    ? (
                        <div className={styles.phoneWrapper}>
                            <Form.Item
                                name="phonenumbercheck"
                                rules={groupBuyFormRules.PHONENUMBERCHECK}
                            >
                                <PhoneVerify onVerifySuccess={onChange} id={id} />
                            </Form.Item>
                        </div>
                    )
                    : null
            }
        </>
    );
};
