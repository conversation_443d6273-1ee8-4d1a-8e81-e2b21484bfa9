import Big from 'big.js';
import {ProductPriceDetailObj} from '@common/interface/page';

export function calcTotalPrice(priceList: ProductPriceDetailObj[]) {
    return priceList.reduce((sum, cur) => sum.add(cur?.price || 0), new Big('0'));
}

/**
 * 格式化价格数字, 默认保留两位小数, 但是如果小数位为 0, 则只显示整数
 */
export function formatPriceNumber(price: number | Big) {
    const priceStr = price.toFixed(2).toString();
    if (priceStr.endsWith('.00')) {
        return priceStr.slice(0, -3);
    }
    return priceStr;
}

// TODO: 放在配置文件里
export const productNameMap: Record<string, string> = {
    BCC: '云服务器 BCC',
    CDN: '内容网络分发 CDN',
    LSS: '音视频直播 LSS',
    AIPAGE: '智能门户 Aipage',
    EIP: '弹性公网IP EIP',
    NAT: 'NAT 网关',
    BOS: '对象存储 BOS',
    CBS: '工商财税服务 CBS',
    TMS: '商标注册服务 TMS',
    FACE: '人脸实名认证 FACE',
    OCR: '场景识别 OCR',
    SPEECH: '语音识别 SPEECH',
    AI_TE: '机器翻译',
    ICR: '内容审核',
    SMS: '简单消息服务 SMS',
};
