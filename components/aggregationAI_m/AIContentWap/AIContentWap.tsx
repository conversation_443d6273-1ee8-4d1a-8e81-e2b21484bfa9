import cn from 'classnames';
import {useMemo, useRef} from 'react';
import {throttle} from 'lodash';
import {Tabs} from 'antd-mobile';
import {useOnMount} from '@baidu/bce-hooks';
import {Ioc} from '@baidu/bce-decorators';
import {UEnvService} from '@common/services/env';
import {getElementTop, getScrollTop} from '@common/helper/page';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {HEADER_NAV_HEIGHT_MAP, LOGIN_URL, REAL_NAME_URL} from '@common/constant/variableConst';
import {checkDialog} from '@components/aggregationAI/AIContent/AIContent';
import {DialogMessageMap} from '@components/aggregationAI/AIContent/model';
import {cardBtnObj, aiContentObj, aiContentCardObj} from '@components/aggregationAI/interface';
import styles from './main_750m.module.less';

const CardItem = (props: {
    card: aiContentCardObj;
}) => {
    const {card} = props;
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({mobile: true});
    const env = Ioc(UEnvService).isProdSandbox ? 'sandbox' : 'online';

    const genLoginURL = () => {
        const encodeCurrentUrl = encodeURIComponent(location.href.replace(location.hash, '')) + location.hash;
        return `${LOGIN_URL[env]}?redirect=${encodeCurrentUrl}`;
    };
    const btnSuccessCb = (href: string) => {
        window.location.href = href;
    };

    const handleClick = (btn: cardBtnObj) => {
        const {login = false, realName = false, href = '', mobileHref = ''} = btn;
        const jumpHref = mobileHref || href || '';
        if (login) {
            if (userinfo?.hasLogin) {
                if (realName) {
                    // check RealName
                    verifyHandler().then(code => {
                        // code表示当前已完成状态
                        if (code === 0) {
                            // 均正常
                            btnSuccessCb(jumpHref);
                        } else if (code === 201) {
                            // 自定义wap端处理未激活
                            checkDialog({
                                ...DialogMessageMap.NO_ACTIVE,
                                cb: () => {
                                    const ActiveLink = `${env === 'sandbox'
                                        ? 'https://qasandbox.bcetest.baidu.com'
                                        : 'https://console.bce.baidu.com'}/?redirect=${encodeURIComponent(window.location.href)}#/accountActive`;
                                    btnSuccessCb(ActiveLink);
                                },
                            });
                        } else if (code === 204) {
                            if (realName) {
                                checkDialog({
                                    ...DialogMessageMap.NO_REALNAME,
                                    cb: () => btnSuccessCb(REAL_NAME_URL[env]),
                                });
                            } else {
                                btnSuccessCb(jumpHref);
                            }
                        } else {
                            checkDialog({...DialogMessageMap.UNKNOWN_ERROR});
                        }
                    }).catch(err => {
                        console.error(err);
                        checkDialog({...DialogMessageMap.UNKNOWN_ERROR});
                    });
                } else {
                    btnSuccessCb(jumpHref);
                }
            } else {
                // 先登录
                checkDialog({
                    ...DialogMessageMap.NO_LOGIN,
                    cb: () => btnSuccessCb(genLoginURL()),
                });
            }
        } else {
            btnSuccessCb(jumpHref);
        }
    };
    return (
        <div className={styles['card-item']}>
            <div className={styles['card-item_title']}>
                <h4>{card.title}</h4>
                {card.tag && <span className={styles['card-item_tag']}>{card.tag}</span>}
            </div>
            <p className={styles['card-item_desc']}>{card.desc}</p>
            <p className={styles['card-item_subTitle']}>
                {card.subTitle}{card.subTitleLast && <><i />{card.subTitleLast}</>}
            </p>
            {card.infoList && card.infoList.length > 0 && (
                <div className={styles['card-item_info']}>
                    {card.infoList.map(info => info && info.title && (
                        <p key={info.title}>
                            <span className={styles['item-info_title']}>{info.title}</span>
                            <span>{info.desc}</span>
                        </p>
                    ))}
                </div>
            )}
            <div className={styles['card-item_footer']}>
                {card.hotTags && (
                    <div className={styles['footer_tags']}>
                        <span>{card.hotTags}</span>
                    </div>
                )}
                {card.btn && !card.btn.disabled && (
                    <div
                        className={styles['footer_btn']}
                        onClick={() => handleClick(card.btn)}
                        data-track-action="click"
                        data-track-category="AI聚合页-产品卡片-wap"
                        data-track-name={card.title}
                        data-track-value={card.btn.text}
                    >{card.btn.text || ''}
                    </div>
                )}
            </div>
        </div>
    );
};

const CardGroup = (props: {
    data: aiContentObj['list'];
    classNames: string;
    navClassName: string;
    domRefTop: number;
}) => {
    const {data, domRefTop = 0} = props;
    const allData = useMemo(() => {
        const res: aiContentCardObj[] = [];
        data?.forEach(item => {
            item?.cards?.forEach(card => {
                card && res.push(card);
            });
        });
        return res;
    }, [data]);

    return (
        <div className={props.classNames}>
            <Tabs
                className={cn(
                    'tab-container-inner',
                    props.navClassName
                )}
                onChange={() => {
                    // 内容滚动到头部
                    if (window.scrollTo) {
                        window.scrollTo({
                            top: domRefTop,
                            behavior: 'smooth',
                        });
                    } else {
                        document.documentElement.scrollTop = domRefTop;
                    }
                }}
            >
                <Tabs.Tab key={'-1'} title={'全部'} forceRender>
                    <div className={styles['group-content']}>
                        {allData?.map((card, cardIndex) => card && (
                            // eslint-disable-next-line react/no-array-index-key
                            <CardItem key={cardIndex} card={card} />
                        ))}
                    </div>
                </Tabs.Tab>
                {data?.map((item, index) => item && (
                    // eslint-disable-next-line react/no-array-index-key
                    <Tabs.Tab key={index} title={item.title || ''} forceRender>
                        <div className={styles['group-content']}>
                            {item.cards?.map((card, cardIndex) => card && (
                                // eslint-disable-next-line react/no-array-index-key
                                <CardItem key={cardIndex} card={card} />
                            ))}
                        </div>
                    </Tabs.Tab>
                ))}
            </Tabs>
        </div>
    );
};

export const AIContentWap = ({contentData}: {contentData: aiContentObj[]}) => {
    const tabRef = useRef<HTMLDivElement>(null);
    const scrollTopPrev = useRef(0);
    const headerTop = useRef(0);
    const subNavTopRef = useRef(null);

    useOnMount(() => {
        const HEADER_HEIGHT = HEADER_NAV_HEIGHT_MAP.wap;
        const navDom: HTMLElement = document.querySelector('.adm-tabs.tab-container > .adm-tabs-header');
        if (!navDom) {
            return null;
        }
        const navDomHeight = navDom.offsetHeight;
        const subNavTop = getElementTop(navDom);
        subNavTopRef.current = subNavTop;
        const groupUlDom: NodeListOf<HTMLElement> = document.querySelectorAll('.content-nav .adm-tabs-header');
        const setStickyTop = (top: number) => {
            navDom.style.top = `${top}px`;
            if (groupUlDom && groupUlDom.length > 0) {
                groupUlDom.forEach((ele: HTMLElement) => {
                    ele.style.top = `${top + navDomHeight}px`;
                });
            }
        };
        setStickyTop(HEADER_HEIGHT);
        // 记录上一次top值
        let preTop = HEADER_HEIGHT;
        // eslint-disable-next-line complexity
        const handleWindowScroll = throttle(() => {
            // 吸顶逻辑
            const scrollTop = getScrollTop();
            const cloudHeaderEle: HTMLDivElement = document.querySelector('.cloud-header-wrapper-wap');
            if (cloudHeaderEle) {
                if (scrollTop < scrollTopPrev.current) {
                    // 下拉
                    if (scrollTop < subNavTop - HEADER_HEIGHT) {
                        headerTop.current = 0;
                        setStickyTop(0);
                        preTop = 0;
                    } else {
                        headerTop.current = Math.min(0, headerTop.current + scrollTopPrev.current - scrollTop);
                        const newTop = Math.min(preTop + scrollTopPrev.current - scrollTop, HEADER_HEIGHT);
                        setStickyTop(newTop);
                        preTop = newTop;
                    }
                } else if (scrollTop < subNavTop - HEADER_HEIGHT) {
                    headerTop.current = 0;
                    setStickyTop(0);
                    preTop = 0;
                } else {
                    // 下滚
                    headerTop.current = Math.max(-HEADER_HEIGHT, headerTop.current + scrollTopPrev.current - scrollTop);
                    // 判断是否吸顶
                    const newTop = Math.max(preTop + scrollTopPrev.current - scrollTop, 0);
                    setStickyTop(newTop);
                    preTop = newTop;
                }
                if (headerTop.current === -HEADER_HEIGHT) {
                    cloudHeaderEle.style.boxShadow = 'none';
                } else {
                    cloudHeaderEle.style.boxShadow = '';
                }
                cloudHeaderEle.style.top = `${headerTop.current}px`;
            }
            scrollTopPrev.current = scrollTop;
        }, 10);
        window.addEventListener('scroll', handleWindowScroll);
        return () => {
            window.removeEventListener('scroll', handleWindowScroll);
        };
    });
    return (
        <section className={styles.content}>
            <div className={styles.container} ref={tabRef}>
                <Tabs
                    className={'tab-container'}
                    onChange={() => {
                        // 内容滚动到头部
                        if (window.scrollTo) {
                            window.scrollTo({
                                top: subNavTopRef.current,
                                behavior: 'smooth',
                            });
                        } else {
                            document.documentElement.scrollTop = subNavTopRef.current;
                        }
                    }}
                >
                    {contentData?.map((item, index) => item && (
                        // eslint-disable-next-line react/no-array-index-key
                        <Tabs.Tab key={index} title={item.name} forceRender>
                            <CardGroup
                                data={item.list}
                                classNames={styles.group}
                                navClassName={'content-nav'}
                                domRefTop={subNavTopRef?.current || 0}
                            />
                        </Tabs.Tab>
                    ))}
                </Tabs>
            </div>
        </section>
    );
};
