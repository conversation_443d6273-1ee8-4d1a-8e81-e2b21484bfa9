@import '@styles/variables.less';
@import '@styles/mixin.less';

.content {
    .container {
        .group {
            padding: 0 0 40px;
            box-sizing: border-box;
            .group-content {
                width: 100%;
                box-sizing: border-box;

                .card-item {
                    padding: 33px 23px 33px 33px;
                    border-radius: 4px;
                    background: #F2F6FA;

                    &:nth-child(n + 2) {
                        margin-top: 20px;
                    }

                    &_title {
                        display: flex;
                        align-items: center;
                        > h4 {
                            color: @C0;
                            font-family: PingFangSC-Medium;
                            font-weight: 500;
                            font-size: 32px;
                            line-height: 50px;
                            letter-spacing: 0px;
                            text-align: left;
                            .ellipsis();
                        }
                        .card-item_tag {
                            display: inline-block;
                            flex-shrink: 0;
                            margin-left: 16px;
                            height: 40px;
                            border-radius: 4px;
                            border: 1px solid rgba(@C0, .24);
                            padding: 2px 12px;
                            box-sizing: border-box;

                            color: rgba(@C0, 0.4);
                            font-family: PingFangSC-Medium;
                            font-weight: 500;
                            font-size: 20px;
                            line-height: 36px;
                            text-align: center;
                        }
                    }

                    &_desc {
                        margin-top: 12px;
                        color: @C7;
                        font-family: PingFangSC-Regular;
                        font-weight: 400;
                        font-size: 26px;
                        line-height: 42px;
                        letter-spacing: 0px;
                        text-align: left;
                        .ellipsis2(2);
                    }

                    &_subTitle {
                        padding: 28px 0;
                        box-sizing: border-box;
                        color: @CB;
                        font-family: PingFangSC-Medium;
                        font-weight: 500;
                        font-size: 34px;
                        line-height: 50px;
                        letter-spacing: 0px;
                        text-align: left;
                        .ellipsis();

                        > i {
                            display: inline-block;
                            width: 40px;
                            height: 1px;
                            background: transparent;
                        }
                    }

                    &_info {
                        border-top: 1px solid @C08;
                        padding: 28px 0;
                        box-sizing: border-box;
                        > p {
                            padding-left: 40px;
                            position: relative;
                            .ellipsis2(2);
                            &:before {
                                content: '';
                                position: absolute;
                                left: 0;
                                top: 5px;
                                width: 32px;
                                height: 32px;
                                background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/wap-check_46748e6.svg') center/cover;
                            }
                            &:nth-child(n + 2) {
                                margin-top: 12px;
                            }
                            span {
                                color: #222222;
                                font-family: PingFangSC-Regular;
                                font-weight: 400;
                                font-size: 26px;
                                line-height: 42px;
                                letter-spacing: 0px;
                                vertical-align: top;

                                &.item-info_title {
                                    font-weight: 600;
                                }
                            }
                        }
                    }

                    &_footer {
                        display: flex;
                        justify-content: space-between;
                        .footer_tags {
                            flex: 1;
                            .ellipsis();
                            > span {
                                color: #F33E3E;
                                font-family: PingFangSC-Medium;
                                font-weight: 500;
                                font-size: 34px;
                                line-height: 76px;
                                letter-spacing: 0px;
                                text-align: left;
                            }
                        }
                        .footer_btn {
                            flex-shrink: 0;
                            padding: 14px 54px;
                            box-sizing: border-box;
                            background-color: @CB;
                            border-radius: 6px;
                            color: @CW;
                            font-family: PingFangSC-Medium;
                            font-weight: 500;
                            font-size: 28px;
                            line-height: 44px;
                            letter-spacing: 0px;
                            text-align: center;
                        }
                    }
                }
            }
        }
    }
}

:global {
    .adm-tabs.tab-container {
        --content-padding: 0;
        --active-title-color: @CB;
        --title-font-size: 28px;
        --active-line-height: 4px;
        > .adm-tabs-header {
            position: sticky;
            background: @CW;
            border-bottom: 0px;
            padding: 0;
            box-sizing: border-box;
            z-index: 23;
            box-shadow: 0 8px 32px 0 rgba(218, 221, 231, .7);
            .adm-tabs-header-mask-left {
                left: 0;
                background: linear-gradient(270deg,hsla(0,0%,100%,0) 0,#fff 71%);
            }
            .adm-tabs-header-mask-right {
                right: 0;
                background: linear-gradient(90deg, hsla(0,0%,100%,0) 0, #fff 71%);
            }
            .adm-tabs-tab-list {
                .adm-tabs-tab-line {
                    height: 4px;
                    background-color: @CB;
                }
                .adm-tabs-tab-wrapper {
                    // 边框
                    padding: 0;
                    margin-right: 40px;
                    &:last-child {
                        padding-right: 25px;
                        margin-right: 0px;
                    }
                    &:nth-child(2) {
                        padding-left: 25px;
                    }
                    .adm-tabs-tab {
                        font-weight: 500;
                        padding: 0;
                        line-height: 88px;
                        font-family: PingFangSC-Medium;
                        color: rgba(34,34,34,.9);
                        font-size: 28px;
                        font-weight: 500;
                        transition: color .3s ease-out;
                    }
                }
            }
        }

        .adm-tabs-header-mask {
            width: 84px;
        }
        .adm-tabs.tab-container-inner {
            --content-padding: 0 25px;
            --active-line-height: 0;
            > .adm-tabs-header {
                width: 100%;
                padding: 20px 25px;
                box-sizing: border-box;
                position: sticky;
                background: @CW;
                z-index: 22;
                border-bottom: 0px;

                .adm-tabs-tab-list {
                    .adm-tabs-tab-wrapper {
                        flex-shrink: 0;
                        flex-grow: 0;
                        height: 64px;
                        padding: 0;
                        border-radius: 4px;
                        &:nth-child(n+3) {
                            margin-left: 20px;
                        }
                        
                        .adm-tabs-tab {
                            padding: 11px 32px;
                            color: @C0;
                            font-family: PingFangSC-Medium;
                            font-weight: 500;
                            font-size: 26px;
                            line-height: 42px;
                            letter-spacing: 0px;
                            text-align: center;
                            border-radius: 4px;
                            background-color: #F2F6FA;

                            &.adm-tabs-tab-active {
                                color: @CW;
                                background: @CB;
                            }
                        }
                    }
                }
            }
        }
    }
}