import {ULazyLoad} from '@baidu/bce-components';
import {aiBannerObj} from '@components/aggregationAI/interface';
import styles from './main_750m.module.less';

export const AIBannerWap = ({bannerData}: {bannerData: aiBannerObj}) => {
    return (
        <ULazyLoad type="backgroundImage">
            <section
                className={styles['banner']}
                data-url={bannerData.bgWap}
            >
                <div className={styles['banner-content']}>
                    <h1 className={styles.title}>{bannerData.title}</h1>
                    {bannerData.textLink && bannerData.textLink.text && (
                        <p className={styles['text-link']}>{bannerData.textLink.text}</p>
                    )}
                </div>
            </section>
        </ULazyLoad>
    );
};
