@import '@styles/variables.less';

.stepper {
    width: unset;

    :global {
        .adm-button {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 64px;
            height: 64px;
            opacity: 1;
            background: @CG;
            
            svg {
                display: none;
            }

            &.adm-stepper-minus,
            &.adm-stepper-plus {
                span {
                    display: block;
                    width: 24px;
                    height: 24px;
                }
            }

            &.adm-stepper-minus {
                span {
                    background: url('https://bce.bdstatic.com/portal-cloud-server/images/bcc-order/icon_number_minus.png') no-repeat;
                    background-size: 48px 24px;
                    background-position: -24px 0;
                }

                &.adm-button-disabled span {
                    background-position: 0 0;
                }
            }

            &.adm-stepper-plus {
                span {
                    background: url('https://bce.bdstatic.com/portal-cloud-server/images/bcc-order/icon_number_add.png') no-repeat;
                    background-size: 48px 24px;
                    background-position: -24px 0;
                }

                &.adm-button-disabled span {
                    background-position: 0 0;
                }
            }
        }

        .adm-stepper-middle {
            .adm-stepper-input {
                width: 80px;
                height: 64px;
                background: @CG;

                input {
                    font-size: 28px;
                    line-height: inherit;
                }
            }
        }
    }
}