import {Stepper, StepperProps} from 'antd-mobile';
import cn from 'classnames';
import styles from './main_750m.module.less';

export default function PurchaseStepper(props: StepperProps) {
    const {className, parser, min, max} = props;

    // 确保输入错误文本时默认行为是会回落到 0, 不可取, 这里确保 value 会回落在 min 和 max 之间
    const stepperParser = (text: string) => {
        const number = Number(text);
        if (Number.isNaN(number) || (min !== undefined && number < (min as number))) {
            return min ?? 0;
        } else if (max !== undefined && number > (max as number)) {
            return max;
        }
        return number;
    };

    return (
        <Stepper
            {...props}
            className={cn(styles.stepper, className)}
            // StepperProps 的类型有点问题, as any
            parser={(parser || stepperParser) as any}
        />
    );
}
