import {PropsWithChildren} from 'react';
import cn from 'classnames';
import {Form} from 'antd-mobile';
import styles from './main_750m.module.less';

interface PurchaseFormItemProps {
    label: string;
    name: string;
    className?: string;
    description?: string;
    inline?: boolean;
}

export default function PurchaseFormItem(props: PropsWithChildren<PurchaseFormItemProps>) {
    const {label, name, description, children, className, inline = false} = props;

    return (
        <div
            className={cn(
                className,
                styles['purchase-form-item'],
                {[styles['purchase-form-item-inline']]: inline}
            )}
        >
            <div className={styles['purchase-form-label']}>{label}</div>
            <Form.Item
                name={name}
                noStyle
            >
                {children}
            </Form.Item>
            {description && (
                <div className={styles['purchase-form-description']}>
                    {description}
                </div>
            )}
        </div>
    );
}
