@import '@styles/variables.less';

.selector-group {
    padding: 28px 32px 38px;

    .selector-container {
        &:nth-child(n+2) {
            margin-top: 40px;
        }

        .selector-title {
            margin-bottom: 12px;
            font-family: PingFangSC-Regular;
            font-size: 26px;
            color: rgba(@C0, .7);
            line-height: 42px;
            font-weight: 400;
        }

        .selector {
            :global {
                .adm-selector-item {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 72px;
                    padding: 0;
                    background: @CG;
                    border-radius: 4px;
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: @C0;
                    line-height: 44px;
                    font-weight: 400;

                    &.adm-selector-item-active {
                        background: @CB;
                        font-family: PingFangSC-Medium;
                        color: @CW;
                    }
                }
            }
        }
    }
}
