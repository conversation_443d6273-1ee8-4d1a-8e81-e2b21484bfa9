/**
 * @file 二级多选列表
 */
/* eslint-disable @typescript-eslint/no-unused-vars */
import {useState} from 'react';
import {Selector, SelectorOption} from 'antd-mobile';
import cn from 'classnames';
import styles from './main_750m.module.less';

export type SelectorGroupOptions = Array<SelectorOption<string> & {
    children: Array<SelectorOption<string> & {extra?: any}>;
}>;

interface SelectorGroupProps {
    value?: [string, string];
    onChange?: (value: [string, string]) => void;
    config?: Array<{
        /** Selector 的 columns 属性, 控制列数 */
        columns?: number;
        className?: string;
    }>;
    options: SelectorGroupOptions;
}

export default function SelectorGroup(props: SelectorGroupProps) {
    const {value, onChange, options, config} = props;

    const getChildOptions = (parentValue: string) => {
        return options.find(opt => opt.value === parentValue)?.children || [];
    };

    // 核数和内存根据规格族动态变化
    const [childOptions, setChildSubOptions]
        = useState<Array<SelectorOption<string>>>(getChildOptions(value[0]));

    const handleParentChange = ([parentValue]: string[]) => {
        // Selector 点击已选项的时候 value 是 undefined
        if (parentValue === undefined) {
            return;
        }
        const newChildOptions = getChildOptions(parentValue);
        setChildSubOptions(newChildOptions);
        onChange([parentValue, newChildOptions[0].value]);
    };

    return (
        <div className={styles['selector-group']}>
            {/* 父选项 */}
            <div className={styles['selector-container']}>
                <div className={styles['selector-title']}>分类</div>
                <Selector
                    className={cn(styles.selector, config?.[0]?.className)}
                    options={options}
                    value={[value[0]]}
                    onChange={handleParentChange}
                    columns={config?.[0]?.columns || 1}
                    showCheckMark={false}
                />
            </div>

            {/* 子选项 */}
            <div className={styles['selector-container']}>
                <div className={styles['selector-title']}>规格</div>
                <Selector
                    className={cn(styles.selector, config?.[1]?.className)}
                    options={childOptions}
                    value={[value[1]]}
                    onChange={([newChildValue]) => {
                        if (newChildValue === undefined) {
                            return;
                        }
                        onChange([value[0], newChildValue]);
                    }}
                    columns={config?.[1]?.columns || 1}
                    showCheckMark={false}
                />
            </div>
        </div>
    );
}
