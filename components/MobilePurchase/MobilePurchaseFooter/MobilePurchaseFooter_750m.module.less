@import '@styles/variables.less';

.footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    // 正常 popup z-index 为 1000, footer 应该在正常 popup 之下, 但在价格详情 popup 之上
    z-index: 900;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: content-box;
    height: 128px;
    background: @CW;
    box-shadow: 0 -6px 16px 2px rgba(7, 12, 20, .08);

    &.no-shadow {
        box-shadow: none;
        border-top: 2px solid rgba(@C0, .08);
    }

    .footer-left {
        display: flex;
        align-items: center;

        .price-loading {
            padding-left: 25px;
            font-family: PingFangSC-Medium;
            font-size: 40px;
            color: @CR;
            line-height: 60px;
            font-weight: 500;
        }

        .price-show {
            display: flex;
            align-items: center;
            padding-left: 32px;
            
            .price-prefix {
                font-family: PingFangSC-Medium;
                font-size: 28px;
                color: @C0;
                letter-spacing: 0;
                line-height: 48px;
                font-weight: 500;
            }

            .price {
                position: relative;
                top: -4px;
                display: flex;
                align-items: flex-end;
                margin-right: 11px;
                color: @CR;

                .price-sign {
                    margin-right: 4px;
                    font-size: 28px;
                    line-height: 56px;
                    font-weight: 500;
                }

                .price-number-int {
                    font-family: PingFangSC-Medium;
                    font-size: 48px;
                    color: @CR;
                    line-height: 68px;
                    font-weight: 500;
                }

                .price-number-decimal {
                    font-family: PingFangSC-Medium;
                    font-size: 32px;
                    color: @CR;
                    line-height: 60px;
                    font-weight: 500;
                }
            }

            .detail-btn {
                display: flex;
                align-items: center;

                span {
                    font-family: PingFangSC-Regular;
                    font-size: 24px;
                    color: rgba(@C0, .7);
                    letter-spacing: 0;
                    line-height: 40px;
                    font-weight: 400;
                }

                .detail-icon {
                    width: 16px;
                    height: 12px;
                    margin-left: 10px;
                    background: url('https://bce.bdstatic.com/portal-cloud-server/images/bcc-order/icon_arrow_up.png') center / cover;
                    transition: transform .3s ease;

                    &.detail-icon-reverse {
                        transform: rotate(180deg);
                    }
                }
            }
        }
    }

    .purchase-btn {
        height: 100%;
        padding: 40px 64px;
        background: @CB;
        border-radius: 0;
        border: 0;

        span {
            font-family: PingFangSC-Medium;
            font-size: 32px;
            color: @CW;
            letter-spacing: 0;
            text-align: center;
            line-height: 48px;
            font-weight: 500;
        }
    }
}

.price-detail-popup {
    position: fixed;
    z-index: 800;
    top: 0;
    right: 0;
    bottom: 128px;
    left: 0;

    :global {
        .adm-mask {
            position: absolute;
            background: rgba(@C0, .5);
        }

        .adm-popup-body {
            position: absolute;
        }
    }

    .price-detail-list {
        padding: 32px 32px 33px;

        li {
            display: flex;
            justify-content: space-between;
            align-items: center;

            &:nth-child(n+2) {
                margin-top: 28px;
            }

            .product-name {
                font-family: PingFangSC-Medium;
                font-size: 28px;
                color: @C0;
                letter-spacing: 0;
                line-height: 44px;
                font-weight: 500;
            }

            .detail-price,
            .detail-discount-price {
                display: flex;
                align-items: flex-end;
                color: rgba(@C0, .85);

                .price-sign {
                    margin-right: 4px;
                    font-family: PingFangSC-Regular;
                    font-size: 26px;
                    letter-spacing: 0;
                    line-height: 42px;
                    font-weight: 400;
                }

                .price-num {
                    font-family: PingFangSC-Regular;
                    font-size: 32px;
                    letter-spacing: 0;
                    text-align: right;
                    line-height: 46px;
                    font-weight: 400;
                }
            }
            .detail-discount-price {
                .origin-price {
                    margin-right: 20px;
                    span {
                        font-size: 28px;
                        margin: 0;
                        color: rgba(@C0, .4);
                        text-decoration: line-through;
                    }
                }
                .discount-price {
                    span {
                        color: #F33E3E; 
                        font-weight: 500;
                        font-family: PingFangSC-Medium;
                    }
                }
            }
        }
    }
}