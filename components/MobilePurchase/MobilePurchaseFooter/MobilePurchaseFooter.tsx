import cn from 'classnames';
import {Button, Popup} from 'antd-mobile';
import {useState} from 'react';
import {MobilePurchaseFooterProps} from '@common/interface/purchase';
import {isNumber} from 'lodash';
import styles from './MobilePurchaseFooter_750m.module.less';

export function MobilePurchaseFooter(props: MobilePurchaseFooterProps) {
    const {priceLoading, totalPrice, onConfirm, prices, confirmText, pricePrefix = '共计：'} = props;
    const [priceDetailVisible, setPriceDetailVisible] = useState<boolean>(false);
    return (
        <div className={cn(styles.footer, priceDetailVisible && styles['no-shadow'])}>
            <div className={styles['footer-left']}>
                {priceLoading ? (
                    <div className={styles['price-loading']}>计算中...</div>
                ) : (
                    <div className={styles['price-show']}>
                        <div className={styles['price-prefix']}>{pricePrefix}</div>
                        <div className={styles.price}>
                            <span className={styles['price-sign']}>¥</span>
                            <span className={styles['price-number-int']}>
                                {Math.floor(totalPrice)}
                            </span>
                            <span className={styles['price-number-decimal']}>
                                .{totalPrice.toFixed(2).toString().slice(-2)}
                            </span>
                        </div>
                        <button
                            className={styles['detail-btn']}
                            onClick={() => setPriceDetailVisible(prev => !prev)}
                        >
                            <span>{priceDetailVisible ? '收起' : '展开'}明细</span>
                            <i
                                className={cn(
                                    styles['detail-icon'],
                                    priceDetailVisible && styles['detail-icon-reverse']
                                )}
                            />
                        </button>
                    </div>
                )}
            </div>
            <Button
                className={styles['purchase-btn']}
                onClick={() => onConfirm()}
            >
                {confirmText}
            </Button>

            {/* 价格详情弹框 */}
            <Popup
                visible={priceDetailVisible}
                onMaskClick={() => setPriceDetailVisible(false)}
                className={styles['price-detail-popup']}
            >
                <ul className={styles['price-detail-list']}>
                    {prices.map(detail => (
                        <li key={detail.productName}>
                            <div className={styles['product-name']}>{detail.productName}</div>
                            {
                                isNumber(detail.discountPrice) && detail.discountPrice !== detail.price ? (
                                    <div className={styles['detail-discount-price']}>
                                        <div className={styles['origin-price']}>
                                            <span className={styles['price-sign']}>¥</span>
                                            <span className={styles['price-num']}>{detail.price.toFixed(2)}</span>
                                        </div>
                                        <div className={styles['discount-price']}>
                                            <span className={styles['price-sign']}>¥</span>
                                            <span className={styles['price-num']}>{detail.discountPrice.toFixed(2)}</span>
                                        </div>
                                    </div>
                                ) : (
                                    <div className={styles['detail-price']}>
                                        <span className={styles['price-sign']}>¥</span>
                                        <span className={styles['price-num']}>{detail.price.toFixed(2)}</span>
                                    </div>
                                )
                            }
                        </li>
                    ))}
                </ul>
            </Popup>
        </div>
    );
}
