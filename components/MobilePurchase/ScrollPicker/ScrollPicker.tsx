/**
 * @file 移动端滚动选择组件
 */
import {ReactNode} from 'react';
import {Picker} from 'antd-mobile';
import {PickerValue} from 'antd-mobile/es/components/picker-view';
import styles from './main_750m.module.less';

// eslint-disable-next-line no-unused-expressions
styles.a;

interface ScrollPickerProps<T> {
    value?: T;
    onChange?: (value: T) => void;
    options: Array<{label: string, value: T}>;
    title?: ReactNode;
}

export default function ScrollPicker<T extends PickerValue = string>(props: ScrollPickerProps<T>) {
    const {value, onChange, options, title} = props;

    const handleClick = () => {
        Picker
            .prompt({
                title,
                columns: [options],
                defaultValue: [value],
                popupClassName: 'scroll-picker-popup',
            })
            .then(value => {
                // 如果用户未选择则 value 为 null
                if (value) {
                    onChange(value[0] as T);
                }
            });
    };

    return (
        <div
            className="scroll-picker"
            onClick={handleClick}
        >
            <span className="value-text">
                {options.find(i => i.value === value)?.label}
            </span>
            <i className="icon" />
        </div>
    );
}
