@import '@styles/variables.less';

:global {
    .scroll-picker {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 18px 24px 18px 28px;
        border-radius: 4px;
        background: @CG;
    
        .value-text {
            font-family: PingFangSC-Medium;
            font-size: 28px;
            color: @CB;
            line-height: 44px;
            font-weight: 500;
        }
    
        i {
            width: 18px;
            height: 18px;
            background: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_list_arrow_1818664.png') center / cover;
        }
    }

    .scroll-picker-popup {
        .adm-popup-body {
            border-radius: 0;
    
            .adm-picker {
                height: 640px;
                // picker item 的高度需要设置内置的 CSS 变量值
                --item-height: 96px;
    
                .adm-picker-header {
                    border-bottom: 2px solid rgba(@C0, .08);
                    height: 96px;
                    padding: 24px 32px;
    
                    .adm-picker-header-button {
                        padding: 0;
                        font-family: PingFangSC-Regular;
                        font-size: 32px;
                        color: @C0;
                        line-height: 48px;
                        font-weight: 400;
        
                        &:last-child {
                            color: @CB;
                        }
                    }
    
                    .adm-picker-header-title {
                        font-family: PingFangSC-Medium;
                        font-size: 32px;
                        color: @C0;
                        text-align: center;
                        line-height: 48px;
                        font-weight: 500;
                    }
                }
    
                .adm-picker-view-column-item {
                    .adm-picker-view-column-item-label {
                        transition: color .3s ease, font-size .1s ease;
                    }
    
                    &[data-selected="true"] .adm-picker-view-column-item-label {
                        font-family: PingFangSC-Medium;
                        font-size: 36px;
                        color: @C0;
                        text-align: center;
                        line-height: 56px;
                        font-weight: 500;
                    }
                }
            }
        }
    }
}
