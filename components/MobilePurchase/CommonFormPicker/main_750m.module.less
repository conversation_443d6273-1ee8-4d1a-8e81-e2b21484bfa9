@import '@styles/variables.less';

.common-picker {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
    padding-left: 28px;
    padding-right: 24px;
    border-radius: 4px;
    background: @CG;

    .value-text {
        font-family: PingFangSC-Medium;
        font-size: 28px;
        color: @CB;
        line-height: 44px;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    // TODO: 图标样式 UE 稿上没标出来, 待定
    i {
        width: 18px;
        height: 18px;
        background: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_list_arrow_1818664.png') center / cover;
    }
}

.common-picker-popup {
    :global(.adm-popup-body) {
        display: flex;
        flex-direction: column;

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 32px;
            border-bottom: 2px solid rgba(@CB, .08);
            
            .popup-title {
                font-family: PingFangSC-Medium;
                font-size: 32px;
                color: @C0;
                text-align: center;
                line-height: 48px;
                font-weight: 500;
            }
    
            .popup-cancel,
            .popup-confirm {
                padding: 0;
                height: 48px;
    
                span {
                    font-family: PingFangSC-Regular;
                    font-size: 32px;
                    line-height: 48px;
                    font-weight: 400;
                }
            }
    
            .popup-cancel span {
                color: @C0;
            }
    
            .popup-confirm span {
                color: @CB;
            }
        }
    
        .popup-content {
            overflow-y: auto;
        }
    }
}