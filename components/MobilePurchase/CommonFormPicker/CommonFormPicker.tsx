/**
* @file 通用的弹框表单控件, 作为 FormItem 的子组件使用. 可接收一个受控组件作为表单项
*/
import {useState, cloneElement, FunctionComponentElement} from 'react';
import {Popup, Button} from 'antd-mobile';
import cn from 'classnames';
import styles from './main_750m.module.less';

interface CommonFormPickerProps<T> {
    value?: T;
    onChange?: (value: T) => void;
    popupClassName?: string;
    /** Popup header 的标题 */
    title?: string;
    /** 根据多组的 value 设置展示的文本 */
    formatText?: (value: T) => string;
    /** Popup 内部组件, 需要能接收 value 和 onChange */
    innerComponent: FunctionComponentElement<{value: T, onChange: (value: T) => void}>;
}

export default function CommonFormPicker<T = string>(props: CommonFormPickerProps<T>) {
    const {value, onChange, popupClassName, title, formatText, innerComponent} = props;

    const [innerValue, setInnerValue] = useState<T>(value);
    const [visible, setVisible] = useState(false);

    const handleConfirm = () => {
        setVisible(false);
        onChange(innerValue);
    };

    return (
        <div
            className={styles['common-picker']}
            onClick={() => {
                setInnerValue(value);
                setVisible(true);
            }}
        >
            <span className={styles['value-text']}>
                {formatText(value) ? formatText(value) : value}
            </span>
            <i className={styles.icon} />
            <Popup
                visible={visible}
                className={cn(styles['common-picker-popup'], popupClassName)}
                onMaskClick={() => setVisible(false)}
                // destroyOnClose 确保内部组件状态会及时更新
                destroyOnClose
            >
                <div className={styles['popup-header']}>
                    <Button
                        className={styles['popup-cancel']}
                        fill="none"
                        onClick={() => setVisible(false)}
                    >
                        取消
                    </Button>
                    {title && <div className={styles['popup-title']}>{title}</div>}
                    <Button
                        className={styles['popup-confirm']}
                        fill="none"
                        onClick={handleConfirm}
                    >
                        确定
                    </Button>
                </div>
                <div className={styles['popup-content']}>
                    {cloneElement(
                        innerComponent,
                        {
                            value: innerValue,
                            onChange: (newValue: T) => setInnerValue(newValue),
                        }
                    )}
                </div>
            </Popup>
        </div>
    );
}
