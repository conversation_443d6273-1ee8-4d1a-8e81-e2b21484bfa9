/**
 * @file ai加速器2024年终活动1
 * <AUTHOR>
 */

import {ULazyLoad} from '@baidu/bce-components';
import {getAiAccelerateCamp1Progress, getFormSubmitStatus, sendMonitor} from '@common/helper/page';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {MouseEventHandler, useEffect, useRef, useState} from 'react';
import cn from 'classnames';
import {useOnMount} from '@baidu/bce-hooks';
import {IS_PROD_ONLINE} from '@common/constant/variableConst';
import {SurveyIframePostMessageObj} from '@common/interface/common';
import {isMobile} from '@baidu/bce-helper';
import {AiAccModal} from '../AiAccModal/AiAccModal';
import {AiAcceleratorShareBtn} from '../AiAcceleratorShareBtn/AiAcceleratorShareBtn';
import styles from './main_1200.module.less';
import {aiAcceleratorActivity1Data} from './model';

const trackCategory = 'ai加速年终活动页活动一';
const trackCategory2 = 'ai加速年终活动助力页';

const CourseItem = (props: {
    item: (typeof aiAcceleratorActivity1Data)[0];
    onApplyStatusChange: (status: boolean) => void;
    onStudyStatusChange: (status: boolean) => void;
    isFromShare: boolean;
}) => {
    const {item, onApplyStatusChange, onStudyStatusChange, isFromShare} = props;
    const [applyStatus, setApplyStatus] = useState(false);
    const [studyStatus, setStudyStatus] = useState(false);
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const loadingRef = useRef(false);
    const handleApply = () => {
        if (loadingRef.current) {
            return;
        }
        sendMonitor({
            category: isFromShare ? trackCategory2 : trackCategory,
            action: 'click',
            name: item.title,
            value: applyStatus ? (studyStatus ? '继续学习' : '去学习') : '立即报名',
        });
        if (applyStatus) {
            if (isMobile()) {
                location.href = item.applyLink;
            } else {
                window.open(item.applyLink);
            }
            return;
        }
        loadingRef.current = true;
        setTimeout(() => {
            loadingRef.current = false;
        }, 300);
        verifyHandler().then(() => {
            getFormSubmitStatus(item.surveyCampaignId).then(res => {
                if (isMobile()) {
                    location.href = item.applyLink;
                } else {
                    window.open(item.applyLink);
                }
                setApplyStatus(res.result?.submited);
                onApplyStatusChange(res.result?.submited);
            });
        });
    };
    useEffect(() => {
        if (userinfo?.hasLogin) {
            getFormSubmitStatus(item.surveyCampaignId).then(res => {
                setApplyStatus(res.result?.submited);
                onApplyStatusChange(res.result?.submited);
            });
            getAiAccelerateCamp1Progress(item.surveyCampaignId).then(res => {
                if (res.result?.completeLearningTask) {
                    setStudyStatus(true);
                    onStudyStatusChange(true);
                }
            });
        }
    }, [item.surveyCampaignId, userinfo]);
    return (
        <li>
            <div className={styles['item-inner']}>
                <div className={styles['title-wrapper']}>
                    <h3 className={styles['colorful-title']}>{item.colorfulTitle}</h3>
                    <h3 className={styles.title}>{item.title}</h3>
                </div>
                <p className={styles.desc}>{item.desc}</p>
                <ul className={styles['course-details']}>
                    {
                        item.details.map(det => (
                            <li key={det}>{det}</li>
                        ))
                    }
                </ul>
                <div className={styles['status']}>
                    <span
                        className={cn(styles['apply-status'], applyStatus && styles['success'])}
                    >
                        {applyStatus ? '已' : '未'}完成报名
                    </span>
                    <span
                        className={cn(styles['study-status'], studyStatus && styles['success'])}
                    >
                        {studyStatus ? '已' : '未'}完成学习
                    </span>
                </div>
                <span className={styles['btn']} onClick={handleApply}>
                    {applyStatus ? (studyStatus ? '继续学习' : '去学习') : '立即报名'}
                </span>
            </div>
        </li>
    );
};

const giftCampaignId = IS_PROD_ONLINE ? '20250116_survey_xinnianjiangli1' : '20250102_survey_huodong1';
const giftSurveyLink = IS_PROD_ONLINE ? 'https://cloud.baidu.com/survey/xinnianjiangli1.html?iframe=true'
    : 'https://cloudtest.baidu.com/survey/huodong1.html?iframe=true';

export const AiAcceleratorActivity1 = (props: {
    isFromShare?: boolean;
}) => {
    const {isFromShare} = props;
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const [getGiftSuccess, setGetGiftSuccess] = useState(false);
    const [applyStatusList, setApplyStatusList] = useState<boolean[]>([false, false, false, false]);
    const [studyStatusList, setStudyStatusList] = useState<boolean[]>([false, false, false, false]);
    const handleReceiveGift: MouseEventHandler<HTMLDivElement> = e => {
        e.preventDefault();
        if (userinfo?.hasLogin) {
            if (applyStatusList.some(item => !item)) {
                sendMonitor({
                    category: trackCategory,
                    action: 'click',
                    name: '领取礼品',
                    value: '未完成报名',
                });
                AiAccModal.open({
                    title: '未完成报名',
                    desc: '您还没有完成全部线上营报名及学习每期任意一节课程，再接再厉完成任务，领取恒温养生壶！',
                    onConfirm: close => {
                        close();
                    },
                });
            } else if (studyStatusList.some(item => !item)) {
                const unStudyList: string[] = [];
                aiAcceleratorActivity1Data.forEach((item, idx) => {
                    if (!studyStatusList[idx]) {
                        unStudyList.push(item.title);
                    }
                });
                sendMonitor({
                    category: trackCategory,
                    action: 'click',
                    name: '领取礼品',
                    value: '未完成课程学习',
                });
                AiAccModal.open({
                    title: '未完成课程学习',
                    desc: (
                        <>
                            您还没有完成 <span style={{fontFamily: 'PingFangSC-Medium', fontWeight: 500}}>{unStudyList.join('、')}</span> 的任意一节课程，希望您再接再厉完成任务
                        </>
                    ),
                    onConfirm: close => {
                        close();
                    },
                });
            }
        } else {
            verifyHandler();
        }
    };
    useEffect(() => {
        !isFromShare && userinfo?.hasLogin && getFormSubmitStatus(giftCampaignId).then(res => {
            setGetGiftSuccess(res.result?.submited);
        });
    }, [userinfo?.hasLogin]);
    useOnMount(() => {
        const handleMessage = (e: MessageEvent<SurveyIframePostMessageObj>) => {
            if (e.data?.surveySubmitSuccess && e.data?.url === giftSurveyLink) {
                getFormSubmitStatus(giftCampaignId).then(res => {
                    setGetGiftSuccess(res.result?.submited);
                });
            }
        };
        !isFromShare && window.addEventListener('message', handleMessage, false);
        return () => {
            !isFromShare && window.removeEventListener('message', handleMessage);
        };
    });
    return (
        <ULazyLoad
            type="backgroundImage"
        >
            <section
                className={cn(styles['ai-accelerator-activity-1'], 'ai-accelerator-module', isFromShare && styles['from-share'])}
                data-url="https://bce.bdstatic.com/p3m/common-service/uploads/part1-bg_1198388.png"
            >
                <div className={styles.container}>
                    <h2>
                        {
                            isFromShare ? '完成线上课程学习 帮助好友助力' : '活动一：完成线上课程学习领入门奖励'
                        }
                    </h2>
                    {
                        !isFromShare && <p className={styles['tip-text']}>完成任务后请刷新页面，获取任务进度</p>
                    }
                    {
                        isFromShare ? (
                            <p className={styles['sub-title']}>
                                报名4期线上营+学习每期任意一节课，完成助力后记得刷新页面
                            </p>
                        ) : (
                            <ULazyLoad type="backgroundImage">
                                <div
                                    className={styles['gift-wrapper']}
                                    data-url="https://bce.bdstatic.com/p3m/common-service/uploads/part1-bg-prize_eb1c3f6.png"
                                >
                                    <div className={styles['gift-info']}>
                                        <div className={styles['gift-title']}>
                                            <div className={styles['gift-img']}>
                                                <img src="https://bce.bdstatic.com/p3m/common-service/uploads/activity-1_f0b913f.png" alt="恒温养生壶" />
                                            </div>
                                            <h3>报名以下4期线上营，学习每期任意一节课程，领恒温养生壶</h3>
                                        </div>
                                        <p>每人限量1份，数量有限，先到先得（活动结束后15个工作日发货）</p>
                                    </div>
                                    {
                                        getGiftSuccess ? (
                                            <div className={cn(styles['gift-btn'], styles['get-success'])}>
                                                <span>已领取</span>
                                            </div>
                                        ) : [...applyStatusList, ...studyStatusList].some(item => !item) ? (
                                            <div
                                                className={styles['gift-btn']}
                                                onClick={handleReceiveGift}
                                            >
                                                <span>领取礼品</span>
                                            </div>
                                        ) : (
                                            <a
                                                className={styles['gift-btn']}
                                                href={giftSurveyLink}
                                                data-track-category={trackCategory}
                                                data-track-name="领取礼品"
                                                data-track-value="领取礼品"
                                            >
                                                <span>领取礼品</span>
                                            </a>
                                        )
                                    }
                                </div>
                            </ULazyLoad>
                        )
                    }
                    <ul className={styles['course-list']}>
                        {
                            aiAcceleratorActivity1Data.map((item, idx) => (
                                <CourseItem
                                    item={item}
                                    key={item.title}
                                    onApplyStatusChange={v => {
                                        setApplyStatusList(prev => {
                                            const newList = [...prev];
                                            newList[idx] = v;
                                            return newList;
                                        });
                                    }}
                                    onStudyStatusChange={v => {
                                        setStudyStatusList(prev => {
                                            const newList = [...prev];
                                            newList[idx] = v;
                                            return newList;
                                        });
                                    }}
                                    isFromShare={isFromShare}
                                />
                            ))
                        }
                    </ul>
                    {
                        isFromShare && <AiAcceleratorShareBtn />
                    }
                </div>
            </section>
        </ULazyLoad>
    );
};
