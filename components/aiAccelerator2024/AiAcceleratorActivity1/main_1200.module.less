@import '@styles/mixin.less';
.container {
    width: 1090px;
    margin: 0 auto;
}
.ai-accelerator-activity-1 {
    padding-top: 64px;
    padding-bottom: 24px;
    background: no-repeat center top / cover;
    &.from-share {
        padding-top: 36px;
        padding-bottom: 64px;
    }
    h2 {
        font-family: PingFangSC-Semibold;
        font-size: 32px;
        color: #091221;
        text-align: center;
        line-height: 48px;
        font-weight: 600;
        width: max-content;
        padding-right: 8px;
        box-sizing: content-box;
        margin: 0 auto;
        background: url('https://bce.bdstatic.com/p3m/common-service/uploads/line-blue_adadb45.png') no-repeat right top / auto 48px;
    }
    .tip-text, .sub-title {
        position: relative;
        margin:  12px auto 32px;
        font-family: PingFangSC-Medium;
        font-size: 12px;
        color: #091221;
        text-align: center;
        line-height: 20px;
        font-weight: 500;
        max-width: max-content;
        &::before, &::after {
            content: '';
            position: absolute;
            width: min(30px, 40PX);
            height: 20px;
            top: 0;
            background: url(https://bce.bdstatic.com/p3m/common-service/uploads/icon-line-left_955929d.png) no-repeat center / 100% auto;
        }
        &::before {
            left: max(-38px, -48PX);
        }
        &::after {
            right: max(-38px, -48PX);
            transform: rotate(180deg);
        }
    }
}
.gift-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 32px;
    margin-bottom: 14px;
    width: 100%;
    background: no-repeat right top / cover;
    border-radius: 16px;
    position: relative;
    .gift-info {
        .gift-img {
            position: absolute;
            left: 32px;
            top: 20px;
            width: 132px;
            height: 60px;
            border-radius: 8px;
            margin-right: 24px;
            background-color: #fff;
            overflow: hidden;
            img {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                width: 62px;
                height: 60px;
            }
        }
        h3 {
            font-family: PingFangSC-Medium;
            font-size: 18px;
            color: #FFFFFF;
            line-height: 24px;
            font-weight: 500;
            padding-left: 156px;
        }
        p {
            margin-top: 8px;
            opacity: 0.8;
            font-family: PingFangSC-Regular;
            font-size: min(12px, 16PX);
            color: #FFFFFF;
            text-align: left;
            line-height: 20px;
            font-weight: 400;
            padding-left: 156px;
        }
    }
    .gift-btn {
        display: block;
        background: #FFFFFF;
        border-radius: 8px;
        width: 120px;
        height: 36px;
        line-height: 36px;
        font-family: PingFangSC-Medium;
        font-size: min(12px, 16PX);
        text-align: center;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.3s ease-out;
        user-select: none;
        span {
            color: #FFFFFF;
            font-family: PingFangSC-Medium;
            background: linear-gradient(to right, #2468F1, #26A8FF);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        &:hover {
            background-color: #EBF6FE;
        }
        &.get-success {
            background-color: #98D5FE;
            opacity: 1 !important;
            cursor: default;
        }
    }
}
.course-list {
    display: flex;
    justify-content: space-between;
    > li {
        width: 262px;
        border: 1px solid #FFFFFF;
        border-radius: 16px;
        padding: 24px;
        background-color: rgba(255,255,255,0.70);
        transition: background-color 0.3s ease-out;
        backdrop-filter: blur(10px);
        &:hover {
            background-color: #fff;
        }
    }
    .item-inner {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
    .colorful-title {
        font-family: PingFangSC-Medium;
        font-size: 22px;
        color: #091221;
        line-height: 30px;
        font-weight: 500;
        background: linear-gradient(to right, #2468F1, #26A8FF);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .title {
        font-family: PingFangSC-Medium;
        font-size: 22px;
        color: #091221;
        line-height: 30px;
        font-weight: 500;
        .ellipsis();
    }
    .desc {
        margin-top: 8px;
        opacity: 0.7;
        font-family: PingFangSC-Regular;
        font-size: min(12px, 16PX);
        color: #091221;
        line-height: 20px;
        font-weight: 400;
        flex-shrink: 1;
        height: 100%;
    }
    .course-details {
        margin-top: 16px;
        padding-bottom: 20px;
        border-bottom: 1px dashed rgba(9,18,33,0.10);
        li {
            position: relative;
            padding-left: 20px;
            font-family: PingFangSC-Regular;
            font-size: min(12px, 16PX);
            color: rgba(#091221, 0.7);
            line-height: 20px;
            font-weight: 400;
            + li {
                margin-top: 8px;
            }
            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: min(12px, 16PX);
                height: min(12px, 16PX);
                background: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon-tick_ecd8a55.png') no-repeat center / cover;
            }
        }
    }
    .status {
        padding-top: 20px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        span {
            position: relative;
            width: 50%;
            font-family: PingFangSC-Regular;
            font-size: min(12px, 16PX);
            color: rgba(#091221, 0.4);
            line-height: 20px;
            font-weight: 400;
            padding-left: 18px;
            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 3px;
                width: min(14px, 18PX);
                height: min(14px, 18PX);
                background: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_finish_11d2252.png') no-repeat center / cover;
            }
        }
        .success {
            color: #1FCC9E;
            &::before {
                background-image: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_finish-green_fc272d5.png');
            }
        }
    }
    .btn {
        display: block;
        margin-top: 16px;
        background-image: linear-gradient(90deg, #2468F1 0%, #26A8FF 100%);
        border-radius: 8px;
        font-family: PingFangSC-Medium;
        font-size: 12px;
        color: #FFFFFF;
        text-align: center;
        line-height: 36px;
        font-weight: 500;
        transition: opacity 0.3s ease-out;
        cursor: pointer;
        &:hover {
            opacity: 0.75;
        }
    }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
    .container {
        width: calc(100vw - 110px);
    }
    .gift-wrapper {
        padding: 24px 20px;
        .gift-info {
            .gift-img {
                left: 20px;
            }
            h3, p {
                padding-left: 148px;
            }
        }
    }
    .course-list {
        gap: 14px;
        > li {
            width: calc(25% - 10.5px);
            padding: 20px;
        }
        .status {
            justify-content: flex-start;
            gap: 14px;
            span {
                width: auto;
            }
        }
    }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
    .container {
        width: calc(100vw - 64px);
    }
    .gift-wrapper {
        padding: 20px;
        .gift-info {
            .gift-img {
                width: 112px;
                height: 76px;
                margin-right: 16px;
                left: 20px;
                top: 20px;
            }
            h3 {
                width: 476px;
                padding-left: 128px;
            }
            p {
                padding-left: 128px;
            }
        }
    }
    .course-list {
        gap: 14px;
        flex-wrap: wrap;
        > li {
            width: calc(50% - 7px);
            padding: 20px;
        }
        .status {
            gap: 28px;
            span {
                width: 78px;
            }
        }
    }
}

@media screen and (max-width: 767px) {
    .ai-accelerator-activity-1 {
        &.from-share {
            padding-bottom: 24px;
        }
        .tip-text, .sub-title {
            font-size: 13px;
            margin-bottom: 20px;
        }
        .sub-title {
            &::before, &::after {
                display: none;
            }
        }
    }
}

@media screen and (min-width: 600px) and (max-width: 767px) {
    .container {
        width: calc(100vw - 64px);
    }
    .ai-accelerator-activity-1 {
        padding-top: 40px;
        h2 {
            line-height: 40px;
            background: none;
            padding-right: 0;
            font-size: 28px;
        }
    }
    .gift-wrapper {
        margin-bottom: 12px;
        padding: 20px;
        padding-left: 160px;
        position: relative;
        display: block;
        background-image: url(https://bce.bdstatic.com/p3m/common-service/uploads/part1-bg-prize_m_1e5b468.png) !important;
        .gift-info {
            transform: none;
            width: 100%;
            .gift-img {
                position: absolute;
                left: 20px;
                top: 20px;
                width: 124px;
                height: 124px;
                img {
                    width: 91px;
                    height: 88px;
                }
            }
            h3 {
                width: 356px;
                padding-left: 0;
            }
            p {
                margin-top: 4px;
                padding-left: 0;
                font-size: 13px;
                width: 100%;
                .ellipsis();
            }
        }
        
        .gift-btn {
            margin-top: 16px;
            font-size: 13px;
        }
    }
    .course-list {
        gap: 12px;
        flex-wrap: wrap;
        > li {
            width: calc(50% - 6px);
            padding: 20px;
            .course-details {
                li {
                    font-size: 13px;
                }
            }
            .desc {
                font-size: 13px;
            }
            .status {
                span {
                    font-size: 13px;
                }
            }
            .btn {
                font-size: 13px;
            }
        }
    }
}

@media screen and (max-width: 599px) {
    .container {
        width: calc(100vw - 48px);
    }
    .ai-accelerator-activity-1 {
        padding-top: 40px;
        background-image: url(https://bce.bdstatic.com/p3m/common-service/uploads/part1-bg-h5_914db7d.png) !important;
        h2 {
            width: 252px;
            line-height: 40px;
            background: none;
            font-size: 28px;
            padding-right: 0;
        }
    }
    .gift-wrapper {
        margin-bottom: 0;
        padding: 20px;
        position: relative;
        display: block;
        background-image: url(https://bce.bdstatic.com/p3m/common-service/uploads/part1-bg-prize_m_1e5b468.png) !important;
        .gift-info {
            .gift-title {
                display: flex;
                align-items: center;
            }
            .gift-img {
                position: relative;
                left: 0;
                top: 0;
                width: 40px;
                height: 40px;
                margin-right: 8px;
                flex-shrink: 0;
                img {
                    width: 35px;
                    height: 34px;
                }
            }
            h3 {
                font-size: 16px;
                padding-left: 0;
            }
            p {
                padding-left: 0;
                font-size: 13px;
                margin-top: 4px;
                .ellipsis();
            }
        }
        .gift-btn {
            width: 100%;
            margin-top: 16px;
            font-size: 13px;
        }
    }
    
    .course-list {
        gap: 0;
        flex-wrap: wrap;
        > li {
            width: 100%;
            padding: 20px;
            margin-top: 12px;
            .course-details {
                display: none;
            }
            .title-wrapper {
                display: flex;
                align-items: center;
                .ellipsis();
            }
            .desc {
                font-size: 13px;
                height: auto;
            }
            .status {
                span {
                    font-size: 13px;
                }
            }
            .btn {
                font-size: 13px;
            }
        }
    }
}