
.ai-acc-modal {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 448px;
    background: #FFFFFF url('https://bce.bdstatic.com/p3m/common-service/uploads/window-bg-2_faecd57.png') no-repeat center top / 100% auto;
    border-radius: 16px;
    padding: 32px 40px;
    h3 {
        font-family: PingFangSC-Medium;
        font-size: 24px;
        color: #091221;
        text-align: center;
        line-height: 32px;
        font-weight: 500;
    }
    .desc {
        margin-top: 16px;
        opacity: 0.7;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #091221;
        text-align: center;
        line-height: 20px;
        font-weight: 400;
    }
    .confirm-btn {
        display: block;
        margin: 32px auto 0;
        width: 256px;
        height: 40px;
        background-image: linear-gradient(90deg, #2468F1 0%, #26A8FF 100%);
        border-radius: 8px;
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #FFFFFF;
        text-align: center;
        line-height: 40px;
        font-weight: 500;
        transition: opacity 0.3s ease-out;
        cursor: pointer;
        &:hover {
            opacity: 0.75;
        }
    }
    .close-icon {
        position: absolute;
        top: 24px;
        right: 24px;
        width: 20px;
        height: 20px;
        cursor: pointer;
        path {
            transition: stroke 0.3s ease-out;
        }
        &:hover {
            path {
                stroke: #2468f2;
            }
        }
    }
}

@media screen and (max-width: 767px) {
    .ai-acc-modal {
        width: 280px;
        padding: 24px 20px;
        h3 {
            font-size: 18px;
            line-height: 28px;
        }
        .desc {
            font-size: 13px;
            margin-top: 12px;
        }
        .close-icon {
            top: 12px;
            right: 12px;
        }
        .confirm-btn {
            width: 240px;
        }
    }
}