/**
 * @file ai加速器公共弹窗
 * <AUTHOR>
 */

import {useStateRef} from '@baidu/bce-hooks';
import {UModalMask} from '@common/components/UModalMask/UModalMask';
import {Ioc} from '@baidu/bce-decorators';
import {UDynamicService} from '@baidu/bce-services';
import {ReactSVG} from 'react-svg';
import {ReactNode} from 'react';

import styles from './main_1200.module.less';

interface AiAccModalProps {
    title: ReactNode;
    desc: ReactNode;
    onConfirm?: (close: () => void) => void;
    confirmText?: string;
    onClose?: () => void;
}

const AiAccModalCom = (props: AiAccModalProps) => {
    const [show, setShow] = useStateRef<boolean>(true);
    const close = () => setShow(false);
    const cancelHandler = () => {
        close();
        props.onClose && props.onClose();
    };
    const confirmHandler = () => {
        props.onConfirm && props.onConfirm(close);
    };
    return (
        <UModalMask
            show={show}
            maskClosable
            onMaskClick={cancelHandler}
        >
            <div className={styles['ai-acc-modal']}>
                <h3>{props.title}</h3>
                <div className={styles.desc}>{props.desc}</div>
                <div className={styles['confirm-btn']} onClick={confirmHandler}>
                    {props.confirmText || '返回页面'}
                </div>
                {/* <i onClick={cancelHandler} /> */}
                <ReactSVG
                    className={styles['close-icon']}
                    src="https://bce.bdstatic.com/p3m/common-service/uploads/icon-close_836d771.svg"
                    onClick={cancelHandler}
                />
            </div>
        </UModalMask>
    );
};

type AiAccModalType = typeof AiAccModalCom & {
    open: (props: AiAccModalProps) => void;
};

const AiAccModal = AiAccModalCom as AiAccModalType;

AiAccModal.open = (props: AiAccModalProps) => {
    Ioc(UDynamicService).open({
        component: AiAccModalCom,
        props,
    });
};

export {AiAccModal};
