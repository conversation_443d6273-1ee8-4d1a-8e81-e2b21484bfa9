/**
 * @file ai加速器活动-新客专享
 * <AUTHOR>
 */

import cn from 'classnames';
import {aIAcceleratorNewCustomerData as data} from './model';
import styles from './main_1200.module.less';

const trackCategory = '新年活动 限时特惠';

export const AIAcceleratorNewCustomer = () => {
    return (
        <section className={cn(styles['ai-accelerator-new-customer'], 'ai-accelerator-module')}>
            <div className={styles.container}>
                <h2>新年活动 限时特惠</h2>
                <ul className={styles.list}>
                    {
                        data.map(item => (
                            <li className={styles.item} key={item.title}>
                                <div className={styles['item-inner']}>
                                    <h3 className={styles.title}>{item.title}</h3>
                                    <p className={styles.desc}>{item.desc}</p>
                                    <ul className={styles.options}>
                                        {
                                            item.options.map((option, idx) => (
                                            // eslint-disable-next-line react/no-array-index-key
                                                <li key={idx} className={cn(!option.name && !option.value && styles['empty'])}>
                                                    <span className={styles['option-name']}>{option.name}</span>
                                                    <span className={styles['option-value']}>{option.value}</span>
                                                </li>
                                            ))
                                        }
                                    </ul>
                                    <div className={styles.tags}>
                                        {
                                            item.tags.map(tag => (
                                                <span key={tag} className={styles.tag}>
                                                    <span>{tag}</span>
                                                </span>
                                            ))
                                        }
                                    </div>
                                    <div className={styles['price-container']}>
                                        <span className={styles['price-unit']}>¥</span>
                                        <span className={styles['price-value']}>{item.price}</span>
                                        <span className={styles['line-price']}>¥{item.linePrice}</span>
                                    </div>
                                    <a
                                        className={styles.btn}
                                        href={item.link}
                                        target="_blank"
                                        data-track-category={trackCategory}
                                        data-track-name="卡片"
                                        data-track-value={item.title}
                                    >
                                        {item.btnText}
                                    </a>
                                    <div className={styles['corner-mark']}>{item.cornerMark}</div>
                                </div>
                            </li>
                        ))
                    }
                </ul>
            </div>
        </section>
    );
};
