@import '@styles/mixin.less';

.container {
    width: 1090px;
    margin: 0 auto;
}

.ai-accelerator-new-customer {
    padding-top: 64px;
    padding-bottom: 0;
    border-radius: 24px 24px 0 0;
    h2 {
        font-family: PingFangSC-Semibold;
        font-size: 32px;
        color: #091221;
        text-align: center;
        line-height: 48px;
        font-weight: 600;
        width: max-content;
        padding-right: 8px;
        box-sizing: content-box;
        margin: 0 auto;
        background: url('https://bce.bdstatic.com/p3m/common-service/uploads/line-blue_adadb45.png') no-repeat right top / auto 48px;
    }
}

.list {
    display: flex;
    gap: 14px;
    margin-top: 32px;
    .item {
        position: relative;
        width: 25%;
        padding: 32px 24px 24px;
        background-color: rgba(#fff, 0.9);
        border-radius: 16px;
        overflow: hidden;
        transition: background-color 0.3s ease-out;
        &:hover {
            background-color: #fff;
        }
    }
    .item-inner {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
    .title {
        font-family: PingFangSC-Medium;
        font-size: 22px;
        color: #091221;
        text-align: left;
        line-height: 30px;
        font-weight: 500;
        flex-shrink: 0;
    }
    .desc {
        margin-top: 4px;
        opacity: 0.7;
        font-family: PingFangSC-Regular;
        font-size: min(12px, 16PX);
        color: #091221;
        text-align: justify;
        line-height: 20px;
        font-weight: 400;
        height: 100%;
        flex-shrink: 1;
        .ellipsis2(3);
    }
    .options {
        margin-top: 16px;
        padding-bottom: 20px;
        border-bottom: 1px dashed rgba(9,18,33,0.10);
        li {
            display: flex;
            gap: 12px;
            height: 20px;
            + li {
                margin-top: 8px;
            }
            .option-name {
                width: 76px;
                opacity: 0.5;
                font-family: PingFangSC-Regular;
                font-size: min(12px, 16PX);
                color: #091221;
                text-align: left;
                line-height: 20px;
                font-weight: 400;
            }
            .option-value {
                font-family: PingFangSC-Medium;
                font-size: min(12px, 16PX);
                color: #091221;
                text-align: left;
                line-height: 20px;
                font-weight: 500;
            }
        }
    }
    .tags {
        display: flex;
        margin-top: 16px;
        .tag {
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            overflow: hidden;
            border-radius: 4px;
            padding: 0 6px;
            background: rgba(#091221, 0.04);
            + .tag {
                margin-left: 8px;
            }
            span {
                font-family: PingFangSC-Regular;
                font-size: min(12px, 16PX);
                text-align: center;
                font-weight: 400;
                color: rgba(#091221, 0.6);
                line-height: 1;
                transform: scale(0.9167);
            }
            &:first-child {
                background: rgba(#F33E3E, 0.1);
                span {
                        color: #F33E3E;
                }
            }
        }
    }
    .price-container {
        margin-top: 6px;
        display: flex;
        align-items: flex-end;
        justify-content: flex-start;
        .price-unit {
            font-family: PingFangSC-Medium;
            font-size: 12px;
            color: #F33E3E;
            text-align: left;
            line-height: 20px;
            font-weight: 500;
        }
        .price-value {
            font-family: PingFangSC-Medium;
            font-size: 24px;
            color: #F33E3E;
            text-align: left;
            line-height: 30px;
            font-weight: 500;
            margin-right: 6px;
        }
        .line-price {
            opacity: 0.7;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #091221;
            text-align: left;
            line-height: 20px;
            font-weight: 400;
            text-decoration: line-through;
        }
    }
    .btn {
        display: block;
        margin-top: 16px;
        height: 36px;
        background-image: linear-gradient(90deg, #2468F1 0%, #26A8FF 100%);
        border-radius: 8px;
        font-family: PingFangSC-Medium;
        font-size: min(12px, 16PX);
        color: #FFFFFF;
        text-align: center;
        line-height: 36px;
        font-weight: 500;
        transition: opacity 0.3s ease-out;
        &:hover {
            opacity: 0.75;
        }
    }
    .corner-mark {
        position: absolute;
        right: 0;
        top: 0;
        width: min(110px, 140PX);
        height: min(38px, 52PX);
        padding-left: min(26px, 38PX);
        background: url(https://bce.bdstatic.com/p3m/common-service/uploads/tag-long_f328c2d.png) no-repeat center / cover;
        font-family: PingFangSC-Medium;
        color: #FFFFFF;
        font-weight: 500;
        font-size: min(12px, 14PX);
        line-height: min(26px, 34PX);
    }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
    .container {
        width: calc(100vw - 110px);
    }
    .list {
        .item {
            padding: 32px 20px 24px;
        }
        .options {
            .option-value {
                .ellipsis();
            }
        } 
    }
}

@media screen and (min-width: 992px) and (max-width: 1103px) {
    .list {
        .title {
            height: 60px;
        }
    }
}


@media screen and (min-width: 768px) and (max-width: 991px) {
    .container {
        width: calc(100vw - 64px);
    }
    .list {
        flex-wrap: wrap;
        .item {
            width: calc(50% - 7px);
            padding: 32px 20px 24px;
        }
    }
}

@media screen and (max-width: 767px) {
    .ai-accelerator-new-customer {
        padding-top: 40px;
        h2 {
            background: none;
            font-size: 28px;
            line-height: 40px;
            padding-right: 0;
        }
    }
    .list {
        flex-wrap: wrap;
        margin-top: 20px;
        gap: 12px;
        .item {
            width: calc(50% - 6px);
            padding: 32px 20px 24px;
        }
        .desc {
            font-size: 13px;
        }
        .options {
            li {
                .option-name, .option-value {
                    font-size: 13px;
                }
            }
        }
        .btn {
            font-size: 13px;
        }
    }
}

@media screen and (min-width: 600px) and (max-width: 767px) {
    .container {
        width: calc(100vw - 64px);
    }
}

@media screen and (max-width: 599px) {
    .container {
        width: calc(100vw - 48px);
    }
    .list {
        .item {
            width: 100%;
            .options {
                li {
                    &.empty {
                        display: none;
                    }
                }
            }
        }
    }
}