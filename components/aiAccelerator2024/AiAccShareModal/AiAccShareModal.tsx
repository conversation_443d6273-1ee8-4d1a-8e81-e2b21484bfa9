/**
 * @file 分享弹窗
 * <AUTHOR>
 */

import {useStateRef} from '@baidu/bce-hooks';
import {UModalMask} from '@common/components/UModalMask/UModalMask';
import {ReactSVG} from 'react-svg';
import cn from 'classnames';
import {Ioc} from '@baidu/bce-decorators';
import {UDynamicService} from '@baidu/bce-services';
import styles from './main_1200.module.less';

interface AiAccShareModalProps {
    code: 200 | 501 | 502 | 503 | 504; // 200 - 成功助力 501 - 已被其他人邀请，正在助力中 502 - 已被其他人邀请，已完成助力 503 - 给自己助力 504 - 开始助力
    inviterId?: string;
}

const AiAccShareModalCom = (props: AiAccShareModalProps) => {
    const {code, inviterId} = props;
    const [show, setShow] = useStateRef<boolean>(true);
    const close = () => setShow(false);
    const cancelHandler = () => {
        close();
    };
    return (
        <UModalMask
            show={show}
            maskClosable
            onMaskClick={cancelHandler}
        >
            <div className={styles['ai-acc-share-modal']}>
                <div className={styles['modal-title-wrapper']}>
                    {
                        code === 200 ? (
                            <h3 className={cn(styles['title'], styles['type-helpSuccess'])}>助力成功<br />谢谢您的助力！</h3>
                        ) : code === 504 ? (
                            <h3 className={cn(styles['title'], styles['type-helpSuccess'])}>您的好友<br />正在邀请您帮TA助力</h3>
                        ) : (
                            <h3 className={styles['title']}>
                                邀请好友报名线上营<br />
                                助力成功得百度精美礼品
                            </h3>
                        )
                    }
                    <div className={styles['img-wrapper']}>
                        <img src="https://bce.bdstatic.com/p3m/common-service/uploads/gift_027fcf5.png" alt="精美礼品" />
                    </div>
                </div>
                <div className={styles['modal-content-wrapper']}>
                    {
                        code === 200 ? (
                            <div className={cn(styles['content'], styles['type-helpSuccess'])}>
                                邀请好友报名线上营<br />助力成功即可得<span>百度精美礼品</span>
                            </div>
                        ) : (
                            <div className={cn(styles['content'], styles['type-unableHelpMyself'])}>
                                {
                                    code === 501 ? (
                                        <>您正在帮其他人助力<br />无法同时助力</>
                                    ) : code === 502 ? (
                                        '一个账号只能助力一次哦'
                                    ) : code === 504 ? (
                                        '报名线上营并学习帮TA助力'
                                    ) : (
                                        <>无法为自己助力<br />请分享给其他朋友</>
                                    )
                                }
                            </div>
                        )
                    }
                    {
                        code === 501 ? (
                            <a className={styles['btn-wrapper']} href={`/campaign/aiAccelerator2024_share.html?inviter_id=${inviterId}`}>
                                前往完成助力
                            </a>
                        ) : code === 502 || code === 503 ? (
                            <a
                                className={styles['btn-wrapper']}
                                href={`/campaign/aiAccelerator2024.html${code === 502 ? '' : '?campaign=3'}`}
                            >
                                返回分享页面
                            </a>
                        ) : code === 504 ? (
                            <span className={styles['btn-wrapper']} onClick={cancelHandler}>
                                立即报名线上营
                            </span>
                        ) : (
                            <a
                                className={styles['btn-wrapper']}
                                href="/campaign/aiAccelerator2024.html?campaign=3"
                            >
                                我也要分享得奖励
                            </a>
                        )
                    }
                </div>
                <ReactSVG
                    className={styles['close-icon']}
                    src="https://bce.bdstatic.com/p3m/common-service/uploads/icon-close_836d771.svg"
                    onClick={cancelHandler}
                />
            </div>
        </UModalMask>
    );
};

type AiAccShareModalType = typeof AiAccShareModalCom & {
    open: (props: AiAccShareModalProps) => void;
};

const AiAccShareModal = AiAccShareModalCom as AiAccShareModalType;

AiAccShareModal.open = (props: AiAccShareModalProps) => {
    Ioc(UDynamicService).open({
        component: AiAccShareModalCom,
        props,
    });
};

export {AiAccShareModal};
