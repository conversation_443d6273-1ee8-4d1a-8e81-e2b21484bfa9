.ai-acc-share-modal {
    width: 448px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 16px;
    overflow: hidden;
    background: url(https://bce.bdstatic.com/p3m/common-service/uploads/window-bg-1_5724e30.png) no-repeat center / cover;
    .modal-title-wrapper {
        display: flex;
        justify-content: space-between;
        height: 120px;
        padding: 0 64px 0 24px;
        .title {
            font-family: PingFangSC-Semibold;
            font-size: 18px;
            color: #FFFFFF;
            line-height: 28px;
            font-weight: 600;
            padding-top: 34px;
            &.type-helpSuccess {
                font-size: 24px;
                line-height: 32px;
                padding-top: 30px;
            }
        }
        .img-wrapper {
            width: 76px;
            height: 76px;
            background: #FFFFFF;
            border-radius: 12px;
            margin-top: 24px;
            position: relative;
            img {
                width: 76px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }
    .modal-content-wrapper {
        background: #FFFFFF;
        border-radius: 16px;
        padding-top: 32px;
        padding-bottom: 32px;
        text-align: center;
        .content {
            &.type-helpSuccess {
                font-family: PingFangSC-Medium;
                font-size: 18px;
                color: #091221;
                text-align: center;
                line-height: 26px;
                font-weight: 500;
                margin-bottom: 32px;
                span {
                    color: #091221;
                    font-family: PingFangSC-Medium;
                    background: linear-gradient(to right, #2468F1, #26A8FF);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }
            }
            &.type-helpFail {
                margin-bottom: 32px;
                h3 {
                    font-family: PingFangSC-Medium;
                    font-size: 24px;
                    color: #091221;
                    text-align: center;
                    line-height: 32px;
                    font-weight: 500;
                }
                p {
                    margin-top: 8px;
                    opacity: 0.7;
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: #091221;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 20px;
                    font-weight: 400;
                    span {
                        font-weight: bold;
                    }
                }
            }
            &.type-unableHelpMyself {
                margin-bottom: 28px;
                font-family: PingFangSC-Medium;
                font-size: 24px;
                color: #091221;
                text-align: center;
                line-height: 32px;
                font-weight: 500;
            }
        }
    }
    
    .btn-wrapper {
        display: block;
        margin: 0 auto;
        width: 264px;
        height: 40px;
        background-image: linear-gradient(90deg, #2468F1 0%, #26A8FF 100%);
        border-radius: 8px;
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 40px;
        font-weight: 500;
        cursor: pointer;
        transition: opacity .3s ease-out;
        &:hover {
            opacity: .75;
        }
    }
    .close-icon {
        position: absolute;
        top: 24px;
        right: 24px;
        width: 20px;
        height: 20px;
        cursor: pointer;
        path {
            stroke: #fff;
        }
    }
}

@media screen and (max-width: 767px) {
    .ai-acc-share-modal {
        width: 294px;
        background-image: url(https://bce.bdstatic.com/p3m/common-service/uploads/wap-window-bg-1_cf9d980.png);
        .modal-title-wrapper {
            height: 96px;
            padding: 0 20px;
            .title {
                font-size: 16px;
                line-height: 26px;
                padding-top: 24px;
                &.type-helpSuccess {
                    font-size: 20px;
                    line-height: 28px;
                    padding-top: 22px;
                }
            }
            .img-wrapper {
                width: 60px;
                height: 60px;
                border-radius: 8px;
                margin-top: 20px;
                img {
                    width: 52px;
                }
            }
        }
        .modal-content-wrapper {
            padding-top: 24px;
            padding-bottom: 24px;
            .content {
                &.type-helpSuccess {
                    margin-bottom: 24px;
                }
                &.type-helpFail {
                    margin-bottom: 24px;
                    h3 {
                        font-size: 18px;
                        line-height: 26px;
                    }
                    p {
                        font-size: 13px;
                        span {
                            font-weight: bold;
                        }
                    }
                }
                &.type-unableHelpMyself {
                    font-size: 18px;
                    line-height: 26px;
                    margin-bottom: 24px;
                }
            }
        }
        .close-icon {
            display: none;
        }
    }
}
