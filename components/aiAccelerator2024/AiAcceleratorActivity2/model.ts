/* eslint-disable max-len */

export const aiAcceleratorActivity2Data = [{
    title: '千帆大模型平台',
    desc: '开始实操训练前，记得开通产品',
    courseLink: 'https://cloud.baidu.com/partner/course-center/index.html?type=all&domain=%E7%99%BE%E5%BA%A6%E6%99%BA%E8%83%BD%E4%BA%91%E5%8D%83%E5%B8%86AI%E5%8A%A0%E9%80%9F%E5%99%A8&product=%E7%99%BE%E5%BA%A6%E6%99%BA%E8%83%BD%E4%BA%91%E5%8D%83%E5%B8%86ModelBuilder&third=all',
    joinLink: 'https://console.bce.baidu.com/qianfan/overview',
    api: '/api/qianfan/check_protocol',
    apiParam: 'cloudId',
    isAppBuilder: false,
}, {
    title: '千帆AppBuilder平台',
    desc: '开始实操训练前，记得开通产品',
    courseLink: 'https://cloud.baidu.com/partner/course-center/index.html?type=all&domain=%E7%99%BE%E5%BA%A6%E6%99%BA%E8%83%BD%E4%BA%91%E5%8D%83%E5%B8%86AI%E5%8A%A0%E9%80%9F%E5%99%A8&product=%E7%99%BE%E5%BA%A6%E6%99%BA%E8%83%BD%E4%BA%91%E5%8D%83%E5%B8%86AppBuilder&third=all',
    joinLink: 'https://console.bce.baidu.com/ai_apaas/dialogHome',
    api: '/api/ai_apaas/v1/internal/account/status',
    apiParam: 'bce_account_id',
    isAppBuilder: true,
}, {
    title: '向量数据库',
    desc: '开始实操训练前，记得开通产品',
    courseLink: 'https://cloud.baidu.com/partner/course-center/index.html?type=all&domain=%E7%99%BE%E5%BA%A6%E6%99%BA%E8%83%BD%E4%BA%91%E5%8D%83%E5%B8%86AI%E5%8A%A0%E9%80%9F%E5%99%A8&product=%E5%90%91%E9%87%8F%E6%95%B0%E6%8D%AE%E5%BA%93VectorDB&third=all',
    joinLink: 'https://console.bce.baidu.com/vdb/#/vdb/instance/create',
    api: '/api/vdb-ai/training/camp/account/status',
    apiParam: 'accountId',
    isAppBuilder: false,
}, {
    title: '数字人',
    desc: '开始实操训练前，记得开通产品',
    courseLink: 'https://cloud.baidu.com/partner/course-center/index.html?type=all&domain=%E7%99%BE%E5%BA%A6%E6%99%BA%E8%83%BD%E4%BA%91%E5%8D%83%E5%B8%86AI%E5%8A%A0%E9%80%9F%E5%99%A8&product=%E6%9B%A6%E7%81%B5%E6%95%B0%E5%AD%97%E4%BA%BA&third=all',
    joinLink: 'https://xiling.cloud.baidu.com/main/home',
    api: 'https://xiling.cloud.baidu.com/api/dhlive-saas-alert/account/v1/status',
    apiParam: 'bceAccountId',
    isAppBuilder: false,
}, {
    title: '百度智能云客悦平台',
    desc: '开始实操训练前，记得开通产品',
    courseLink: 'https://cloud.baidu.com/partner/course-center/index.html?type=all&domain=%E7%99%BE%E5%BA%A6%E6%99%BA%E8%83%BD%E4%BA%91%E5%8D%83%E5%B8%86AI%E5%8A%A0%E9%80%9F%E5%99%A8&product=%E7%99%BE%E5%BA%A6%E6%99%BA%E8%83%BD%E4%BA%91%E5%AE%A2%E6%82%A6&third=all',
    joinLink: 'https://keyue.cloud.baidu.com/unit',
    api: 'https://keyue.cloud.baidu.com/api/v1/tenant-user/check/user/exist',
    apiParam: 'accountId',
    isAppBuilder: false,
}, {
    title: '文心快码Baidu Comate',
    desc: '开始实操训练前，记得开通产品',
    courseLink: 'https://cloud.baidu.com/partner/course-center/index.html?type=all&domain=%E7%99%BE%E5%BA%A6%E6%99%BA%E8%83%BD%E4%BA%91%E5%8D%83%E5%B8%86AI%E5%8A%A0%E9%80%9F%E5%99%A8&product=%E6%96%87%E5%BF%83%E5%BF%AB%E7%A0%81%E6%99%BA%E8%83%BD%E4%BB%A3%E7%A0%81%E5%8A%A9%E6%89%8B&third=all',
    joinLink: 'https://console.bce.baidu.com/comate/#/apply?track=baiduTrainingCamp',
    api: '/api/comate/console/internal/account/status',
    apiParam: 'bceAccountId',
    isAppBuilder: false,
}];
