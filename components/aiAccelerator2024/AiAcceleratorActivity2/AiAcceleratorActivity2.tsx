/**
 * @file ai加速器活动 - 活动二
 * <AUTHOR>
 */

import cn from 'classnames';
import {ULazyLoad} from '@baidu/bce-components';
import {MouseEventHandler, useEffect, useRef, useState} from 'react';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {netService} from '@baidu/bce-services';
import {getFormSubmitStatus, sendMonitor} from '@common/helper/page';
import {useOnMount} from '@baidu/bce-hooks';
import {IS_PROD_ONLINE} from '@common/constant/variableConst';
import {SurveyIframePostMessageObj} from '@common/interface/common';
import {isMobile} from '@baidu/bce-helper';
import {AiAccModal} from '../AiAccModal/AiAccModal';
import styles from './main_1200.module.less';
import {aiAcceleratorActivity2Data} from './model';

const trackCategory = 'ai加速年终活动页活动二';

const ProductItem = (props: {
    item: typeof aiAcceleratorActivity2Data[number];
    onApplyStatusChange: (status: boolean) => void;
    onExpandChange: (status: boolean) => void;
    expandProp: boolean;
}) => {
    const {item, onApplyStatusChange, onExpandChange, expandProp} = props;
    const [expand, setExpand] = useState(expandProp);
    const [applyStatus, setApplyStatus] = useState(false);
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const loadingRef = useRef(false);

    const getStatus = (item: typeof aiAcceleratorActivity2Data[number]) =>
        netService.get<{[props: string]: string}, boolean | {'is_registered': boolean}, null>(
            item.api,
            {[item.apiParam]: userinfo.accountId}, null,
            {
                headers: item.isAppBuilder ? {
                    'X-Authorization': 'bearer bce-v3/ALTAK-BKuyFYsSH0KzcMo3QyU5b/eb431f6b1e4e4b22e79acb6e55e6ec0109acda46',
                } : null,
            }
        );

    const handleApply = () => {
        if (loadingRef.current) {
            return;
        }
        if (applyStatus) {
            sendMonitor({
                category: trackCategory,
                action: 'click',
                name: item.title,
                value: '体验产品',
            });
            if (isMobile()) {
                location.href = item.joinLink;
            } else {
                window.open(item.joinLink);
            }
            return;
        }
        loadingRef.current = true;
        setTimeout(() => {
            loadingRef.current = false;
        }, 300);
        verifyHandler().then(() => {
            getStatus(item).then(res => {
                let isApplied = false;
                if (item.isAppBuilder) {
                    isApplied = (res.result as {'is_registered': boolean}).is_registered;
                } else {
                    isApplied = res.result as boolean;
                }
                setApplyStatus(isApplied);
                onApplyStatusChange(isApplied);
                if (isMobile()) {
                    location.href = item.joinLink;
                } else {
                    window.open(item.joinLink);
                }
                sendMonitor({
                    category: trackCategory,
                    action: 'click',
                    name: item.title,
                    value: isApplied ? '体验产品' : '立即开通',
                });
            });
        });
    };
    useEffect(() => {
        userinfo?.hasLogin
        && getStatus(item).then(res => {
            let isApplied = false;
            if (item.isAppBuilder) {
                isApplied = (res.result as {'is_registered': boolean}).is_registered;
            } else {
                isApplied = res.result as boolean;
            }
            setApplyStatus(isApplied);
            onApplyStatusChange(isApplied);
        });
    }, [userinfo?.hasLogin]);
    useEffect(() => {
        setExpand(expandProp);
    }, [expandProp]);
    return (
        <li className={cn(expand ? styles.expand : '')}>
            <div className={styles['item-inner']}>
                <h3>{item.title}</h3>
                <p>{item.desc}</p>
                <span
                    className={styles['toggle-btn']}
                    onClick={() => {
                        setExpand(!expand);
                        onExpandChange(!expand);
                    }}
                />
                <div className={styles['btn-wrapper']}>
                    <span className={styles.btn} onClick={handleApply}>
                        {applyStatus ? '体验产品' : '立即开通'}
                    </span>
                    <a
                        className={styles.btn}
                        href={item.courseLink}
                        target="_blank"
                        data-track-category={trackCategory}
                        data-track-name={item.title}
                        data-track-value="学习课程"
                    >
                        学习课程
                    </a>
                </div>
            </div>
        </li>
    );
};

const giftCampaignId = IS_PROD_ONLINE ? '20250116_survey_xinnianjiangli2' : '20250102_survey_huodong2';
const giftSurveyLink = IS_PROD_ONLINE ? 'https://cloud.baidu.com/survey/xinnianjiangli2.html?iframe=true'
    : 'https://cloudtest.baidu.com/survey/huodong2.html?iframe=true';

export const AiAcceleratorActivity2 = () => {
    const [applyStatusList, setApplyStatusList] = useState<boolean[]>(Array.from({length: 6}).fill(false) as boolean[]);
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const [getGiftSuccess, setGetGiftSuccess] = useState(false);
    const [currentExpand, setCurrentExpand] = useState<number>(0);
    const handleReceiveGift: MouseEventHandler<HTMLDivElement> = e => {
        e.preventDefault();
        if (userinfo?.hasLogin) {
            if (applyStatusList.some(item => !item)) {
                const unApplyList: string[] = [];
                aiAcceleratorActivity2Data.forEach((item, idx) => {
                    if (!applyStatusList[idx]) {
                        unApplyList.push(item.title);
                    }
                });
                sendMonitor({
                    category: trackCategory,
                    action: 'click',
                    name: '领取礼品',
                    value: '未完成产品开通',
                });
                AiAccModal.open({
                    title: '未完成产品开通',
                    desc: (
                        <>
                            您还没有完成
                            &nbsp;<span style={{fontFamily: 'PingFangSC-Medium', fontWeight: 500}}>{unApplyList.join('、')}</span>
                            &nbsp;的产品开通，希望您再接再厉完成开通任务
                        </>
                    ),
                    onConfirm: close => {
                        close();
                    },
                });
            }
        } else {
            verifyHandler();
        }
    };
    useEffect(() => {
        userinfo?.hasLogin && getFormSubmitStatus(giftCampaignId).then(res => {
            setGetGiftSuccess(res.result?.submited);
        });
    }, [userinfo?.hasLogin]);
    useOnMount(() => {
        const handleMessage = (e: MessageEvent<SurveyIframePostMessageObj>) => {
            if (e.data?.surveySubmitSuccess && e.data?.url === giftSurveyLink) {
                getFormSubmitStatus(giftCampaignId).then(res => {
                    setGetGiftSuccess(res.result?.submited);
                });
            }
        };
        window.addEventListener('message', handleMessage, false);
        return () => {
            window.removeEventListener('message', handleMessage);
        };
    });
    return (
        <ULazyLoad type="backgroundImage">
            <section
                className={cn(styles['ai-accelerator-activity-2'], 'ai-accelerator-module')}
                data-url="https://bce.bdstatic.com/p3m/common-service/uploads/part2-bg_f84cf76.png"
            >
                <div className={styles.container}>
                    <h2>活动二：完成产品开通领进阶奖励</h2>
                    <p className={styles['tip-text']}>完成任务后请刷新页面，获取任务进度</p>
                    <ULazyLoad type="backgroundImage">
                        <div
                            className={styles['gift-wrapper']}
                            data-url="https://bce.bdstatic.com/p3m/common-service/uploads/part1-bg-prize_eb1c3f6.png"
                        >
                            <div className={styles['gift-info']}>
                                <div className={styles['gift-title']}>
                                    <div className={styles['gift-img']}>
                                        <img src="https://bce.bdstatic.com/p3m/common-service/uploads/activity-2_8e5981c.png" alt="体脂秤" />
                                    </div>
                                    <h3>开通以下6款产品，领智能体脂秤</h3>
                                </div>
                                <p>每人限量1份，数量有限，先到先得（活动结束后15个工作日发货）</p>
                            </div>
                            {
                                getGiftSuccess ? (
                                    <div className={cn(styles['gift-btn'], styles['get-success'])}>
                                        <span>已领取</span>
                                    </div>
                                ) : applyStatusList.some(item => !item) ? (
                                    <div
                                        className={styles['gift-btn']}
                                        onClick={handleReceiveGift}
                                    >
                                        <span>领取礼品</span>
                                    </div>
                                ) : (
                                    <a
                                        className={styles['gift-btn']}
                                        href={giftSurveyLink}
                                        data-track-category={trackCategory}
                                        data-track-name="领取礼品"
                                        data-track-value="领取礼品"
                                    >
                                        <span>领取礼品</span>
                                    </a>
                                )
                            }
                        </div>
                    </ULazyLoad>
                    <ul className={styles['product-list']}>
                        {
                            aiAcceleratorActivity2Data.map((item, idx) => (
                                <ProductItem
                                    key={item.title}
                                    item={item}
                                    onApplyStatusChange={v => {
                                        setApplyStatusList(prev => {
                                            const newList = [...prev];
                                            newList[idx] = v;
                                            return newList;
                                        });
                                    }}
                                    expandProp={currentExpand === idx}
                                    onExpandChange={v => {
                                        setCurrentExpand(v ? idx : -1);
                                    }}
                                />
                            ))
                        }
                    </ul>
                </div>
            </section>
        </ULazyLoad>
    );
};
