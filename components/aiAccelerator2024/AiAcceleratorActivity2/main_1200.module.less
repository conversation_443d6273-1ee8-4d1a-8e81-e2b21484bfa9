@import '@styles/mixin.less';
.container {
    width: 1090px;
    margin: 0 auto;
}
.ai-accelerator-activity-2 {
    padding-top: 64px;
    padding-bottom: 64px;
    background: no-repeat center / cover;
    h2 {
        font-family: PingFangSC-Semibold;
        font-size: 32px;
        color: #091221;
        text-align: center;
        line-height: 48px;
        font-weight: 600;
        width: max-content;
        padding-right: 8px;
        box-sizing: content-box;
        margin: 0 auto;
        background: url('https://bce.bdstatic.com/p3m/common-service/uploads/line-blue_adadb45.png') no-repeat right top / auto 48px;
    }
    .tip-text {
        position: relative;
        margin:  12px auto 32px;
        font-family: PingFangSC-Medium;
        font-size: 12px;
        color: #091221;
        text-align: center;
        line-height: 20px;
        font-weight: 500;
        width: max-content;
        &::before, &::after {
            content: '';
            position: absolute;
            width: min(30px, 40PX);
            height: 20px;
            top: 0;
            background: url(https://bce.bdstatic.com/p3m/common-service/uploads/icon-line-left_955929d.png) no-repeat center / 100% auto;
        }
        &::before {
            left: max(-38px, -48PX);
        }
        &::after {
            right: max(-38px, -48PX);
            transform: rotate(180deg);
        }
    }
}
.gift-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 32px;
    width: 100%;
    background: no-repeat right top / cover;
    border-radius: 16px;
    position: relative;
    .gift-info {
        .gift-img {
            position: absolute;
            left: 32px;
            top: 20px;
            width: 132px;
            height: 60px;
            border-radius: 8px;
            margin-right: 24px;
            background-color: #fff;
            img {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                width: 62px;
                height: 60px;
            }
        }
        h3 {
            font-family: PingFangSC-Medium;
            font-size: 18px;
            color: #FFFFFF;
            line-height: 24px;
            font-weight: 500;
            padding-left: 156px;
        }
        p {
            margin-top: 8px;
            opacity: 0.8;
            font-family: PingFangSC-Regular;
            font-size: min(12px, 16PX);
            color: #FFFFFF;
            text-align: left;
            line-height: 20px;
            font-weight: 400;
            padding-left: 156px;
        }
    }
    .gift-btn {
        display: block;
        background: #FFFFFF;
        border-radius: 8px;
        width: 120px;
        height: 36px;
        line-height: 36px;
        font-family: PingFangSC-Medium;
        font-size: min(12px, 16PX);
        text-align: center;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.3s ease-out;
        user-select: none;
        span {
            font-family: PingFangSC-Medium;
            color: #FFFFFF;
            background: linear-gradient(to right, #2468F1, #26A8FF);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        &:hover {
            background-color: #EBF6FE;
        }
        &.get-success {
            background-color: #98D5FE;
            opacity: 1 !important;
            cursor: default;
        }
    }
}

.product-list {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 14px;
    margin-top: 14px;
    li {
        width: calc(33.33% - 9.333px);
        background: rgba(255,255,255,0.70);
        border: 1px solid #FFFFFF;
        border-radius: 16px;
        padding: 24px;
        transition: background-color 0.3s ease-out;
        &:hover {
            background-color: #fff;
        }
        .toggle-btn {
            display: none;
        }
        h3 {
            font-family: PingFangSC-Medium;
            font-size: 22px;
            color: #091221;
            text-align: left;
            line-height: 30px;
            font-weight: 500;
        }
        p {
            margin-top: 8px;
            opacity: 0.7;
            font-family: PingFangSC-Regular;
            font-size: min(12px, 16PX);
            color: #091221;
            line-height: 20px;
            font-weight: 400;
            .ellipsis();
        }
        .btn-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 24px;
            .btn {
                width: calc(50% - 7px);
                height: 36px;
                border-radius: 8px;
                text-align: center;
                font-family: PingFangSC-Medium;
                font-size: min(12px, 16PX);
                text-align: center;
                font-weight: 500;
                transition: opacity 0.3s ease-out;
                cursor: pointer;
                &:hover {
                    opacity: 0.75;
                }
                &:first-child {
                    color: #FFFFFF;
                    line-height: 36px;
                    background-image: linear-gradient(90deg, #2468F1 0%, #26A8FF 100%);
                }
                &:last-child {
                    border: 1px solid #091221;
                    color: #091221;
                    line-height: 34px;
                }
            }
        }
    }
}


@media screen and (min-width: 992px) and (max-width: 1199px) {
    .container {
        width: calc(100vw - 110px);
    }
    .gift-wrapper {
        padding: 24px 20px;
        .gift-info {
            .gift-img {
                left: 20px;
            }
            h3, p {
                padding-left: 148px;
            }
        }
    }
    .product-list {
        li {
            padding: 20px;
        }
    }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
    .container {
        width: calc(100vw - 64px);
    }
    .gift-wrapper {
        padding: 20px;
        height: 116px;
        .gift-info {
            .gift-img {
                width: 112px;
                height: 76px;
                margin-right: 16px;
                left: 20px;
                top: 20px;
            }
            h3 {
                width: 476px;
                padding-left: 128px;
            }
            p {
                padding-left: 128px;
            }
        }
    }
    .product-list {
        li {
            padding: 20px;
            width: calc(50% - 7px);
        }
    }
}

@media screen and (max-width: 767px) {
    .ai-accelerator-activity-2 {
        .tip-text {
            font-size: 13px;
            margin-bottom: 20px;
        }
    }
}

@media screen and (min-width: 600px) and (max-width: 767px) {
    .container {
        width: calc(100vw - 64px);
    }
    .ai-accelerator-activity-2 {
        padding-bottom: 40px;
        padding-top: 40px;
        h2 {
            line-height: 40px;
            background: none;
            font-size: 28px;
            padding-right: 0;
        }
    }
    .gift-wrapper {
        padding: 20px;
        padding-left: 160px;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        height: 164px;
        .gift-info {
            transform: none;
            width: 100%;
            .gift-img {
                position: absolute;
                left: 20px;
                top: 20px;
                width: 124px;
                height: 124px;
                img {
                    width: 91px;
                    height: 88px;
                }
            }
            h3 {
                width: 356px;
                padding-left: 0;
            }
            p {
                margin-top: 4px;
                padding-left: 0;
                font-size: 13px;
                width: 100%;
                .ellipsis();
            }
        }
        
        .gift-btn {
            margin-top: 16px;
            font-size: 13px;
        }
    }
    .product-list {
        margin-top: 12px;
        gap: 12px;
        align-items: stretch;
        li {
            width: calc(50% - 6px);
            padding: 20px;
            p {
                font-size: 13px;
            }
            .btn-wrapper {
                .btn {
                    font-size: 13px;
                    width: calc(50% - 5px);
                }
            }
        }
        .item-inner {
            display: flex;
            flex-direction: column;
            height: 100%;
            h3 {
                height: 100%;
                flex-shrink: 1;
            }
            p {
                flex-shrink: 0;
            }
        }
    }
}

@media screen and (max-width: 599px) {
    .container {
        width: calc(100vw - 48px);
    }
    .ai-accelerator-activity-2 {
        padding-bottom: 40px;
        padding-top: 40px;
        background-image: url(https://bce.bdstatic.com/p3m/common-service/uploads/part2-bg-h5_2757ece.png) !important;
        h2 {
            width: 252px;
            line-height: 40px;
            background: none;
            font-size: 28px;
            padding-right: 0;
        }
    }
    .gift-wrapper {
        margin-top: 20px;
        padding: 20px;
        position: relative;
        display: block;
        background-image: url(https://bce.bdstatic.com/p3m/common-service/uploads/part1-bg-prize-h5_a467c94.png) !important;
        .gift-info {
            .gift-title {
                display: flex;
                align-items: center;
            }
            .gift-img {
                position: relative;
                left: 0;
                top: 0;
                width: 40px;
                height: 40px;
                margin-right: 8px;
                flex-shrink: 0;
                img {
                    width: 35px;
                    height: 34px;
                }
            }
            h3 {
                font-size: 16px;
                padding-left: 0;
            }
            p {
                padding-left: 0;
                font-size: 13px;
                margin-top: 4px;
                .ellipsis();
            }
        }
        .gift-btn {
            width: 100%;
            margin-top: 16px;
            font-size: 13px;
        }
    }
    .product-list {
        margin-top: 0;
        gap: 0;
        li {
            margin-top: 12px;
            padding: 20px;
            width: 100%;
            position: relative;
            .btn-wrapper {
                height: 0;
                overflow: hidden;
                margin-top: 0;
                transition: all .3s ease-out;
                align-items: flex-start;
                a {
                    width: calc(50% - 5px);
                    font-size: 13px;
                }
            }
            p {
                font-size: 13px;
            }
            .toggle-btn {
                position: absolute;
                display: block;
                width: 16px;
                height: 16px;
                right: 20px;
                top: 27px;
                cursor: pointer;
                background: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon-ford-h5_275b7fd.png') no-repeat center / cover;
                transition: transform .3s ease-out;
            }
            &.expand {
                .btn-wrapper {
                    height: 36px;
                    margin-top: 20px;
                }
                .toggle-btn {
                    transform: rotate(180deg);
                }
            }
            .btn-wrapper {
                .btn {
                    font-size: 13px;
                }
            }
        }
    }
}