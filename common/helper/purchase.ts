import sha256 from 'crypto-js/sha256';
import Base64 from 'crypto-js/enc-base64';
import {utc} from 'moment';
import {GetPriceParams, GroupPurchaseProductObj} from '@common/interface/purchase';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {getCookie} from '@baidu/bce-helper';
import {message} from 'antd';
import {PRODUCT_PRICE_SHA_KEY} from '@common/constant/variableConst';

/**
 * 解析核数和内存, c24m48 -> {core: 24, memory: 48}
 */
export function parseCoreMemory(raw: string) {
    const reg = /c(\d+)m(\d+)/;
    const execResult = reg.exec(raw);
    return {
        core: parseInt(execResult[1], 10),
        memory: parseInt(execResult[2], 10),
    };
}

/**
 * 从 product object 中解析出 spec 和 core memory 字符串
 */
export function getSpecFromProduct(product: GroupPurchaseProductObj) {
    const specStr = product.flavor.find(i => i.name === 'spec').value as string;
    // specStr 格式均形如 bcc.g4.c2m8
    const [, spec, coreMemory] = specStr.split('.');
    return [spec, coreMemory];
}

/**
 * 生成活动价 sign
 */
export function genProductPriceSign(
    product: GroupPurchaseProductObj,
    time: {duration: string, timeUnit: string},
    flavor?: string
) {
    const sign = `${product.serviceType}${product.region}${time.duration}${time.timeUnit}${flavor || ''}`;
    const message = Base64.stringify(sha256(sign + PRODUCT_PRICE_SHA_KEY));
    return message;
}

/**
 * 将 SKU Object 的 flavor 和 flavorDisplayName 组合成询价参数中的 flavorItems 字段
 */
function assembleFlavor(flavor: GroupPurchaseProductObj['flavor'], flavorDisplayName: GroupPurchaseProductObj['flavorDisplayName']) {
    if (!flavorDisplayName || !flavor) {
        return flavor;
    }
    const resFlavor = [];
    for (const item of flavor) {
        // 剔除 subServiceType = default
        if (item.name === 'subServiceType' && item.value === 'default') {
            continue;
        }
        // 拼装单位
        const flavorDisplayNameTarget = flavorDisplayName.find(i => i.flavor === item.name);
        if (flavorDisplayNameTarget?.unitPrefix) {
            item.value = `${item.value}${flavorDisplayNameTarget.unitPrefix.toLowerCase()}`;
        }
        resFlavor.push(item);
    }
    return resFlavor;
}

/**
 * 生成询价参数
 */
export function getPriceParams(product: GroupPurchaseProductObj, time: string): GetPriceParams {
    const flavorItems = assembleFlavor(product.flavor, product.flavorDisplayName);
    const [duration, timeUnit] = time.split('-');
    const params = {
        accountId: '',
        agentAccountId: '',
        serviceType: product.serviceType,
        subServiceType: product.subServiceType,
        chargeItemName: product.chargeItem,
        region: product.region,
        scene: 'NEW',
        flavor: {flavorItems},
        time: {
            startTime: utc(new Date()).format(),
            period: `P${duration}${timeUnit.toUpperCase()[0]}`,
        },
        count: 1,
        signAuth: genProductPriceSign(product, {duration, timeUnit}, JSON.stringify(flavorItems)),
        channel: 'MOBILE',
    };

    const hasTime = Array.isArray(product.dimension)
        ? product.dimension.includes('time')
        : product.packageTime;

    if (hasTime) {
        return params;
    } else {
        const {time, ...paramsWithoutTime} = params;
        return paramsWithoutTime;
    }
}

interface FieldFilter {
    name: string;
    isFlavor: boolean;
    isScale?: boolean;
    value: any;
}

/**
 * 根据指定字段做数据筛选
 */
export function getProductByFields(products: GroupPurchaseProductObj[], fields: FieldFilter[]) {
    try {
        for (const product of products) {
            if (fields.every(field => {
                if (field.isFlavor) {
                    const flavor = product.flavor.find(i => i.name === field.name);
                    return flavor[field.isScale ? 'scale' : 'value'] === field.value;
                } else {
                    return (product as any)[field.name] === field.value;
                }
            })) {
                return product;
            }
        }
    } catch (e) {
        console.error('@function:getProductByFields', e);
    }
    return null;
}

/**
 * 生成实例配置名称, genFullSpecName(['c4', 'c28g'], map) => '计算型c4，2核8G'
 */
export function genFullSpecName([spec, coreMemory]: [string, string], specNameMap: Map<string, string>) {
    const specName = specNameMap.get(spec);
    const {core, memory} = parseCoreMemory(coreMemory);
    return `${specName}，${core}核${memory}G`;
}

// 拿物理区
export function getLogicalZone(region: string, spec: string) {
    return new Promise((resolve, reject) => {
        netService.post<any, any, null>(
            urlConst.GET_INSTANCE_FLAVORSPEC,
            {},
            null,
            {
                headers: {
                    'csrftoken': getCookie('bce-user-info') || '',
                    'X-Region': region || 'bj',
                    'X-Request-By': 'RestClient',
                },
            }
        ).then(res => {
            if (res.message) {
                reject(res.message.global);
                message.warning(`BCC: ${res.message.global || '获取区域出错'}`);
            }
            const target = res.result.length ? res.result.find((item: {bccResources: any}) => (
                item.bccResources?.flavorGroups?.find((flavorGroup: {flavors: any[]}) => (
                    flavorGroup.flavors?.find(flavor => (
                        flavor.gpuCapcacity > 0 && flavor.productType === 'prepay' && flavor.spec === spec
                    ))
                ))
            )) : null;
            resolve(target?.logicalZone ?? '');
        });
    });
}
