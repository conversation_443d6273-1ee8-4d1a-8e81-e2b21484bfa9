import {Ioc} from '@baidu/bce-decorators';
import {GetServerSidePropsContext} from 'next';
import Cookies from 'js-cookie';
import {isArray, jsonp, replaceOfString} from '@baidu/bce-helper';
import {wxSdkLoad} from '@baidu/bce-wx';
import {netService, ResponseObj} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {
    LOGIN_URL, REGISTER_URL, LOGOUT_URL, PARIS_SID, HEADER_NAV_HEIGHT_MAP,
    BOS_ORIGIN_URL, BOS_CDN_ORIGIN_URL, H5_FINANCE_PAY_URL, cmsSurveyZhEnMap,
} from '@common/constant/variableConst';
import {CmsPageDataObj, PartialOneProperty} from '@common/interface/common';
import {Abtest, AbtestConsructorParams} from '@baidu/bce-abtest';
import sha256 from 'crypto-js/sha256';
import Base64 from 'crypto-js/enc-base64';
import {ProductCouponObj, RecommendConfigDataObj} from '@common/interface/page';
import {CmsSurveyApplyObj} from '@common/interface/cmsSurvey';
import {UEnvService} from '../services/env';
import {idVerify} from './idVerify';

/**
 * @file 页面公用方法
 */

export const getScrollTop = () => (
    document.documentElement.scrollTop || document.body.scrollTop
);

export const setScrollTop = (top: number) => {
    document.documentElement.scrollTop = top;
    document.body.scrollTop = top;
};

export function getElementTop(element: HTMLElement) {
    return getScrollTop() + element.getBoundingClientRect().top;
}

export function getLoginUrl() {
    const url = Ioc(UEnvService).isProdOnline ? LOGIN_URL.online : LOGIN_URL.sandbox;
    return `${url}?redirect=${encodeURIComponent(location.href.replace(location.hash, '')) + location.hash}`;
}

export function getLogoutUrl() {
    const url = Ioc(UEnvService).isProdOnline ? LOGOUT_URL.online : LOGOUT_URL.sandbox;
    return `${url}?redirect=${encodeURIComponent(location.href.replace(location.hash, '')) + location.hash}`;
}

export function getRegisterUrl() {
    return `${REGISTER_URL}?reg&u=${encodeURIComponent(location.href)}`;
}

// 在ServerSideProps中判断是否是移动端
export function isMobileInServerSideProps(context: GetServerSidePropsContext) {
    const userAgent = context.req.headers['user-agent']?.toLowerCase();
    const patPhone = /ipad|iphone os|midp|rv:*******|ucweb|android|windows ce|windows mobile/;
    return patPhone.test(userAgent);
}

// 是否是百度云app
export function isNativeApp(context: GetServerSidePropsContext) {
    const ua = context.req.headers['user-agent']?.toLowerCase();
    return ua.includes('bai du yun');
}

// 判断是否已登录
export function hasLogin() {
    const apiHost = location.host.includes('cloud.baidu.com')
        ? 'bce.baidu.com'
        : 'bcetest.baidu.com';
    const url = `//${apiHost}/api/account/v2/displayName`;

    return new Promise((resolve, reject) => {
        jsonp<ResponseObj<{hasLogin: boolean, cookies: {[key: string]: string}}>>(url, {timeout: 3000})
            .then(res => {
                if (!res.success) {
                    reject(res);
                }

                const result = res && res.result;
                const isLogin = result.hasLogin === true;
                if (!isLogin) {
                    reject(res);
                }

                const cookies = result.cookies;

                // 处理displayName接口
                // 1. 删除 location.hostname下的bce-sessionid cookie值（bce-sessionId下线）；
                // 2. 判断 cookies['bce-sessionid'] 存在且不为空的情况下才去设置；(方便displayName回退处理)
                const hostname = location.hostname;
                Cookies.remove('bce-sessionid', {path: '/', domain: hostname});
                for (const element in cookies) {
                    if (element === 'bce-sessionid') {
                        const sessionId = cookies['bce-sessionid'];
                        sessionId && Cookies.set('bce-sessionid', sessionId, {path: '/', domain: `.${hostname}`});
                    } else {
                        const cookieValue = cookies[element] || '';
                        Cookies.set(element, cookieValue, {path: '/', domain: `.${hostname}`});
                    }
                }

                resolve(res);
            })
            .catch(err => reject(err));
    });
}

export function othersTransEnCn(item: string) {
    return item === 'others' ? '其它' : (
        item === '其它' ? 'others' : item
    );
}

export const wxShare = (config: {title?: string, desc: string, img?: string}, appId = 'wx2e48c7f781c8f283') => {
    netService.post<
    {appId: string, url: string},
    {appId: string, timestamp: string, nonceStr: string, signature: string}>(
        urlConst.GET_WX_SHARE,
        {
            appId: appId,
            url: encodeURIComponent(window.location.href.split('#')[0]),
        }
    ).then(wxRes => {
        wxSdkLoad({
            appId: wxRes.result.appId,
            timestamp: wxRes.result.timestamp,
            nonceStr: wxRes.result.nonceStr,
            signature: wxRes.result.signature,
            shareInfo: {
                title: config.title || document.title,
                desc: config.desc,
                imgUrl: config.img || 'https://bce.bdstatic.com/p3m/common-service/uploads/cloud_preview_5050c8c.png',
                link: location.href,
                success: () => {
                    console.info('微信sdk加载成功了');
                },
            },
        });
    }).catch(err => {
        console.info(err);
    });
};

export function importUEditorMdStyle() {
    const link = document.createElement('link');
    link.href = 'https://bce.bdstatic.com/developer-static/dep/editor.md/css/editormd.css';
    link.rel = 'stylesheet';
    document.head.appendChild(link);
}

export function isIE() {
    if (Ioc(UEnvService).isClient) {
        return !!(window as any).ActiveXObject || 'ActiveXObject' in window;
    } else {
        return false;
    }
}

export function isSafari() {
    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
}

// 获取反爬sdk签名
export const getAntiParam = async () => {
    const parisInstance = (window as any)[`paris_${PARIS_SID}`];
    let acsToken = 600;
    if (!parisInstance) {
        return acsToken;
    }
    // 必须保证在ParisFactory.create同时传入acsUrl与abcliteUrl
    await parisInstance.getAcsTokenWithAbcliteActiveReport({
        subid: '',
    }, (acsTokenOrErrorCode: number) => {
        acsToken = acsTokenOrErrorCode;
    });
    return acsToken;
};

export function getHeaderNavHeight(winWidth: number) {
    if (winWidth <= 700) {
        return HEADER_NAV_HEIGHT_MAP.wap;
    } else if (winWidth <= 1220) {
        return HEADER_NAV_HEIGHT_MAP.pcWrap;
    } else {
        return HEADER_NAV_HEIGHT_MAP.pc;
    }
}

// js发送monitor埋点信息
export const sendMonitor = (params: {
    action?: string;
    category?: string;
    name?: string;
    value?: string;
}) => {
    if (window.require) {
        // 使用 window.require 避免 webpack 将其作为 cjs 来解析
        (window.require as any)(['monitor'], (Monitor: any) => {
            Monitor.default.trackBehavior(params);
        });
    }
    else if ((window as any).Monitor) {
        (window as any).Monitor.default.trackBehavior(params);
    } else {
        // 给个定时器，因为首屏加载出来的时候，fe-monitor还没加载
        let timer = setInterval(() => {
            if ((window as any).Monitor) {
                (window as any).Monitor.default.trackBehavior(params);
                clearInterval(timer);
                timer = null;
            }
        }, 500);
    }
};

// Abtest的promise用法
export function abtestPromise(params: PartialOneProperty<AbtestConsructorParams, 'callback'>): Promise<number> {
    return new Promise((resolve, reject) => {
        // eslint-disable-next-line no-new
        new Abtest({
            ...params,
            callback: (val, info) => {
                params.callback && params.callback(val, info);
                resolve(val);
            },
            errHandler: errMsg => {
                reject(errMsg);
            },
        });
    });
}

export function escape2Html(str: string) {
    const arrEntities = {'lt': '<', 'gt': '>', 'nbsp': ' ', 'amp': '&', 'quot': '"'};
    return str.replace(/&(lt|gt|nbsp|amp|quot);/ig, (all, t) => {
        return arrEntities[t as keyof typeof arrEntities];
    });
}

export function replaceCDNByBos(cdnStr: string) {
    return cdnStr.replace(BOS_CDN_ORIGIN_URL, BOS_ORIGIN_URL);
}

export function loadCyberplayerJs() {
    const script = document.createElement('script');
    script.src = 'https://bce.bdstatic.com/jwplayer/3.5.2/cyberplayer.js';
    script.async = true;
    document.body.appendChild(script);
}

// 组合购询价生成签名
export const genGroupBuySignAuth = (config: any) => {
    config.count = config.count || 1; // count默认1
    const msg = ['serviceType', 'configId', 'configGroupId', 'region', 'duration', 'timeUnit', 'count', 'specs'].reduce((res, key) => {
        if (key === 'specs') {
            return config[key]?.reduce(
                (temp: string, item: {name: string, value: string, scale: string}) => (
                    `${temp}${item.name || ''}${item.value || ''}${item.scale || ''}`
                ),
                res
            ) ?? res;
        } else {
            return `${res}${config[key] || ''}`;
        }
    }, '');
    const secretKey = Ioc(UEnvService).isProdOnline ? '07d92e84-7d59-46cf-84bf-e339b6d32e09' : '8b661235-ac07-478c-9b7d-a8ab463f8c89';
    return Base64.stringify(sha256(msg + secretKey));
};

export function toCamelCaseObj(obj: { [props: string]: any}) {
    // eslint-disable-next-line no-negated-condition
    if (Object.prototype.toString.call(obj) !== '[object Object]') {
        return obj;
    }
    else {
        const keys = Object.keys(obj);
        const result: { [props: string]: any} = {};
        keys.forEach(key => {
            const camelKey = key.replace(/([_][a-z])/ig, $1 => $1.toUpperCase().replace('_', ''));
            result[camelKey] = obj[key];
        });
        return result;
    }
}

// 活动领取状态码和弹窗信息映射
export const genCouponApplyCodeMap: () => ({[props: number]: {title?: string, content: string, okText: string, url: string}}) = () => {
    return {
        18628: {content: '您不是百度智能云新用户，请领取其他代金券', okText: '确定', url: ''},
        18656: {content: '您已领取过优惠券', okText: '确定', url: ''},
        230: {content: '您还未完成实名，请完成实名认证后再参与活动', okText: '去实名', url: 'https://console.bce.baidu.com/iam/#/iam/user/overview'},
        209: {content: '您还未完成实名，请完成实名认证后再参与活动', okText: '去实名', url: 'https://console.bce.baidu.com/iam/#/iam/user/overview'},
        210: {content: '您还未完成实名，请完成实名认证后再参与活动', okText: '去实名', url: 'https://console.bce.baidu.com/iam/#/iam/user/overview'},
        211: {content: '您还未完成实名，请完成实名认证后再参与活动', okText: '去实名', url: 'https://console.bce.baidu.com/iam/#/iam/user/overview'},
        203: {content: '您还未完成实名，请完成实名认证后再参与活动', okText: '去实名', url: 'https://console.bce.baidu.com/iam/#/iam/user/overview'},
        226: {content: '您不是企业用户，请完成企业实名后再参与活动', okText: '去企业实名', url: 'https://console.bce.baidu.com/iam/#/iam/user/overview'},
        227: {content: '您不是个人用户，请完成个人实名后再参与活动', okText: '去个人实名', url: 'https://console.bce.baidu.com/iam/#/iam/user/overview'},
        201: {
            content: '您还未完成实名和激活，请激活并实名后再参与活动', okText: '去激活',
            url: `https://console.bce.baidu.com/iam/#/iam/user/v2/activate~hideBar=1&redirect=${encodeURIComponent(window.location.href)}`,
        },
    };
};
export const getCouponTypeAndDesc: (item: ProductCouponObj) => {
    type: 'common' | 'discount' | 'discount_limit' | 'single_product_order_cash_full' | 'single_product_cash_full' | 'other';
    name: string;
    isApportionmentAmount: boolean;
} = item => {
    // 有限制折扣券和无限制折扣券
    if (item.couponType === 'DISCOUNT_COUPON') {
        return {
            type: item?.conditionArgsMap?.productTotalPrice ? 'discount_limit' : 'discount',
            name: '预付费单订单通用券',
            isApportionmentAmount: false,
        };
    }
    if (item.couponType === 'CASH_COUPON' && item.conditionArgsMap?.productTotalPrice) {
        return {
            type: 'single_product_order_cash_full',
            name: '单产品单订单满减券',
            isApportionmentAmount: true,
        };
    }
    if (item.couponType === 'CASH_COUPON' && item?.conditionArgsMap?.price) {
        return {
            type: 'single_product_cash_full',
            name: '单订单满减券',
            isApportionmentAmount: true,
        };
    }
    if (item.couponType === 'CASH_COUPON' && item.conditionArgsMap === null) {
        return {
            type: 'common',
            name: '通用券',
            isApportionmentAmount: true,
        };
    }

    return {
        type: 'other',
        name: item.conditionEffectDescription,
        isApportionmentAmount: false,
    };
};

/**
 *
 * @param orderId 订单ID
 * @param succUrl 支付成功的跳转url
 * @param failUrl 支付失败的跳转url
 * @returns 移动端支付地址
 */
export function getH5FinancePayUrl(orderId: string, succUrl: string, failUrl: string) {
    const url = Ioc(UEnvService).isProdOnline ? H5_FINANCE_PAY_URL.online : H5_FINANCE_PAY_URL.sandbox;
    return `${url}?orderId=${orderId}&succUrl=${succUrl}&failUrl=${failUrl}`;
}

/**
 * 新窗口跳转
 */
export const openUrl = (url: string, target: '_self' | '_blank' = '_blank') => {
    const a = document.createElement('a');
    a.setAttribute('href', url);
    a.setAttribute('target', target);
    a.setAttribute('style', 'display: none;');
    document.body.appendChild(a);
    a.click();
    a.parentNode.removeChild(a);
};

let scrollTop: number = null;
export const isCanScrollBody = (isScroll: boolean = true) => {
    if (isScroll) {
        // 清除body类型
        document.body.classList.remove('header-body-no-scroll');
        document.body.style.top = null;
        document.body.style.overflowY = null;
        if (scrollTop) {
            window.scrollTo({top: scrollTop});
        }
        // 重置scrollTop
        scrollTop = null;
    } else {
        // body 不可移动
        // 判断是否要重新计算scrollTop
        if (!scrollTop) {
            scrollTop = window.scrollY;
        }
        document.body.classList.add('header-body-no-scroll');
        document.body.style.top = `-${scrollTop || 0}px`;
        // 判断body本身是否可以滚动，如果可以滚动加上scroll，保证有滑动条
        if (document.body.scrollHeight > document.body.clientHeight) {
            document.body.style.overflowY = 'scroll';
        }
    }
};
/**
 * 由 750 宽设计稿计算实际尺寸
 */
export function getEnvSize(size: number) {
    return window.innerWidth * (size / 750);
}

/**
 * 用户侧重定向到404
 */

export const redirectTo404 = () => {
    const portalPageUrlStr = window.localStorage.getItem('portalPageUrl');
    const from = (portalPageUrlStr && JSON.parse(portalPageUrlStr) || {prev: ''}).prev;
    const target = location.href.replace(location.origin, '');
    const enLocale = location.pathname.includes('/en/') || location.search.includes('locale=en');
    const notFoundPagePath = `${enLocale ? '/en/404.html' : '/404.html'}`;
    location.replace(`${notFoundPagePath}?from=${encodeURIComponent(from)}&target=${encodeURIComponent(target)}`);
};

// 中文映射英文
export const handleZh = (key: string, isIntl: boolean = false) => (
    isIntl ? (cmsSurveyZhEnMap[key] || key) : key
);

// 表单校验
export const getSurveyValidationObj = (isIntl: boolean = false) => ({
    id: {
        validator: idVerify,
        message: handleZh('身份证信息不正确', isIntl),
    },
    mobile: {
        pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/,
        message: handleZh('请正确填写手机号码', isIntl),
    },
    email: {
        pattern: /^([A-Za-z0-9_\-.])+@([A-Za-z0-9_\-.])+\.([A-Za-z]{2,4})$/,
        message: handleZh('请正确填写电子邮箱', isIntl),
    },
    number: {
        pattern: /^[0-9]+\.{0,1}[0-9]{0,2}$/,
        message: handleZh('输入有误，请输入数字', isIntl),
    },
    url: {
        pattern: /[-a-zA-Z0-9@:%._\\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)/,
        message: handleZh('输入的URL有误，请输入正确的URL地址', isIntl),
    },
    letter: {
        pattern: /^[A-Za-z]+$/,
        message: handleZh('输入有误，请输入字母', isIntl),
    },
});

// 生成cms表单数据
export const genCmsSurveySubmitData = ({formData, pageData}: {
    formData: {[props: string]: string | string[]};
    pageData: CmsPageDataObj;
}) => {
    // todo: 接入crm的参数需要拼接leads-
    const questions = Object.entries(formData).map(([title, value]) => {
        return {
            title,
            answers: isArray(value) ? (value as string[]).map(answer => ({answer})) : [{answer: value as string}],
        };
    });
    const submitData: CmsSurveyApplyObj = {
        questions,
        campaignId: pageData.serviceType,
    };
    if (pageData.needLogin) {
        submitData.pageId = pageData.pageId;
    }
    if (pageData.path.includes('survey_summit')) {
        submitData.pageId = pageData.pageId;
        submitData.needLogin = !!pageData.needLogin;
    }

    return submitData;
};

// 查找父级元素
export function findParentNode(ele: HTMLElement, className: string) {
    const parentNode = ele.parentNode as HTMLElement;
    if (!parentNode) {
        return null;
    }
    if (parentNode.classList.contains(className)) {
        return parentNode;
    }
    if (parentNode.tagName === 'BODY') {
        return null;
    }
    return findParentNode(parentNode, className);
}

// 根据用户产品历史浏览记录LRU，更新数据排序
export const refactorDataByRecommend = (data: RecommendConfigDataObj[], len: number) => {
    const viewHistory: string[] = JSON.parse(localStorage.getItem('PORTAL_LATEST_PRODUCT') || '[]');
    const dataList: RecommendConfigDataObj[] = [];
    const leftDataList: RecommendConfigDataObj[] = [];
    data?.forEach(item => {
        // 固定位 | 动态的且支持的动态数组在曾经访问过的历史列表里面(时间复杂度似乎有点高 两个数组有没有相同的字段 => 改用set，用空间换时间)
        if (dataList.length < len) { // 达到个数就不加了
            if (item.recommendType === 'dynamic') {
                const historySet: Set<string> = new Set(viewHistory); // string[]
                const validSet = new Set(item.recommendKey?.split(',').map(item => item.toUpperCase()) || []); // string[]
                const mergeSet = new Set([...Array.from(historySet), ...Array.from(validSet)]);
                if (mergeSet.size < historySet.size + validSet.size) {
                    dataList.push(item);
                } else {
                    leftDataList.push(item);
                }
            } else {
                dataList.push(item);
            }
        }
    });
    if (dataList.length < len) {
        dataList.push(...leftDataList.slice(0, len - dataList.length));
    }
    return dataList;
};

// 需要推送的host列表
const needPushHostList = ['cloud.baidu.com', 'cloudtest.baidu.com'];
export const pushProduct2Crm = () => {
    const urlParams = new URLSearchParams(location.search);
    const track = urlParams.get('track');
    const isDocPage = location.pathname.startsWith('/doc/');
    if (
        needPushHostList.includes(location.host) && (
            location.pathname.startsWith('/product/')
            || location.pathname.startsWith('/campaign/')
            || location.pathname === '/'
            || isDocPage
            || location.pathname.startsWith('/cloudlive/')
        )
    ) {
        netService.post(urlConst.POST_PUSH_LEADS_TO_CRM, {
            sourcePath: isDocPage ? location.pathname.split('/').slice(0, 3).join('/') : location.pathname,
        }, null, {
            headers: {
                track,
            },
        });
    }
};

// 企服以及商标域名推送crm需要做特殊处理
const qifuAndTmsDomain = [
    'qifu.baidu.com',
    'qifu-sandbox.baidu-int.com',
    'tms.baidu.com',
    'tms.baidu-int.com',
];
export const pushQifuAndTmsCrm = () => {
    if (qifuAndTmsDomain.includes(location.host)) {
        netService.post('/api/portal/leads', {
            sourcePath: location.origin + location.pathname,
        });
    }
};
// 获取ai加速营报名状态
export const getAiTrainingCampStatus = (surveyCampaignId: string) => {
    return netService.get<{campaignId: string}, boolean>(
        urlConst.GET_AI_ACCELERATE_JOIN_STATUS,
        {campaignId: surveyCampaignId}
    );
};

// aidemo迁移，匹配错误提示
export function getAidemoErrMsg(code: number, codeMap: {[props: string]: Array<number | string>}, errMsgMap: {[props: string]: string}) {
    const keys = Object.keys(codeMap);
    const resKey = keys.find(codeKey => codeMap[codeKey].includes(code)) || 'serverErr';
    return errMsgMap[resKey];
}
// 复制文本至剪切板
export const copyText = ({text, successCb, errorCb}: {text: string, successCb: () => void, errorCb?: () => void}) => {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text);
        successCb && successCb();
    } else {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        // 使text area不在viewport，同时设置不可见
        textArea.style.position = 'absolute';
        textArea.style.opacity = '0';
        textArea.style.width = '0px';
        textArea.style.height = '0px';
        textArea.style.overflow = 'hidden';
        document.body.appendChild(textArea);
        textArea.select();
        if (document.execCommand('copy')) {
            successCb && successCb();
        } else {
            errorCb && errorCb();
        }
        document.body.removeChild(textArea);
    }
};

// 获取表单的提交状态，判断表单是否提交过
export const getFormSubmitStatus = (campaignId: string) => {
    return netService.get<null, {submited: boolean}>(
        replaceOfString(urlConst.GET_SURVEY_SUBMIT_STATUS, {':campaignId': campaignId})
    );
};

// ai加速器活动2024-活动1-获取学习进度
export const getAiAccelerateCamp1Progress = (campaignId: string) => {
    return netService.get<null, {completeLearningTask: boolean}>(
        replaceOfString(urlConst.GET_AI_ACCELERATE_2024_CAMPAIGN1_PROGRESS, {':campaignId': campaignId})
    );
};


export function getHeightFromEleToBody(elementOrigin: HTMLElement): number {
    let offsetTop = 0;
    let element: any = elementOrigin;
    // let element: any = containerDom.current;
    while (element) {
        offsetTop += element.offsetTop;
        element = element.offsetParent;
    }
    return offsetTop;
}

// 计算实际像素
export function pxToRealPx(px: number) {
    const rem = px / 10;
    let rootFontSize = 12;
    if (typeof window !== 'undefined') {
        rootFontSize = parseFloat(
            getComputedStyle(document.documentElement).fontSize
        );
    }

    return rem * rootFontSize;
}
