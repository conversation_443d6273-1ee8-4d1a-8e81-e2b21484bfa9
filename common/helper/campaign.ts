import {groupBy, mapValues, omit} from 'lodash';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
// import {getApiNameAndIdByValueDisplayName} from '@common/helper/groupPurchase';
import {EnumProductServiceType} from '@common/interface/common';
import {GroupPurchaseProductObj} from '@common/interface/groupPurchase';
import {CampaignInfoItemObj} from '@common/interface/page';
import {getAntiParam} from './page';

export type FilterOption = {label: string, value: string | number, extra?: Record<string, string>};
export type Filters = Array<{
    name: string;
    key: string;
    options: FilterOption[];
    type?: 'radio' | 'checkbox';
}>;
export type FilterValues = Record<string, string | string[]>;

// 活动优惠
export interface CampaignCardItemObj {
    simpleFlavor: string;
    bandwidth: string;
    duration: string;
    timeUnit: string;
    region: string;
    flavorDisplayName: string;
    bccSpec: string;
    campaignInfo: CampaignInfoItemObj;
    aiSpecValue?: string; // 存储规格信息（备选）
    aiSpecName?: string; // 存储规格信息展示（仅前端展示使用）
}

export interface CampaignCardBoxObj {
    simpleSpec: string;
    newUserLimit: string;
    realNameLimit: string;
    flavorDisplayName: string;
    specInfoList: CampaignCardItemObj[];
    subServiceType?: string;
}

export interface CampaignCardDetailParamsObj {
    bccSimpleSpec?: string; // bcc传参
    newUserLimit?: string;
    realNameLimit?: string;
    region?: string; // 可选，从过滤项传进来
    serviceType?: string; // bcc，ocr
    subServiceType?: string; // ai传参
}

export interface ActivityListPayload {
    serviceType: string;
    params: string;
    pageNo: number;
    orderBy: 'asc' | 'desc';
    newUserLimit: string;
}


// fe-monitor 上报数据格式
export interface MonitorTrackConfig {
    category?: string;
    name?: string;
    value?: string;
}

const {
    BCC, FACE, OCR, WEBIMAGE_OCR, LOCATION_OCR, BCN_OCR, ID_OCR,
    GENERAL_OCR, IMAGE_RECOGNITION, SPEECH, ICR, INTELLIGENTWRITING,
} = EnumProductServiceType;

export const TABS = [
    {
        label: '计算',
        key: 'calc',
        children: [
            {serviceType: BCC, label: '云服务器', link: 'https://cloud.baidu.com/product/bcc.html'},
        ],
    },
    {
        label: '人工智能',
        key: 'ai',
        children: [
            {serviceType: FACE, label: '人脸识别', link: 'https://cloud.baidu.com/product/face'},
            {serviceType: OCR, label: '文字识别', link: 'https://cloud.baidu.com/product/ocr'},
            {serviceType: IMAGE_RECOGNITION, label: '图像识别', link: 'https://cloud.baidu.com/product/imagerecognition'},
            {serviceType: SPEECH, label: '语音识别', link: 'https://cloud.baidu.com/product/speech'},
            {serviceType: ICR, label: '内容审核', link: 'https://ai.baidu.com/censoring#/strategylist'},
            {serviceType: INTELLIGENTWRITING, label: '智能创作平台', link: 'https://yinian.cloud.baidu.com/creativity/index'},
        ],
    },
];

export const SERVICE_TYPE_MAP = TABS.map(tab => tab.children).flat().reduce((map, i) => {
    map[i.serviceType] = {label: i.label, link: i.link};
    return map;
}, {} as Record<string, {label: string, link: string}>);
export const aiSet = new Set([FACE, OCR, WEBIMAGE_OCR, LOCATION_OCR, BCN_OCR, ID_OCR, GENERAL_OCR, IMAGE_RECOGNITION, SPEECH, ICR]);

// 智能创作平台 (INTELLIGENTWRITING) 不同 subServiceType 有不同的 flavor 结构, 也就有不同的筛选项 (下文中 'package' 代表用 name 作为 value 的 flavor)
export const intelligentWritingSet = {
    // 1. subServiceType, DYL
    dyl: new Set(['AICP', 'AIZH-GJB', 'ERNIE-ViLG-AIZH']),
    // 2. subServiceType, Version_0
    version: new Set(['AIZH-KCFW', 'GYY-QYBB-XMFZSP', 'SJJHXZ']),
    // 3. subServiceType, SysgenConfig_0
    sys: new Set(['TPCLGJ']),
    // 4. subServiceType, Version_0, Specification_0
    versionSpec: new Set(['JGHSJXZ', 'ZNCH', 'ZNCZPT-GYY-QYBB']),
    // 5. subServiceType, Version_0, package
    versionPackage: new Set(['SJZSP', 'TWZSP', 'TWZSP-XM', 'YCDSB']),
    // 6. subServiceType, Version_0, SysgenConfig_0, package
    versionSysPackage: new Set(['TWSP']),
    // 7. 其余情况: subServiceType, package
};

/**
 * 请求 serviceType 对应的产品集合
 */
// eslint-disable-next-line complexity
export async function fetchProducts(serviceType: EnumProductServiceType, showVerify?: () => void): Promise<GroupPurchaseProductObj[]> {
    // eslint-disable-next-line no-console
    console.log('fetchProducts', serviceType);
    let reqServiceTypeList = null;
    if (serviceType === OCR) {
        reqServiceTypeList = [OCR, WEBIMAGE_OCR, LOCATION_OCR, BCN_OCR, ID_OCR, GENERAL_OCR];
    } else if (serviceType === ICR) {
        reqServiceTypeList = [ICR, SPEECH];
    } else {
        reqServiceTypeList = [serviceType];
    }

    let headers: any = null;
    if (typeof window !== 'undefined') {
        if (window?.passMachine?.tkDs) {
            headers = window.passMachine.tkDs;
            window.passMachine.tkDs = null;
        } else {
            headers = {'Acs-Token': await getAntiParam()};
        }
    }
    try {
        const resList = await Promise.all(reqServiceTypeList.map(serviceType =>
            netService.get<{serviceType: string, scene: string}, GroupPurchaseProductObj>(
                urlConst.GET_GROUP_PURCHASE_PRODUCTS_NO_LOGIN,
                {serviceType, scene: 'activityPage'},
                null as never,
                {headers}
            )
        ));
        const products = [];
        for (const res of resList) {
            if (!res.success) {
                throw new Error(JSON.stringify(res.message));
            }
            // ICR 的数据需要拼装上 SPEECH 中的 短语音审核 (subServiceType: Short_Speechcensoring_Count)
            if (serviceType === ICR && res.page.result[0]?.serviceType === 'SPEECH') {
                products.push(...res.page.result.filter(i => i.subServiceType === 'Short_Speechcensoring_Count'));
            } else {
                products.push(...res.page.result);
            }
        }
        return products;
    } catch (err: any) {
        console.error(`${serviceType} 数据获取失败:`, err);
        if (err?.code === 'yumenguan check exception') {
            showVerify?.();
        }
        return null;
    }
}

/**
 * 将 formValue 转化为后端接口指定的格式
 * - 把对象中所有的 'all' 换成 null
 * - BCC 中 duration 需要拆成 duration 和 timeUnit 两个字段
 * - AI 产品中, 有些字段需要额外处理
 */
export function formatFormValue(obj: any, filters: Filters) {
    let res = {...obj};
    res = mapValues(res, value => (value === 'all' ? null : value));

    if (res.duration) {
        if (res.duration % 12 === 0) {
            res.duration = `${res.duration / 12}`;
            res.timeUnit = 'year';
        } else {
            res.duration = `${res.duration}`;
            res.timeUnit = 'month';
        }
    }

    // subServiceType 相关逻辑
    const subServiceTypeFilter = filters?.find(i => i.key === 'subServiceType');
    if (!subServiceTypeFilter) {
        return res;
    }
    const option = subServiceTypeFilter.options.find(i => i.value === res.subServiceType);
    if (option?.extra) {
        const keyMap = option.extra;
        for (const key of Object.keys(keyMap)) {
            // PACKAGE_NAME 是特殊字
            if (keyMap[key] === 'PACKAGE_NAME') {
                res[res.spec] = ['1'];
                res = omit(res, ['spec', 'null']);
                continue;
            }
            res[keyMap[key]] = res[key];
            res = omit(res, key);
        }
    }
    return res;
}

/**
 * 在生成 filter 的函数中使用, 将 Map (k 为 value, v 为 label) 转化为 FilterOption[]
 */
function mapToOptions(map: Map<string, string>) {
    const options: FilterOption[] = [];
    map.forEach((label, value) => {
        options.push({label, value});
    });
    return options;
}

/**
 * sort compare 函数, 用于排序数字字符串
 */
function compareNumber(a: FilterOption, b: FilterOption) {
    return Number(a.value) - Number(b.value);
}

/**
 * sort compare 函数, 用于排序中文字符串
 */
function compareText(a: FilterOption, b: FilterOption) {
    return a.label.localeCompare(b.label);
}

/**
 * 将数字转化为规范的展示格式:
 *   - 500000 -> 50万次
 *   - 200000000 -> 2亿
 * 也有一些特殊情况, 在 FACE 产品中:
 *   - 5000w次 -> 5000万次
 *   - 1亿次 -> 1亿次 (保持不变)
 */
export function formatCount(count: string) {
    if (count.endsWith('w次')) {
        return `${Number(count.slice(0, -2))}万次`;
    } else if (count.endsWith('0'.repeat(8))) {
        return `${count.slice(0, -8)}亿次`;
    } else if (count.endsWith('0'.repeat(4))) {
        return `${count.slice(0, -4)}万次`;
    } else if (count.endsWith('次')) {
        return count;
    } else {
        return `${count}次`;
    }
}

// 将bcc的2C8M转化为2核8G
export function formatBccSpec(spec: string) {
    const regResult = /c(\d+)m(\d+)/.exec(spec);
    return [regResult[1], regResult[2]];
}

function genBccFilters(products: GroupPurchaseProductObj[]) {
    const regionMap = new Map();
    const cpuMap = new Map();
    const memoryMap = new Map();
    const specIdMap = new Map();

    for (const product of products) {
        for (const flavorItem of product.flavorDisplayName) {
            if (flavorItem.flavor === 'region') {
                regionMap.set(flavorItem.value, flavorItem.valueDisplayName);
            } else if (flavorItem.flavor === 'spec') {
                const [cpu, memory] = formatBccSpec(flavorItem.value as string);
                cpuMap.set(cpu, `${cpu}核`);
                memoryMap.set(memory, `${memory}G`);
            } else if (flavorItem.flavor === 'specId') {
                specIdMap.set(flavorItem.value, flavorItem.valueDisplayName);
            }
        }
    }

    // 地域排序优先级
    const regionSortTemplate = ['bj', 'bd', 'cd', 'gz', 'su', 'nj', 'fsh', 'fwh', 'hkg'];
    // 防止有些地域没有在 regionSortTemplate 里, 做一下兜底
    const leftRegion = Array.from(regionMap).filter(([key]) => !regionSortTemplate.includes(key));

    return [
        {
            name: '地域',
            key: 'region',
            options: [
                {label: '全部', value: 'all'},
                ...regionSortTemplate
                    .map(region => (regionMap.has(region) ? ({label: regionMap.get(region), value: region}) : false))
                    .filter(Boolean),
                ...leftRegion.map(([key, value]) => ({label: value, value: key})),
            ],
            type: 'radio',
        },
        {
            name: 'CPU',
            key: 'cpuList',
            options: mapToOptions(cpuMap).sort(compareNumber),
        },
        {
            name: '内存',
            key: 'memoryList',
            options: mapToOptions(memoryMap).sort(compareNumber),
        },
        // {
        //     name: '型号',
        //     key: 'specList',
        //     options: mapToOptions(specIdMap).sort(compareText),
        // },
        {
            name: '带宽',
            key: 'bandwidth',
            // TODO: 1-10, 后面再改
            options: [
                {label: '全部', value: 'all'},
                ...Array.from({length: 10}, (_, i) => ({label: `${i + 1}M`, value: `${i + 1}M`})),
            ],
            type: 'radio',
        },
        // {
        //     name: '时长',
        //     key: 'duration',
        //     options: [
        //         {label: '全部', value: 'all'},
        //         {label: '1月', value: 1},
        //         {label: '2月', value: 2},
        //         {label: '3月', value: 3},
        //         {label: '4月', value: 4},
        //         {label: '5月', value: 5},
        //         {label: '6月', value: 6},
        //         {label: '7月', value: 7},
        //         {label: '8月', value: 8},
        //         {label: '9月', value: 9},
        //         {label: '1年', value: 12},
        //         {label: '2年', value: 24},
        //         {label: '3年', value: 36},
        //     ],
        //     type: 'radio',
        // },
    ] as Filters;
}

/**
 * 为除了智能创作的 AI 产品生成筛选项, 由于存在一个产品有多个 serviceType 的情况,
 * 返回的 subServiceType 字段为 `subServiceType#serviceType` 的形式
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function genAiFilter(products: GroupPurchaseProductObj[], serviceType: EnumProductServiceType) {
    const filterMap: Record<string, Filters> = {};
    const productMap = groupBy(products, i => `${i.flavor.find(i => i.name === 'subServiceType').value}#${i.serviceType}`);
    // eslint-disable-next-line no-console
    console.log(`${serviceType} productMap:`, productMap);
    const subServiceTypeMap = new Map();
    const subServiceTypeExtra: Record<string, Record<string, string>> = {};

    // const apiInfo = await netService.get(urlConst.GET_PRODUCT_API_ID.replace(':product', serviceType.toLowerCase())
    // as unknown as ProductApiInfo[];

    for (const subServiceType of Object.keys(productMap)) {
        const products = productMap[subServiceType];
        const specMap = new Map();
        const displayName = products[0].flavorDisplayName.find(i => i.flavor === 'subServiceType').valueDisplayName;
        // const apiItem = getApiNameAndIdByValueDisplayName(displayName, apiInfo);
        // 可能有一个 subServiceType 对应多个 api_name 的情况, 只取父层级作为筛选项
        // subServiceTypeMap.set(subServiceType, apiItem.parent_api_name || apiItem.api_name);
        subServiceTypeMap.set(subServiceType, displayName);
        for (const product of products) {
            for (const flavorItem of product.flavorDisplayName) {
                // eslint-disable-next-line max-depth
                if (['Specification_0', 'count', 'DYL'].includes(flavorItem.flavor)) {
                    specMap.set(flavorItem.value, formatCount(flavorItem.valueDisplayName));
                    subServiceTypeExtra[subServiceType] = {'spec': flavorItem.flavor};
                }
            }
        }
        filterMap[subServiceType] = [
            {
                name: '规格',
                key: 'spec',
                options: mapToOptions(specMap).sort(compareNumber),
            },
        ];
    }

    // 为每个 filterMap 添加上公共的 subServiceType
    const subServiceTypeField: Filters[number] = {
        name: '服务类型',
        key: 'subServiceType',
        options: mapToOptions(subServiceTypeMap).sort(compareText).map(option => ({
            ...option,
            extra: subServiceTypeExtra[option.value],
        })),
        type: 'radio',
    };
    for (const subServiceType of Object.keys(filterMap)) {
        filterMap[subServiceType].unshift(subServiceTypeField);
    }

    return filterMap;
}

/* eslint-disable */
/**
 * 为智能创作产品生成 Filters, 不同的 subServiceType 对应不同的筛选项
 */
export function genIntelligentWritingFilters(products: GroupPurchaseProductObj[]) {
    const filterMap: Record<string, Filters> = {};
    const productMap = groupBy(products, i => i.flavor.find(i => i.name === 'subServiceType').value);
    const subServiceTypeMap = new Map();
    const subServiceTypeExtra: Record<string, Record<string, string>> = {};

    // 目前只有两种 subServiceType, 对应面 Set 中的 1(dyl) 和 7, 后续有新的商品需要继续添加

    for (const subServiceType of Object.keys(productMap)) {
        const products = productMap[subServiceType];
        subServiceTypeMap.set(subServiceType, products[0].flavorDisplayName.find(i => i.flavor === 'subServiceType').valueDisplayName);
        if (intelligentWritingSet.dyl.has(subServiceType)) {
            // 两个有效 flavor -> subServiceType, DYL
            const dylMap = new Map();
            for (const product of products) {
                for (const flavorItem of product.flavorDisplayName) {
                    if (flavorItem.flavor === 'DYL') {
                        dylMap.set(flavorItem.value, flavorItem.valueDisplayName);
                        subServiceTypeExtra[subServiceType] = {'spec': 'DYL'};
                        break;
                    }
                }
            }
            filterMap[subServiceType] = [
                {
                    name: '规格',
                    key: 'spec',
                    options: mapToOptions(dylMap).sort(compareNumber),
                },
            ];
        } else {
            // 两个有效 flavor -> subServiceType, package(有效值为name)
            const packageMap = new Map();
            for (const product of products) {
                const packageName = product.flavor.find(i => i.name !== 'subServiceType').name;
                for (const flavorItem of product.flavorDisplayName) {
                    if (flavorItem.flavor === packageName) {
                        packageMap.set(packageName, flavorItem.flavorDisplayName);
                        subServiceTypeExtra[subServiceType] = {'spec': 'PACKAGE_NAME'};
                        break;
                    }
                }
            }
            filterMap[subServiceType] = [
                {
                    name: '规格',
                    key: 'spec',
                    options: mapToOptions(packageMap).sort(compareText),
                },
            ];
        }
    }

    // 为每个 filterMap 添加上公共的 subServiceType
    const subServiceTypeField: Filters[number] = {
        name: '服务类型',
        key: 'subServiceType',
        options: mapToOptions(subServiceTypeMap).sort(compareText).map(option => ({
            ...option,
            extra: subServiceTypeExtra[option.value],
        })),
        type: 'radio',
    };
    for (const subServiceType of Object.keys(filterMap)) {
        filterMap[subServiceType].unshift(subServiceTypeField);
    }

    return filterMap;
}
/* eslint-enable */

/**
 * serviceType -> 生成相应 Filters 函数
 * 函数返回可能是 Filters, 也可能是 subServiceType -> Filters 对象 (同一个 serviceType 下, 不同 subServiceType 对应不同筛选项), 同时也可能被 Promise 包裹
 */
const filterFunctionMap: Partial<Record<
    EnumProductServiceType,
    (products: GroupPurchaseProductObj[]) =>
        Filters | Record<string, Filters> |
        Promise<Filters | Record<string, Filters>>>
> = {
    [BCC]: genBccFilters,
    [FACE]: products => genAiFilter(products, FACE),
    [OCR]: products => genAiFilter(products, OCR),
    [IMAGE_RECOGNITION]: products => genAiFilter(products, IMAGE_RECOGNITION),
    [SPEECH]: products => genAiFilter(products, SPEECH),
    [ICR]: products => genAiFilter(products, ICR),
    [INTELLIGENTWRITING]: genIntelligentWritingFilters,
};

/**
 * 生成 serviceType 对应的 filters
 */
export function genFiltersByServiceType(serviceType: EnumProductServiceType, products: GroupPurchaseProductObj[]) {
    return filterFunctionMap[serviceType](products);
}

// 时间展示单位
export const TIME_UNIT_MAP: {
    [key: string]: string;
} = {
    'second': '秒',
    'minute': '分钟',
    'hour': '小时',
    'day': '天',
    'month': '月',
    'year': '年',
    'M': '月',
    'Y': '年',
};

/**
 * 活动价优惠标签展示
 */
// 一维：userLimit，二维：realNameLimit
const CAMPAIGN_TITLE_TAG = [
    ['', '企业用户专享', '个人用户专享'],
    ['限产品首购', '限企业产品首购', '限个人产品首购'],
    ['新用户专享', '企业新用户专享', '个人新用户专享'],
];
// 只提供了5种tag
const CAMPAIGN_TITLE_TAG_ICON = [
    ['', 'comp_user_tag'],
    ['product_first_tag', 'com_product_first_tag'],
    ['new_user_tag', 'comp_new_user_tag'],
];

export const getCampaignTitleTag = (userLimit: number, realNameLimit: number, isText = false) => {
    const TAG_LIST = isText ? CAMPAIGN_TITLE_TAG : CAMPAIGN_TITLE_TAG_ICON;
    if (typeof userLimit === 'number' && typeof realNameLimit === 'number') {
        return TAG_LIST[userLimit] ? (TAG_LIST[userLimit][realNameLimit] || '') : '';
    }
    return '';
};

/**
 * serviceType -> 生成相应购买链接, query后接&qps=xxx 表示数量
 * eg: BCC -> /campaign/order.html${location.search}#/bcc/activity~product=activitybcc&config=${campaignId}
 */
export const PURCHASE_LINK_MAP: Record<string, string> = {
    'BCC': '#/bcc/activity~product=activitybcc&config=',
    'FACE': '#/aiDay/activity~product=activityface&config=',
    'OCR': '#/aiDay/activity~product=activityocr&config=', // ocr
    'IMAGE_RECOGNITION': '',
    'SPEECH': '#/speech/activity~product=activityspeech&config=',
    'ICR': '#/aiDay/activity~product=activityicr&config=',
    'INTELLIGENTWRITING': '#/aiDay/activity~product=activityocr&config=', // 智能创作平台
    // 'CDN': '#/cdn/activity~product=activitycdn&campaignId=',
    // 'LSS': '#/lss/activity~product=activitylss&config=',
    // 'AIPAGE': '#/aipage/activity~product=activityaipage&config=',
    // 'BOS': '#/bos/activity~product=activitybos&config=', // bos
    // 'CDS': '#/cds/activity~product=activitycds&config=',
    // 'EIP': '#/eip/activity~product=activityeip&config=',
    // 'LS': `/campaign/order.html${location.search}#/ls/activity~product=activityls&config=`,
    // 'BES': `/campaign/order.html${location.search}#/bes/activity~product=activitybes&config=`,
    // 'BCH': `/campaign/order.html${location.search}#/bch/activity~product=activitybch&config=`,
    // 'DDOS': `/campaign/order.html${location.search}#/ddos/activity~product=activityddos&config=`,
    // 'HOSTEYE': `/campaign/order.html${location.search}#/hosteye/activity~product=activityhosteye&config=`, // 云主机
    // 'WAF': `/campaign/order.html${location.search}#/waf/activity~product=activitywaf&config=`, // waf
    // 'RDS': `/campaign/order.html${location.search}#/rds/activity~product=activityrds&config=`, // rds
    // 'SCS': `/campaign/order.html${location.search}#/scs/activity~product=activityscs&config=`, // scs
    // 'CAS': `/campaign/order.html${location.search}#/cas/activity~product=activitycas&config=`, // cas
    // 'SPEECH_SDK': `/campaign/order.html${location.search}#/speech/activity~product=activityspeechsdk&config=`,
    // 'EASYDL': `/campaign/order.html${location.search}#/easydl/activity~product=activityeasydl&config=`,
    // 'SMS': `/campaign/order.html${location.search}#/sms/activity~product=activitysms&config=`,
    // 'OCR_SDK': `/campaign/order.html${location.search}#/aiSDK/activity~product=activityocrsdk&config=`,
    // 'BDTRD': `/campaign/order.html${location.search}#/sentiment/activity~product=activitysentiment&config=`,
    // 'FACE_SDK': `/campaign/order.html${location.search}#/aiDay/activity~product=activityfacesdk&config=`, // FACE_SDK
    // 'NLP': `/campaign/order.html${location.search}#/aiDay/activity~product=activitynlp&config=`,
    // buy_imagesearch: `/campaign/order.html${location.search}#/aiDay/activity~product=activityimagesearch&config=`, //
    // buy_image: `/campaign/order.html${location.search}#/aiDay/activity~product=activityimage&config=`,
    // buy_kg: `/campaign/order.html${location.search}#/aiDay/activity~product=activitykg&config=`,
    // buy_dwz: `/campaign/order.html${location.search}#/dwz/activity~product=activitydwz&config=`,
    // buy_xuperasset: `/campaign/order.html${location.search}#/xuperasset/activity~product=activityxuperasset&config=`,
    // buy_sugar: `/campaign/order.html${location.search}#/sugar/activity~product=activitysugar&config=`,
    // bcc_rds: `/campaign/order.html${location.search}#/bccRdsCom/activity~product=bccRdsCom&config=`,
    // buy_gpu: `/campaign/order.html${location.search}#/gpu/bigPromotion~product=activitygpu&config=`,
    // bcc_cdn: `/campaign/order.html${location.search}#/bccCdnCom/activity~product=bccCdnBuyAll&config=`,
    // cdn_cdn: `/campaign/order.html${location.search}#/cdnCdnBatch/activity~product=cdnCdnBuyAll&config=`,
    // bos_bos: `/campaign/order.html${location.search}#/bosBosBatch/activity~product=bosBosBuyAll&config=`,
    // xuperasset_xuperasset: `/campaign/order.html${location.search}#/xuperassetXuperasset/activity~product=activityxuperasset&config=`,
    // sms_sms: `/campaign/order.html${location.search}#/smsSms/activity~product=activitysms&config=`,
};
