/**
 * 迁移自portal-server lib/middleware/cms.js
 */
import {CmsPageDataObj} from '@common/interface/common';

/**
 * (非纯函数)B010 和 C002 需要计算卡片中参数的最大的个数，然后基于此渲染卡片高度
 * 计算后，会在卡片中添加 config.maxParameterLength
 * @param {Object} module 单个区块数据
 */
const genMaxParameterLength: (m: any) => void = m => {
    const needCalcType = ['V5B010B', 'V5C002'];
    if (needCalcType.includes(m.type)) {
        const {render, config} = m.data;
        const maxParameterLength = render.items.reduce((maxLength: any, item: any) => {
            const {length} = item.units;
            return maxLength > length ? maxLength : length;
        }, 0);

        config.maxParameterLength = maxParameterLength;
    }
};

/**
 * (非纯函数)根据每个区块配置信息中的列数配置，删除多余的items
 * @param {Object} module 单个区块数据
 */
// eslint-disable-next-line complexity
const deleteUselessBlockItem: (m: any) => void = m => {
    // 自定义html组件不做处理
    if (m.data?.html || !m?.data?.config) {
        return;
    }

    const {render, config} = m.data;
    const {promotionColumns, columns, rows, column} = config;
    // V5S001 banner区块带有该字段
    if (promotionColumns && render.promotion) {
        const items = render.promotion.items;
        if (Array.isArray(items)) {
            render.promotion.items = items.slice(0, promotionColumns);
        }
    }
    // 如果有 column 字段，那么优先用column判断
    if (column) {
        return;
    }
    // 普通的单行列表区块
    if (columns && !rows) {
        const items = render.items;
        if (Array.isArray(items) && !Array.isArray(columns)) {
            render.items = items.slice(0, columns);
        }
        // 当 columns 为数组，那么需要深入一层
        if (Array.isArray(columns)) {
            const tabs = render.tabs;

            if (Array.isArray(tabs)) {
                tabs.forEach((container, index) => {
                    container.items = container.items.slice(0, columns[index]);
                });
            }
        }
    }
    // 多行列表区块，例如V5G005A
    if (columns && rows) {
        const rowList = render.items;
        if (Array.isArray(rowList)) {
            render.items = rowList.slice(0, rows);
            render.items.forEach((columnList: any[], index: number) => {
                render.items[index] = columnList.slice(0, columns);
            });
        }
    }
};

/**
 * 区块的循环处理
 * @param {Array} modules 卡片列表
 * @param {Array<Function>} funcArray 需要在循环中处理module的函数
 */
// eslint-disable-next-line @typescript-eslint/ban-types
const modulesLoopProcess: (modules: CmsPageDataObj['modules'], funcArray?: Array<(m: any) => any>) => void = (modules, funcArray) => {
    modules.forEach(m => {
        funcArray.forEach(func => func(m));
    });
};

/**
 * (非纯函数) 遍历所有的V5G,V5B区块，获取其name，生成导航栏区块信息插入到banner区块
 * @param {Array} modules block的数组
 */
const genNavBlockData: (modules: CmsPageDataObj['modules']) => void = modules => {
    const navModuleRe = /^V5[GBC]/;
    const solutionNavRe = /^V1S[GB]/;
    const intlModuleRe = /^V1E[GB]/;
    const bannerModule = modules.find(m => /V5S001(_en)?$/g.test(m.type) || m.type === 'V1SS001');
    const bannerData = bannerModule.data.render.banner;
    const navContent = modules
        .filter(module => navModuleRe.test(module.type)
            || module.type === 'CodePanel' || solutionNavRe.test(module.type) || intlModuleRe.test(module.type))
        .map(module => ({
            control: {
                type: module.type,
            },
            data: {
                name: module.displayName,
            },
        }));
    bannerModule.data.render.nav = {
        'dropDownAble': true,
        'fixed': true,
        'name': bannerData.title,
        'apply': {
            'type': 'button',
            'className': 'buy',
            ...bannerData.primaryLink,
        },
        'content': navContent,
    };
};

/**
 * 对模版数据的modules进行处理，生成 navModules
 * @param {Array} modules 渲染的 modules 数组
 * @return {Array} 返回添加了navModules的渲染数组
 */
const processModules: (modules: CmsPageDataObj['modules'], path: string) => CmsPageDataObj['modules'] = (modules, path) => {
    modules.forEach(module => (typeof module.data === 'string' && (module.data = JSON.parse(module.data))));

    if (path && /^\/(product|solution)\//.test(path)) {
        genNavBlockData(modules);
        modulesLoopProcess(modules, [
            deleteUselessBlockItem,
            genMaxParameterLength,
        ]);
    }

    return modules;
};

// api处理后，result中包含结构后的data，moudules为data.data
export const pageModulesProcess: (page: CmsPageDataObj) => void = page => {
    page.modules = processModules(page.modules, page.path);
    // page.productKey = page.serviceType || '';
    // page.productName = page.serviceName || '';
    // page.desc = page.description || '';
};

// 处理新产品页（product-s）的数据
export const productSPageModulesProcess: (page: CmsPageDataObj) => void = page => {
    page.modules.forEach(module => (typeof module.data === 'string' && (module.data = JSON.parse(module.data))));

    if (page.path && /^\/(product-s)\//.test(page.path)) {
        modulesLoopProcess(page.modules, [
            deleteUselessBlockItem,
        ]);
    }
};
