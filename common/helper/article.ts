import {ArticleParamOrder} from '@common/interface/article';
import {DEV_PAGE_URL_CONST} from '@common/constant/variableConst';

export function joinHomePath(
    order: ArticleParamOrder,
    pageNo: number,
    tagId: string | number
) {
    return `${DEV_PAGE_URL_CONST.ARTICLE}/${order}-${pageNo}${tagId ? `-${tagId}` : ''}`;
}

export function joinDetailPath(
    id: number | string
) {
    return `${DEV_PAGE_URL_CONST.ARTICLE}/${id}`;
}
