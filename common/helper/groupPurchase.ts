import {ProductApiInfo} from '@common/interface/common';
import sha256 from 'crypto-js/sha256';
import Base64 from 'crypto-js/enc-base64';
import {
    GroupPurchaseFormValue,
    GroupPurchaseProductFlavorDisplayNameObj,
    GroupPurchaseProductFlavorObj,
    GroupPurchaseProductObj,
    GroupPurchaseProductObjIndex,
    GroupPurchaseProductPriceParams,
} from '@common/interface/groupPurchase';
import {PRODUCT_PRICE_SHA_KEY} from '@common/constant/variableConst';
import {cloneDeep, isArray, isString, lowerCase, omit} from 'lodash';
import {utc} from 'moment';

/**
 * 从 flavorDisplayName 拿到 flavor对应的中文信息
 * @param flavorDisplayName flavorDisplayName
 * @param flavor flavor
 * @returns 对应的中文
 */
export function getZhFromFlavorDisplayName(
    flavorDisplayName: GroupPurchaseProductObj['flavorDisplayName'],
    flavor: string
): string {
    const target = flavorDisplayName.find(item => item.flavor === flavor);
    return target?.valueDisplayName;
}

/**
 *
 * @param displayname subServiceType flavorDisplayName
 * @param apiNameList api转换规则数组
 * @returns ProductApiInfo, 没找到 api_name 是 displayname
 */
export function getApiNameAndIdByValueDisplayName(displayname: string, apiNameList: ProductApiInfo[]): ProductApiInfo {
    const target = apiNameList.find(item => item.p3m_name === displayname);
    // 按道理不可能找不到， 找不到就意味着 apiid 不存在 下不了单
    if (!target) {
        throw new Error(`没有找到 ${displayname} 对应的 api id`);
    }
    return target;
}

/**
 * 某个产品参数项里面 提取 某个参数字段 对应的值
 * @param skuObj 产品对象
 * @param field 字段
 * @param isScale 是否是scale
 * @param isFlavor 是否在flavor里面 因为flavor外层和flavor里层有同样名的字段 这里需要指定字段是否在flavor
 * @returns 字段值
 */
export function getProductFieldVal(
    skuObj: GroupPurchaseProductObj,
    field: GroupPurchaseProductObjIndex | string,
    isScale = true,
    isFlavor = false
) {
    try {
        // 自身找，找不到 去flavor遍历再找
        if (skuObj[field as GroupPurchaseProductObjIndex] && !isFlavor) {
            return skuObj[field as GroupPurchaseProductObjIndex] as string;
        }
        const res = skuObj.flavor.find(item => item.name === field);
        return res ? res[isScale ? 'scale' : 'value'] : null;
    } catch (e) {
        console.error('@function: getProductFieldVal', skuObj);
        return null;
    }
}

/**
 * 根据字段和字段值匹配到目标产品
 * @param products 所有产品
 * @param fields 要匹配的字段
 * @returns 目标产品
 */
export function getProductByFields(
    products: GroupPurchaseProductObj[],
    fields: Array<{
        name: string;
        isScale?: boolean;
        isFlavor: boolean;
        val: any;
    }>
): GroupPurchaseProductObj {
    let res: GroupPurchaseProductObj = null;
    products.forEach(item => {
        try {
            const flag: boolean[] = [];
            fields.forEach(field => {
                if (field.isFlavor) {
                    const target = item.flavor.find(i => i.name === field.name);
                    flag.push(
                        target?.[field.isScale ? 'scale' : 'value'] === field.val
                    );
                } else {
                    flag.push((item as any)[field.name] === field.val);
                }
            });
            if (flag.length === fields.length && !flag.includes(false)) {
                res = item;
            }
        } catch (e) {
            console.error('@function:getProductByFields', e, item, fields);
        }
    });
    return res;
}

/**
 * @param skuObj 产品对象
 * @param field 字段
 * @param isScale 设置的是否是scale
 * @param isFlavor 设置的字段是否在 flavor里面
 * @param value 设置的值
 * @returns 返回产品对象
 */
export function setProductFieldVal(
    skuObj: GroupPurchaseProductObj,
    field: string,
    isScale = true,
    isFlavor = false,
    value: any
) {
    const skuObjCopy: GroupPurchaseProductObj = cloneDeep(skuObj);
    try {
        if (isFlavor) {
            skuObjCopy.flavor.forEach(item => {
                if (item.name === field) {
                    item[isScale ? 'scale' : 'value'] = value;
                }
            });
        } else {
            skuObjCopy[field as unknown as keyof GroupPurchaseProductObj] = null;
        }
        return skuObjCopy;
    } catch (e) {
        // eslint-disable-next-line no-console
        console.error('setProductFieldVal', skuObj);
        return skuObjCopy;
    }
}

/**
 * 组装询价的flavor
 * @param flavor
 * @param flavorDisplayName
 * @returns assembled Flavor
 */
function assembleFlavor(
    flavor: GroupPurchaseProductFlavorObj[],
    flavorDisplayName: GroupPurchaseProductFlavorDisplayNameObj[],
    formValue: GroupPurchaseFormValue
): GroupPurchaseProductFlavorObj[] {
    if (!flavorDisplayName || !flavor) {
        return flavor;
    }
    const resFlavor = [];
    for (const item of flavor) {
        // 剔除 subServiceType = default
        if (item.name === 'subServiceType' && item.value === 'default') {
            continue;
        }
        // 拼装单位
        const flavorDisplayNameTarget = flavorDisplayName.find(i => i.flavor === item.name);
        if (flavorDisplayNameTarget?.unitPrefix) {
            item.value = `${formValue[item.name]}${lowerCase(flavorDisplayNameTarget.unitPrefix)}`;
        }
        resFlavor.push(item);
    }
    return resFlavor;
}

/**
 * 询价参数组合
 * @param item 产品对象
 * @param duration 购买时长
 * @returns 询价参数
 */
export function genPriceParamsByProductObj(
    item: GroupPurchaseProductObj,
    formValue: GroupPurchaseFormValue
): GroupPurchaseProductPriceParams {
    const {duration, count} = formValue;
    const {region, serviceType, subServiceType, chargeItem, flavor, flavorDisplayName} = item;

    let hasTime = false;
    // 针对 CBS 的特判, 固定传 1Y 的 duration
    if (serviceType === 'CBS') {
        hasTime = true;
    } else if (isArray(item.dimension)) {
        hasTime = item.dimension.includes('time');
    } else {
        hasTime = !!item.packageTime;
    }

    const durationNumber = parseInt(duration, 10);
    const durationUnit = duration
        .replace(/\d/g, '')
        .replace('Y', 'YEAR')
        .replace('M', 'MONTH')
        .replace('D', 'DAY');

    // const hasFlavor = item.dimension.includes('flavor');
    const signDuration = duration.replace('Y', 'year').replace('M', 'month').replace('D', 'day');
    const flavorItems = assembleFlavor(flavor, flavorDisplayName, formValue);
    // eslint-disable-next-line no-console
    // console.log(`${serviceType}${region}${signDuration}${JSON.stringify(flavorItems)}${PRODUCT_PRICE_SHA_KEY}`);
    let params: GroupPurchaseProductPriceParams = {
        accountId: '',
        agentAccountId: '',
        serviceType,
        subServiceType,
        chargeItemName: chargeItem,
        region,
        scene: 'NEW',
        flavor: {flavorItems},
        time: {
            startTime: utc(new Date()).format(),
            period: `P${duration}`,
        },
        count: count || 1,
        // "queryTime": "2022-01-20T09:52:29Z"
        duration: durationNumber,
        timeUnit: durationUnit,
        signAuth: Base64.stringify(sha256(`${serviceType}${region}${signDuration}${JSON.stringify(flavorItems)}${PRODUCT_PRICE_SHA_KEY}`)),
    };
    !hasTime && (params = omit(params, 'time'));
    // !hasFlavor && (params = omit(params, 'flavor'));
    return params;
}

/**
 * 数字计数器的展示
 *
 * @param count 数字计数器的值，可以是数字或字符串类型
 * @returns 返回数字计数器的展示形式，单位分别为'万'和'亿'
 */
export function getAiCountSplitRange(count: number | string) {
    const num = isString(count) && +count;
    switch (true) {
        case num < 10000:
            return num.toString();
        case num < 100000000:
            return (num / 10000).toFixed() + '万';
        default:
            return (num / 100000000).toFixed() + '亿';
    }

}

/**
 * 对容量字段的选项进行排序, 优先单位, 再是数值
 * @param options {label: string}
 */
export function sortCapacityOptions(options: Array<{label: string}>) {
    const unitSortWeight: Record<string, number> = {
        'MB': 0,
        'GB': 1,
        'TB': 2,
        'PB': 3,
    };
    options.sort((a, b) => {
        const unitReg = /[^\d]+$/;
        const unitA = unitReg.exec(a.label)[0];
        const unitB = unitReg.exec(b.label)[0];
        if (unitSortWeight[unitA] !== unitSortWeight[unitB]) {
            return unitSortWeight[unitA] - unitSortWeight[unitB];
        }
        const numA = parseInt(a.label, 10);
        const numB = parseInt(b.label, 10);
        return numA - numB;
    });
}

/**
 * 将 period 字段转化为月数
 */
export function period2month(period: string) {
    const match = /P(\d+)([YM])/.exec(period);
    if (!match) {
        throw new Error(`period 解析错误: ${period}`);
    }
    const num = parseInt(match[1], 10);
    const unit = match[2];
    if (unit === 'M') {
        return num;
    } else if (unit === 'Y') {
        return num * 12;
    } else {
        throw new Error(`period 未识别的单位: ${period}`);
    }
}

export const defaultFormValues = [
    {
        serviceType: 'BCC',
        selected: true,
        product: {
            region: 'bj',
            spec: 'g4',
            cpu: 2,
            memory: 8,
            size: 20,
            bandwidth: 1,
            osType: 'Ubuntu',
            osVersion: '22.04 LTS amd64 (64bit)',
            duration: '1M',
        } as any,
    },
    {
        serviceType: 'CBS',
        selected: true,
        product: {
            type: 'dailijizhang',
            taxpayerType: '小规模纳税人',
            declarationMethod: '零申报',
            serviceDuration: '一年',
            companyType: '有限公司',
            serviceType: 'bookkeeping',
            area: 'Beijing',
        } as any,
    },
    {
        serviceType: 'BOS',
        selected: true,
        product: {
            region: 'bj',
            subServiceType: 'SpaceUsedBytes',
            capacity: '100g',
            duration: '12M',
            isCustomize: false,
            count: 1,
        } as any,
    },
    {
        serviceType: 'CDN',
        selected: true,
        product: {
            subServiceType: 'default',
            spec: '100g',
            duration: '3M',
            count: 1,
        } as any,
    },
    {
        serviceType: 'EIP',
        selected: true,
        product: {
            region: 'bj',
            subServiceType: 'default',
            duration: '1M',
            bandwidth: 1,
            count: 1,
        } as any,
    },
    {
        serviceType: 'LSS',
        selected: true,
        product: {
            type: 'package',
            capacity: '500g',
            subServiceType: 'default',
            spec: '', // TODO: 转码包目前没被打标, 后续如果要添加需要测试
            duration: '12M',
            count: 1,
        } as any,
    },
    {
        serviceType: 'NAT',
        selected: true,
        product: {
            region: 'bj',
            natGatewayScale: 'little',
            name: '',
            duration: '4M',
        } as any,
    },
    {
        serviceType: 'SPEECH',
        selected: true,
        product: {
            region: 'global',
            apiId: '633',
            // specification: '100000',
            count: '1000',
            duration: '12M',
        } as any,
    },
    {
        serviceType: 'AIPAGE',
        selected: true,
        product: {
            type: 'site',
            version: 'miniprogram_2',
            siteName: '',
            duration: '1Y',
        } as any,
    },
    {
        serviceType: 'TMS',
        selected: true,
        product: {
            displayName: '至尊无忧注册',
            brandName: '',
        } as any,
    },
    {
        serviceType: 'OCR',
        selected: true,
        product: {
            region: 'global',
            apiId: '1007',
            specification: '1000',
            duration: '12M',
        } as any,
    },
    {
        serviceType: 'AI_TE',
        selected: true,
        product: {
            region: 'global',
            apiId: '1008',
            version: '10000000',
            duration: '12M',
        } as any,
    },
    {
        serviceType: 'FACE',
        selected: true,
        product: {
            region: 'global',
            subServiceType: 'dateverify',
            specification: '10000',
            duration: '12M',
        } as any,
    },
    {
        serviceType: 'SMS',
        selected: true,
        product: {
            unit: '0.5万条',
            purchaseCount: 1,
        } as any,
    },
    {
        serviceType: 'ICR',
        selected: true,
        product: {
            region: 'global',
            apiId: '362',
            count: '500000',
            specification: null,
            duration: '12M',
        } as any,
    },
];

// export const defaultFormValues = [
//     {
//         serviceType: 'BCC',
//         selected: true,
//         product: {
//             region: 'bj',
//             spec: 'm4',
//             cpu: 8,
//             memory: 64,
//             size: 20,
//             bandwidth: 100,
//             osType: 'Ubuntu',
//             osVersion: '16.04 LTS i386 (32bit)',
//             duration: '5M',
//         } as any,
//     },
// ];
