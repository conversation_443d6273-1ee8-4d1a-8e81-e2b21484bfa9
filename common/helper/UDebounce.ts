type Handler<T> = (p: T) => void;

export class UDebounce<T> {
    private gap: number = 100;
    private handler: <PERSON>ler<T>;
    private timer: NodeJS.Timeout = null;
    constructor(gap: number) {
        this.gap = gap;
    }
    setDebounceTime(gap: number) {
        this.gap = gap;
    }
    subscribe(handler: Handler<T>) {
        this.handler = handler;
    }
    next(v: T) {
        this.timer && clearTimeout(this.timer);
        this.timer = setTimeout(() => {
            this.handler(v);
        }, this.gap) as any;
    }
}
