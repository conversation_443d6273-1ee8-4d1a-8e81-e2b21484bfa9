/* eslint-disable max-len */
import {Ioc} from '@baidu/bce-decorators';
import {UEnvService} from '@common/services/env';
import {SearchType} from '../interface/search';
import {NewsListQueryType} from './../interface/news';

// 登录链接
export const LOGIN_URL = {
    online: 'https://login.bce.baidu.com/',
    sandbox: 'https://login.bcetest.baidu.com/',
};
// 登出链接
export const LOGOUT_URL = {
    online: 'https://login.bce.baidu.com/logout',
    sandbox: 'https://login.bcetest.baidu.com/logout',
};
// 注册链接
export const REGISTER_URL = 'https://login.bce.baidu.com/reg.html?tpl=bceplat&from=portal';

// 开发者中心
export const DEVELOPER_URL = 'https://developer.baidu.com';

// portalCommon链接
export const PORTAL_COMMON_URL = {
    online: 'https://bce.bdstatic.com/fe-static/baidu/bce-portal/portal-common/load.online.js',
    sandbox: 'https://bce.bdstatic.com/fe-static/baidu/bce-portal/portal-common/load.sandbox.js',
};

// portalCommon英文页面链接
export const PORTAL_COMMON_EN_URL = {
    online: 'https://bce.bdstatic.com/fe-static/baidu/bce-portal/portal-common-intl/load.online.js',
    sandbox: 'https://bce.bdstatic.com/fe-static/baidu/bce-portal/portal-common-intl/load.sandbox.js',
};

// 实名认证链接
export const REAL_NAME_URL = {
    online: 'https://console.bce.baidu.com/qualify/#/qualify/index?isActivated=1',
    sandbox: 'https://qasandbox.bcetest.baidu.com/qualify/#/qualify/index?isActivated=1',
};

// bcc支付跳转链接
export const H5_FINANCE_PAY_URL = {
    online: 'https://console.bce.baidu.com/finance/h5/pay',
    sandbox: 'https://qasandbox.bcetest.baidu.com/finance/h5/pay',
};

// rem的倍数
export const remRate = 10;

// bos路径,非CDN加速
export const BOS_ORIGIN_URL = 'https://bce-cdn.bj.bcebos.com';
// bos路径，CDN加速
export const BOS_CDN_ORIGIN_URL = 'https://bce.bdstatic.com';

export const BOS_PORTAL_CLOUD_SERVER = `${BOS_ORIGIN_URL}/portal-cloud-server`;

export const DEVELOPER_CDN_URL_IMG_BASE = `${BOS_CDN_ORIGIN_URL}/developer-static/imgs/`;
export const DEVELOPER_BOS_URL_IMG_BASE = `${BOS_ORIGIN_URL}/developer-static/imgs/`;

// 新闻列表页类型
export const NEWS_LIST_TYPES: Array<NewsListQueryType['type']> = ['campaign', 'news', 'notice', 'productNews'];

// 百度智能云公众号二维码
export const PORTAL_WX_QR_CODE = 'https://bce.bdstatic.com/p3m/common-service/uploads/qrcode_for_gh_9365d9d9caca_1280_7ca133a.jpg';
// 百度智能云B站二维码
export const PORTAL_BILI_QR_CODE = 'https://bce.bdstatic.com/p3m/common-service/uploads/portal_bilibili_9dacf77.png';


// 字母集合
export const LETTER_ARR = [
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
    'U', 'V', 'W', 'X', 'Y', 'Z', '其它',
];

export const SearchTypeTextMap = {
    [SearchType.ALL]: '全部',
    [SearchType.PRO_SOL]: '产品和解决方案',
    [SearchType.DOC]: '产品文档',
    [SearchType.MARKET]: '云市场',
    [SearchType.FORUM]: '论坛',
    [SearchType.SITE]: '网站',
    [SearchType.PRODUCT]: '产品',
    [SearchType.SOLUTION]: '解决方案',
};

// 用户默认头像
export const DEFAULT_AVATAR = 'https://bce.bdstatic.com/p3m/common-service/uploads/user_92f1c4a.png';

export const THEME_DETAIL_HOT_PRODUCTS = [
    {
        title: '云服务器BCC',
        link: 'https://cloud.baidu.com/product/bcc.html',
    },
    {
        title: '内容分发网络CDN',
        link: 'https://cloud.baidu.com/product/cdn.html',
    },
    {
        title: '弹性公网IP EIP',
        link: 'https://cloud.baidu.com/product/eip.html',
    },
    {
        title: '云磁盘CDS',
        link: 'https://cloud.baidu.com/product/cds.html',
    },
    {
        title: '对象存储BOS',
        link: 'https://cloud.baidu.com/product/bos.html',
    },
    {
        title: '智能门户 AIpage',
        link: 'https://cloud.baidu.com/product/aipage.html',
    },
    {
        title: '云虚拟主机BCH',
        link: 'https://cloud.baidu.com/product/bch.html',
    },
    {
        title: '简单消息服务SMS',
        link: 'https://cloud.baidu.com/product/sms.html',
    },
    {
        title: '数据可视化Sugar BI',
        link: 'https://cloud.baidu.com/product/sugar.html',
    },
];


// CDN base
export const CDN_URL_BASE = 'https://bce.bdstatic.com';
// BOS base
export const BOS_URL_BASE = 'https://bce-cdn.bj.bcebos.com';

export const CDN_URL_IMG_BASE_CLOUD_SERVER = `${CDN_URL_BASE}/portal-cloud-server/images/`;
export const BOS_URL_IMG_BASE_CLOUD_SERVER = `${BOS_URL_BASE}/portal-cloud-server/images/`;

export const CDN_URL_IMG_BASE_DEVELOPER = `${CDN_URL_BASE}/developer-static/imgs/`;
export const BOS_URL_IMG_BASE_DEVELOPER = `${BOS_URL_BASE}/developer-static/imgs/`;

export const HEADER_NAV_HEIGHT_MAP = {
    'pc': 60,
    'pcWrap': 60,
    'wap': 54,
};
export const PARIS_SID = 2103; // 玉门关渠道号


// 已迁移的开发者社区页面路由汇总
export const DEV_PAGE_URL_CONST = {
    ARTICLE: '/article', // ${order}-${pageNo}-${tagId} or {DetailId}
    ACTIVITY: '/devActivity', // ${category}-${status}-${pageNo}
    LIVE: '/devLive', // ${id}
};

// 开发者社区直播用
export const LIVE_QRCODE_MAP: {
    [props: string]: {
        talk?: string;
        wxPublic?: string;
    };
} = {
    '22': {
        talk: 'https://bce.bdstatic.com/p3m/common-service/uploads/%E5%B0%8F%E5%8A%A9%E6%89%8B%E4%BA%8C%E7%BB%B4%E7%A0%81_a6314d6.png',
        wxPublic: 'https://bce.bdstatic.com/p3m/common-service/uploads/%E7%99%BE%E5%BA%A6VR%E5%85%AC%E4%BC%97%E5%8F%B7_49bfc90.jpeg',
    },
    '24': {
        talk: 'https://bce.bdstatic.com/p3m/common-service/uploads/1641373557238_4dd05b3.png',
    },
    '25': {
        talk: 'https://bce.bdstatic.com/p3m/common-service/uploads/1641373557238_4dd05b3.png',
    },
    '26': {
        talk: 'https://bce.bdstatic.com/p3m/common-service/uploads/1641373557238_4dd05b3.png',
    },
    '28': {
        talk: `${DEVELOPER_CDN_URL_IMG_BASE}live/wx_talk_28.jpg`,
    },
    '35': {
        talk: `${DEVELOPER_CDN_URL_IMG_BASE}live/wx_talk_35.png`,
    },
};
export const LAZY_PLACEHOLDER_IMAGE = 'https://bce.bdstatic.com/portal-server/common/lazy-icon.svg';

// 曝光统计action
export const EXPOSURE_STATISTIC_ACTION = 'exposure';

// 组合购sessionStorage名称
export const GROUP_BUY = 'GROUP_BUY';

// 新组合购sessionStorage名称
export const GROUP_PURCHASE = 'GROUP_PURCHASE';

// 移动端 BCC 购买页 sessionStorage key
export const BCC_ORDER_DATA_KEY = 'BCC_ORDER_DATA_KEY';

// BCC产品组合项
export const BCC_PRODUCT_GROUP_NAME_MAP = {
    'BCC': '云服务器BCC',
    'CDS': '云磁盘CDS',
    'EIP': '弹性网络EIP',
};


export const PRODUCT_ORDER_SHA_KEY = Ioc(UEnvService).isProdOnline
    ? '07d92e84-7d59-46cf-84bf-e339b6d32e09'
    : '8b661235-ac07-478c-9b7d-a8ab463f8c89';
export const PRODUCT_PRICE_SHA_KEY = '28545491-f8f6-48b3-8634-330060bd5abc';

export const IS_PROD_ONLINE = Ioc(UEnvService).isProdOnline;
export const IS_PROD_SANDBOX = Ioc(UEnvService).isProdSandbox;

export const logoSvg = 'https://bce.bdstatic.com/portal-cloud-server/images/portal-logo.svg';

export const cmsSurveyZhEnMap: {[props: string]: string} = {
    '请正确填写手机号码': 'Please input the correct phone number',
    '请正确填写电子邮箱': 'Please input the correct email address',
    '输入有误，请输入数字': 'Input error, please input number',
    '输入的URL有误，请输入正确的URL地址': 'Please input the correct URL address',
    '输入有误，请输入字母': 'Input error, please input letters',
    '最大字数限制为': 'The maximum number of words is ',
    '上传失败，请重试': 'Failed to upload. Please try again',
    '网络错误': 'Network error',
    '此项必填，请填写后再提交': 'This is required',
    '未知错误': 'Unknown Error',
    '返回首页': 'Return Home Page',
    '活动已过期': 'Activity Expired',
    '未激活': 'Not Active',
    '抱歉，您的账户未激活': 'Sorry, your account hasn\'t been activated',
    '立即激活': 'Activate Now',
    '温馨提示': 'Reminder',
    '您已成功提交申请，请耐心等候': 'Sorry, you have already filled out the questionnaire',
    '未实名': 'Not Certified',
    '对不起，您还未实名认证': 'Sorry, you have not been certified',
    '前往实名认证': 'Go to Certification',
    '未登录': 'Not Logged In',
    '抱歉，您还没有登录': 'Sorry, you are not logged in yet',
    '马上登录': 'Log in',
    '验证码填写错误': 'Incorrect verification code',
    '网络问题': 'Network Problems',
    '网络问题，请稍后重试': 'Network problem, please try again later',
    '请选择': 'Please select',
    '客户类型': 'Customer Type',
    '公司名称': 'Company Name',
    '个人用户': 'Individual Customer',
    '验证失败': 'Authentication Failed',
    '身份证号和姓名匹配失败': 'ID number and name match failed',
    '身份证信息不正确': 'ID card information error',
    '输入错误，请输入正常内容': 'Input error, please input the normal content',
    '文件大小不能超过50M': 'File size cannot exceed 50M',
    '最多上传fileNumLimit个文件': 'The number of files cannot exceed fileNumLimit',
    'https://cloud.baidu.com': 'https://intl.cloud.baidu.com',
    'https://console.bce.baidu.com/': 'https://console.bce.baidu.com/?locale=en&lang=en',
    'https://console.bce.baidu.com/qualify/#/qualify/index': 'https://console.bce.baidu.com/qualify/?locale=en&lang=en#/qualify/index',
    'https://login.bce.baidu.com/?redirect=': 'https://login.bce.baidu.com/?locale=en&lang=en&redirect=',
    'https://login.bcetest.baidu.com/?redirect=': 'https://login.bcetest.baidu.com/?locale=en&lang=en&redirect=',
};

export const SUMMIT_RESULT_ICON: {
    [key: string]: string;
} = {
    success: 'https://bce.bdstatic.com/p3m/common-service/uploads/success_1a47100.svg',
    error: 'https://bce.bdstatic.com/p3m/common-service/uploads/error_4f41b4f.svg',
    warning: 'https://bce.bdstatic.com/p3m/common-service/uploads/warning_0f5731f.svg',
    info: 'https://bce.bdstatic.com/p3m/common-service/uploads/info_b82a6bc.svg',
};

export const DIALOG_MESSAGE_MAP = {
    'NO_LOGIN': {
        title: '未登录',
        content: '对不起，您还未登录，请完成登录后再参加活动',
        okText: '前往登录',
    },
    'NO_ACTIVE': {
        title: '未激活',
        content: '对不起，您还未激活，请完成激活后再参加活动',
        okText: '前往激活',
    },
    'NO_REALNAME': {
        title: '温馨提示',
        content: '对不起，您还未实名认证，请完成实名认证后再参与活动',
        okText: '前往实名认证',
    },
    'UNKNOWN_ERROR': {
        title: '未知错误',
        content: '未知错误，请稍后重试',
        okText: '返回活动',
    },
};

// DialogMessageMap: 弹窗提示语
// Fix: link若与window.location.href相关，直接写到Map中会触发“window is not defined”，改为触发点击事件后获取link值
export const PriceCardDialogMsgMap = {
    'NO_LOGIN': {
        title: '未登录',
        subTitle: '对不起，您还未登录，请完成登录后再参加活动',
        link: '',
        btnText: '前往登录',
    },
    'NO_ACTIVE': {
        title: '未激活',
        subTitle: '对不起，您还未激活，请完成激活后再参加活动',
        link: '',
        btnText: '前往激活',
    },
    'NO_REALNAME': {
        title: '温馨提示',
        subTitle: '对不起，您还未实名认证，请完成实名认证后再参与活动',
        link: '',
        btnText: '前往实名认证',
    },
    'UNKNOWN_ERROR': {
        title: '未知错误',
        subTitle: '未知错误，请稍后重试',
        link: '',
        btnText: '返回活动',
    },
};

export const navSeoData: Array<{
    text: string;
    link: string;
}> = [{
    text: '最新活动',
    link: 'https://cloud.baidu.com/campaign/PromotionActivity/index.html',
}, {
    text: '产品',
    link: 'https://cloud.baidu.com/products/index.html',
}, {
    text: '解决方案',
    link: 'https://cloud.baidu.com/solution/index.html',
}, {
    text: '千帆社区',
    link: 'https://cloud.baidu.com/qianfandev',
}, {
    text: 'AI原生应用商店',
    link: 'https://qianfanmarket.baidu.com/',
}, {
    text: '企业服务',
    link: 'https://qifu.baidu.com',
}, {
    text: '云市场',
    link: 'https://market.baidu.com',
}, {
    text: '合作与生态',
    link: 'https://cloud.baidu.com/partner/plan.html',
}, {
    text: '开发者',
    link: 'https://developer.baidu.com',
}, {
    text: '服务与支持',
    link: 'https://cloud.baidu.com/doc/support/index.html',
}, {
    text: '了解智能云',
    link: 'https://cloud.baidu.com/cloudai.html',
}, {
    text: '备案',
    link: 'https://cloud.baidu.com/beian/index.html',
}, {
    text: '文档',
    link: 'https://cloud.baidu.com/doc/index.html',
}, {
    text: '管理控制台',
    link: 'https://console.bce.baidu.com',
}];

// 指定产品获解决方案页圆角修改
export const PRODUCT_SOLUTION_CORNER_LIST = [
    '/solution/qianfanplatform.html',
    '/solution/aihc.html',
    '/solution/intelligentservice.html',
    '/solution/xiling.html',
    '/solution/cloud/zhisuancenter.html',
    '/solution/manufacture/safetyplatform.html',
    '/solution/sccvrci.html',
    '/solution/jirong.html',
    '/solution/autopilot.html',
    '/solution/powergridai.html',
    '/solution/city/one-network.html',
    '/solution/2024world.html',
];
