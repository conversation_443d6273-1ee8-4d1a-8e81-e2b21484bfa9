/* eslint-disable max-len */
// 峰会专用：峰会名与模块名映射
import dynamic from 'next/dynamic';
import {ComponentType} from 'react';

// const AICloudSummit2023New = dynamic(
//     () => import('@components/summit/AICloudSummit2023/AICloudSummit2023New').then(mod => mod.AICloudSummit2023New)
// );
// const AICloudSummit2023NewWap = dynamic(
//     () => import('@components/summit/AICloudSummit2023/AICloudSummit2023NewWap').then(mod => mod.AICloudSummit2023NewWap)
// );
const AICloudSummit2023Live = dynamic(
    () => import('@components/summit/AICloudSummit2023/AICloudSummit2023Live').then(mod => mod.AICloudSummit2023Live)
);
const AICloudSummit2023LiveWap = dynamic(
    () => import('@components/summit/AICloudSummit2023/AICloudSummit2023LiveWap').then(mod => mod.AICloudSummit2023LiveWap)
);
const AICloudSummit2023Forum = dynamic(
    () => import('@components/summit/AICloudSummit2023/AICloudSummit2023Forum').then(mod => mod.AICloudSummit2023Forum)
);
const AICloudSummit2023ForumWap = dynamic(
    () => import('@components/summit/AICloudSummit2023/AICloudSummit2023ForumWap').then(mod => mod.AICloudSummit2023ForumWap)
);
const AIComputing2023 = dynamic(() => import('@components/summit/AIComputing2023/AIComputing2023').then(mod => mod.AIComputing2023));
const AIComputing2023Wap = dynamic(() => import('@components/summit/AIComputing2023/AIComputing2023Wap').then(mod => mod.AIComputing2023Wap));

// 2024 310生态大会
const Ecology2024 = dynamic(() => import('@components/summit/ecology2024/ecology2024').then(mod => mod.Ecology2024));
const Ecology2024Wap = dynamic(() => import('@components/summit/ecology2024/ecology2024Wap').then(mod => mod.Ecology2024Wap));
const Ecology2024Forum = dynamic(() => import('@components/summit/ecology2024/Ecology2024Forum').then(mod => mod.Ecology2024Forum));
const Ecology2024ForumWap = dynamic(() => import('@components/summit/ecology2024/Ecology2024ForumWap').then(mod => mod.Ecology2024ForumWap));

// 2024 322AICloudDay
const AICloudDay = dynamic(() => import('@components/summit/aiCloudDay2024/aiCloudDay').then(mod => mod.AICloudDay));
const AICloudDayWap = dynamic(() => import('@components/summit/aiCloudDay2024/aiCloudDayWap').then(mod => mod.AICloudDayWap));

// 2024 智能经济大会
const IntelligentEconomy2024 = dynamic(() => import('@components/summit/IntelligentEconomy2024/IntelligentEconomy2024').then(mod => mod.IntelligentEconomy2024));
const IntelligentEconomy2024Wap = dynamic(() => import('@components/summit/IntelligentEconomy2024/IntelligentEconomy2024Wap').then(mod => mod.IntelligentEconomy2024Wap));

// 2024 云智大会-城市峰会
const AIcloudcitysummit2024 = dynamic(() => import('@components/summit/AIcloudcitysummit2024/AIcloudcitysummit2024').then(mod => mod.AIcloudcitysummit2024));
const AIcloudcitysummit2024Wap = dynamic(() => import('@components/summit/AIcloudcitysummit2024/AIcloudcitysummit2024Wap').then(mod => mod.AIcloudcitysummit2024Wap));

// 2024 云智大会
const AICloudSummit2024 = dynamic(
    () => import('@components/summit/AICloudSummit2024/AICloudSummit2024').then(mod => mod.AICloudSummit2024)
);
const AICloudSummit2024Wap = dynamic(
    () => import('@components/summit/AICloudSummit2024/AICloudSummit2024Wap').then(mod => mod.AICloudSummit2024Wap)
);
const AICloudSummit2024Official = dynamic(
    () => import('@components/summit/AICloudSummit2024/AICloudSummit2024Official').then(mod => mod.AICloudSummit2024Official)
);
const AICloudSummit2024OfficialWap = dynamic(
    () => import('@components/summit/AICloudSummit2024/AICloudSummit2024OfficialWap').then(mod => mod.AICloudSummit2024OfficialWap)
);
const AICloudSummit2024Agenda = dynamic(
    () => import('@components/summit/AICloudSummit2024/SecondPage/Agenda').then(mod => mod.AICloudSummit2024Agenda)
);
const AICloudSummit2024AgendaWap = dynamic(
    () => import('@components/summit/AICloudSummit2024/SecondPage/AgendaWap').then(mod => mod.AICloudSummit2024AgendaWap)
);
const AICloudSummit2024Venue = dynamic(
    () => import('@components/summit/AICloudSummit2024/SecondPage/Venue').then(mod => mod.AICloudSummit2024Venue)
);
const AICloudSummit2024VenueWap = dynamic(
    () => import('@components/summit/AICloudSummit2024/SecondPage/VenueWap').then(mod => mod.AICloudSummit2024VenueWap)
);

// 2025 智能经济大会
const IntelligentEconomy2025 = dynamic(
    () => import('@components/summit/IntelligentEconomy2025/IntelligentEconomy2025').then(mod => mod.IntelligentEconomy2025)
);
const IntelligentEconomy2025Wap = dynamic(
    () => import('@components/summit/IntelligentEconomy2025/IntelligentEconomy2025Wap').then(mod => mod.IntelligentEconomy2025Wap)
);
// 2025 云智大会
const AICloudSummit2025 = dynamic(
    () => import('@components/summit/AICloudSummit2025/AICloudSummit2025').then(mod => mod.AICloudSummit2025)
);
const AICloudSummit2025Wap = dynamic(
    () => import('@components/summit/AICloudSummit2025/AICloudSummit2025Wap').then(mod => mod.AICloudSummit2025Wap)
);

const AICloudSummit20250925 = dynamic(
    () => import('@components/summit/AICloudSummit20250925/AICloudSummit2025').then(mod => mod.AICloudSummit2025)
);
const AICloudSummit20250925Wap = dynamic(
    () => import('@components/summit/AICloudSummit20250925/AICloudSummit2025Wap').then(mod => mod.AICloudSummit2025Wap)
);

const AICloudSummit2025BuyTicket = dynamic(
    () => import('@components/summit/AICloudSummit20250925/SecondPage/BuyTicket').then(mod => mod.AICloudSummit2025BuyTicket)
);
const AICloudSummit2025BuyTicketWap = dynamic(
    () => import('@components/summit/AICloudSummit20250925/SecondPage/BuyTicketWap').then(mod => mod.AICloudSummit2025BuyTicketWap)
);

const AICloudSummit2025Agenda = dynamic(
    () => import('@components/summit/AICloudSummit20250925/SecondPage/Agenda').then(mod => mod.AICloudSummit2025Agenda)
);
const AICloudSummit2025AgendaWap = dynamic(
    () => import('@components/summit/AICloudSummit20250925/SecondPage/AgendaWap').then(mod => mod.AICloudSummit2025AgendaWap)
);

const AICloudSummit2025Venue = dynamic(
    () => import('@components/summit/AICloudSummit20250925/SecondPage/Venue').then(mod => mod.AICloudSummit2025Venue)
);
const AICloudSummit2025VenueWap = dynamic(
    () => import('@components/summit/AICloudSummit20250925/SecondPage/VenueWap').then(mod => mod.AICloudSummit2025VenueWap)
);

export const summitPageMap: {
    [props: string]: {
        pc: ComponentType<any>;
        wap: ComponentType<any>;
    };
} = {
    // AIcloudsummit2023: {pc: AICloudSummit2023, wap: AICloudSummit2023Wap},
    AIcloudsummit2023: {pc: AICloudSummit2023Live, wap: AICloudSummit2023LiveWap},
    AIcloudsummit2023Live: {pc: AICloudSummit2023Live, wap: AICloudSummit2023LiveWap},
    AIcloudsummit2023New: {pc: AICloudSummit2023Live, wap: AICloudSummit2023LiveWap}, // 主论坛直播后验收页面
    'AIcloudsummit2023_forum': {pc: AICloudSummit2023Forum, wap: AICloudSummit2023ForumWap},
    aicomputing2023: {pc: AIComputing2023, wap: AIComputing2023Wap},
    aicomputing2023Live: {pc: AIComputing2023, wap: AIComputing2023Wap},
    ecology2024: {pc: Ecology2024, wap: Ecology2024Wap},
    ecology2024Live: {pc: Ecology2024, wap: Ecology2024Wap},
    'ecology2024_forum': {pc: Ecology2024Forum, wap: Ecology2024ForumWap},
    aicloudday: {pc: AICloudDay, wap: AICloudDayWap},
    aiclouddayLive: {pc: AICloudDay, wap: AICloudDayWap},
    intelligentEconomy2024: {pc: IntelligentEconomy2024, wap: IntelligentEconomy2024Wap},
    intelligentEconomy2024Live: {pc: IntelligentEconomy2024, wap: IntelligentEconomy2024Wap},
    AIcloudcitysummit2024: {pc: AIcloudcitysummit2024, wap: AIcloudcitysummit2024Wap},
    AIcloudcitysummit2024Live: {pc: AIcloudcitysummit2024, wap: AIcloudcitysummit2024Wap},
    // AIcloudsummit2024: {pc: AICloudSummit2024, wap: AICloudSummit2024Wap}, // 预热版
    AIcloudsummit2024: {pc: AICloudSummit2024Official, wap: AICloudSummit2024OfficialWap},
    AIcloudsummit20240925: {pc: AICloudSummit2024, wap: AICloudSummit2024Wap}, // 2024预热版使用
    'AIcloudsummit2024_agenda': {pc: AICloudSummit2024Agenda, wap: AICloudSummit2024AgendaWap},
    'AIcloudsummit2024_venue': {pc: AICloudSummit2024Venue, wap: AICloudSummit2024VenueWap},
    intelligentEconomy2025: {pc: IntelligentEconomy2025, wap: IntelligentEconomy2025Wap},
    intelligentEconomy2025Live: {pc: IntelligentEconomy2025, wap: IntelligentEconomy2025Wap},
    AIcloudsummit20250925: {pc: AICloudSummit2025, wap: AICloudSummit2025Wap},
    AIcloudsummit2025: {pc: AICloudSummit20250925, wap: AICloudSummit20250925Wap},
    'AIcloudsummit2025_tickets': {pc: AICloudSummit2025BuyTicket, wap: AICloudSummit2025BuyTicketWap},
    'AIcloudsummit2025_agenda': {pc: AICloudSummit2025Agenda, wap: AICloudSummit2025AgendaWap},
    'AIcloudsummit2025_venue': {pc: AICloudSummit2025Venue, wap: AICloudSummit2025VenueWap},
};
