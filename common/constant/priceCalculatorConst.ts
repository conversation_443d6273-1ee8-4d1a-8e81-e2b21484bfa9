/**
 * @file 价格计算器map文件
 */

import {PriceCalculatorMapObj} from '@common/interface/calculator';
import {PriceCalculatorTest, PriceDetailTest} from '@components/priceCalculator/moduleTest';
// import BccPrice from '@components/priceCalculator/bcc/origin';
import BccPrice from '@components/priceCalculator/bcc';
import {QianfanCalculator} from '@components/priceCalculator/modules/qianfan';
import {EipPriceCalculator, EipPriceDetail} from '@components/priceCalculator/modules/eip';
import RdsPriceCalculator from '@components/priceCalculator/modules/rds/RdsPriceCalculator/RdsPriceCalculator';
import RdsPriceDetail from '@components/priceCalculator/modules/rds/RdsPriceDetail/RdsPriceDetail';
import VdbPriceCalculator from '@components/priceCalculator/modules/vdb/VdbPriceCalculator/VdbPriceCalculator';
import VdbPriceDetail from '@components/priceCalculator/modules/vdb/VdbPriceDetail/VdbPriceDetail';
import ScsPriceCalculator from '@components/priceCalculator/modules/scs/ScsPriceCalculator/ScsPriceCalculator';
import ScsPriceDetail from '@components/priceCalculator/modules/scs/ScsPriceDetail/ScsPriceDetail';
import VpnPriceCalculator from '@components/priceCalculator/modules/vpn/Calculator/Calculator';
import VpnPriceDetail from '@components/priceCalculator/modules/vpn/Detail/Detail';
import {EipgroupPriceCalculator, EipgroupDetail} from '@components/priceCalculator/modules/eipgroup';
import {BlbPriceCalculator, BlbPriceDetail, BlbPriceEstimateCalculator} from '@components/priceCalculator/modules/blb';
import {BlsPriceCalculator, BlsPriceDetail} from '@components/priceCalculator/modules/bls';
import {BmrPriceCalculator, BmrPriceDetail} from '@components/priceCalculator/modules/bmr';
import {NatPriceCalculator, NatPriceDetail} from '@components/priceCalculator/modules/nat';
import {BecCalculator, BecDetail} from '@components/priceCalculator/modules/bec';
import {DwzPriceCalculator, DwzPriceDetail} from '@components/priceCalculator/modules/dwz';
import {BosPriceCalculator, BosPriceDetail} from '@components/priceCalculator/modules/bos';
import {CfsPriceCalculator, CfsPriceDetail} from '@components/priceCalculator/modules/cfs';
import {PaloPriceCalculator, PaloPriceDetail} from '@components/priceCalculator/modules/palo';
import {OcrCalculator, OCRPriceDetail, FaceCalculator, FacePriceDetail} from '@components/priceCalculator/modules/ocr';
import BccPriceTest from '@components/priceCalculator/bcc/test';
import LsPrice from '@components/priceCalculator/modules/ls';
import CdsPrice from '@components/priceCalculator/modules/cds';
import CdsPriceTest from '@components/priceCalculator/modules/cds/test';
import BesPriceCalculator from '@components/priceCalculator/modules/bes/BesPriceCalculator/BesPriceCalculator';
import BesPriceDetail from '@components/priceCalculator/modules/bes/BesPriceDetail/BesPriceDetail';
import GaiadbPriceCalculator from '@components/priceCalculator/modules/gaiadb/PriceCalculator';
import GaiadbPriceDetail from '@components/priceCalculator/modules/gaiadb/PriceDetail';
import {PfsPriceCalculator, PfsPriceDetail} from '@components/priceCalculator/modules/pfs';

export const priceCalculatorMap: PriceCalculatorMapObj = {
    ocr: {
        priceCalculator: {
            comp: OcrCalculator,
        },
        priceDetail: {
            comp: OCRPriceDetail,
        },
    },
    face: {
        priceCalculator: {
            comp: FaceCalculator,
        },
        priceDetail: {
            comp: FacePriceDetail,
        },
    },
    speech: {
        priceCalculator: {
            comp: OcrCalculator,
        },
        priceDetail: {
            comp: OCRPriceDetail,
        },
    },
    imagerecognition: {
        priceCalculator: {
            comp: OcrCalculator,
        },
        priceDetail: {
            comp: OCRPriceDetail,
        },
    },
    nlp: {
        priceCalculator: {
            comp: OcrCalculator,
        },
        priceDetail: {
            comp: OCRPriceDetail,
        },
    },
    antiporn: {
        priceCalculator: {
            comp: OcrCalculator,
        },
        priceDetail: {
            comp: OCRPriceDetail,
        },
    },
    intelligentwriting: {
        priceCalculator: {
            comp: OcrCalculator,
        },
        priceDetail: {
            comp: OCRPriceDetail,
        },
    },
    machinetranslation: {
        priceCalculator: {
            comp: OcrCalculator,
        },
        priceDetail: {
            comp: OCRPriceDetail,
        },
    },
    imagesearch: {
        priceCalculator: {
            comp: OcrCalculator,
        },
        priceDetail: {
            comp: OCRPriceDetail,
        },
    },
    imageprocess: {
        priceCalculator: {
            comp: OcrCalculator,
        },
        priceDetail: {
            comp: OCRPriceDetail,
        },
    },
    body: {
        priceCalculator: {
            comp: OcrCalculator,
        },
        priceDetail: {
            comp: OCRPriceDetail,
        },
    },
    test: {
        priceCalculator: {
            comp: PriceCalculatorTest,
        },
        priceDetail: {
            comp: PriceDetailTest,
        },
    },
    bcc: {
        priceCalculator: {
            comp: BccPrice.PriceCalculator,
        },
        priceDetail: {
            comp: BccPrice.PriceDetail,
        },
    },
    // eslint-disable-next-line camelcase
    bcc_test: {
        priceCalculator: {
            comp: BccPriceTest.PriceCalculator,
        },
        priceDetail: {
            comp: BccPriceTest.PriceDetail,
        },
    },
    // 原始的 bcc 组件, 现在被上面的 bcc 取代
    // bcc: {
    //     priceCalculator: {
    //         comp: BccPrice.PriceCalculator,
    //     },
    //     priceDetail: {
    //         comp: BccPrice.PriceDetail,
    //     },
    // },
    cds: {
        priceCalculator: {
            comp: CdsPrice.PriceCalculator,
        },
        priceDetail: {
            comp: CdsPrice.PriceDetail,
        },
    },
    // eslint-disable-next-line camelcase
    cds_test: {
        priceCalculator: {
            comp: CdsPriceTest.PriceCalculator,
        },
        priceDetail: {
            comp: CdsPriceTest.PriceDetail,
        },
    },
    qianfan: {
        priceCalculator: {
            comp: QianfanCalculator,
        },
        priceDetail: {
            comp: PriceDetailTest,
        },
    },
    eip: {
        priceCalculator: {
            comp: EipPriceCalculator as any,
        },
        priceDetail: {
            comp: EipPriceDetail,
        },
    },
    rds: {
        priceCalculator: {
            comp: RdsPriceCalculator,
        },
        priceDetail: {
            comp: RdsPriceDetail,
        },
    },
    vdb: {
        priceCalculator: {
            comp: VdbPriceCalculator,
        },
        priceDetail: {
            comp: VdbPriceDetail,
        },
    },
    scs: {
        priceCalculator: {
            comp: ScsPriceCalculator,
        },
        priceDetail: {
            comp: ScsPriceDetail,
        },
    },
    vpn: {
        priceCalculator: {
            comp: VpnPriceCalculator,
        },
        priceDetail: {
            comp: VpnPriceDetail,
        },
    },
    eipgroup: {
        priceCalculator: {
            comp: EipgroupPriceCalculator as any,
        },
        priceDetail: {
            comp: EipgroupDetail,
        },
    },
    blb: {
        priceCalculator: {
            comp: BlbPriceCalculator,
        },
        priceDetail: {
            comp: BlbPriceDetail,
        },
        priceEstimateDetail: {
            comp: BlbPriceEstimateCalculator,
        },
    },
    bls: {
        priceCalculator: {
            comp: BlsPriceCalculator,
        },
        priceDetail: {
            comp: BlsPriceDetail,
        },
    },
    bmr: {
        priceCalculator: {
            comp: BmrPriceCalculator,
        },
        priceDetail: {
            comp: BmrPriceDetail,
        },
    },
    nat: {
        priceCalculator: {
            comp: NatPriceCalculator,
        },
        priceDetail: {
            comp: NatPriceDetail,
        },
    },
    bec: {
        priceCalculator: {
            comp: BecCalculator,
        },
        priceDetail: {
            comp: BecDetail,
        },
    },
    dwz: {
        priceCalculator: {
            comp: DwzPriceCalculator,
        },
        priceDetail: {
            comp: DwzPriceDetail,
        },
    },
    ls: {
        priceCalculator: {
            comp: LsPrice.PriceCalculator,
        },
        priceDetail: {
            comp: LsPrice.PriceDetail,
        },
    },
    bos: {
        priceCalculator: {
            comp: BosPriceCalculator,
        },
        priceDetail: {
            comp: BosPriceDetail,
        },
    },
    bes: {
        priceCalculator: {
            comp: BesPriceCalculator,
        },
        priceDetail: {
            comp: BesPriceDetail,
        },
    },
    palo: {
        priceCalculator: {
            comp: PaloPriceCalculator as any,
        },
        priceDetail: {
            comp: PaloPriceDetail,
        },
    },
    gaiadb: {
        priceCalculator: {
            comp: GaiadbPriceCalculator,
        },
        priceDetail: {
            comp: GaiadbPriceDetail,
        },
    },
    pfs: {
        priceCalculator: {
            comp: PfsPriceCalculator,
        },
        priceDetail: {
            comp: PfsPriceDetail,
        },
    },
    cfs: {
        priceCalculator: {
            comp: CfsPriceCalculator,
        },
        priceDetail: {
            comp: CfsPriceDetail,
        },
    },
};
