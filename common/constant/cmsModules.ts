import {Block} from '@common/interface/blocks';
import dynamic from 'next/dynamic';
import {ComponentType} from 'react';

const CodePanel = dynamic<Block>(() => import('@components/cms/CodePanel/CodePanel').then(mod => mod.CodePanel));
const Markdown = dynamic<Block>(() => import('@components/cms/Markdown/Markdown').then(mod => mod.Markdown));
const V5S001 = dynamic<Block>(() => import('@components/cms/V5S001/V5S001').then(mod => mod.V5S001));
const V5S001Wap = dynamic<Block>(() => import('@components/cms/V5S001/V5S001Wap').then(mod => mod.V5S001Wap));
const V5G015 = dynamic<Block>(() => import('@components/cms/V5G015/V5G015').then(mod => mod.V5G015));
const V5G015B = dynamic<Block>(() => import('@components/cms/V5G015/V5G015B').then(mod => mod.V5G015B));
const V5G015C = dynamic<Block>(() => import('@components/cms/V5G015/V5G015C').then(mod => mod.V5G015C));
const V5G015D = dynamic<Block>(() => import('@components/cms/V5G015/V5G015D').then(mod => mod.V5G015D));
const V5G015E = dynamic<Block>(() => import('@components/cms/V5G015/V5G015E').then(mod => mod.V5G015E));
const V5B014B = dynamic<Block>(() => import('@components/cms/V5B014B/V5B014B').then(mod => mod.V5B014B));
const V5B014BWap = dynamic<Block>(() => import('@components/cms/V5B014B/V5B014BWap').then(mod => mod.V5B014BWap));
const V1SS001 = dynamic<Block>(() => import('@components/cms/V1SS001/V1SS001').then(mod => mod.V1SS001));
const V1SS001Wap = dynamic<Block>(() => import('@components/cms/V1SS001/V1SS001Wap').then(mod => mod.V1SS001Wap));
const V5G001 = dynamic<Block>(() => import('@components/cms/V5G001/V5G001').then(mod => mod.V5G001));
const V5G001Wap = dynamic<Block>(() => import('@components/cms/V5G001/V5G001Wap').then(mod => mod.V5G001Wap));
const V5G004A = dynamic<Block>(() => import('@components/cms/V5G004/V5G004A').then(mod => mod.V5G004A));
const V5G004D = dynamic<Block>(() => import('@components/cms/V5G004/V5G004D').then(mod => mod.V5G004D));
const V5G005A = dynamic<Block>(() => import('@components/cms/V5G004/V5G005A').then(mod => mod.V5G005A));
const V5G005B = dynamic<Block>(() => import('@components/cms/V5G004/V5G005B').then(mod => mod.V5G005B));
const V5G007A = dynamic<Block>(() => import('@components/cms/V5B002/V5G007A').then(mod => mod.V5G007A));
const V5G007AWap = dynamic<Block>(() => import('@components/cms/V5B002/V5G007AWap').then(mod => mod.V5G007AWap));
const V5G008 = dynamic<Block>(() => import('@components/cms/V5G008/V5G008').then(mod => mod.V5G008));
const V5G008Wap = dynamic<Block>(() => import('@components/cms/V5G008/V5G008Wap').then(mod => mod.V5G008Wap));
const V5G009A = dynamic<Block>(() => import('@components/cms/V5G009/V5G009A').then(mod => mod.V5G009A));
const V5G010 = dynamic<Block>(() => import('@components/cms/V5G010/V5G010').then(mod => mod.V5G010));
const V5G010Wap = dynamic<Block>(() => import('@components/cms/V5G010/V5G010Wap').then(mod => mod.V5G010Wap));
const V5G024 = dynamic<Block>(() => import('@components/cms/V5G024/V5G024').then(mod => mod.V5G024));
const V5G024Wap = dynamic<Block>(() => import('@components/cms/V5G024/V5G024Wap').then(mod => mod.V5G024Wap));
const V5G025 = dynamic<Block>(() => import('@components/cms/V5G025/V5G025').then(mod => mod.V5G025));
const V5B001 = dynamic<Block>(() => import('@components/cms/V5B001/V5B001').then(mod => mod.V5B001));
const V5B001Wap = dynamic<Block>(() => import('@components/cms/V5B001/V5B001Wap').then(mod => mod.V5B001Wap));
const V5B001B = dynamic<Block>(() => import('@components/cms/V5B001/V5B001B').then(mod => mod.V5B001B));
const V5B001BWap = dynamic<Block>(() => import('@components/cms/V5B001/V5B001BWap').then(mod => mod.V5B001BWap));
const V5B002 = dynamic<Block>(() => import('@components/cms/V5B002/V5B002').then(mod => mod.V5B002));
const V5B002Wap = dynamic<Block>(() => import('@components/cms/V5B002/V5B002Wap').then(mod => mod.V5B002Wap));
const V5B003A = dynamic<Block>(() => import('@components/cms/V5B003/V5B003A').then(mod => mod.V5B003A));
const V5B003AWap = dynamic<Block>(() => import('@components/cms/V5B003/V5B003AWap').then(mod => mod.V5B003AWap));
const V5B003B = dynamic<Block>(() => import('@components/cms/V5B003/V5B003B').then(mod => mod.V5B003B));
const V5B003BWap = dynamic<Block>(() => import('@components/cms/V5B003/V5B003BWap').then(mod => mod.V5B003BWap));
const V5B005 = dynamic<Block>(() => import('@components/cms/V5B002/V5B005').then(mod => mod.V5B005));
const V5B005Wap = dynamic<Block>(() => import('@components/cms/V5B002/V5B005Wap').then(mod => mod.V5B005Wap));
const V5B008 = dynamic<Block>(() => import('@components/cms/V5B008/V5B008').then(mod => mod.V5B008));
const V5B008Wap = dynamic<Block>(() => import('@components/cms/V5B008/V5B008Wap').then(mod => mod.V5B008Wap));
const V5B009 = dynamic<Block>(() => import('@components/cms/V5B009/V5B009').then(mod => mod.V5B009));
const V5B009Wap = dynamic<Block>(() => import('@components/cms/V5B009/V5B009Wap').then(mod => mod.V5B009Wap));
const V5B010 = dynamic<Block>(() => import('@components/cms/V5B010/V5B010').then(mod => mod.V5B010));
const V5B010Wap = dynamic<Block>(() => import('@components/cms/V5B010/V5B010Wap').then(mod => mod.V5B010Wap));
const V5B011 = dynamic<Block>(() => import('@components/cms/V5B011/V5B011').then(mod => mod.V5B011));
const V5B016 = dynamic<Block>(() => import('@components/cms/V5B016/V5B016').then(mod => mod.V5B016));
const V5B016Wap = dynamic<Block>(() => import('@components/cms/V5B016/V5B016Wap').then(mod => mod.V5B016Wap));
const V5B017 = dynamic<Block>(() => import('@components/cms/V5B017/V5B017').then(mod => mod.V5B017));
const V5B017Wap = dynamic<Block>(() => import('@components/cms/V5B017/V5B017Wap').then(mod => mod.V5B017Wap));
const V5B018 = dynamic<Block>(() => import('@components/cms/V5B018/V5B018').then(mod => mod.V5B018));
const V5B018Wap = dynamic<Block>(() => import('@components/cms/V5B018/V5B018Wap').then(mod => mod.V5B018Wap));
const V5G011 = dynamic<Block>(() => import('@components/cms/V5G011/V5G011').then(mod => mod.V5G011));
const V5G011Wap = dynamic<Block>(() => import('@components/cms/V5G011/V5G011Wap').then(mod => mod.V5G011Wap));
const V5G012 = dynamic<Block>(() => import('@components/cms/V5G012/V5G012').then(mod => mod.V5G012));
const V5G012Wap = dynamic<Block>(() => import('@components/cms/V5G012/V5G012Wap').then(mod => mod.V5G012Wap));
const V5G013 = dynamic<Block>(() => import('@components/cms/V5G013/V5G013').then(mod => mod.V5G013));
const V5G013Wap = dynamic<Block>(() => import('@components/cms/V5G013/V5G013Wap').then(mod => mod.V5G013Wap));
const V5G014 = dynamic<Block>(() => import('@components/cms/V5G014/V5G014').then(mod => mod.V5G014));
const V5G014Wap = dynamic<Block>(() => import('@components/cms/V5G014/V5G014Wap').then(mod => mod.V5G014Wap));
const V1DS001 = dynamic<Block>(() => import('@components/cms/V1DS001/V1DS001').then(mod => mod.V1DS001));
const V1DS001Wap = dynamic<Block>(() => import('@components/cms/V1DS001/V1DS001Wap').then(mod => mod.V1DS001Wap));
const V1DS002 = dynamic<Block>(() => import('@components/cms/V1DS002/V1DS002').then(mod => mod.V1DS002));
const V1DS002Wap = dynamic<Block>(() => import('@components/cms/V1DS002/V1DS002Wap').then(mod => mod.V1DS002Wap));
const V1DG002 = dynamic<Block>(() => import('@components/cms/V1DG002/V1DG002').then(mod => mod.V1DG002));
const V1DG002Wap = dynamic<Block>(() => import('@components/cms/V1DG002/V1DG002Wap').then(mod => mod.V1DG002Wap));
const V1DG003 = dynamic<Block>(() => import('@components/cms/V1DG003/V1DG003').then(mod => mod.V1DG003));
const V1DG003Wap = dynamic<Block>(() => import('@components/cms/V1DG003/V1DG003Wap').then(mod => mod.V1DG003Wap));
const V1DG004 = dynamic<Block>(() => import('@components/cms/V1DG004/V1DG004').then(mod => mod.V1DG004));
const V1DG004Wap = dynamic<Block>(() => import('@components/cms/V1DG004/V1DG004Wap').then(mod => mod.V1DG004Wap));
const V5G016 = dynamic<Block>(() => import('@components/cms/V5G016/V5G016').then(mod => mod.V5G016));
const V5G016Wap = dynamic<Block>(() => import('@components/cms/V5G016/V5G016Wap').then(mod => mod.V5G016Wap));
const V5G017 = dynamic<Block>(() => import('@components/cms/V5G017/V5G017').then(mod => mod.V5G017));
const V5G017Wap = dynamic<Block>(() => import('@components/cms/V5G017/V5G017Wap').then(mod => mod.V5G017Wap));
const V5G018A = dynamic<Block>(() => import('@components/cms/V5G018A/V5G018A').then(mod => mod.V5G018A));
const V5G018AWap = dynamic<Block>(() => import('@components/cms/V5G018A/V5G018AWap').then(mod => mod.V5G018AWap));
const V5G018B = dynamic<Block>(() => import('@components/cms/V5G018B/V5G018B').then(mod => mod.V5G018B));
const V5G018BWap = dynamic<Block>(() => import('@components/cms/V5G018B/V5G018BWap').then(mod => mod.V5G018BWap));
const V5G019 = dynamic<Block>(() => import('@components/cms/V5G019/V5G019').then(mod => mod.V5G019));
const V5G019Wap = dynamic<Block>(() => import('@components/cms/V5G019/V5G019Wap').then(mod => mod.V5G019Wap));
const V5G020 = dynamic<Block>(() => import('@components/cms/V5G020/V5G020').then(mod => mod.V5G020));
const V5G020Wap = dynamic<Block>(() => import('@components/cms/V5G020/V5G020Wap').then(mod => mod.V5G020Wap));
const V5G020B = dynamic<Block>(() => import('@components/cms/V5G020B/V5G020B').then(mod => mod.V5G020B));
const V5G020BWap = dynamic<Block>(() => import('@components/cms/V5G020B/V5G020BWap').then(mod => mod.V5G020BWap));
const V5G021 = dynamic<Block>(() => import('@components/cms/V5G021/V5G021').then(mod => mod.V5G021));
const V5G021Wap = dynamic<Block>(() => import('@components/cms/V5G021/V5G021Wap').then(mod => mod.V5G021Wap));
const V5G022A = dynamic<Block>(() => import('@components/cms/V5G022/V5G022A/V5G022A').then(mod => mod.V5G022A));
const V5G022AWap = dynamic<Block>(() => import('@components/cms/V5G022/V5G022A/V5G022AWap').then(mod => mod.V5G022AWap));
const V5G022B = dynamic<Block>(() => import('@components/cms/V5G022/V5G022B/V5G022B').then(mod => mod.V5G022B));
const V5G022BWap = dynamic<Block>(() => import('@components/cms/V5G022/V5G022B/V5G022BWap').then(mod => mod.V5G022BWap));
const V5G023 = dynamic<Block>(() => import('@components/cms/V5G023/V5G023').then(mod => mod.V5G023));
const V5G023Wap = dynamic<Block>(() => import('@components/cms/V5G023/V5G023Wap').then(mod => mod.V5G023Wap));
const V5G026 = dynamic<Block>(() => import('@components/cms/V5G026/V5G026').then(mod => mod.V5G026));
const V5G026Wap = dynamic<Block>(() => import('@components/cms/V5G026/V5G026Wap').then(mod => mod.V5G026Wap));
const V5G027 = dynamic(() => import('@components/cms/V5G027/V5G027').then(mod => mod.V5G027) as any);
const V5G027Wap = dynamic(() => import('@components/cms/V5G027/V5G027Wap').then(mod => mod.V5G027Wap as any));
const V5G028 = dynamic(() => import('@components/cms/V5G028/V5G028').then(mod => mod.V5G028));
const V5G028Wap = dynamic(() => import('@components/cms/V5G028/V5G028Wap').then(mod => mod.V5G028Wap));
const V5G029 = dynamic(() => import('@components/cms/V5G029/V5G029').then(mod => mod.V5G029));
const V5G029Wap = dynamic(() => import('@components/cms/V5G029/V5G029Wap').then(mod => mod.V5G029Wap));
const V5G030 = dynamic(() => import('@components/cms/V5G030/V5G030').then(mod => mod.V5G030));
const V5G030Wap = dynamic(() => import('@components/cms/V5G030/V5G030Wap').then(mod => mod.V5G030Wap));
const V5S004 = dynamic(() => import('@components/cms/V5S004/V5S004').then(mod => mod.V5S004));
const V5C002 = dynamic(() => import('@components/cms/V5C002/V5C002').then(mod => mod.V5C002));
const V5C002Wap = dynamic(() => import('@components/cms/V5C002/V5C002Wap').then(mod => mod.V5C002Wap));
const V1SG003 = dynamic(() => import('@components/cms/V1SG003/V1SG003').then(mod => mod.V1SG003));
const V1SG003Wap = dynamic(() => import('@components/cms/V1SG003/V1SG003Wap').then(mod => mod.V1SG003Wap));
const V1EG001 = dynamic(() => import('@components/cms/V1EG001/V1EG001').then(mod => mod.V1EG001));
const V1EG002A = dynamic(() => import('@components/cms/V1EG002A/V1EG002A').then(mod => mod.V1EG002A));
const V1EG002B = dynamic(() => import('@components/cms/V1EG002B/V1EG002B').then(mod => mod.V1EG002B));
const V1EG003 = dynamic(() => import('@components/cms/V1EG003/V1EG003').then(mod => mod.V1EG003));
const V1EG004 = dynamic(() => import('@components/cms/V1EG004/V1EG004').then(mod => mod.V1EG004));
const V5G031 = dynamic(() => import('@components/cms/V5G031/V5G031').then(mod => mod.V5G031));
const V5G031Wap = dynamic(() => import('@components/cms/V5G031/V5G031Wap').then(mod => mod.V5G031Wap));
const V5G032 = dynamic(() => import('@components/cms/V5G032/V5G032').then(mod => mod.V5G032));
const V5G032Wap = dynamic(() => import('@components/cms/V5G032/V5G032Wap').then(mod => mod.V5G032Wap));
const V5G033 = dynamic(() => import('@components/cms/V5G033/V5G033').then(mod => mod.V5G033));
const V5G034 = dynamic(() => import('@components/cms/V5G034/V5G034').then(mod => mod.V5G034));
const V5G034Wap = dynamic(() => import('@components/cms/V5G034/V5G034Wap').then(mod => mod.V5G034Wap));
const V5G035 = dynamic(() => import('@components/cms/V5G035/V5G035').then(mod => mod.V5G035));
const V5G035Wap = dynamic(() => import('@components/cms/V5G035/V5G035Wap').then(mod => mod.V5G035Wap));

const V6S001A = dynamic(() => import('@components/cms/V6S001/V6S001A').then(mod => mod.V6S001A));
const V6S001B = dynamic(() => import('@components/cms/V6S001/V6S001B').then(mod => mod.V6S001B));
const V6S003 = dynamic(() => import('@components/cms/V6S003/V6S003').then(mod => mod.V6S003));
const V6G001 = dynamic(() => import('@components/cms/V6G001/V6G001').then(mod => mod.V6G001));
const V6G002 = dynamic(() => import('@components/cms/V6G002/V6G002').then(mod => mod.V6G002));
const V6G003 = dynamic(() => import('@components/cms/V6G003/V6G003').then(mod => mod.V6G003));
const V6G006 = dynamic(() => import('@components/cms/V6G006/V6G006').then(mod => mod.V6G006));
const V6G010 = dynamic(() => import('@components/cms/V6G010/V6G010').then(mod => mod.V6G010));
const V6G007 = dynamic(() => import('@components/cms/V6G007/V6G007').then(mod => mod.V6G007));
const V6G012 = dynamic(() => import('@components/cms/V6G012/v6G012').then(mod => mod.V6G012));
const V6G014 = dynamic(() => import('@components/cms/V6G014/V6G014').then(mod => mod.V6G014));
const V6S002 = dynamic(() => import('@components/cms/V6S002/V6S002').then(mod => mod.V6S002));
const V6G017 = dynamic(() => import('@components/cms/V6G017/V6G017').then(mod => mod.V6G017));
const V6G018 = dynamic(() => import('@components/cms/V6G018/V6G018').then(mod => mod.V6G018));
const V6G009 = dynamic(() => import('@components/cms/V6G009/V6G009').then(mod => mod.V6G009));
const V6G008 = dynamic(() => import('@components/cms/V6G008/V6G008').then(mod => mod.V6G008));
const V6G015 = dynamic(() => import('@components/cms/V6G015/V6G015').then(mod => mod.V6G015));
const V6G016 = dynamic(() => import('@components/cms/V6G016/V6G016').then(mod => mod.V6G016));
const V6G013 = dynamic(() => import('@components/cms/V6G013/V6G013').then(mod => mod.V6G013));
const V6G004 = dynamic(() => import('@components/cms/V6G004/V6G004').then(mod => mod.V6G004));

export const proAndSolModuleMap: {
    [props: string]: {
        pc: ComponentType<Block>;
        wap: ComponentType<Block>;
    };
} = {
    CodePanel: {pc: CodePanel, wap: CodePanel},
    Markdown: {pc: Markdown, wap: Markdown},
    V5S001: {pc: V5S001, wap: V5S001Wap},
    'V5S001_en': {pc: V5S001, wap: V5S001Wap},
    V5G015: {pc: V5G015, wap: V5G015},
    V5G015_C: {pc: V5G015, wap: V5G015}, // V5G015 对照版本
    V5G015B: {pc: V5G015B, wap: V5G015B},
    V5G015C: {pc: V5G015C, wap: V5G015C},
    V5G015C_C: {pc: V5G015C, wap: V5G015C}, // V5G015C 对照版本
    V5G015D: {pc: V5G015D, wap: V5G015D},
    V5G015E: {pc: V5G015E, wap: V5G015E},
    V5B014B: {pc: V5B014B, wap: V5B014BWap},
    V1SS001: {pc: V1SS001, wap: V1SS001Wap},
    V5G001: {pc: V5G001, wap: V5G001Wap},
    V5G002: {pc: V5G001, wap: V5G001Wap},
    'V5G002_en': {pc: V5G001, wap: V5G001Wap},
    V5G003: {pc: V5G001, wap: V5G001Wap},
    V5G004A: {pc: V5G004A, wap: V5G004A},
    V5G004B: {pc: V5G004A, wap: V5G004A},
    V5G004C: {pc: V5G004A, wap: V5G004A},
    'V5G004D_en': {pc: V5G004D, wap: V5G004D},
    V5G005A: {pc: V5G005A, wap: V5G005A},
    V5G005B: {pc: V5G005B, wap: V5G005B},
    V5G006A: {pc: V5G004A, wap: V5G004A},
    V5G006B: {pc: V5G004A, wap: V5G004A},
    V5G007A: {pc: V5G007A, wap: V5G007AWap},
    V5G007B: {pc: V5G008, wap: V5G008Wap},
    V5G008A: {pc: V5G008, wap: V5G008Wap},
    V5G008B: {pc: V5G008, wap: V5G008Wap},
    V5G009A: {pc: V5G009A, wap: V5G009A},
    V5G009B: {pc: V5G004A, wap: V5G004A},
    V5G010: {pc: V5G010, wap: V5G010Wap},
    V5G024: {pc: V5G024, wap: V5G024Wap},
    V5G025: {pc: V5G025, wap: V5G025},
    V5B001: {pc: V5B001, wap: V5B001Wap},
    V5B001B: {pc: V5B001B, wap: V5B001BWap},
    V5B002: {pc: V5B002, wap: V5B002Wap},
    V5B003A: {pc: V5B003A, wap: V5B003AWap},
    V5B003B: {pc: V5B003B, wap: V5B003BWap},
    V5B004: {pc: V5B002, wap: V5B002Wap},
    V5B005: {pc: V5B005, wap: V5B005Wap},
    V5B006: {pc: V5B005, wap: V5B005Wap},
    V5B007: {pc: V5B003B, wap: V5B003BWap},
    V5B008: {pc: V5B008, wap: V5B008Wap},
    V5B009: {pc: V5B009, wap: V5B009Wap},
    V5B010A: {pc: V5B010, wap: V5B010Wap},
    V5B010B: {pc: V5B010, wap: V5B010Wap},
    V5B011: {pc: V5B011, wap: V5B011},
    V5B016: {pc: V5B016, wap: V5B016Wap},
    V5B017: {pc: V5B017, wap: V5B017Wap},
    V5B018: {pc: V5B018, wap: V5B018Wap},
    'V5B018_en': {pc: V5B018, wap: V5B018Wap},
    V1SG001: {pc: V5G008, wap: V5G008Wap},
    V1SG002: {pc: V5G005A, wap: V5G005A},
    V5G011: {pc: V5G011, wap: V5G011Wap},
    V5G012: {pc: V5G012, wap: V5G012Wap},
    V5G013: {pc: V5G013, wap: V5G013Wap},
    V5G014: {pc: V5G014, wap: V5G014Wap},
    V5G016: {pc: V5G016, wap: V5G016Wap},
    V5G017: {pc: V5G017, wap: V5G017Wap},
    V5G018A: {pc: V5G018A, wap: V5G018AWap},
    V5G018B: {pc: V5G018B, wap: V5G018BWap},
    V5G019: {pc: V5G019, wap: V5G019Wap},
    V5G020: {pc: V5G020, wap: V5G020Wap},
    V5G020B: {pc: V5G020B, wap: V5G020BWap},
    V5G021: {pc: V5G021, wap: V5G021Wap},
    V5G022A: {pc: V5G022A, wap: V5G022AWap},
    V5G022B: {pc: V5G022B, wap: V5G022BWap},
    V5G023: {pc: V5G023, wap: V5G023Wap},
    V5G026: {pc: V5G026, wap: V5G026Wap},
    V5G027A: {pc: V5G027 as any, wap: V5G027Wap as any},
    V5G027B: {pc: V5G027 as any, wap: V5G027Wap as any},
    V5G028: {pc: V5G028, wap: V5G028Wap},
    V5G029: {pc: V5G029, wap: V5G029Wap},
    V5G030: {pc: V5G030, wap: V5G030Wap},
    V5S004: {pc: V5S004, wap: V5S004},
    V5C002: {pc: V5C002, wap: V5C002Wap},
    V1SG003: {pc: V1SG003, wap: V1SG003Wap},
    V1EG001: {pc: V1EG001, wap: V1EG001},
    V1EG002A: {pc: V1EG002A, wap: V1EG002A},
    V1EG002B: {pc: V1EG002B, wap: V1EG002B},
    V1EG003: {pc: V1EG003, wap: V1EG003},
    V1EG004: {pc: V1EG004, wap: V1EG004},
    V5G031: {pc: V5G031, wap: V5G031Wap},
    V5G032: {pc: V5G032, wap: V5G032Wap},
    V5G033: {pc: V5G033, wap: V5G033},
    V5G034: {pc: V5G034, wap: V5G034Wap},
    V5G035: {pc: V5G035, wap: V5G035Wap},
};

export const proAndSolBannerBlockList = ['V5S001', 'V5S001_en', 'V1SS001'];

export const docModuleMap: {
    [props: string]: {
        pc: ComponentType<Block>;
        wap: ComponentType<Block>;
    };
} = {
    V1DS001: {pc: V1DS001, wap: V1DS001Wap},
    V1DS002: {pc: V1DS002, wap: V1DS002Wap},
    V1DG002: {pc: V1DG002, wap: V1DG002Wap},
    V1DG003: {pc: V1DG003, wap: V1DG003Wap},
    V1DG004: {pc: V1DG004, wap: V1DG004Wap},
};

export const productSBannerBlockList = ['V6S001A', 'V6S001B', 'V6S002', 'V6S003'];

export const flexModuleMap: {
    [props: string]: ComponentType<Block>;
} = {
    V6S001A: V6S001A, // banner
    V6S001B: V6S001B, // banner
    V6S002: V6S002,
    V6S003: V6S003,
    V6G001: V6G001,
    V6G002: V6G002,
    V6G003: V6G003,
    V6G006: V6G006,
    V6G010: V6G010,
    V6G007: V6G007,
    V6G012: V6G012,
    V6G014: V6G014,
    V6G017: V6G017,
    V6G018: V6G018,
    V6G009: V6G009,
    V6G008: V6G008,
    V6G015: V6G015,
    V6G016: V6G016,
    V6G013: V6G013,
    V6G004: V6G004,
};
