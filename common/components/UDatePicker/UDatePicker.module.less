@import '@styles/variables.less';
@active-border-color: rgba(36, 104, 242, 1);
@primary-color: @CB;

.u-date-picker {
    border-radius: 4px;
    height: 32px;
    &:hover {
        border: 1px solid @active-border-color;
    }
    :global {
        .ant-picker-focused {
            border: 1px solid @active-border-color;
        }
        .ant-picker-input > input {
            font-size: 12px;
        }
        .ant-picker-suffix {
            color: rgba(@C0 .7);
        }
    }
}
.u-date-picker-popup {
    border-radius: 4px;
    font-size: 12px;
    :global {
        .ant-picker-header {
            color: #222;
            border-bottom: 1px solid rgba(@C0 0.08);
        }
        .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
            background: @primary-color;
        }
        .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before,
        .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
            border-color: @primary-color;
        }
        .ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
            background: #E6F0FF;
        }
        .ant-picker-time-panel-column:not(:first-child),
        .ant-picker-datetime-panel .ant-picker-time-panel,
        .ant-picker-panel .ant-picker-footer {
            border-left: 1px solid rgba(@C0 0.08);;
        }
        .ant-picker-panel .ant-picker-footer {
            border-top: 1px solid rgba(@C0 0.08);
        }
        .ant-picker-now-btn {
            color: @primary-color;
        }
        .ant-picker-ok .ant-btn {
            background: @primary-color;
            font-size: 12px;
            letter-spacing: -1px;
            color: #fff;
            &:hover {
                background: #528EFF;
            }
        }
        .ant-picker-header-view button:hover {
            color: @primary-color;
        }
    }
}

:global {
    .ant-picker-focused {
        border: 1px solid @active-border-color;
        box-shadow: none;
    }
}