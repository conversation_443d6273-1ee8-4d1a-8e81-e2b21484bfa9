/**
 * @file cms通用按钮
 */

import {CmsBtnObj} from '@common/interface/common';
import {AnchorHTMLAttributes, CSSProperties, HTMLAttributeAnchorTarget, MouseEventHandler} from 'react';
import cn from 'classnames';

/**
 * cms通用按钮
 * @param data 按钮数据
 * @param dataTrackCategory 埋点category
 * @param dataTrackName 埋点name
 * @param target 超链接打开方式
 * @param className 类名
 * @param style 样式
 * @returns JSX.Element
 */
export const UCmsBtn = (props: {
    data: CmsBtnObj;
    dataTrackCategory: string;
    dataTrackName: string;
    dataTrackValue?: string;
    target?: HTMLAttributeAnchorTarget;
    className?: string;
    style?: CSSProperties;
    onClick?: MouseEventHandler<HTMLAnchorElement>;
    anchorProps?: AnchorHTMLAttributes<HTMLAnchorElement>;
}) => {
    const {data, target, dataTrackCategory, dataTrackName, dataTrackValue, style, className, onClick, anchorProps = {}} = props;
    return data ? (
        !data.disabled && data.text ? (
            <a
                className={cn(className)}
                href={data.href}
                target={target || '_self'}
                data-track-category={dataTrackCategory}
                data-track-name={dataTrackName}
                data-track-value={dataTrackValue ?? data.text}
                style={style}
                onClick={onClick}
                {...anchorProps}
            >
                {data.text}
            </a>
        ) : null
    ) : null;
};
