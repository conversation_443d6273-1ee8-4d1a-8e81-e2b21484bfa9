/* eslint-disable max-len */
import React, {useState, useEffect} from 'react';
import ReactDOM from 'react-dom';
import classNames from 'classnames';
import styles from './index.module.less';

interface NumberInputProps {
    value?: number;
    initialValue?: number;
    className?: string;
    onChange?: (value: number) => void;
    disabled?: boolean;
    max?: number;
    min?: number;
}

export function NumberInput(props: NumberInputProps) {
    const {value, className, onChange, initialValue, disabled = false, max = 999, min = 1} = props;
    const [displayValue, setDisplayValue] = useState<number | ''>(value === undefined ? (initialValue || 1) : value);

    useEffect(() => {
        setDisplayValue(value);
    }, [value]);

    const handleChange = (newValue: number) => {
        setDisplayValue(newValue);
        onChange && onChange(newValue);
    };

    const handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void = ({target: {value}}) => {
        let num = parseInt(value, 10);
        num = num > max ? max : num < min ? min : num;
        setDisplayValue(isNaN(num) ? undefined : num);
    };

    const handleInputBlur = () => {
        if (displayValue >= min) {
            onChange && onChange(displayValue as number);
        }
        else {
            ReactDOM.flushSync(() => {
                setDisplayValue(value || min);
            });
        }
    };

    const minusDisabled = disabled || displayValue <= min;
    const plusDisabled = disabled || displayValue >= max;

    return (
        <div className={classNames(className, styles['number-input'], {[styles.disabled]: disabled})}>
            <span
                className={classNames(styles.minus, minusDisabled ? 'disable' : '')}
                onClick={() => !minusDisabled && handleChange(displayValue as number - 1)}
            >
                <svg width="16px" height="16px" viewBox="0 0 16 16">
                    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                        <g transform="translate(-419.000000, -2346.000000)">
                            <rect fill="#F2F6FA" x="0" y="0" width="1920" height="2533"></rect>
                            <rect fill="#FFFFFF" x="0" y="1011" width="1920" height="1522"></rect>
                            <g transform="translate(411.000000, 2338.000000)">
                                <g stroke="#222222" strokeOpacity="0.0819458861">
                                    <rect x="0.5" y="0.5" width="127" height="31" rx="4"></rect>
                                </g>
                                <g transform="translate(8.000000, 8.000000)" fill="currentColor">
                                    <rect stroke="#222222" opacity="0" x="0.5" y="0.5" width="15" height="15"></rect>
                                    <path d="M2.75,8.75 L13.25,8.75 C13.6642136,8.75 14,8.41421356 14,8 C14,7.58578644 13.6642136,7.25 13.25,7.25 L2.75,7.25 C2.33578644,7.25 2,7.58578644 2,8 C2,8.41421356 2.33578644,8.75 2.75,8.75 Z"></path>
                                </g>
                            </g>
                        </g>
                    </g>
                </svg>
            </span>
            <input
                disabled={disabled}
                className={styles.value}
                value={displayValue}
                onChange={handleInputChange}
                onBlur={handleInputBlur}
            />
            <span
                className={classNames(styles.plus, plusDisabled ? 'disable' : '')}
                onClick={() => !plusDisabled && handleChange(displayValue as number + 1)}
            >
                <svg width="16px" height="16px" viewBox="0 0 16 16">
                    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                        <g transform="translate(-515.000000, -2346.000000)">
                            <rect fill="#F2F6FA" x="0" y="0" width="1920" height="2533"></rect>
                            <rect fill="#FFFFFF" x="0" y="1011" width="1920" height="1522"></rect>
                            <g transform="translate(411.000000, 2338.000000)">
                                <g stroke="#222222" strokeOpacity="0.0819458861">
                                    <rect x="0.5" y="0.5" width="127" height="31" rx="4"></rect>
                                </g>
                                <g transform="translate(104.000000, 8.000000)" fill="currentColor">
                                    <rect opacity="0" x="0" y="0" width="16" height="16"></rect>
                                    <path d="M8,2 C8.41421356,2 8.75,2.33578644 8.75,2.75 L8.75,2.75 L8.75,7.25 L13.25,7.25 C13.6296958,7.25 13.943491,7.53215388 13.9931534,7.89822944 L14,8 C14,8.41421356 13.6642136,8.75 13.25,8.75 L13.25,8.75 L8.75,8.75 L8.75,13.25 C8.75,13.6296958 8.46784612,13.943491 8.10177056,13.9931534 L8,14 C7.58578644,14 7.25,13.6642136 7.25,13.25 L7.25,13.25 L7.25,8.75 L2.75,8.75 C2.37030423,8.75 2.05650904,8.46784612 2.00684662,8.10177056 L2,8 C2,7.58578644 2.33578644,7.25 2.75,7.25 L2.75,7.25 L7.25,7.25 L7.25,2.75 C7.25,2.37030423 7.53215388,2.05650904 7.89822944,2.00684662 Z"></path>
                                </g>
                            </g>
                        </g>
                    </g>
                </svg>
            </span>
        </div>
    );
}
