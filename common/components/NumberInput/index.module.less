@import '@styles/variables.less';

.number-input {
    // display: inline-block;
    border: 1px solid @C08;
    border-radius: 4px;
    overflow: hidden;
    width: max-content;

    &:hover {
        border-color: @CB;
    }

    &.disabled {
        input.value,
        .minus,
        .plus {
            opacity: 0.16;
            background: @C0;
        }
    }

    span,
    input.value {
        display: inline-block;
        background: @CW;
        height: 30px;
        line-height: 30px;
        text-align: center;
        vertical-align: top;

        &.minus,
        &.plus {
            width: 30px;
            user-select: none;
            color: @C0;
            svg {
                width: 16px;
                height: 16px;
                margin-top: 7px;
                color: @C0;
                opacity: .7;
                path {
                    width: 10px;
                    height: 10px;
                }
                &:hover {
                    color: @CB;
                    opacity: 1;
                }
            }
            &:global(.disable) svg {
                color: @C0;
                opacity: .2;
            }
            &:global(:not(.disable)):hover {
                cursor: pointer;
                fill: @CB;
            }
        }
    }
    input.value {
        width: 63px;
        border-width: 0 1px;
        border-color: @C08;
        font-size: 12px;
        &:focus-visible {
            outline: none;
        }
    }
}