.scale-wrap {
    position: fixed;
    background-color: #000;
    z-index: 999999;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    overflow: hidden;

    img {
        z-index: 1;
        transform-origin: left top;
    }

    .hide-btn {
        position: fixed;
        z-index: 9999999;
        top: 13px;
        right: 13px;
        border: none;
        width: 24px;
        height: 24px;
        padding: 0;
        background: url(https://bce.bdstatic.com/p3m/common-service/uploads/wap_preview_close_c54215d.svg) center / cover;
        &::before {
            content: '';
            position: fixed;
            width: 50px;
            height: 50px;
            top: 0;
            right: 0;
        }
    }
}
:global {
    body.forbidden-scroll {
        overflow: hidden;
    }
}