/**
 * @file 放大缩小图片
 * <AUTHOR>
 */

import {CSSProperties, useRef, useState} from 'react';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {UModalMask} from '../UModalMask/UModalMask';
import style from './UScaleImgWap_m.module.less';

type coordinateObj = {
    x: number;
    y: number;
};

// 图片 transform 相关变量
const defaultStore = {
    scale: 1,
    x1: 0,
    y1: 0,
    x2: 0,
    y2: 0,
    offsetX: 0,
    offsetY: 0,
    originOffsetX: 0,
    originOffsetY: 0,
    movable: false,
    originScale: 1,
};

// 最初的平移值, 使图片居中显示
const defaultCoordinateData = {
    defaultOffsetX: 0,
    defaultOffsetY: 0,
    midX: 0,
    midY: 0,
    lastX: 0,
    lastY: 0,
};

// 显示图片，需要传图片链接
export const UScaleImgWap = (props: {
    src: string;
    maxScale?: number;
}) => {
    const {src, maxScale = 5} = props;
    const [show, setShow] = useStateRef<boolean>(true);
    const imgWrapperRef = useRef<HTMLDivElement>(null);
    const imgRef = useRef<HTMLImageElement>(null);
    const [imgStyle, setImgStyle] = useState<CSSProperties>(null);
    const imgPositionRef = useRef(defaultStore);
    const coordinateCenterRef = useRef(defaultCoordinateData);
    // 更新 store 和图片 transform 状态
    const updateStore = (nextStore: Partial<typeof defaultStore>) => {
        const newPosition = {...imgPositionRef.current, ...nextStore};
        imgPositionRef.current = {...imgPositionRef.current, ...nextStore};
        setImgStyle(pre => ({...pre, transform: `translate(${newPosition.offsetX}px, ${newPosition.offsetY}px) scale(${newPosition.scale})`}));
    };

    // 获取坐标之间的距离
    const getDistance = (start: coordinateObj, stop: coordinateObj) => {
        return Math.sqrt(Math.pow(stop.x - start.x, 2) + Math.pow(stop.y - start.y, 2));
    };

    // 获取两点中点坐标
    const getMidPoint = (p1: coordinateObj, p2: coordinateObj) => {
        return {
            x: (p1.x + p2.x) / 2,
            y: (p1.y + p2.y) / 2,
        };
    };

    // 图片的 touchstart 回调, 记录触摸坐标 (1 个或 2 个)
    const handleImageTouchStart = (event: TouchEvent) => {
        const touches = event.touches;
        const events1 = touches[0];
        const events2 = touches[1];

        event.preventDefault();

        // 第一个触摸点的坐标
        coordinateCenterRef.current.lastX = events1.clientX;
        coordinateCenterRef.current.lastY = events1.clientY;


        if (imgPositionRef.current.scale === 1) {
            const midPoint = getMidPoint(
                {x: events1.clientX, y: events1.clientY},
                {x: events2 ? events2.clientX : imgPositionRef.current.x2, y: events2 ? events2.clientY : imgPositionRef.current.y2}
            );
            coordinateCenterRef.current.midX = midPoint.x;
            coordinateCenterRef.current.midY = midPoint.y;
        }

        const newPosition = {
            x1: events1.clientX,
            y1: events1.clientY,
            movable: true,
            originScale: imgPositionRef.current.scale || 1,
        };
        if (events2) {
            imgPositionRef.current = {...imgPositionRef.current, ...newPosition, x2: events2.clientX, y2: events2.clientY};
        } else {
            imgPositionRef.current = {...imgPositionRef.current, ...newPosition};
        }
    };

    // touchmove 时若单指移动则移动图片, 双指移动则缩放图片
    const handleTouchMove = (event: TouchEvent) => {
        event.preventDefault();
        if (!imgPositionRef.current.movable) {
            return;
        }

        const touches = event.touches;
        const events1 = touches[0];
        const events2 = touches[1];

        // 放大后且单指移动
        if (imgPositionRef.current.scale > 1 && !events2) {
            updateStore({
                offsetX: imgPositionRef.current.offsetX + (events1.clientX - coordinateCenterRef.current.lastX),
                offsetY: imgPositionRef.current.offsetY + (events1.clientY - coordinateCenterRef.current.lastY),
                originOffsetX: imgPositionRef.current.originOffsetX
                + (events1.clientX - coordinateCenterRef.current.lastX) / imgPositionRef.current.scale,
                originOffsetY: imgPositionRef.current.originOffsetY
                + (events1.clientY - coordinateCenterRef.current.lastY) / imgPositionRef.current.scale,
            });
        }

        coordinateCenterRef.current.lastX = events1.clientX;
        coordinateCenterRef.current.lastY = events1.clientY;

        // 双指移动
        if (events2) {
            // 第2个指头坐标在touchmove时候获取
            const newX2 = imgPositionRef.current.x2 ? imgPositionRef.current.x2 : events2.clientX;
            const newY2 = imgPositionRef.current.y2 ? imgPositionRef.current.y2 : events2.clientY;

            // 双指缩放比例计算
            const zoom = getDistance({x: events1.clientX, y: events1.clientY}, {x: events2.clientX, y: events2.clientY})
                / getDistance({x: imgPositionRef.current.x1, y: imgPositionRef.current.y1}, {x: newX2, y: newY2});

            // 应用在元素上的缩放比例
            const newScale = imgPositionRef.current.originScale * zoom;

            updateStore({
                x2: newX2,
                y2: newY2,
                scale: newScale,
                offsetX: 0 - coordinateCenterRef.current.midX * (newScale - 1) + newScale * imgPositionRef.current.originOffsetX,
                offsetY: coordinateCenterRef.current.midY - newScale * (coordinateCenterRef.current.midY - imgPositionRef.current.originOffsetY),
            });
        }
    };

    // 结束后状态复原
    const handleTouchEnd = () => {
        imgPositionRef.current = {...imgPositionRef.current, movable: false, x2: 0, y2: 0};

        if (imgPositionRef.current.scale < 1) {
            updateStore({
                offsetX: coordinateCenterRef.current.defaultOffsetX,
                offsetY: coordinateCenterRef.current.defaultOffsetY,
                originOffsetX: coordinateCenterRef.current.defaultOffsetX,
                originOffsetY: coordinateCenterRef.current.defaultOffsetY,
                scale: 1,
            });
        } else if (imgPositionRef.current.scale > maxScale) {
            updateStore({
                scale: maxScale,
                offsetX: 0 - coordinateCenterRef.current.midX * (maxScale - 1) + maxScale * imgPositionRef.current.originOffsetX,
                offsetY: coordinateCenterRef.current.midY - maxScale * (coordinateCenterRef.current.midY - imgPositionRef.current.originOffsetY),
            });
        }
    };

    const init = () => {
        // 重置 / 初始化数据
        imgPositionRef.current = defaultStore;

        let offsetX = 0;
        let offsetY = 0;
        if (imgRef.current.width / imgRef.current.height >= window.innerWidth / window.innerHeight) {
            setImgStyle(pre => ({...pre, width: '100%', height: 'auto'}));
            // iPhone 上无法通过 img.height 的方式及时获取图片的高度 (奇怪的兼容问题), 改用 getComputedStyle 的方式, 下面同理
            offsetY = window.innerHeight / 2 - parseInt(getComputedStyle(imgRef.current).height, 10) / 2;
        } else {
            setImgStyle(pre => ({...pre, width: 'auto', height: '100%'}));
            offsetX = window.innerWidth / 2 - parseInt(getComputedStyle(imgRef.current).width, 10) / 2;
        }

        coordinateCenterRef.current.defaultOffsetX = offsetX;
        coordinateCenterRef.current.defaultOffsetY = offsetY;

        updateStore({offsetX, offsetY, originOffsetX: offsetX, originOffsetY: offsetY});
    };
    useOnMount(() => {
        if (!src) {
            throw new Error('Image Src is necessary!');
        }
        if (typeof src !== 'string') {
            throw new Error(`Image Src: Expect string, but got ${typeof src}`);
        }

        imgRef.current.onload = () => {
            init();
        };
        imgRef.current.src = src;
        setShow(true);
        document.body.classList.add('forbidden-scroll');

        imgRef.current.addEventListener('touchstart', handleImageTouchStart);
        imgWrapperRef.current.addEventListener('touchmove', handleTouchMove);
        imgWrapperRef.current.addEventListener('touchend', handleTouchEnd);
        imgWrapperRef.current.addEventListener('touchcancel', handleTouchEnd);
    });

    const handleClickHide = () => {
        setShow(false);
        document.body.classList.remove('forbidden-scroll');
    };

    return (
        <UModalMask show={show} maskClosable={false}>
            <div className={style['scale-wrap']} ref={imgWrapperRef}>
                <img src="" alt="image-preview" ref={imgRef} style={imgStyle} />
                <button className={style['hide-btn']} onClick={handleClickHide}></button>
            </div>
        </UModalMask>
    );
};
