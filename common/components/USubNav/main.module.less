@import '@styles/variables.less';

@navHeight: 52px;

.sub-nav-wrapper {
    height: @navHeight;
    background-color: @CW;
    .sub-nav {
        position: relative;
        width: 100%;
        height: @navHeight;
        background-color: @CW;
        z-index: 19;
        box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
    }
    :global(.container) {
        height: 100%;
        overflow: hidden;
        position: relative;
    }
    .btn-left, .btn-right {
        width: 48px;
        height: 100%;
        position: absolute;
        z-index: 2;
        top: 0;
        background-color: @CW;
        cursor: pointer;
        > div {
            position: absolute;
            left: 50%;
            top: 50%;
            width: 16px;
            height: 16px;
        }
        svg {
            color: rgba(@C0, 0.7);
        }
        &::after {
            content: '';
            position: absolute;
            top: 0;
            width: 44px;
            height: 100%;
        }
    }
    .btn-left {
        left: 0;
        > div {
            transform: translate(-50%, -50%) rotate(90deg);
        }
        &::after {
            left: 100%;
            background-image: linear-gradient(90deg, #FFFFFF 0, rgba(255,255,255,0) 100%);
        }
    }
    .btn-right {
        right: 0;
        > div {
            transform: translate(-50%, -50%) rotate(-90deg);
        }
        &::after {
            right: 100%;
            background-image: linear-gradient(270deg, #FFFFFF 0, rgba(255,255,255,0) 100%);
        }
    }
    ul {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        height: @navHeight * 2;
        position: relative;
        overflow-x: auto;
        li {
            width: 30%;
            text-align: center;
            line-height: @navHeight;
            cursor: pointer;
            &:global(.current), &:hover {
                span {
                    color: @CB;
                }
            }
            span {
                font-family: PingFangSC-Semibold;
                font-size: 16px;
                color: @C9;
                font-weight: 500;
                transition: color .3s ease-out;
            }
        }
        .line {
            position: absolute;
            left: 0;
            top: @navHeight - 2px;
            width: 0;
            height: 2px;
            background: @CB;
            transition: all 0.3s ease-out;
        }
    }
}