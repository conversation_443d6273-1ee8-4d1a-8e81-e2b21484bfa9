/**
 * @file 二级导航
 */

import {UStickyContainer} from '@common/components/UStickyContainer/UStickyContainer';
import {useScrollBind} from '@common/hooks/useScrollBind';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {CSSProperties, MutableRefObject, useEffect, useRef, useState} from 'react';
import {reduce, throttle} from 'lodash';
import {GetFuncType} from '@common/interface/common';
import cn from 'classnames';
import {ReactSVG} from 'react-svg';

import styles from './main.module.less';

const coverWidth = 92;

export const USubNav = (
    {subNavData, className, scrollBindOption, id = '', wrapperRef, hashHandlerFn}: {
        subNavData: Array<{name: string}>;
        className?: string;
        scrollBindOption?: GetFuncType<typeof useScrollBind>;
        id?: string;
        wrapperRef?: MutableRefObject<HTMLDivElement>;
        hashHandlerFn?: (hash: string) => void;
    }) => {
    const [current, setCurrent, currentRef] = useScrollBind(scrollBindOption || {
        itemSelector: '.module',
        fixTop: -52,
    });
    const [scrollLeft, setScrollLeft, scrollLeftRef] = useStateRef(0);
    const [lineStyle, setLineStyle] = useState<CSSProperties>(null);
    const [rightBtnShow, setRightBtnShow] = useState(false);
    const [listWidth, setListWidth] = useState(0);
    const ref = useRef<HTMLUListElement>(null);

    const handleLineMove = (index: number) => {
        const curLi: HTMLLIElement = ref.current.querySelector(`li:nth-child(${index + 1})`);
        const width = curLi.querySelector('span').offsetWidth;
        const left = curLi.offsetLeft + (curLi.offsetWidth - width) / 2;
        setLineStyle({width, left});
    };

    const handleScroll = (direct: 'left' | 'right') => {
        const navWidth = ref.current.offsetWidth;
        if (direct === 'left' && scrollLeft !== 0) {
            const newScrollLeft = Math.max(0, scrollLeft - navWidth);
            ref.current.scrollTo({left: newScrollLeft, behavior: 'smooth'});
            setScrollLeft(newScrollLeft);
        } else if (direct === 'right') {
            if (scrollLeft < listWidth - navWidth) {
                const newScrollLeft = Math.min(listWidth - navWidth, scrollLeft + navWidth);
                ref.current.scrollTo({left: newScrollLeft, behavior: 'smooth'});
                setScrollLeft(newScrollLeft);
            }
        }
    };

    useOnMount(() => {
        const allLiEles = ref.current.querySelectorAll('li');
        setListWidth(Math.floor(reduce(allLiEles, (res, item) => (
            res + item.getBoundingClientRect().width
            + parseInt(getComputedStyle(item, null).marginLeft, 10)
            + parseInt(getComputedStyle(item, null).marginRight, 10)
        ), 0)));
        if (location.hash) {
            // 判断当前是否存在index
            const data = decodeURIComponent(location.hash).slice(1);
            const index = subNavData.findIndex(item => item.name === data);
            index >= 0 && setTimeout(() => setCurrent(index), 400);
            if (index < 0 && hashHandlerFn) {
                hashHandlerFn(data);
            }
        }
        const handleResize = throttle(() => handleLineMove(currentRef.current), 40);
        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    });
    useEffect(() => {
        const UlW = ref.current.offsetWidth;
        if (listWidth > UlW) {
            const curLi: HTMLLIElement = ref.current.querySelector(`li:nth-child(${current + 1})`);
            const curLiLeft = curLi.offsetLeft;
            if (curLiLeft - coverWidth < scrollLeftRef.current) {
                ref.current.scrollTo({left: curLiLeft - coverWidth, behavior: 'smooth'});
            } else if (curLiLeft + coverWidth > scrollLeftRef.current + UlW) {
                ref.current.scrollTo({left: curLiLeft - UlW + curLi.offsetWidth + coverWidth, behavior: 'smooth'});
            }
        }
        handleLineMove(current);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [current, listWidth]);
    useEffect(() => {
        const ulW = ref.current.offsetWidth;
        setRightBtnShow(listWidth > ulW && scrollLeft < listWidth - ulW);
    }, [scrollLeft, listWidth]);
    return (
        <div className={cn(styles['sub-nav-wrapper'], className)} id={id} ref={wrapperRef}>
            <UStickyContainer>
                <div className={styles['sub-nav']}>
                    <div className="container">
                        {
                            scrollLeft ? (
                                <ReactSVG
                                    className={styles['btn-left']}
                                    src="https://bce.bdstatic.com/portal-cloud-server/images/arrow_down.svg"
                                    onClick={() => handleScroll('left')}
                                />
                            ) : null
                        }
                        <ul
                            ref={ref}
                            onScroll={throttle(() => {
                                setScrollLeft(ref.current.scrollLeft);
                            }, 40)}
                        >
                            {
                                subNavData.map((item, index) => (
                                    <li
                                        key={item.name}
                                        className={cn(index === current && 'current')}
                                        onClick={() => {
                                            if (current !== index) {
                                                setCurrent(index);
                                                history.pushState({index}, '', `${location.pathname}${location.search}#${subNavData[index].name}`);
                                            }
                                        }}
                                    >
                                        <span>{item.name}</span>
                                    </li>
                                ))
                            }
                            <li className={styles['line']} style={lineStyle} />
                        </ul>
                        {
                            rightBtnShow && (
                                <ReactSVG
                                    className={styles['btn-right']}
                                    onClick={() => handleScroll('right')}
                                    src="https://bce.bdstatic.com/portal-cloud-server/images/arrow_down.svg"
                                />
                            )
                        }
                    </div>
                </div>
            </UStickyContainer>
        </div>
    );
};
