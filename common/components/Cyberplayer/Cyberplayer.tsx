import {useOnMount} from '@baidu/bce-hooks';
import React, {useState, useRef} from 'react';
import {CyberplayerConfig, CyberplayerApi, CyberplayerVersion} from '@common/interface/common';
import stylePc from './Cyberplayer.module.less';


// eslint-disable-next-line no-unused-expressions
stylePc.a;

export const ACCESS_KEY = '55875f38623d4faa842ce78af0fbb631';

const defaultConfig: CyberplayerConfig = {
    width: '100%', // 宽度，也可以支持百分比(不过父元素宽度要有)
    height: '100%', // 高度，也可以支持百分比
    title: '', // 标题
    file: '', // 播放地址
    image: 'http://cyberplayer.bcelive.com/thumbnail.jpg', // 预览图
    autostart: false, // 是否自动播放
    stretching: 'uniform', // 拉伸设置
    repeat: false, // 是否重复播放
    volume: 50, // 音量
    controls: true, // controlbar是否显示
    starttime: 0, // 视频开始播放时间点(单位s)，如果不设置，则可以从上次播放时间点续播
    ak: ACCESS_KEY, // 公有云平台注册即可获得accessKey
    flv: {
        reconnecttime: 2,
    },
};

export const Cyberplayer = React.memo((props: {
    config?: CyberplayerConfig;
    onStatusChange?: (status: 'play' | 'pause') => void;
    onload?: (player: CyberplayerApi) => void;
    initSdk?: boolean;
    playerId?: string;
    cyberplayerVersion?: CyberplayerVersion;
}) => {
    const [showErrorTip, setShowErrorTip] = useState<boolean>(false);
    const player = useRef<CyberplayerApi>(null);
    const timer = useRef<NodeJS.Timeout>(null);
    const initPlayer = () => {
        const prepare = () => {
            player.current = (window as any).cyberplayer(props.playerId || 'playercontainer')?.setup({
                ...defaultConfig,
                ...props.config,
            });
            player.current.on('error', e => {
                console.info(e);
                if (e.msg?.details === 'internalException') {
                    return;
                }
                setShowErrorTip(true);
            });
            props.onload && props.onload(player.current);
        };
        if ((window as any).cyberplayer) {
            prepare();
        } else if (window.require) {
            (window as any).require(['cyberplayer'], (cyberplayer: any) => {
                (window as any).cyberplayer = cyberplayer;
                prepare();
            });
        }
    };

    useOnMount(() => {
        if (props.initSdk) {
            init(props.cyberplayerVersion).then(() => {
                initPlayer();
            });
        } else {
            initPlayer();
        }
    });
    const controlControlsBar = (onlyShow: boolean) => {
        player.current.setControls(true);
        if (timer.current) {
            clearTimeout(timer.current);
            timer.current = null;
        }
        if (!onlyShow) {
            timer.current = setTimeout(() => {
                player.current.setControls(false);
            }, 1000);
        }
    };
    const cyberplayerMouseMoveHandler = () => {
        if (player.current) {
            if (player.current.getState() === 'playing') {
                controlControlsBar(false);
            }
            if (player.current.getState() === 'paused') {
                controlControlsBar(true);
            }
        }
    };
    const cyberplayerMouseClickHandler = () => {
        setTimeout(() => {
            if (player.current && player.current.getState() === 'paused') {
                controlControlsBar(true);
            } else {
                controlControlsBar(false);
            }
        });
    };
    return (
        <div
            className="u-cyberplayer"
            onMouseMove={cyberplayerMouseMoveHandler}
            onClick={cyberplayerMouseClickHandler}
        >
            <div id={props.playerId || 'playercontainer'}></div>
            {
                showErrorTip && props?.config?.errorTip && (
                    <div className="error-tips">{props?.config?.errorTip}</div>
                )
            }
        </div>
    );
}, (prevProps, nextProps) => {
    return JSON.stringify(prevProps) === JSON.stringify(nextProps);
});

function init(cyberplayerVersion?: CyberplayerVersion) {
    return new Promise(resolve => {
        if ((window as any).cyberplayer) {
            resolve(true);
            return;
        }
        const script = document.createElement('script');
        // script.src = 'https://bce-cdn.bj.bcebos.com/jwplayer/*******/cyberplayer.js';
        if (cyberplayerVersion === '3.5.2') {
            script.src = 'https://bce.bdstatic.com/jwplayer/3.5.2/cyberplayer.js';
        } else if (cyberplayerVersion === '*******') {
            script.src = 'https://bce-cdn.bj.bcebos.com/jwplayer/*******/cyberplayer-evs.js';
        } else {
            script.src = 'https://bce.bdstatic.com/portal-cloud-server/dep/cyberplayer_v4.3.2/cyberplayer.js'; // 支持webRtc的版本
        }
        document.head.appendChild(script);
        script.onload = () => {
            resolve(true);
        };
    });
}

// 详细文档
// https://cloud.baidu.com/doc/MCT/s/yjwvz4xm8#cyberplayer
// http://cyberplayer.bcelive.com/demo/new/index.html
