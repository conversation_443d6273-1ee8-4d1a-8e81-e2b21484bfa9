:global {
    .u-cyberplayer {
        width: 100%;
        height: 100%;
        position: relative;
        .jw-controlbar {
            width: 100%;
        }
        .jw-reset.jw-title {
            display: none;
        }
    
        .jw-state-buffering {
            &::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 30px;
                height: 30px;
                background: url('https://bce.bdstatic.com/developer-static/imgs/live/loading.svg') no-repeat;
                background-size: 100%;
                animation: circle 2s ease infinite;
            }
        }
    
        .jw-error,
        .jw-state-error,
        .jw-state-buffering  {
            .jw-display-icon-container {
                display: none;
            }
        }
    
        .jw-state-idle,
        .jw-state-paused {
            .jw-display-icon-container.jw-background-color {
                display: block;
                background-color: #fff;
                border: none;
                width: 56px;
                height: 56px;
                position: relative;
                z-index: 1;
                &::before {
                    content: '';
                    display: block;
                    width: 26px;
                    height: 26px;
                    position: absolute;
                    top: 50%;
                    left: calc(50% + 2px);
                    transform: translate(-50%, -50%);
                    background: url('https://bce.bdstatic.com/developer-static/imgs/live/start_btn.svg') no-repeat;
                    background-size: 100%;
                }
                .jw-icon {
                    display: none;
                }
            }
        }
        .error-tips {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            background-color: rgba(0, 0, 0, .4);
            z-index: 1212;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #FFFFFF;
            letter-spacing: 0;
            line-height: 24px;
            font-weight: 400;
            padding-left: 10px;
        }
    }
}

@keyframes circle {
    0% {
        transform: translate(-50%, -50%) rotateZ(0);
    }
    100% {
        transform: translate(-50%, -50%) rotateZ(360deg);
    }
}