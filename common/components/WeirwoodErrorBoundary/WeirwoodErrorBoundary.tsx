import {Component} from 'react';
import {weirwoodOptions} from '@common/config/weirwood';

let _weirwood: any = null;
export class WeirwoodErrorBoundary extends Component<any, {hasError: boolean}> {

    constructor(props: any) {
        super(props);
        this.state = {
            hasError: false,
        };
    }

    static getDerivedStateFromError() {
        return {hasError: true};
    }

    async componentDidMount() {
        if (this.isClient) {
            const Weirwood = (await import('@baidu/weirwood-sdk'));
            _weirwood = Weirwood && Weirwood.init(weirwoodOptions);
        }
    }

    get isClient() {
        return typeof window === 'object';
    }

    componentDidCatch(error: any) {
        if (this.isClient && _weirwood) {
            _weirwood.error.captureException(error);
        }
    }

    render() {
        return this.props.children;
    }
}
