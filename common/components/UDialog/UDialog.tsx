/**
 * @file 官网公共对话框
 */

import {useStateRef} from '@baidu/bce-hooks';
import {Ioc} from '@baidu/bce-decorators';
import {UDynamicService} from '@baidu/bce-services';
import {UModalMask} from '../UModalMask/UModalMask';

import styles from './main.module.less';

// eslint-disable-next-line no-unused-expressions
styles.a;

export interface UDialogProps {
    className?: string;
    title: string;
    content: string | JSX.Element;
    okText: string;
    cancelText?: string;
    showCloseIcon?: boolean;
    maskClosable?: boolean;
    zIndex?: number;
    onCancel?: () => void;
    onOk?: (close: () => void) => void;
}

const UDialogComponent = (props: UDialogProps) => {
    const {
        cancelText,
        className,
        content,
        maskClosable,
        okText,
        onCancel,
        onOk,
        showCloseIcon,
        title,
        zIndex,
    } = props;
    const [show, setShow] = useStateRef<boolean>(true);
    const cancelHandler = () => {
        setShow(false);
        onCancel?.();
    };

    const close = () => {
        setShow(false);
    };

    return (
        <UModalMask
            className={`u-dialog ${className}`}
            show={show}
            maskClosable={maskClosable}
            onClose={cancelHandler}
            style={{zIndex}}
        >
            <div className="u-dialog-content">
                <div className="u-dialog-head">{title}</div>
                <div className="u-dialog-center">
                    <div className="u-dialog-body">
                        {content}
                    </div>
                </div>
                <div className="u-dialog-footer">
                    {Boolean(cancelText) && (
                        <span
                            className="u-dialog-button u-dialog-button-cancel"
                            onClick={() => cancelHandler()}
                        >
                            {cancelText}
                        </span>
                    )}
                    <span
                        className="u-dialog-button"
                        onClick={() => onOk?.(close)}
                    >
                        {okText}
                    </span>
                </div>
                {
                    showCloseIcon && <div className="u-dialog-close-icon" onClick={cancelHandler}></div>
                }
            </div>
        </UModalMask>
    );
};

type UDialogType = typeof UDialogComponent & {
    open: (props: UDialogProps) => void;
};

const UDialog = UDialogComponent as UDialogType;

UDialog.open = (props: UDialogProps) => {
    Ioc(UDynamicService).open({
        component: UDialog,
        props,
    });
};

export {UDialog};
