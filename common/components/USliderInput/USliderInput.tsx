import {InputNumber, Slider} from 'antd';
import {USliderInputProps} from '@common/interface/common';
import style from './USliderInput.module.less';

export default function USliderInput(props: USliderInputProps) {
    const {value, onChange, min, max, unit, sliderProps, inputNumberProps} = props;
    const average = min && max ? Math.floor((min + max) / 2) : null;

    // 避免 value 为 null 的情况
    const parser = (valueStr: number | string) => {
        return Number(valueStr).toFixed(0);
    };

    return (
        <div className={style['silder-input']}>
            <Slider
                className={style.slider}
                value={value as any}
                onChange={onChange}
                min={min}
                max={max}
                marks={{
                    [min]: `${min}${unit}`,
                    [average]: `${average}${unit}`,
                    [max]: `${max}${unit}`,
                }}
                {...sliderProps}
            />

            <InputNumber
                className={style.input}
                value={value as any}
                onChange={onChange as any}
                min={min}
                max={max}
                parser={parser}
                {...inputNumberProps}
            />
            {unit}
        </div>
    );
}
