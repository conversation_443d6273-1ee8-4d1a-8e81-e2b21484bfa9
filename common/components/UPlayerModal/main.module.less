@import '@styles/variables.less';

.modal-mask {
    background-color: rgba(0, 0, 0, 0.9);
    :global {
        .content-container {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 1000px;
            height: 515px;
            background-color: #151515;
            .jw-preview {
                background-color: transparent;
            }
            .jw-controls {
                font-size: 12px;
                .jw-icon {
                    font-size: 1.5em;
                }
            }
            .jw-controlbar {
                font-size: 12px;
                .jw-text {
                    display: inline;
                }
                .jw-slider-time {
                    top: -6px;
                }
            }
        }
    }
    .close-icon {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 16px;
        height: 16px;
        cursor: pointer;
        top: calc(50% - 252px);
        right: calc(50% - 524px);

        &::before,
        &::after {
            content: '';
            position: absolute;
            display: block;
            width: 2px;
            height: 22px;
            background: #ffffff;
        }

        &::before {
            transform: rotate(45deg);
        }
        
        &::after {
            transform: rotate(135deg);
        }
    }
}

@media screen and (max-width: @MobileWidth) {
    .modal-mask {
        :global {
            .content-container {
                width: 100%;
                height: 282px;
            }
        }
        .close-icon {
            top: calc(50% - 168px);
            right: 8px;
        }
    }
}