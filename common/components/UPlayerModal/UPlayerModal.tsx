/**
 * @file 视频播放弹窗
 */

import {UModalMask} from '@common/components/UModalMask/UModalMask';
import {useState} from 'react';
import {Cyberplayer} from '@common/components/Cyberplayer/Cyberplayer';
import {CyberplayerConfig} from '@common/interface/common';

import styles from './main.module.less';

export const UPlayerModal = (props: {
    config: CyberplayerConfig;
    hideCloseIcon?: boolean;
    initSdk?: boolean;
}) => {
    const [show, setShow] = useState<boolean>(true);
    const cancelHandler = () => {
        setShow(false);
    };

    return (
        <UModalMask className={styles['modal-mask']} show={show} maskClosable onClose={cancelHandler}>
            <>
                <div id="playercontainer">
                    <Cyberplayer
                        initSdk={props.initSdk}
                        onload={player => {
                            player.play();
                        }}
                        config={{
                            controls: 'over',
                            stretching: 'uniform',
                            image: '',
                            ...props.config,
                        }}
                    />
                </div>
                {
                    !props.hideCloseIcon && (
                        <i className={styles['close-icon']} onClick={cancelHandler}></i>
                    )
                }
            </>
        </UModalMask>
    );
};
