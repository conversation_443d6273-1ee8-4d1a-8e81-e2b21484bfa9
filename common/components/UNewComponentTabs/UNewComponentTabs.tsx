import {useRef, useState} from 'react';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import classNames from 'classnames';
import {ReactSVG} from 'react-svg';
import {debounce, isFunction} from 'lodash';
import style from './UNewComponentTabs_1200.module.less';

export function UNewComponentTabs(props: {
    items: Array<{
        key: string;
        label: string;
        children: JSX.Element | ((activeTab: string) => JSX.Element);
        icon?: string;
    }>;
    isMobile: boolean;
    whiteArrowBg?: boolean;
    wrapperClassName?: string;
    leftcolorClassName?: string;
    rightcolorClassName?: string;
}) {
    const {items, whiteArrowBg, wrapperClassName, leftcolorClassName, rightcolorClassName} = props;
    const [activeTab, setActiveTab] = useStateRef(items[0].key);
    const [scrollLeft, setScrollLeft] = useState(0);
    const [maxDistance, setMaxDistance] = useState(0);
    const contentDom = useRef<HTMLUListElement>(null);
    const containerDom = useRef<HTMLDivElement>(null);
    const viewDistance = useRef(0);

    const initNavBaseData = debounce(() => {
        if (contentDom.current && containerDom.current) {
            const contentWidth = contentDom.current.offsetWidth;
            const containerWidth = containerDom.current.offsetWidth;
            viewDistance.current = containerWidth;
            setMaxDistance(contentWidth - containerWidth);
        }
    }, 300);

    useOnMount(() => {
        initNavBaseData();
        window.addEventListener('resize', initNavBaseData);
    });

    const clickTab = (e: React.MouseEvent, tab: typeof props.items[0]) => {
        if (tab.key !== activeTab) {
            setActiveTab(tab.key);
        }
        let target = e.target as HTMLElement;
        if (target.tagName === 'DIV' && target.parentElement.tagName === 'LI') {
            target = target.parentElement;
        }
        const container = containerDom.current;
        const liRect = target.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        // 如果 li 左侧超出容器
        if (liRect.left < containerRect.left) {
            container.scrollBy({
                left: liRect.left - containerRect.left - 30,
                behavior: 'smooth',
            });
        }
        // 如果 li 右侧超出容器
        else if (liRect.right > containerRect.right) {
            container.scrollBy({
                left: liRect.right - containerRect.right + 30,
                behavior: 'smooth',
            });
        }
    };

    return (
        <div
            className={classNames(
                style['tabs-container']
            )}
        >
            <div
                className={
                    classNames(
                        style['tabs-nav'],
                        items.length > 1 && style['tabs-nav-show']
                    )
                }
            >
                <div
                    className={style['tabs-nav-wrap']}
                    ref={containerDom}
                    onScroll={e => {
                        setScrollLeft((e.target as any).scrollLeft);
                    }}
                >
                    <ul
                        ref={contentDom}
                        className={style['tabs-nav-ul']}
                    >
                        {items.map(tab => (
                            <li
                                key={tab.key}
                                onClick={e => clickTab(e, tab)}
                                className={classNames(
                                    style['tabs-nav-item'],
                                    wrapperClassName,
                                    activeTab === tab.key
                                        ? style['tabs-nav-item-active']
                                        : style['tabs-nav-item-inactive']
                                )}
                            >
                                {
                                    tab.icon && (
                                        <ReactSVG
                                            className={style['label-icon']}
                                            src={tab.icon}
                                        />
                                    )
                                }
                                {tab.label}
                            </li>
                        ))}
                    </ul>
                </div>
                <div
                    className={classNames(
                        style['scroll-btn'],
                        style['scroll-btn-left'],
                        scrollLeft > 0 && style['btn-show'],
                        leftcolorClassName,
                        whiteArrowBg && style['scroll-btn-white-bg']
                    )}
                    onClick={() => {
                        containerDom.current.scroll({
                            left: 0,
                            behavior: 'smooth',
                        });
                        setScrollLeft(0);
                    }}
                >
                    <ReactSVG src="https://bce-cdn.bj.bcebos.com/portal-cloud-server/images/index-product/icon_leftarrow_black.svg" />
                </div>
                <div
                    className={classNames(
                        style['scroll-btn'],
                        style['scroll-btn-right'],
                        rightcolorClassName,
                        (maxDistance > 0 && scrollLeft < (maxDistance - 1)) && style['btn-show'],
                        whiteArrowBg && style['scroll-btn-white-bg']
                    )}
                    data-maxDistance={maxDistance}
                    onClick={() => {
                        const max = containerDom.current.offsetWidth;
                        containerDom.current.scroll({
                            left: max,
                            behavior: 'smooth',
                        });
                        setScrollLeft(max);
                    }}
                >
                    <ReactSVG src="https://bce-cdn.bj.bcebos.com/portal-cloud-server/images/index-product/icon_leftarrow_black.svg" />
                </div>
            </div>
            <div className={style['tabs-content']}>
                {
                    items.map(item => (
                        <div
                            key={item.key}
                            className={classNames(
                                style['tabs-content-item'],
                                activeTab === item.key && style['tabs-content-item-active']
                            )}
                        >
                            {isFunction(item.children) ? item.children(activeTab) : item.children}
                        </div>
                    ))
                }
            </div>
        </div>
    );
}
