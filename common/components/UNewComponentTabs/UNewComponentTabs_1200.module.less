@import '@styles/mixin.less';

.tabs-container {
    .tabs-nav {
        margin-bottom: 20px;
        height: 36px;
        overflow: hidden;
        position: relative;
        display: none;
        width: calc(100% + 64px);
        position: relative;
        left: -32px;
        padding: 0 32px;
        .media-max(599, {
            width: calc(100% + 32px);
            left: -16px;
            padding: 0 16px;
        });
        &.tabs-nav-show {
            display: block;
        }
        .tabs-nav-wrap {
            width: 100%;
            overflow-x: scroll;
            overflow-y: hidden;
            padding-bottom: 20px;
            .tabs-nav-ul {
                display: flex;
                flex-wrap: nowrap;
                width: max-content;
                margin: 0 auto;
                gap: 12px;
                .tabs-nav-item {
                    height: 36px;
                    line-height: 36px;
                    padding: 0 20px;
                    border: 1PX solid rgba(9,18,33,0.12);
                    border-radius: 18px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    transition: all .3s;
                    user-select: none;
                    transition: all 0.1s linear;
                    font-family: PingFangSC-Semibold;
                    font-size: 12px;
                    color: #091221;
                    text-align: center;
                    font-weight: 600;
                    .media-max(767, {
                        padding: 0 16px;
                    });
                    .label-icon {
                        width: 16px;
                        height: 16px;
                        margin-right: 2px;
                        > div {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        div,
                        svg {
                            width: 100%;
                            height: 100%;
                        }
                    }
                    &:first-child {
                        margin-left: 0;
                    }
                    // &:nth-child(2) {
                    //     width: 142px;
                    // }
                    &:hover  {
                        color: #2468F2;
                        svg *[fill]:not([fill="none"]) {
                            fill: #2468F2;
                        }
                    }
                    &.tabs-nav-item-active {
                        border-color: rgba(21,27,38,1);
                        color: #151B26;
                        svg *[fill]:not([fill="none"]) {
                            fill: #091221;
                        }
                    }
                }
            }
        }
        .scroll-btn {
            position: absolute;
            width: 50px;
            height: 100%;
            z-index: 1;
            top: 0;
            opacity: 0;
            transition: all 0.3s;
            background-color: #F2F5F9;
            cursor: pointer;
            .media-max(600, {
                width: 36px;
            });
            &::after {
                content: '';
                position: absolute;
                width: 20px;
                height: 100%;
                top: 0;
            }
    
            > div {
                width: 16px;
                height: 16px;
                position: absolute;
                top: 50%;
                left: 8px;
                transform: translateY(-50%);
            }
            &:hover {
                > div {
                    svg g[stroke="#091221"] {
                        stroke: #2468F2;
                    }
                }
            }
            &.scroll-btn-left {
                left: 0px;
                &::after {
                    background-image: linear-gradient(270deg, rgba(242, 245, 249, 0), #F2F5F9);
                    right: -20px;
                }
                > div {
                    transform: translateY(-50%) rotateZ(180deg);
                    right: 8px;
                    left: auto;
                }
            }
            &.scroll-btn-right {
                right: 0;
                &::after {
                    background-image: linear-gradient(270deg, #f2f5f9, rgba(242, 245, 249, 0));
                    left: -20px;
                }
            }
            &.btn-show {
                opacity: 1;
                z-index: 1;
            }
            &.scroll-btn-white-bg {
                background-color: #fff;
                &.scroll-btn-left {
                    &::after {
                        background-image: linear-gradient(270deg, rgba(242, 245, 249, 0), #ffffff);
                    }
                }
                &.scroll-btn-right {
                    &::after {
                        background-image: linear-gradient(270deg,#ffffff,rgba(242,245,249,0));
                    }
                }
            }
        }
    }
    .tabs-content {
        position: relative;
        .tabs-content-item {
            // position: absolute;
            // width: 100%;
            // height: max-content;
            // left: 0;
            // top: 0;
            // transition: opacity 13s;
            &:not(&.tabs-content-item-active) {
                display: none;
            }
            &-active {
                // z-index: 1;
                display: block;
            }
        }
    }
}