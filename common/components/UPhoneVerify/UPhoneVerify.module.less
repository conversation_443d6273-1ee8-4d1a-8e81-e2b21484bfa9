
@import '@styles/variables.less';

.u-phone-verify {
    .phone-wrapper {
        margin-bottom: 20px;
        // 现在不需要label样式
        .label {

        }
        .content {
            display: flex;
            align-items: center;
            .text,
            .time-text {
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: @CB;
                height: 20px;
                line-height: 20px;
                font-weight: 400;
                padding-left: 8px;
                cursor: pointer;
                &:hover {
                    color: #528EFF;
                }
            }
            .time-text,
            .time-text:hover {
                cursor: text;
                color: rgba(@C0, 0.4);
            }
        }
    }
    .code-wrapper {
        margin-bottom: 20px;
    }
    .code-wrapper,
    .phone-wrapper {
        .error-msg {
            font-size: 12px;
            color: #F33E3E;
        }
    }

    :global {
        .ant-input {
            width: 278px;
            height: 32px;
            border: 1px solid rgba(@C08);
            border-radius: 4px;
            font-size: 12px;
            &:hover,
            &:focus {
                border-color: rgba(36, 104, 242, 1);
                box-shadow: none;
            }
        }
    }
}