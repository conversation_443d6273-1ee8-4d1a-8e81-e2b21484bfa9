import {Input, InputProps} from 'antd';
import {ChangeEvent, useState, useRef} from 'react';
import classNames from 'classnames';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import style from './UPhoneVerify.module.less';

type GroupItemObj = {
    label?: string;
    inputProps?: InputProps;
};

type PhoneResObj = {
    // authCode: string;
    message: string;
    code: number;
};

type CodeResObj = Pick<PhoneResObj, 'message' | 'code'>;

const phoneRule = /^1[123456789]\d{9}$/;


const getErrorMessage = (e: PhoneResObj | CodeResObj) => {
    return e.code !== 200 && e.message ? e.message : '';
};

export function UPhoneVerify(props: {
    phone?: GroupItemObj;
    code?: GroupItemObj;
    productName: string;
    className?: string;
    // 验证码验证成功后的回调
    verifySuccessCallBack?: (e: {phone: string, code: string}) => void;
    verifyFailCallBack?: () => void;
}) {
    const [phoneNumber, setPhoneNumber] = useState<string>('');
    const [time, setTime] = useState<number>(60);
    const timerRef = useRef<NodeJS.Timer>(null);
    const [phoneRes, setPhoneRes] = useState<PhoneResObj>({
        // authCode: '',
        message: '',
        code: 200,
    });
    const [codeRes, setCodeRes] = useState<CodeResObj>({
        message: '',
        code: 200,
    });
    const clearTimer = () => {
        clearInterval(timerRef.current as any);
        timerRef.current = null;
    };
    const startTimer = () => {
        if (time < 60 && time >= 0) {
            return;
        }
        if (timerRef.current) {
            clearTimer();
        }
        setTime(60);
        timerRef.current = setInterval(() => {
            setTime(e => {
                if (e <= 0) {
                    clearTimer();
                }
                return e <= 0 ? 60 : e - 1;
            });
        }, 1000);
    };
    const getCodeHander = () => {
        if (phoneRule.test(phoneNumber)) {
            startTimer();
            setPhoneRes(e => ({...e, message: ''}));
            netService.post<{phoneNumber: string, productName: string}, PhoneResObj>(
                urlConst.POST_GROUPBUY_AUTHCODE,
                {
                    phoneNumber,
                    productName: props.productName || 'TMS',
                }
            ).then(res => {
                // 校验过后的手机号，接口code不等于200，一定是接口本身的问题，这里提示让用户重试
                if (res.result.code === 200) {
                    setPhoneRes(res.result);
                } else {
                    setPhoneRes({...res.result, message: '出错了，请重试'});
                    clearTimer();
                }
            }).catch(e => {
                clearTimer();
                setPhoneRes({
                    // authCode: '',
                    message: e?.message?.global ?? '网络出错，请重试',
                    code: -1,
                });
            });
        } else {
            setPhoneRes(e => ({...e, message: '请检查您的手机号', code: -1}));
        }
    };
    const phoneChangeHandler = (e: ChangeEvent<HTMLInputElement>) => {
        setPhoneNumber(e.target.value);
        props?.phone?.inputProps?.onChange && props.phone.inputProps.onChange(e);
    };
    const codeChangeHandler = (e: ChangeEvent<HTMLInputElement>) => {
        const val = e.target.value;
        // 前端暂不校验验证码，直接发请求校验
        if (/^[0-9]{6}$/.test(val) && phoneRule.test(phoneNumber)) {
            netService.post<{authCode: string, phoneNumber: string, productName: string}, CodeResObj>(
                urlConst.POST_GROUPBUY_AUTHCODE_VERIFY,
                {
                    authCode: val,
                    phoneNumber,
                    productName: props.productName || 'TMS',
                }
            ).then(res => {
                if (res.result.code === 200) {
                    setCodeRes(res.result);
                    props.verifySuccessCallBack({
                        phone: phoneNumber,
                        code: val,
                    });
                } else {
                    props.verifyFailCallBack();
                    setCodeRes({...res.result, message: '验证码错误'});
                }
            }).catch(() => {
                setCodeRes(e => ({...e, message: '网络出错'}));
            });
        }
    };
    // 错误信息
    const phoneErrorMsg = getErrorMessage(phoneRes);
    const codeErrorMsg = getErrorMessage(codeRes);
    return (
        <div className={classNames(style['u-phone-verify'], props.className)}>
            <div className={style['phone-wrapper']}>
                {
                    props?.phone?.label && (
                        <span className={style['label']}>{props?.phone?.label}</span>
                    )
                }
                <div>
                    <div className={style['content']}>
                        <Input
                            placeholder="请输入手机号码"
                            {...props?.phone?.inputProps}
                            maxLength={11}
                            value={phoneNumber}
                            onChange={e => phoneChangeHandler(e)}
                            status={phoneErrorMsg && 'error'}
                        />
                        {
                            time < 60 ? (
                                <span className={style['time-text']}>{`重新获取（${time}）`}</span>
                            ) : (
                                <span
                                    className={style['text']}
                                    onClick={getCodeHander}
                                >
                                    获取验证码
                                </span>
                            )
                        }
                    </div>
                    {
                        phoneErrorMsg ? (
                            <p className={style['error-msg']}>{phoneErrorMsg}</p>
                        ) : null
                    }
                </div>
            </div>
            <div className={style['code-wrapper']}>
                {
                    props?.code?.label && (
                        <span className={style['label']}>{props?.code?.label}</span>
                    )
                }
                <div>
                    <div className={style['content']}>
                        <Input
                            placeholder="请输入验证码"
                            {...props?.code?.inputProps}
                            maxLength={6}
                            status={codeErrorMsg && 'error'}
                            onChange={e => codeChangeHandler(e)}
                        />
                    </div>
                    {
                        codeErrorMsg ? (
                            <p className={style['error-msg']}>{codeErrorMsg}</p>
                        ) : null
                    }
                </div>
            </div>
        </div>
    );
}
