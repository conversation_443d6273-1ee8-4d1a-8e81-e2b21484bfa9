import {Upload, UploadProps, message} from 'antd';
import cn from 'classnames';
import {RcFile} from 'antd/lib/upload';
import style from './UUpload.module.less';

export function UUpload(props: UploadProps & {value: any}) {
    console.log('UUpload', props);
    return (
        <Upload
            className={cn(style.upload, props.className)}
            action="/api/survey/upload"
            beforeUpload={(file: RcFile) => {
                if (file.size <= 50 * 1024 * 1024) {
                    return true;
                }
                message.error('文件大小不能超过50M');
                return false;
            }}
            {...props}
            fileList={props.value?.fileList || []}
        >
            <span className={style.img} />
            <span className={style.text}>
                请选择要上传的文件
            </span>
        </Upload>
    );
}
