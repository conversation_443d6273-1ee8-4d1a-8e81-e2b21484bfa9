@import '@styles/variables.less';

@media (max-width: @MobileWidth) {
    .u-select {
        font-size: 13px;
        .icon {
            width: 14px;
            height: 14px;
        }
        &:global(.ant-select) {
            :global {
                .ant-select-selector {
                    height: 36px;
                    padding: 0 11px;
                    
                    .ant-select-selection-search-input {
                        height: 34px;
                        line-height: 34px;
                        font-size: 13px;
                    }
                }
            }
        }
        &:global(.ant-select .ant-select-selector .ant-select-selection-item), &:global(.ant-select .ant-select-selector .ant-select-selection-placeholder) {
            line-height: 34px;
        }
        &:global(.ant-select.ant-select-show-arrow .ant-select-selection-item), &:global(.ant-select.ant-select-show-arrow .ant-select-selection-placeholder) {
            padding-right: 20px;
        }
    }
    
    .u-select-dropdown {
        z-index: 99;
        box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
        border-radius: 2px;
        :global {
            .rc-virtual-list-holder[style*="auto"] {
                .rc-virtual-list-scrollbar {
                    display: block !important;
                }
            }
            .ant-select-item {
                min-height: 36px;
                padding: 0 12px;
                line-height: 36px;
            }
        }
    }
}