@import '@styles/variables.less';

.u-select {
    width: 100%;
    .icon {
        width: 16px;
        height: 16px;
        background: url(https://bce.bdstatic.com/p3m/common-service/uploads/arrow-down_c8dc68d.svg) no-repeat center / cover;
        transition: transform 0.3s linear;
    }
    &:global(.ant-select-open) {
        .icon {
            transform: rotate(180deg);
        }
    }
    &:global(.ant-select) {
        :global {
            .ant-select-selector {
                height: 28px;
                border: 1px solid @CW;
                padding: 0 7px;
                
                .ant-select-selection-search-input {
                    height: 26px;
                    line-height: 26px;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: @C0;
                    font-weight: 400;
                }
            }
        }
    }
    // 悬浮边框变蓝
    &:global(.ant-select:not(.ant-select-disabled):hover .ant-select-selector) {
        border-color: @CB;
    }
    &:global(.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector) {
        box-shadow: none;
        border-color: @CB;
    }
    &:global(.ant-select-focused:not(.ant-select-open):not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector) {
        border-color: @CW;
        &:hover {
            border-color: @CB;
        }
    }
    &:global(.ant-select .ant-select-selector .ant-select-selection-item), &:global(.ant-select .ant-select-selector .ant-select-selection-placeholder) {
        line-height: 26px;
    }
    &:global(.ant-select.ant-select-show-arrow .ant-select-selection-item), &:global(.ant-select.ant-select-show-arrow .ant-select-selection-placeholder) {
        padding-right: 20px;
    }
    &:global(.ant-select.ant-select-open .ant-select-selection-item ) {
        color: @C0;
    }
}
.u-select-dropdown {
    background: @CW;
    box-shadow: 0 6px 16px 2px rgba(7, 12, 20, 0.08);
    border-radius: 2px;
    :global {
        .ant-select-item {
            min-height: 28px;
            box-sizing: border-box;
            padding: 2px 8px;
            font-weight: normal;
            font-family: PingFangSC-Regular;
            color: @C0;
            letter-spacing: 0;
            line-height: 24px;
            font-weight: 400;
        }
        .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
            background-color: @CW;
            color: @CB;
        }
        .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
            background-color: rgba(@CB, 0.08);
        }
        .rc-virtual-list-scrollbar {
            width: 7px !important;
            .rc-virtual-list-scrollbar-thumb {
                width: 3px !important;
                background-color: rgba(@C0, 0.12) !important;
                border-radius: 2px !important;
            }
        }
    }
}
