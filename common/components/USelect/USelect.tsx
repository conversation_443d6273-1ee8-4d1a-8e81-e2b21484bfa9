/**
 * @file 下拉公共组件
 * <AUTHOR>
 */

import {getScrollTop} from '@common/helper/page';
import {Select, SelectProps} from 'antd';
import {useState, useEffect, memo, useRef} from 'react';
import {useOnMount} from '@baidu/bce-hooks';
import {omit} from 'lodash';
import cn from 'classnames';
import {isAndroid} from '@baidu/bce-helper';
import styles from './main.module.less';
import stylesM from './main_m.module.less';

const {Option} = Select;

export type USelectProps = SelectProps & {triggerChangeDefault?: boolean};
type USelectType = ((props: USelectProps) => JSX.Element) & typeof Select;

export const USelect: USelectType = memo((props: USelectProps) => {
    const {options, onChange, value, triggerChangeDefault} = props;
    const [listHeight, setListHeight] = useState(160);
    const flag = useRef(true);
    useOnMount(() => {
        window.innerWidth <= 700 && setListHeight(220 * window.innerWidth / 375);
    });
    useEffect(() => {
        if (options && options.length > 0 && onChange && triggerChangeDefault && flag.current) {
            const item = options.find(item => item.value === value);
            onChange(item ? item.value : options[0].value, item || options[0]);
            flag.current = false;
        }
    }, [onChange, options, triggerChangeDefault, value]);
    return (
        <Select
            placement="bottomRight"
            {...(omit(props, 'triggerChangeDefault'))}
            className={cn(styles['u-select'], stylesM['u-select'], props.className)}
            popupClassName={cn(styles['u-select-dropdown'], stylesM['u-select-dropdown'], props.popupClassName)}
            listHeight={props.listHeight || listHeight}
            suffixIcon={props.suffixIcon || <span className={styles.icon}></span>}
            onFocus={e => {
                // 移动端做处理
                if (window.innerWidth <= 700 && e.target.getBoundingClientRect().top > window.outerHeight * 0.35 && isAndroid()) {
                    window.scrollTo({
                        top: getScrollTop() + e.target.getBoundingClientRect().top - window.outerHeight * 0.35,
                        left: document.documentElement.scrollLeft || document.body.scrollLeft,
                        behavior: 'smooth',
                    });
                }
            }}
        >
            {props.children}
        </Select>
    );
}, (prev, next) => {
    if (prev.value || next.value) {
        return (prev.value === next.value) && (JSON.stringify(prev.options) === JSON.stringify(next.options));
    }
    return next?.options?.length <= 0;
}) as any;

USelect.Option = Option;

