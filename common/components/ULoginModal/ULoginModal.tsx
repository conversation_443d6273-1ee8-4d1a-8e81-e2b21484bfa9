/**
 * @file 登录modal
 * <AUTHOR>
 */

import {Ioc} from '@baidu/bce-decorators';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {UEnvService} from '@common/services/env';
import {UDynamicService} from '@baidu/bce-services';
import {UModalMask} from '../UModalMask/UModalMask';

import styles from './main.module.less';

const isSandbox = Ioc(UEnvService).isProdSandbox;

type ULoginModalComponentProps = {
    maskClosable?: boolean;
    hideCloseIcon?: boolean;
    loginUrl?: string;
    onOk?: (info: {regSuccess: boolean}) => void;
    onCancel?: () => void;
};

const ULoginModalComponent = (props: ULoginModalComponentProps) => {
    const [show, setShow] = useStateRef<boolean>(true);
    const [, , flagRef] = useStateRef<boolean>(false);
    const cancelHandler = () => {
        setShow(false);
        props.onCancel && props.onCancel();
    };

    const close = () => {
        setShow(false);
    };

    useOnMount(() => {
        let regSuccess = false;
        const handleMessage = (e: MessageEvent<{loginSuccess?: boolean, regSuccess?: boolean}>) => {
            if (e.data?.loginSuccess) {
                close();
                if (!flagRef.current) {
                    flagRef.current = true;
                    props.onOk && props.onOk({regSuccess});
                }
            }
            if (e.data?.regSuccess) { // UC注册成功会发送regSuccess消息
                regSuccess = true;
            }
        };
        window.addEventListener('message', handleMessage, false);
        return () => {
            window.removeEventListener('message', handleMessage);
        };
    });

    return (
        <UModalMask
            show={show}
            maskClosable={props.maskClosable}
            onMaskClick={cancelHandler}
            className="u-login-modal"
        >
            <>
                <iframe
                    className={styles['iframe']}
                    width="600"
                    height="600"
                    src={props.loginUrl || `https://login.bce${isSandbox ? 'test' : ''}.baidu.com/?redirect=${encodeURIComponent(`https://${isSandbox
                        ? 'cloudtest' : 'cloud'}.baidu.com/inner-login.html`)}`}
                    referrerPolicy="unsafe-url"
                />
                {
                    !props.hideCloseIcon && <div className={styles['close-icon']} onClick={cancelHandler} />
                }
            </>
        </UModalMask>
    );
};

type ULoginModalType = typeof ULoginModalComponent & {
    open: (props: ULoginModalComponentProps) => void;
};

const ULoginModal = ULoginModalComponent as ULoginModalType;

ULoginModal.open = (props: ULoginModalComponentProps) => {
    Ioc(UDynamicService).open({
        component: ULoginModalComponent,
        props,
    });
};

export {ULoginModal};
