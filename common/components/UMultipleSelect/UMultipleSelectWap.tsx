/**
 * @file 支持设置多选和限制最多选多个的select组件 - wap
 */
import cn from 'classnames';
import {UMultipleSelectProps} from '@common/interface/common';
import {Popup} from 'antd-mobile';
import {useState} from 'react';
import style from './index_m.module.less';

// eslint-disable-next-line no-unused-expressions
style.a;

// TODO：处理多选
export const UMultipleSelectWap = (props: UMultipleSelectProps) => {
    const {value, mode, options, placeholder, maxLength = 1, defaultValue, onChange, id} = props;

    const [curValue, setCurValue] = useState<string | string[]>(mode === 'multiple' ? [] : '');
    const [showPopup, setShowPopup] = useState(false);

    const handlerAlertClick = (item: string) => {
        if (mode === 'multiple') {
            if (curValue.includes(item)) {
                setCurValue(prev => (prev as string[]).filter(cur => cur !== item));
            } else {
                const newValueSet = new Set([...(curValue as string[]), item]);
                setCurValue(options
                    .filter(({value}) => newValueSet.has(value as string))
                    .map(({value}) => value as string)
                );
            }
        } else {
            setCurValue(item);
            onChange(item, options);
            setShowPopup(false);
        }
    };

    const handleCancel = () => {
        setShowPopup(false);
        setCurValue(value || defaultValue || (mode === 'multiple' ? [] : ''));
    };

    const handleConfirm = () => {
        onChange(curValue, options);
        setShowPopup(false);
    };

    return (
        <>
            <div
                id={id}
                className="custom-survey-dialog-select"
                onClick={() => setShowPopup(true)}
            >
                <div className={cn('current-select', value?.length > 0 && 'selected')}>
                    {
                        Array.isArray(value) ? (
                            <div className="current-select-overflow">
                                {
                                    value.length > 0 ? (
                                        <>
                                            {value.map((val, index) => val && (
                                                <div
                                                    key={index.toString()}
                                                    className="current-select-overflow-item"
                                                >
                                                    {val}
                                                    <span
                                                        onClick={e => {
                                                            e.stopPropagation();
                                                            const newValue = (value as string[]).filter(cur => cur !== val);
                                                            setCurValue(newValue);
                                                            onChange(newValue, options);
                                                        }}
                                                        className="current-select-overflow-item-remove"
                                                    >
                                                    </span>
                                                </div>
                                            ))}
                                        </>
                                    ) : <span className="current-select-item">{placeholder || '请选择'}</span>
                                }
                            </div>
                        ) : (
                            <span className="current-select-item">{value || placeholder || '请选择'}</span>
                        )
                    }
                </div>
                <span className="select-icon">去选择</span>
            </div>
            <Popup
                visible={showPopup}
                onMaskClick={handleCancel}
                bodyClassName="custom-survey-dialog-select-popup"
                bodyStyle={{
                    borderTopLeftRadius: '1.6rem',
                    borderTopRightRadius: '1.6rem',
                    maxHeight: 'calc(100vh - 21.1rem)',
                }}
            >
                <div className="dialog-alert-header">
                    <span className="dialog-alert-header-close" onClick={handleCancel}></span>
                    <p className="dialog-alert-header-title">{placeholder}</p>
                    <span
                        className={cn('dialog-alert-header-confirm', mode === 'multiple' && 'show')}
                        onClick={handleConfirm}
                    >
                        确定
                    </span>
                </div>
                <ul className="dialog-alert-list">
                    {options?.map((item, index) => {
                        const isChecked = (Array.isArray(curValue) ? curValue.includes(item.value as string) : curValue === item.value);
                        return (
                            <li
                                // eslint-disable-next-line react/no-array-index-key
                                key={index}
                                className={cn(
                                    'dialog-alert-list-item',
                                    isChecked && 'checked',
                                    mode === 'multiple' && 'dialog-alert-list-item-multi',
                                    (mode === 'multiple' && curValue.length >= maxLength && !isChecked) ? 'forbidden' : ''
                                )}
                                onClick={() => handlerAlertClick(item.value as string)}
                            >
                                {item.value}
                            </li>
                        );
                    })}
                </ul>
            </Popup>
        </>
    );
};
