@import '@styles/variables.less';
@import '@styles/mixin.less';

:global {
    .custom-survey-dialog-select {
        width: 100%;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        min-height: 4.4rem;
        line-height: 2.1rem;
        background: #fff;
        border-radius: 0.2rem;
        padding: 0 1.2rem;
        border: 0.1rem solid rgba(34,34,34,.12);
    
        .current-select {
            width: 100%;
            padding-right: 52px;

            .current-select-item,
            .current-select-overflow-item {
                display: inline-block;
                font-family: PingFangSC-Regular;
                font-size: 13px;
                color: @C4;
                text-align: justify;
                line-height: 21px;
                font-weight: 400;
                vertical-align: top;
            }

            &.selected {
                .current-select-item,
                .current-select-overflow-item {
                    color: #222;
                }
            }

            // 多选内容
            .current-select-overflow {
                display: flex;
                flex: auto;
                flex-wrap: wrap;
                max-width: 100%;
                padding: 0.5rem 0;
                .current-select-overflow-item {
                    display: inline-block;
                    background: #F7F7F9;
                    border-radius: 1px;
                    margin-top: 4px;
                    margin-bottom: 4px;
                    margin-inline-end: 4px;
                    padding-inline-start: 8px;
                    padding-inline-end: 4px;
                    overflow: hidden;
                    white-space: pre;
                    text-overflow: ellipsis;
                    position: relative;
                    padding: 2px 23px 2px 6px;

                    .current-select-overflow-item-remove {
                        position: absolute;
                        top: 50%;
                        right: 6px;
                        transform: translateY(-50%);
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/icon-close_77cc15a.png') center/cover;
                    }
                }
            }
        }
    
        .select-icon {
            position: absolute;
            right: 1.2rem;
            display: inline-block;
            font-family: PingFangSC-Regular;
            font-size: 1.3rem;
            color: #2468f2;
            text-align: justify;
            line-height: 2.1rem;
            font-weight: 400;
            padding-right: 1.6rem;
            background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/select-icon@3x_4512222.png') right center;
            background-size: 1.4rem 1.4rem;
        }
    }
    
    // dropdown的弹窗样式
    .custom-survey-dialog-select-popup {
        .dialog-alert-header {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 4.8rem;
            padding: 0 1.25rem 0 1rem;
            text-align: center;
            box-sizing: border-box;
    
            .dialog-alert-header-title {
                position: relative;
                display: block;
                font-family: PingFangSC-Medium;
                font-size: 1.5rem;
                color: #151b26;
                text-align: center;
                line-height: 2.3rem;
                font-weight: 500;
                .ellipsis();
            }
            .dialog-alert-header-close {
                left: 1rem;
                top: 1.2rem;
                width: 2.4rem;
                height: 2.4rem;
                background: url(https://bce.bdstatic.com/p3m/common-service/uploads/back-icon_b5c276b.png) no-repeat 50%/cover;
                background-size: 2.4rem 2.4rem;
            }
            .dialog-alert-header-confirm {
                visibility: hidden;
                font-family: PingFangSC-Regular;
                font-size: 1.5rem;
                color: @CB;
                text-align: justify;
                line-height: 2.3rem;
                font-weight: 400;

                &.show {
                    visibility: visible;
                }
            }
        }
        .dialog-alert-list {
            padding-top: 1.3rem;
            width: 100%;
            height: calc(100vh - 25.9rem);
            overflow-y: auto;
            box-sizing: border-box;
            background-color: #fff;
    
            &-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                box-sizing: border-box;
                padding: 0 1.25rem;
                min-height: 4.4rem;
                font-family: PingFangSC-Regular;
                font-size: 1.3rem;
                color: #222;
                line-height: 2.1rem;
                font-weight: 400;
    
                &.forbidden {
                    pointer-events: none;
                    color: rgba(34, 34, 34, .4);
                    &::after {
                        background-position: -4.2rem 0;
                    }
                }
    
                &::after {
                    content: '';
                    flex-shrink: 0;
                    display: inline-block;
                    width: 1.6rem;
                    height: 1.6rem;
                    background-repeat: no-repeat;
                    background-size: auto 1.6rem;
                    background-position: 0 0;
                    background-image: url(https://bce.bdstatic.com/p3m/common-service/uploads/radio_8459532.png);
                }
    
                &.checked {
                    &::after {
                        background-position: -2.1rem 0;
                    }
                }

                &.dialog-alert-list-item-multi {
                    &::after {
                        background-image: url('https://bce.bdstatic.com/p3m/common-service/uploads/checkbox_be702e9.png');
                    }
                }
            }
        }
    }
}