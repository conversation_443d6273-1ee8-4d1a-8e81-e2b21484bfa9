/**
 * @file 支持设置多选和限制最多选多个的select组件
 */

import {UMultipleSelectProps} from '@common/interface/common';
import {Select} from 'antd';
import {useMemo} from 'react';

export const UMultipleSelect = (props: UMultipleSelectProps) => {
    const {value, options, maxLength, defaultValue} = props;
    const newOptions = useMemo(() => {
        const tempValue = value || defaultValue;
        if (options && tempValue && tempValue.length >= maxLength) {
            return options.map(option => (
                tempValue.includes(option.value) ? option : {disabled: true, ...option}
            ));
        }
        return options;
    }, [maxLength, options, value, defaultValue]);
    return <Select {...props} options={newOptions} />;
};
