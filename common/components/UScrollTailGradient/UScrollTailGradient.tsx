import {useRef, useState, useEffect} from 'react';
import {useOnMount} from '@baidu/bce-hooks';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import styles from './main.module.less';


export function UScrollTailGradient(props: {
    children: JSX.Element;
    tailBoxHeight?: number;
    loading: boolean;
}) {
    const {tailBoxHeight = 86, children, loading} = props;
    const refP = useRef<HTMLDivElement>(null);
    const refC = useRef<HTMLDivElement>(null);
    const [isScrollBottom, setIsScrollBottom] = useState<boolean>(false);
    useOnMount(() => {
        refP.current.addEventListener('scroll', e => {
            const scrollTop = (e.target as HTMLDivElement).scrollTop;
            if (refC.current.offsetHeight - scrollTop - refP.current.offsetHeight <= 10) {
                setIsScrollBottom(true);
            } else {
                setIsScrollBottom(false);
            }
        });
    });
    useEffect(() => {
        if (!loading) {
            setTimeout(() => {
                setIsScrollBottom(refC.current.offsetHeight <= refP.current.offsetHeight);
            }, 100);
        }
    }, [loading]);
    return (
        <div className={styles['scroll-tail-gradient-wrapper']}>
            <div ref={refP} className={styles['scroll-tail-gradient-scroll-container']}>
                <div ref={refC} className={styles['scroll-tail-gradient-container']}>
                    {children}
                </div>
            </div>
            {
                !isScrollBottom && (
                    <div className={styles['tail-box']} style={{height: tailBoxHeight}}></div>
                )
            }
        </div>
    );
}
