/**
 * @file 注册弹窗
 */

import {Ioc} from '@baidu/bce-decorators';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {UEnvService} from '@common/services/env';
import {pushProduct2Crm, pushQifuAndTmsCrm} from '@common/helper/page';
import {UModalMask} from '../UModalMask/UModalMask';

import styles from './main.module.less';

const isSandbox = Ioc(UEnvService).isProdSandbox;

export const UActivateModal = (props: {
    maskClosable?: boolean;
    hideCloseIcon?: boolean;
    onOk?: () => void;
    onCancel?: () => void;
}) => {
    const [show, setShow] = useStateRef<boolean>(true);
    const [, , flagRef] = useStateRef<boolean>(false);
    const cancelHandler = () => {
        setShow(false);
        props.onCancel && props.onCancel();
    };

    const close = () => {
        setShow(false);
    };

    useOnMount(() => {
        const handleMessage = (e: MessageEvent<{activateSuccess: boolean}>) => {
            if (e.data && e.data.activateSuccess) {
                close();
                if (!flagRef.current) {
                    flagRef.current = true;
                    if (/(qifu)|(qifu-sandbox)|(tms)/.test(location.host)) {
                        pushQifuAndTmsCrm();
                    } else {
                        pushProduct2Crm();
                    }
                }
                props.onOk && props.onOk();
            }
        };
        window.addEventListener('message', handleMessage, false);
        return () => {
            window.removeEventListener('message', handleMessage);
        };
    });

    return (
        <UModalMask show={show} maskClosable={props.maskClosable} onMaskClick={cancelHandler}>
            <>
                <iframe
                    className={styles['iframe']}
                    width="600"
                    height="600"
                    src={`${isSandbox ? 'https://cloudtest.baidu.com' : 'https://cloud.baidu.com'}/iam/#/iam/user/v2/activate~hideBar=1`}
                />
                {
                    !props.hideCloseIcon && <div className={styles['close-icon']} onClick={cancelHandler} />
                }
            </>
        </UModalMask>
    );
};
