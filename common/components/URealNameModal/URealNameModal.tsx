/**
 * @file 实名弹窗
 */

import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {Ioc} from '@baidu/bce-decorators';
import {UEnvService} from '@common/services/env';
import {UModalMask} from '../UModalMask/UModalMask';
import styles from './main.module.less';

const isSandbox = Ioc(UEnvService).isProdSandbox;

/**
 * 实名弹窗
 * @param {object} props
 * @param {boolean} props.hasLogin 是否获取登录态（默认已获取）
 * @param {boolean} props.maskClosable mask是否可点
 * @param {boolean} props.hideCloseIcon 是否显示
 * @param {function} props.onOk 登录成功
 * @param {function} props.onCancel 登录失败
 * @returns JSX.Element
 */
export const URealNameModal = (props: {
    hasLogin?: boolean;
    maskClosable?: boolean;
    hideCloseIcon?: boolean;
    onOk?: () => void;
    onCancel?: () => void;
}) => {
    const {hasLogin = true} = props;
    const [show, setShow] = useStateRef(true);

    const genRealNameUrl = () => {
        if (hasLogin) {
            const hostName = isSandbox ? 'https://qasandbox.bcetest.baidu.com' : 'https://console.bce.baidu.com';
            return `${hostName}/qualify/#/qualify/index`;
        } else {
            return `https://login.bce${isSandbox ? 'test' : ''}.baidu.com/?needQualify=1`;
        }
    };

    const cancelHandler = () => {
        setShow(false);
        props.onCancel && props.onCancel();
    };

    const close = () => {
        setShow(false);
    };

    useOnMount(() => {
        const handleMessage = (e: MessageEvent<{qualifySuccess: boolean}>) => {
            if (e.data && e.data.qualifySuccess) {
                close();
                props.onOk && props.onOk();
            }
        };
        window.addEventListener('message', handleMessage, false);
        return () => {
            window.removeEventListener('message', handleMessage);
        };
    });

    return (
        <UModalMask show={show} maskClosable={props.maskClosable} onMaskClick={cancelHandler}>
            <>
                <iframe
                    className={styles['realName-iframe']}
                    width="700"
                    height="560"
                    src={genRealNameUrl()}
                    referrerPolicy="unsafe-url"
                />
                {
                    !props.hideCloseIcon && <div className={styles['close-icon']} onClick={cancelHandler} />
                }
            </>
        </UModalMask>
    );
};

// 实名iframe接入指南： https://ku.baidu-int.com/d/CfUeSZI3R6FiB8
