@import '@styles/variables.less';

:global {
    .has-umessage {
        display: block;
        .u-message {
            z-index: 100;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: none;

            display: flex;
            height: 40px;
            align-items: center;
            padding: 0 16px;
            box-sizing: border-box;
            background: @CW;
            box-shadow: 0 6px 16px 2px rgba(0,0,0,0.08);
            border-radius: 4px;

            &.u-message-fade-in {
                &:local {
                    animation: messageFadeIn 0.2s ease-out forwards;
                }
            }

            &.u-message-fade-out {
                &:local {
                    animation: messageFadeOut 0.2s ease-out forwards;
                }
            }
            span {
                font-size: 16px; // 控制默认svg icon大小
            }
            label {
                margin-left: 8px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: rgba(34,34,34,0.90);
                letter-spacing: 0;
                line-height: 24px;
                font-weight: 400;
                white-space: nowrap;
            }
        }
    }
}

@keyframes messageFadeOut {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}