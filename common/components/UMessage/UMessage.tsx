import {useOnMount} from '@baidu/bce-hooks';
import {unmountComponentAtNode, render} from 'react-dom';
import cx from 'classnames';
import {InfoCircleFilled, CheckCircleFilled, CloseCircleFilled, ExclamationCircleFilled} from '@ant-design/icons';

import styles from './main.module.less';
import stylesM from './main_m.module.less';

// eslint-disable-next-line no-unused-expressions
styles.a;
// eslint-disable-next-line no-unused-expressions
stylesM.a;

type MessageTypes = 'info' | 'error' | 'success' | 'warning';
type UMessageProps = {
    type?: MessageTypes;
    content: React.ReactNode;
    wrapper?: HTMLElement; // 在哪个容器里边打开
    style?: React.CSSProperties; // TODO：暂未添加
    icon?: React.ReactNode;
    duration?: number;
    transitionDuration?: number; // 退出动画效果延时
};

const typeToIcon = {
    info: <InfoCircleFilled style={{color: '#1677ff'}} />,
    success: <CheckCircleFilled style={{color: '#52c41a'}} />,
    error: <CloseCircleFilled style={{color: '#ff4d4f'}} />,
    warning: <ExclamationCircleFilled style={{color: '#faad14'}} />,
};

const getContainer = (wrapper: HTMLElement, type: MessageTypes) => {
    const container = wrapper.querySelector('.u-message');
    if (container) {
        unmountComponentAtNode(container);
        wrapper.contains(container) && wrapper.removeChild(container);
        wrapper.classList.remove('has-umessage');
    }
    const _container = document.createElement('div');
    _container.setAttribute('class', cx(`u-message u-message-${type} u-message-fade-in`));
    return _container;
};

const BaseMessage = (props: (UMessageProps & {onClose?: () => void})) => {
    const {type = 'info', content, icon, duration = 2000, onClose} = props;
    useOnMount(() => {
        const t = setTimeout(onClose, duration);
        return () => {
            t && clearTimeout(t);
        };
    });
    return (
        <>
            {icon || typeToIcon[type]}
            <label>{content}</label>
        </>
    );
};

export const UMessage = (props: UMessageProps) => {
    const {type = 'info', wrapper = document.body, transitionDuration = 200} = props;
    const _dom = getContainer(wrapper, type);
    wrapper.appendChild(_dom);
    wrapper.classList.add('has-umessage');

    const hanldeClose = () => {
        _dom.classList.remove('u-message-fade-in');
        _dom.classList.add('u-message-fade-out');
        setTimeout(() => {
            unmountComponentAtNode(_dom);
            wrapper.contains(_dom) && wrapper.removeChild(_dom);
            wrapper.classList.remove('has-umessage');
        }, transitionDuration);
    };

    render(
        <BaseMessage
            {...props}
            onClose={hanldeClose}
        />,
        _dom
    );
};
