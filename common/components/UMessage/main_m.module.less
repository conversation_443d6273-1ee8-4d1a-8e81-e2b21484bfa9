@import '@styles/variables.less';

@media (max-width: @MobileWidth) {
    :global {
        .has-umessage {
            display: block;
            .u-message {
                z-index: 100;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                transition: none;

                display: flex;
                height: 42px;
                align-items: center;
                padding: 0 16px;
                box-sizing: border-box;
                background: @CW;
                box-shadow: 0 3px 8px 1px rgba(0,0,0,0.08);
                border-radius: 4px;

                &.u-message-fade-in {
                    &:local {
                        animation: messageFadeIn 0.2s ease-out forwards;
                    }
                }

                &.u-message-fade-out {
                    &:local {
                        animation: messageFadeOut 0.2s ease-out forwards;
                    }
                }
                span {
                    font-size: 16px; // 控制默认svg icon大小
                }
                label {
                    margin-left: 8px;
                    font-family: PingFangSC-Regular;
                    font-size: 13px;
                    color: #222222;
                    text-align: justify;
                    line-height: 21px;
                    font-weight: 400;
                }
            }
        }
    }
}

@keyframes messageFadeIn {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes messageFadeOut {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}