/* eslint-disable @next/next/no-img-element */
import React from 'react';
import {useStateRef} from '@baidu/bce-hooks';
import {BOS_CDN_ORIGIN_URL} from '@common/constant/variableConst';
import {Ioc} from '@baidu/bce-decorators';
import {UDynamicService} from '@baidu/bce-services';
import {UModalMask} from '../UModalMask/UModalMask';
import styles from './index.module.less';

// eslint-disable-next-line no-unused-expressions
styles.a;

type ConfirmTypes = 'info' | 'error' | 'success' | 'warning';

const confirmTypesIconMap: {
    [val in ConfirmTypes]: string
} = {
    info: BOS_CDN_ORIGIN_URL + '/developer-static/imgs/common/info_fill.svg',
    error: '',
    success: '',
    warning: '',
};

interface UConfirmComponentProps {
    content: string | JSX.Element;
    title?: string;
    okText?: string;
    onOk?: (close: () => void) => void;
    cancelText?: string;
    onCancel?: () => void;
    hideCancel?: boolean;
    maskClosable?: boolean;
    style?: React.CSSProperties;
    type?: ConfirmTypes;
    id?: any;
    className?: string;
    hideTypeIcon?: boolean;
    hideCloseIcon?: boolean;
}

// eslint-disable-next-line complexity
export function UConfirmComponent(props: {
    content: string | JSX.Element;
    title?: string;
    okText?: string;
    onOk?: (close: () => void) => void;
    cancelText?: string;
    onCancel?: () => void;
    hideCancel?: boolean;
    maskClosable?: boolean;
    style?: React.CSSProperties;
    type?: ConfirmTypes;
    id?: any;
    className?: string;
    hideTypeIcon?: boolean;
    hideCloseIcon?: boolean;
}) {
    const [show, setShow] = useStateRef<boolean>(true);
    const cancelHandler = () => {
        setShow(false);
        props.onCancel && props.onCancel();
    };


    const close = () => {
        setShow(false);
    };
    return (
        <UModalMask className={props.className} show={show} maskClosable={props.maskClosable} onClose={cancelHandler}>
            <div
                className={`u-confirm ${props.title ? 'has-title' : ''}`}
                onClick={e => e.stopPropagation()}
                style={props.style || {}}
            >
                {
                    props.title ? <div className="title">{props.title}</div> : null
                }
                {
                    typeof props.content === 'string' ? (
                        <div className="u-confirm-content">
                            {!props.hideTypeIcon && <img src={confirmTypesIconMap[props.type || 'info']} />}<span>{props.content}</span>
                        </div>
                    ) : props.content
                }
                <div className="bottom-oper-btns">
                    <span className="confirm" onClick={() => props.onOk && props.onOk(close)}>{props.okText || '确定'}</span>
                    {
                        !props.hideCancel && <span className="cancel" onClick={() => cancelHandler()}>{props.cancelText || '取消'}</span>
                    }
                </div>
                {
                    !props.hideCloseIcon && <span onClick={() => cancelHandler()} className="close-btn"></span>
                }
            </div>
        </UModalMask>
    );
}

type ULoginModalType = typeof UConfirmComponent & {
    open: (props: UConfirmComponentProps) => void;
};

const UConfirm = UConfirmComponent as ULoginModalType;

UConfirm.open = (props: UConfirmComponentProps) => {
    Ioc(UDynamicService).open({
        component: UConfirmComponent,
        props,
    });
};

export {UConfirm};

// 使用示例

// const dy: UDynamicService = Ioc(UDynamicService);
// dy.open({
//     component: UConfirm,
//     props: {
//         maskClosable: true,
//         content: '1232',
//         onOk: () => {
//             console.log(121);
//         },
//         onCancel: () => {
//             console.log(345);
//         },
//     },
// });
