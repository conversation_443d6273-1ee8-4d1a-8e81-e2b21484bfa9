/**
 * @file 基于DomExposure进一步封装自动发送fe-monitor的埋点
 * <AUTHOR>
 */

/**
 * 当通过height: 0，opacity: 0，visbility: hidden等方式来隐藏元素时，DomExposure也会认为该元素曝光了
 * 所以此时需要开发自己处理了
 */

import {DomExposure} from '@baidu/bce-components';
import {EXPOSURE_STATISTIC_ACTION} from '@common/constant/variableConst';
import {sendMonitor} from '@common/helper/page';
import {useEffect, useRef} from 'react';

export const UFeMonitorExposure = (props: {
    category: string;
    name: string;
    value: string;
    children: JSX.Element;
    options?: IntersectionObserverInit;
    exposureHandler?: () => void;
}) => {
    const {category, name, value, children, exposureHandler, options = {rootMargin: '0px 0px -250px 0px', threshold: 0}} = props;
    const categoryRef = useRef(category);
    const nameRef = useRef(name);
    const valueRef = useRef(value);

    useEffect(() => {
        categoryRef.current = category;
        nameRef.current = name;
        valueRef.current = value;
    }, [category, name, value]);
    return (
        <DomExposure
            once={false}
            useRootDom
            exposureHandler={() => {
                sendMonitor({
                    category: categoryRef.current,
                    name: nameRef.current,
                    value: valueRef.current,
                    action: EXPOSURE_STATISTIC_ACTION,
                });
                exposureHandler && exposureHandler();
            }}
            options={options}
        >
            {children}
        </DomExposure>
    );
};
