import {useRef} from 'react';
import {useOnMount} from '@baidu/bce-hooks';

export function ScrollInfiniteBox(props: {
    children: JSX.Element | JSX.Element[];
    className?: string;
    rtl?: boolean;
    speed?: number;
    isHoverPaused?: boolean;
}) {
    const {speed, rtl, className, isHoverPaused} = props;
    const ref = useRef<HTMLDivElement>(null);
    const timer = useRef(null);
    const clearTimer = () => {
        if (timer.current) {
            clearInterval(timer.current);
            timer.current = null;
        }
    };
    useOnMount(() => {
        if (timer.current) {
            clearTimer();
        }
        let x = 0;
        // 走轮播内容的一半开始拉回
        const step = ref.current.offsetWidth / 2;
        const handleMove = () => {
            x = rtl ? x + 1 : x - 1;
            if (Math.abs(x) >= step) {
                x = 0;
            }
            ref.current.style.transform = `translateX(${x}px)`;
        };
        timer.current = setInterval(handleMove, speed || 80);
        const handleMouseOut = () => {
            timer.current = setInterval(handleMove, speed || 80);
        };
        if (isHoverPaused) {
            ref.current.addEventListener('mouseover', clearTimer);
            ref.current.addEventListener('mouseout', handleMouseOut);
        }
        return () => {
            clearTimer();
            if (isHoverPaused) {
                ref.current.removeEventListener('mouseover', clearTimer);
                ref.current.removeEventListener('mouseout', handleMouseOut);
            }
        };
    });
    return (
        <div
            className={className}
            style={{overflow: 'hidden', display: 'flex', justifyContent: rtl ? 'flex-end' : 'flex-start'}}
        >
            <div ref={ref} style={{display: 'flex', width: 'max-content'}}>
                {props.children}
            </div>
        </div>
    );
}
