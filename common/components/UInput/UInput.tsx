/**
 * @file 公共的输入框组件
 * <AUTHOR>
 */

import {Input, InputProps} from 'antd';
import {ChangeEvent, useState} from 'react';
import cn from 'classnames';
import {useOnMount} from '@baidu/bce-hooks';

import style from './main.module.less';

type VerificationObj = {
    rule: 'required' | RegExp;
    message: string;
};

type UInputProps = Omit<InputProps, 'onChange'> & {
    verification?: VerificationObj[];
    onChange?: (e: ChangeEvent<HTMLInputElement>, verificatedItem: VerificationObj) => void; // verificatedItem为falsy，表示判定通过
};

export const UInput = (props: UInputProps) => {
    const [msg, setMsg] = useState('');
    const {verification} = props;

    const handleVerificate = (val: string = '') => {
        if (verification) {
            const verificatedItem = verification.find(item => {
                if (item.rule === 'required') {
                    return Boolean(!val.trim());
                } else {
                    return !item.rule.test(val);
                }
            });
            setMsg(verificatedItem?.message ?? '');
            return verificatedItem;
        }
        return null;
    };

    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
        const val = e.target.value;
        const verificatedItem = handleVerificate(val);
        props.onChange && props.onChange(e, verificatedItem);
    };

    useOnMount(() => {
        handleVerificate((props.value || props.defaultValue) as string);
    });

    const newProps: UInputProps = {...props, verification: null};
    return (
        <div className={cn(style['u-input'], props.className)} data-tip={msg}>
            <Input
                {...newProps}
                onChange={handleChange}
                status={props.status || msg && 'error'}
                className=""
            />
            {
                msg ? (
                    <div className={style['msg']}>{msg}</div>
                ) : null
            }
        </div>
    );
};
