/**
 * @file 分页器
 */

import {useEffect, useMemo, useState} from 'react';
import cn from 'classnames';
import {UPagerPropsObj} from '../../interface/page';

import styles from './index.module.less';

export default function UPager(props: UPagerPropsObj) {
    const {
        totalCount,
        pageSize = 10,
        currentPage,
        isHide,
        size = 'default',
        showTotal = false,
        showQuickJumper = false,
        showSizeChanger = false,
        pageSizeOptions = [10, 20, 50, 100],
        ellipsisJump = false,
        onChange,
        className,
        pageNumRender,
    } = props;
    const [current, setCurrent] = useState(currentPage || 1);
    const [currentPageSize, setCurrentPageSize] = useState(pageSize);
    const [inputPageNo, setInputPageNo] = useState('');
    const [showSizeChangerSpread, setShowSizeChangerSpread] = useState(false);

    const totalPage = useMemo(() => Math.ceil(totalCount / pageSize), [pageSize, totalCount]);

    // eslint-disable-next-line complexity
    const genPageNumberList = () => {
        let arr: Array<number | '…'> = [];
        const config = {
            gapLen: size === 'default' ? 5 : 3,
            gridCount: size === 'default' ? 9 : 6, // 数字格总数
        };

        // 总数过少显示全部
        if (totalPage <= config.gridCount) {
            return Array.from({length: totalPage}, (_, i) => i + 1);
        }

        const leftBoundary = config.gapLen;
        const rightBoundary = totalPage - config.gapLen + 1;

        if (current <= leftBoundary) {
            const headPage = Array.from({length: config.gridCount - 2}, (_, i) => i + 1);
            arr = [...headPage, '…', totalPage];
        } else if (current > leftBoundary && current < rightBoundary) {
            // 当前页位于居中位置
            const centerLen = config.gridCount - 4;
            const curIndex = Math.floor((centerLen + 1) / 2) - 1;
            const centerPage: number[] = new Array(centerLen).fill(current);
            for (let i = curIndex - 1; i >= 0; i--) {
                centerPage[i] = centerPage[i + 1] - 1;
            }
            for (let i = curIndex + 1; i < centerLen; i++) {
                centerPage[i] = centerPage[i - 1] + 1;
            }
            arr = [1, '…', ...centerPage, '…', totalPage];
        } else {
            const tailPage = Array.from({length: config.gridCount - 2}, (_, i) => totalPage - i).reverse();
            arr = [1, '…', ...tailPage];
        }
        return arr;
    };

    const [pageNumberList, setPageNumberList] = useState<Array<number | '…'>>(genPageNumberList);

    // eslint-disable-next-line consistent-return
    useEffect(() => {
        if (showSizeChanger) {
            const body = document.body;
            const h = () => {
                setShowSizeChangerSpread(false);
            };
            body.addEventListener('click', h);

            return () => {
                body.removeEventListener('click', h);
            };
        }
    }, [showSizeChanger]);

    useEffect(() => {
        setCurrent(currentPage || 1);
    }, [currentPage]);

    useEffect(() => {
        setCurrentPageSize(pageSize);
    }, [pageSize]);

    useEffect(() => {
        setPageNumberList(genPageNumberList());
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [totalCount, current, totalPage]);

    const handlePageChange: (pageNum: number) => void = pageNum => {
        !currentPage && setCurrent(pageNum);
        onChange && onChange(pageNum, currentPageSize);
    };

    const handleEllipsisPageChange = (ellipsisIndex: number) => {
        let targetNumber = 1;
        if (ellipsisIndex === 1) {
            // 向左跳转 5 页
            targetNumber = Math.max(1, current - 5);
        } else {
            // 向右跳转 5 页
            targetNumber = Math.min(totalPage, current + 5);
        }

        !currentPage && setCurrent(targetNumber);
        onChange && onChange(targetNumber, currentPageSize);
    };

    const jumpPage = () => {
        const ips = Number(inputPageNo);
        setInputPageNo('');
        if (ips >= 1 && ips !== current) {
            !currentPage && setCurrent(ips);
            onChange && onChange(ips < totalPage ? ips : totalPage, pageSize);
        }
    };

    const handleKeyboardJumpPage: (event: React.KeyboardEvent<HTMLInputElement>) => void = e => {
        if (e.key === 'Enter') {
            jumpPage();
        }
    };

    const handleClickSizeChanger = (e: any) => {
        // eslint-disable-next-line no-unused-expressions
        window.event ? (window.event.cancelBubble = true) : e.stopPropagation();
        setShowSizeChangerSpread(!showSizeChangerSpread);
    };

    const handlePageSizeChange: (pageSize: number) => void = size => {
        setShowSizeChangerSpread(false);
        if (size !== currentPageSize) {
            setCurrentPageSize(size);
            onChange && onChange(current, size);
        }
    };

    if (isHide) {
        return null;
    }

    return (
        <div className={`${styles['u-pager']} ${className}`}>
            {
                showTotal ? <div className="total-count">共<span>{totalCount}</span>条记录</div> : null
            }
            <ul className="u-pager-list">
                <li
                    className={current <= 1 ? 'left disabled' : 'left'}
                    onClick={() => {
                        if (current > 1) {
                            handlePageChange(current - 1);
                        }
                    }}
                >
                    <span></span>
                </li>
                {
                    pageNumberList.map((item, index) => (
                        <li
                            // eslint-disable-next-line react/no-array-index-key
                            key={index}
                            className={cn(
                                {'current': item === current},
                                {'ellipsis': item === '…'},
                                {'ellipsis-jumper-left': ellipsisJump && item === '…' && index === 1},
                                {'ellipsis-jumper-right': ellipsisJump && item === '…' && index > 1}
                            )}
                            onClick={() => {
                                if (item !== '…' && item !== current) {
                                    handlePageChange(item);
                                } else if (item === '…' && ellipsisJump) {
                                    handleEllipsisPageChange(index);
                                }
                            }}
                        >
                            {pageNumRender ? pageNumRender(item) : item}
                        </li>
                    ))
                }
                <li
                    className={current >= totalPage ? 'right disabled' : 'right'}
                    onClick={() => {
                        if (current < totalPage) {
                            handlePageChange(current + 1);
                        }
                    }}
                >
                    <span></span>
                </li>
            </ul>
            {
                showSizeChanger ? (
                    <div className={`size-changer ${showSizeChangerSpread ? 'border-blue' : ''}`}>
                        <span onClick={handleClickSizeChanger}>{currentPageSize} 条/页</span>
                        <ul className={showSizeChangerSpread ? 'active' : ''}>
                            {
                                pageSizeOptions.map((item, index) => (
                                    <li
                                        // eslint-disable-next-line react/no-array-index-key
                                        key={index}
                                        className={item === currentPageSize ? 'current' : ''}
                                        onClick={() => handlePageSizeChange(item)}
                                    >
                                        {item} 条/页
                                    </li>
                                ))
                            }
                        </ul>
                    </div>
                ) : null
            }
            {
                showQuickJumper ? (
                    <div className="quick-jumper">
                        <span>跳转至</span>
                        <input
                            type="text"
                            value={inputPageNo}
                            onChange={e => setInputPageNo(e.target.value)}
                            onKeyUp={handleKeyboardJumpPage}
                        />
                        {/* 跳转按钮目前只在搜索结果页显示, 所以默认是 display: none 的 */}
                        <button
                            className="jump-btn"
                            onClick={jumpPage}
                        >
                            GO
                        </button>
                        <span>页</span>
                    </div>
                ) : null
            }
        </div>
    );
}
