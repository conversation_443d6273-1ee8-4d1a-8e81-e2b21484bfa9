/**
 * @file 分页器样式
*/
@import '@styles/variables.less';

.u-pager {
    display: flex;
    align-items: center;

    :global {
        .total-count {
            display: flex;
            margin-right: 12px;
            font: 14px/24px PingFangSC-Regular;
            color: #191a24;

            span {
                max-width: 80px;
                overflow-x: auto;
            }
        }

        .u-pager-list {
            display: flex;

            li {
                width: 36px;
                height: 36px;
                overflow-x: auto;
                box-sizing: border-box;
                margin-right: 6px;
                border: 1px solid #d1d8e4;
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #191a24;
                text-align: center;
                line-height: 34px;
                border-radius: 2px;
                cursor: pointer;
                user-select: none;
                box-sizing: content-box;
                overflow: hidden;
                a {
                    color: #191a24;
                }
                &.ellipsis {
                    cursor: default;

                    &.ellipsis-jumper-left,
                    &.ellipsis-jumper-right {
                        cursor: pointer;
                        transition: background-image .1s ease-out;

                        &:hover {
                            background-size: 16px 16px !important;
                        }
                    }

                    &.ellipsis-jumper-left:hover {
                        background-image: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_leftarrow2_eb0fedb.svg') !important;
                    }

                    &.ellipsis-jumper-right:hover {
                        background-image: url('https://bce.bdstatic.com/p3m/common-service/uploads/icon_rightarrow2_7c7155d.svg') !important;
                    }
                }

                &.current {
                    border-color: #2468f2;
                    color: #2468f2;
                    a {
                        color: #2468f2;
                    }
                }

                &.left {
                    position: relative;

                    span {
                        position: absolute;
                        left: 15px;
                        top: 13px;
                        width: 7px;
                        height: 7px;
                        border-left: 1px #758096 solid;
                        border-bottom: 1px solid #758096;
                        transform: rotate(45deg);
                    }
                }

                &.right {
                    position: relative;

                    span {
                        position: absolute;
                        left: 12px;
                        top: 13px;
                        width: 7px;
                        height: 7px;
                        border-top: 1px #758096 solid;
                        border-right: 1px solid #758096;
                        transform: rotate(45deg);
                    }
                }

                &.disabled {
                    cursor: not-allowed;
                    color: #d1d8e4;

                    span {
                        border-color: #d1d8e4;
                    }
                }
            }
            &:last-child {
                margin-right: 0;
            }
        }

        .size-changer {
            position: relative;
            height: 36px;
            box-sizing: border-box;
            border: 1px solid #d1d8e4;
            margin-left: 12px;
            border-radius: 2px;
            cursor: pointer;

            &:hover,
            &.border-blue {
                border-color: #2468f2;
            }

            &::after {
                content: '';
                position: absolute;
                border-top: 1px #758096 solid;
                border-right: 1px solid #758096;
                transform: rotate(135deg);
                right: 7px;
                top: 10px;
                width: 7px;
                height: 7px;
            }

            span {
                display: block;
                padding: 5px 27px 5px 10px;
                font: 12px/24px PingFangSC-Regular;
                color: #191a24;
                user-select: none;
            }

            ul {
                display: none;
                position: absolute;
                top: 36px;
                left: 0;
                right: 0;
                background-color: #fff;
                box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

                &.active {
                    display: block;
                }

                li {
                    font: 12px/30px PingFangSC-Regular;
                    padding-left: 10px;

                    &:hover {
                        background-color: rgba(36, 104, 242, 0.5);
                        color: #fff;
                    }

                    &.current {
                        background-color: #2468f2;
                        color: #fff;
                    }
                }
            }
        }

        .quick-jumper {
            margin-left: 6px;
            display: flex;
            align-items: center;

            span {
                font: 12px/24px PingFangSC-Regular;
            }

            input {
                width: 66px;
                height: 36px;
                border: 1px solid #d1d8e4;
                outline: none;
                border-radius: 2px;
                text-align: center;
                font-size: 12px PingFangSC-Regular;
                margin: 0 6px;
                background: transparent;
                &:hover,
                &:focus {
                    border-color: #2468f2;
                }
            }

            .jump-btn {
                display: none;
            }
        }
    }
}

@media screen and (max-width: @MobileWidth) {
    .u-pager {
        :global {
            .u-pager-list {
                li {
                    width: 28px;
                    height: 28px;
                    line-height: 28px;

                    &.left {
                        span {
                            top: 10px;
                            left: 12px;
                        }
                    }

                    &.right {
                        span {
                            top: 10px;
                            left: 9px;
                        }
                    }
                }
            }
        }
    }
}
