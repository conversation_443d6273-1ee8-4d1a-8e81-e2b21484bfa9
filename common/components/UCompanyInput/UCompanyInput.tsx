import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {getAntiParam} from '@common/helper/page';
import {UCompanyInputProps} from '@common/interface/common';
import {AutoComplete} from 'antd';
import {DefaultOptionType} from 'antd/lib/select';
import {debounce, omit} from 'lodash';
import {useState, useRef, useCallback} from 'react';

type CompanyResultObj = {
    entName: string; // 转成value
    unifiedCode: string; // 转成label
};

export const UCompanyInput = (props: UCompanyInputProps) => {
    const [options, setOptions] = useState<UCompanyInputProps['options']>([]);
    const {value, onChange, defaultValue} = props;
    const [tempValue, setTempValue] = useState(value || defaultValue);
    // 解决接口返回的时序问题, 用最新发出的请求设置 options
    const lastTimeRef = useRef(0);

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const onSearch = useCallback(debounce(async (searchText: string) => {
        if (searchText) {
            // 获取数据
            const now = Date.now();
            netService.get<{companyName: string, campaignId: string}, CompanyResultObj[], null>(
                urlConst.GET_SEARCH_COMPANY,
                {
                    companyName: searchText,
                    campaignId: props.campaignId,
                },
                null,
                {
                    headers: {
                        'Acs-Token': await getAntiParam(),
                    },
                }
            ).then(res => {
                if (res.success && res.result && Array.isArray(res.result) && res.result.length > 0 && now > lastTimeRef.current) {
                    lastTimeRef.current = now;
                    // 将结果的entName和unifiedCode转成value和label
                    setOptions(res.result.map(item => ({
                        label: item.entName,
                        value: item.unifiedCode,
                    })));
                }
            }).catch(err => {
                setOptions([]);
                console.error('获取公司数据报错', err);
            });
        } else {
            setOptions([]);
        }
    }, 300), []);

    const onSelect = (data: string, option: UCompanyInputProps['options'][0]) => {
        // 展示data: 选中的value
        // 表单选项设置leads-统一社会信用代码
        // {title: 'leads-统一社会信用代码', answers: [{answer: unifiedCode}]}
        // console.log('onSelect', data, option.label);
        setTempValue(option);
        onChange && onChange(option, option);
    };

    const handleValueChange = (value: string, option: DefaultOptionType | DefaultOptionType[]) => {
        setTempValue({label: value, value: ''});
        onChange && onChange({label: value, value: ''}, option);
    };

    return (
        <AutoComplete
            {...omit(props, 'campaignId')}
            options={options}
            onSelect={onSelect}
            onSearch={onSearch}
            onChange={handleValueChange}
            value={tempValue?.label}
        />
    );
};
