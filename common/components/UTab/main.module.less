@import '@styles/variables.less';

:global {
    .u-tab {
        .u-tabs-nav-list {
            display: flex;
            justify-content: space-around;
            border-bottom: 1px solid rgba(@C0, .16);
    
            .u-tab-item {
                .u-tab-item-inner {
                    display: inline-block;
                    padding-bottom: 10px;
                    font-size: 16px;
                    font-family: PingFangSC-Semibold;
                    color: rgba(@C0, .9);
                    font-weight: 600;
                    text-align: center;
                    line-height: 30px;
                    cursor: pointer;
                    transition: .4s color cubic-bezier(.215, .61, .355, 1);
                }
    
                &.u-tab-item-active {
                    .u-tab-item-inner {
                        color: @CB;
                    }
    
                    .border {
                        position: relative;
                        width: 100%;
                        top: 12px;
                        border-top: 3px solid @CB;
                    }
                }
            }
        }
    
        :global {
            .ant-tabs-content-holder {
                padding-top: 30px;
            }
        }

        &.u-tab-left {
            .u-tabs-nav-list {
                width: 276px;
                padding-right: 28px;
                box-sizing: border-box;
                flex-direction: column;
                justify-content: flex-start;
                border-bottom: none;

                .u-tab-item {
                    .u-tab-item-inner {
                        transition: all 0.3s linear;
                        display: block;
                        font-family: PingFangSC-Regular;
                        font-size: 18px;
                        color: @C0;
                        line-height: 30px;
                        font-weight: 400;
                        box-sizing: border-box;
                        padding: 10px 12px 10px 12px;
                        text-align: left;
                        border-radius: 4px;
                        cursor: pointer;
                        .border {
                            display: none;
                        }                
                    }
                    &.u-tab-item-active {
                        .u-tab-item-inner {
                            background-color: @CB;
                            color: @CW;
                        }
                    }
                }
            }
            > .ant-tabs-content-holder, > div > .ant-tabs-content-holder {
                padding-top: 0px;
                border-left: 1px #E5E5E5 solid;
            }
        }
    }
}
