/**
 * @file PC 端产品组件公用 tab, 样式和 portal-server 产品页保持同步
 * <AUTHOR>

import {Tabs, TabsProps} from 'antd';
import cn from 'classnames';
import styles from './main.module.less';

// eslint-disable-next-line no-unused-expressions
styles.a;

export function UTab(props: TabsProps) {
    return (
        <Tabs
            renderTabBar={props => {
                const {panes, onTabClick, activeKey} = props;
                return (
                    <div className="u-tabs-nav-list">
                        {(panes as any[]).map(pane => {
                            const {props, key} = pane;
                            return (
                                <div
                                    key={key}
                                    onClick={e => onTabClick(props.tabKey, e)}
                                    className={cn('u-tab-item', {'u-tab-item-active': activeKey === props.tabKey})}
                                >
                                    <div className="u-tab-item-inner">
                                        {props.tab}
                                        <div className="border" />
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                );
            }}
            {...props}
            className={cn('u-tab', props.tabPosition === 'left' && 'u-tab-left', props.className)}
        />
    );
}
