import {useEffect, useState, useRef, useCallback} from 'react';
import ReactDOM from 'react-dom';
import styles from './index.module.less';

const DefaultZIndex = '1999';
export function UModalMask(props: {
    children: JSX.Element;
    show: boolean;
    onClose?: () => void; // 关闭弹窗执行的回调
    onMaskClick?: () => void; // 关闭蒙层执行的回调
    maskClosable?: boolean;
    style?: React.CSSProperties;
    className?: string;
    isResetBody?: boolean;
    keepModule?: boolean; // 关闭modal时，不销毁module内容
}) {
    const {isResetBody = true, className, style, onClose, maskClosable, onMaskClick, show, keepModule = false} = props;
    const [opacity, setOpacity] = useState<string>('0');
    const [zIndex, setZIndex] = useState<string>('-1');
    const [display, setDisplay] = useState<string>('none');
    const rootNode = useRef<HTMLDivElement>();
    const isDestroy = useRef<boolean>(false);
    const html = useRef<HTMLElement>();
    const body = useRef<HTMLElement>();
    const scrollTop = useRef(null);

    const resetBodyAttr = useCallback(() => {
        setOpacity('0');
        setTimeout(() => {
            setZIndex('-1');
        }, 400);
        isDestroy.current = true;
        if (isResetBody && document.body.classList.contains('header-body-no-scroll')) {
            document.body.classList.remove('header-body-no-scroll');
            document.body.style.top = null;
            document.body.style.overflowY = null;
            window.scrollTo({top: scrollTop.current});
            // 重置scrollTop
            scrollTop.current = null;
        }
    }, [isResetBody]);

    useEffect(() => {
        html.current = document.querySelector('html');
        body.current = document.body;
        if (show) {
            setDisplay('block');
            setTimeout(() => {
                setOpacity('1');
                setZIndex(DefaultZIndex);
            }, 100);
            isDestroy.current = false;
            // body 不可移动
            // 判断是否要重新计算scrollTop
            if (!scrollTop.current) {
                scrollTop.current = window.scrollY;
            }
            document.body.classList.add('header-body-no-scroll');
            document.body.style.top = `-${scrollTop.current || 0}px`;
            // 判断body本身是否可以滚动，如果可以滚动加上scroll，保证有滑动条
            if (document.body.scrollHeight > document.body.clientHeight) {
                document.body.style.overflowY = 'scroll';
            }
        } else {
            resetBodyAttr();
        }
        return () => {
            !keepModule && resetBodyAttr();
        };
    }, [show, keepModule, resetBodyAttr]);

    const clickMaskHandler = () => {
        if (maskClosable) {
            resetBodyAttr();
            onMaskClick && onMaskClick();
        }
    };

    const onTransitionEndHandler = (isDestroy: boolean) => {
        if (isDestroy) {
            onClose && onClose();
            if (keepModule) {
                return;
            }
            const parent = rootNode.current.parentElement;
            const parentTagName = parent?.tagName;
            const rootEle = parentTagName === 'DIV' ? parent : rootNode.current;
            ReactDOM.unmountComponentAtNode(rootEle);
            rootEle.remove();
        }
    };

    return (
        <div
            onTransitionEnd={() => onTransitionEndHandler(isDestroy.current)}
            className={`${styles['u-modal-mask']} ${className || ''}`}
            style={{...style, opacity, display, zIndex}}
            onClick={clickMaskHandler}
            ref={rootNode}
        >
            <div
                onClick={e => {
                    e.stopPropagation();
                }}
                className="content-container"
                style={{opacity}}
            >
                {
                    props.children
                }
            </div>
        </div>
    );
}
