/**
 * @file 通用按钮
 */

import {UButtonProps} from '@common/interface/common';
import {Fragment} from 'react';
import cn from 'classnames';
import styles from './main.module.less';

export function UButton(props: UButtonProps) {
    const {type = 'default', link, children, onClick, className, style, target = '_blank', track} = props;
    const finalClassName = cn(styles['u-button'], styles[type], className);
    const trackProps = track ? {
        'data-track-category': track.category,
        'data-track-name': track.name,
        'data-track-value': track.value,
        'data-track-action': track.action || 'click',
    } : null;
    return (
        <Fragment>
            {
                link ? (
                    <a
                        onClick={onClick}
                        className={finalClassName}
                        style={style}
                        href={link}
                        target={target}
                        {...trackProps}
                    >{children}
                    </a>
                ) : (
                    <span
                        onClick={onClick}
                        className={finalClassName}
                        style={style}
                        {...trackProps}
                    >{children}
                    </span>
                )
            }
        </Fragment>
    );
}
