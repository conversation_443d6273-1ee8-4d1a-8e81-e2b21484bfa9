@import '@styles/variables.less';
@componentWidth: 470px;

.u-textarea {
    position: relative;
    border: 1px solid rgba(34,34,34,0.16);
    border-radius: 4px;
    padding: 0 0 22px 0;

    &::after {
        content: '';
        position: absolute;
        bottom: 2px;
        right: 2px;
        width: 16px;
        height: 16px;
        opacity: .4;
        background: url(https://bce.bdstatic.com/p3m/common-service/uploads/icon_textarea_da23279.png) center / cover;
    }

    &:hover {
        border-color: @CB;
    }

    :global {
        .ant-input-textarea {
            border: none;
            textarea.ant-input {
                border: none;
                resize: none;
                position: relative;
                box-sizing: border-box;
                width: @componentWidth;
                height: 88px;
                box-shadow: none;
                padding: 6px 8px 0;
    
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: @C0;
                font-weight: 400;
    
                &::placeholder {
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: rgba(34,34,34,0.40);
                    line-height: 24px;
                    font-weight: 400;
                }
                &:hover, &:focus {
                    box-shadow: none !important;
                    border-color: @CB;
                }
            }

            &::after {
                position: absolute;
                bottom: 0;
                right: 12px;
                margin-bottom: 0;

                opacity: .4;
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #222;
                line-height: 20px;
                font-weight: 400;
            }
        }
    }
}

:global {
    .ant-form-item.ant-form-item-has-error {
        :local(.u-textarea) {
            border-color: @CR;
        }
    }
}