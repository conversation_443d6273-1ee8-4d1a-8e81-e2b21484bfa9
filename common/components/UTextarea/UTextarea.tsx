/**
 * @file 公共的文本框组件
 * @description 与antd input.textarea的区别在于，支持最短长度的验证且样式不同
 * <AUTHOR>
 */

// TODO： minLength的错误验证
import {Input} from 'antd';
import cn from 'classnames';
import {TextAreaProps} from 'antd/lib/input';
import style from './main.module.less';

export const UTextarea = (props: TextAreaProps) => {
    const handleCount = (info: { value: string, count: number, maxLength?: number}) => {
        const {count, maxLength} = info;
        return maxLength ? `${count}/${maxLength}` : '';
    };

    return (
        <div className={cn(style['u-textarea'], props.className, 'u-textarea-wrapper')}>
            <Input.TextArea
                {...props}
                showCount={{formatter: handleCount}}
                className=""
            />
        </div>
    );
};
