/**
 * @file 支持选中后增加input框自由填写的checkbox组件
 */

import {useStateRef} from '@baidu/bce-hooks';
import {USurveyCheckBoxProps} from '@common/interface/common';
import {Checkbox, Input} from 'antd';
import {CheckboxOptionType} from 'antd/lib/checkbox';
import {Fragment, useRef, useState} from 'react';

export const USurveyCheckBox = (props: USurveyCheckBoxProps) => {
    const {options, answerRule, onChange} = props;
    const dataListRef = useRef<string[]>([]);
    const checkBoxRef = useRef(null);
    const [showInputList, setShowInputList] = useState(Array.from({length: options?.length}, () => false));
    const [inputDataList, setInputDataList, inputDataListRef] = useStateRef(Array.from({length: options?.length}, () => ''));

    const updateFormData = () => {
        // 更新当前的待上传的数据
        onChange && onChange(dataListRef.current);
    };

    const handleChange = (e: any, index: number) => {
        // 当前选中 如果是选中状态，且有show属性，且show为true，则展示input框
        if (e.target.checked) {
            if (answerRule?.[index]?.show) {
                setShowInputList(prev => {
                    prev[index] = true;
                    return [...prev];
                });
                dataListRef.current.push(inputDataList[index]);
            } else {
                dataListRef.current.push(e.target.value);
            }
        } else if (answerRule?.[index]?.show) {
            setShowInputList(prev => {
                prev[index] = false;
                return [...prev];
            });
            dataListRef.current = dataListRef.current.filter(item => item !== inputDataList[index]);
        } else {
            dataListRef.current = dataListRef.current.filter(item => item !== e.target.value);
        }
        updateFormData();
    };

    const handleTextChange = (value: string, index: number) => {
        const arrayIndex = dataListRef.current.findIndex(item => item === inputDataListRef.current[index]);
        dataListRef.current[arrayIndex] = value;
        setInputDataList(prev => {
            prev[index] = value;
            return [...prev];
        });
        updateFormData();
    };

    return (
        <Checkbox.Group className={props.className} ref={checkBoxRef}>
            {(props.options as CheckboxOptionType[])?.map((item, index) => (
                <Fragment key={index.toString()}>
                    <Checkbox value={item.value} onChange={e => handleChange(e, index)}>{item.label}</Checkbox>
                    {answerRule?.[index]?.show && showInputList[index] && (
                        <Input
                            value={inputDataList[index]}
                            onChange={e => handleTextChange(e.target.value, index)}
                            placeholder={answerRule[index].placeholder || '请输入内容'}
                        />
                    )}
                </Fragment>
            ))}
        </Checkbox.Group>
    );
};
