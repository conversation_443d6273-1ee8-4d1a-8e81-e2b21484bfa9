import {createPortal} from 'react-dom';
import {InfoCircleFilled, CheckCircleFilled, CloseCircleFilled, ExclamationCircleFilled} from '@ant-design/icons';
import {AnimatePresence, motion} from 'framer-motion';
import {useEffect, useState} from 'react';
import styles from './main.module.less';

type MessageTypes = 'info' | 'error' | 'success' | 'warning';
type UMessageProps = {
    type?: MessageTypes;
    top?: number;
    content: React.ReactNode;
    duration?: number;
    onClose: () => void;
};

const typeToIcon = {
    info: <InfoCircleFilled style={{color: '#1677ff'}} />,
    success: <CheckCircleFilled style={{color: '#52c41a'}} />,
    error: <CloseCircleFilled style={{color: '#ff4d4f'}} />,
    warning: <ExclamationCircleFilled style={{color: '#faad14'}} />,
};

export const UMessage: React.FC<UMessageProps> = ({
    type = 'success',
    content,
    duration = 1000,
    top = 88,
    onClose,
}) => {
    const [showMessage, setShowMessage] = useState(true);
    useEffect(() => {
        const timer = setTimeout(() => {
            setShowMessage(false);
            onClose();
        }, duration);
        return () => clearTimeout(timer);
    }, [duration, onClose]);

    return createPortal(
        <AnimatePresence>
            {showMessage && (
                <motion.div
                    initial={{opacity: 0, y: 20, translateX: '-50%', scale: 0.95}}
                    animate={{opacity: 1, y: 0, translateX: '-50%', scale: 1}}
                    exit={{opacity: 0, translateX: '-50%'}}
                    transition={{
                        opacity: {duration: 0.4, ease: 'easeOut'},
                        y: {duration: 0.3, ease: 'easeOut'},
                        scale: {duration: 0.3, ease: 'easeOut'},
                        exit: {opacity: {duration: 0.1, ease: 'easeOut'}},
                    }}
                    className={styles['u-message-box']}
                    style={{top}}
                >
                    {typeToIcon[type]}
                    <label>{content}</label>
                </motion.div>
            )}
        </AnimatePresence>,
        document.body
    );
};
