import {useState} from 'react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import style from './UShowMoreContainer.module.less';

export function UShowMoreContainer(props: {
    children: JSX.Element;
    boundaryHeight?: number;
    btnBg?: string;
    ellipsis?: boolean;
}) {
    const [height, setHeight] = useState<number | string>(props.boundaryHeight || 300);
    const [isMore, setIsMore] = useState<boolean>(false);
    const clickShrinkHandler: React.MouseEventHandler<HTMLSpanElement> = e => {
        // eslint-disable-next-line no-negated-condition
        if (!isMore) {
            setHeight('max-content');
        } else {
            setHeight(props.boundaryHeight || 300);
        }
        setIsMore(val => {
            return !val;
        });
        e.stopPropagation();
    };
    return (
        <div
            className={style['show-more-container']}
            style={{maxHeight: height}}
        >
            {props.children}
            <span
                className={props.ellipsis ? `${style.ellipsis} ${style['shrink-btn']}` : style['shrink-btn']}
                onClick={clickShrinkHandler}
                style={{
                    top: isMore ? 'initial' : (parseInt(`${height}`, 10) - 14),
                    bottom: isMore ? '0px' : 'initial',
                    background: props.btnBg || '#fff',
                }}
            >
                {isMore ? '收起' : (props.ellipsis ? '...' : '查看详情')}
            </span>
        </div>
    );
}

