/**
 * @file 公共的移动端tab切换组件，需要支持下拉，所以集成了一下
 * <AUTHOR>
 */

import {useEffect, useRef, useState} from 'react';
import cn from 'classnames';
import {Tabs, TabsProps} from 'antd';
import {useOnMount} from '@baidu/bce-hooks';
import {debounce} from 'lodash';
import styles from './main_m.module.less';

// eslint-disable-next-line no-unused-expressions
styles.a;

// 由 750 宽设计稿尺寸计算实际尺寸
const getEnvSize = (size: number) => window.innerWidth * (size / 750);

const SCROLL_LEFT_BOUNDARY = 10;

// 两侧 padding 值 (基于 750 宽设计稿)
const GROUP_PADDING = 25;
// 两侧遮罩宽度 (基于 750 宽设计稿)
const COVER_WIDTH = 84;

export interface UTabWapProps {
    tabProps: TabsProps;
    className?: string;
    tabScrollable?: boolean; // Fix：产品页4个tab的滑动
}

export const UTabWap = (props: UTabWapProps) => {
    const {tabScrollable = true} = props;
    const [tabbarActive, setTabbarActive] = useState(false);
    const [activeKey, setActiveKey] = useState<string>(null);
    const [prevCoverVisible, setPrevCoverVisible] = useState(false);
    const tabbarRef = useRef<HTMLDivElement>(null);
    const tabContainerRef = useRef<HTMLDivElement>(null);
    const tabNavWrapRef = useRef<HTMLDivElement>(null);
    const tabNavEleRef = useRef<HTMLDivElement>(null);

    // 多个产品组件之间下拉菜单和导航下拉菜单只能 active 一个, 不同组件会互相影响,
    // active class 就不用 react state 控制了
    const toggleActive = (active?: boolean) => {
        const curActive = tabbarRef.current.classList.contains('active');
        if ((typeof active === 'undefined' && !curActive) || (typeof active !== 'undefined' && active)) {
            setTabbarActive(true);
            tabbarRef.current.classList.add('active');
            // TODO: 导航收起需要用 jquery 控制, 现在还无法实现
            document
                .querySelectorAll('[ui-type="module-box"] .u-tab-wap.show-menu .tabbar.active')
                .forEach(el => el.classList.remove('active'));
        } else {
            setTabbarActive(false);
            tabbarRef.current.classList.remove('active');
        }
    };

    // tab bar 滚动过程中检测是否显示左侧遮罩
    const checkPrevCoverVisible = () => {
        const matchObj: RegExpExecArray = /\d+/.exec(tabNavEleRef.current.getAttribute('style'));
        if (matchObj) {
            const scrollLeft = parseInt(matchObj[0], 10);
            setPrevCoverVisible(scrollLeft > SCROLL_LEFT_BOUNDARY);
        }
    };

    const handleClickTab = (_: string, e: React.KeyboardEvent<Element> | React.MouseEvent<Element, MouseEvent>) => {
        checkPrevCoverVisible();

        // 如果和左侧或右侧被遮罩遮住了, 需要再平移一段
        setTimeout(() => {
            const curTab = e.target as HTMLDivElement;
            const curRect = curTab.getBoundingClientRect();
            const transNumReg = /translate\((-?\d+).*\)/;
            const curTranslate = (transNumReg.exec(tabNavEleRef.current.style.transform))?.[1];

            if (!curTranslate || !tabScrollable) {
                return;
            }

            if (curRect.left < getEnvSize(GROUP_PADDING + COVER_WIDTH)) {
                const translateX = Math.min(parseInt(curTranslate, 10) + getEnvSize(COVER_WIDTH + GROUP_PADDING), 0);
                tabNavEleRef.current.style.transform = `translate(${translateX}px, 0px)`;
            } else if (curRect.right > getEnvSize(750 - COVER_WIDTH / 2 - GROUP_PADDING)) {
                const translateX = parseInt(curTranslate, 10) - getEnvSize(COVER_WIDTH / 2 + GROUP_PADDING);
                tabNavEleRef.current.style.transform = `translate(${translateX}px, 0px)`;
            }
        }, 50);
    };

    useOnMount(() => {
        tabNavWrapRef.current = tabContainerRef.current.querySelector('.ant-tabs-nav-wrap');
        tabNavEleRef.current = tabContainerRef.current.querySelector('.ant-tabs-nav-list');
        const handleWindowScroll = () => {
            tabbarRef.current.classList.remove('active');
        };
        // 滑动页面后要收起下拉面板
        window.addEventListener('scroll', handleWindowScroll);
        return () => {
            window.removeEventListener('scroll', handleWindowScroll);
        };
    });

    useEffect(() => {
        if (props.tabProps.activeKey) {
            setActiveKey(props.tabProps.activeKey);
        } else if (props.tabProps.items) {
            setActiveKey(props.tabProps.items[0]?.key);
        }
    }, [props.tabProps.activeKey, props.tabProps.items]);

    return (
        <div className={cn('u-tab-wap', props.className)} ref={tabContainerRef}>
            <div className="tab-bar" ref={tabbarRef}>
                {!tabbarActive && prevCoverVisible && <span className="prev-cover" />}
                <span className="icon" onClick={() => toggleActive()} />
                <span className="expand-btn" onClick={() => toggleActive()}>
                    {/* eslint-disable-next-line max-len */}
                    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink"><g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd"><rect fillRule="nonzero" x="0" y="0" width="16" height="16"></rect><polyline stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" points="12.3333333 6 8.33333333 10 4.33333333 6"></polyline></g></svg>
                </span>
                <div className="select-wrapper">
                    <span>请选择</span>
                    <ul>
                        {
                            props.tabProps.items.map((item, index) => (
                                <li
                                    key={item.key}
                                    className={item.key === activeKey ? 'current' : ''}
                                    onClick={() => {
                                        if (item.key !== activeKey) {
                                            setActiveKey(item.key);
                                            props.tabProps.onChange && props.tabProps.onChange(item.key);
                                            toggleActive(false);
                                            const maxTranslate = tabNavEleRef.current.offsetWidth - tabNavWrapRef.current.offsetWidth;
                                            const curTabEle: HTMLDivElement
                                            = tabNavEleRef.current.querySelector(`.ant-tabs-tab:nth-child(${index + 1})`);
                                            const translateX = Math.max(Math.min(curTabEle.offsetLeft - 40, maxTranslate), 0);
                                            // 要覆盖tab组件本身的逻辑，所以给了点延迟
                                            setTimeout(() => {
                                                setPrevCoverVisible(translateX > SCROLL_LEFT_BOUNDARY);
                                                tabNavEleRef.current.style.transform = `translate(-${translateX}px, 0px)`;
                                            }, 60);
                                        }
                                    }}
                                >
                                    {item.label}
                                </li>
                            ))
                        }
                    </ul>
                </div>
            </div>
            <Tabs
                {...props.tabProps}
                activeKey={activeKey}
                onChange={key => {
                    setActiveKey(key);
                    props.tabProps.onChange && props.tabProps.onChange(key);
                }}
                onTabClick={handleClickTab}
                onTabScroll={debounce(checkPrevCoverVisible, 100)}
            />
        </div>
    );
};
