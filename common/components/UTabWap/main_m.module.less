@import '@styles/variables.less';
@import '@styles/utils.less';
@import '@styles/mixin.less';

:global {
    .u-tab-wap {
        position: relative;
        padding: 0 12.5px;
        margin-top: 10px;
        .ant-tabs-nav {
            margin-bottom: 8px;
            &::before {
                border-bottom-color: rgba(@C0, 0.08);
            }
            .ant-tabs-nav-wrap {
                &::after, &::before {
                    display: none;
                }
            }
            .ant-tabs-tab {
                padding: 11px 0;
                + .ant-tabs-tab {
                    margin-left: 20px;
                }
                &:nth-last-child(2) {
                    margin-right: 30px;
                }
                .ant-tabs-tab-btn {
                    font-family: PingFangSC-Medium;
                    font-size: 14px;
                    color: rgba(@C0, 0.9);
                    font-weight: 500;
                }
            }
            .ant-tabs-tab-active {
                .ant-tabs-tab-btn {
                    color: @CB;
                }
            }
            .ant-tabs-ink-bar {
                height: 1px;
                background-color: @CB;
            }
        }
        .tab-bar {
            position: absolute;
            left: 0;
            top: 0;
            z-index: 10;
            width: 375px;
            box-shadow: 0 4px 16px 0 rgba(218, 221, 231, 0.70);
            .prev-cover {
                position: absolute;
                left: 12px;
                top: 0;
                height: 42px;
                width: 42px;
                background-image: linear-gradient(to left, rgba(@CW, 0) 0%, @CW 71%);
            }
            .icon {
                position: absolute;
                right: 10.5px;
                top: 0;
                height: 42px;
                width: 44px;
                background-image: linear-gradient(to right, rgba(@CW, 0) 0%, @CW 71%);
                &::after {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 14px;
                    width: 16px;
                    height: 16px;
                    background: url(https://bce.bdstatic.com/p3m/common-service/uploads/icon_arrow_home_wap_5fc9bd2.png) no-repeat;
                    background-size: auto 16px;
                    background-position: 0 0;
                }
            }
            .expand-btn {
                // 默认不显示产品组件 tab 里启用这个按钮
                display: none;
            }
            .select-wrapper {
                display: none;
                span {
                    display: block;
                    padding-left: 12.5px;
                    padding-top: 11px;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: rgba(@C0, 0.9);
                    line-height: 22px;
                    font-weight: 400;
                }
                ul {
                    display: flex;
                    justify-content: flex-start;
                    flex-wrap: wrap;
                    padding: 0 12.5px 16px;
                    li {
                        margin-top: 10px;
                        width: 110px;
                        height: 32px;
                        background: #F2F6FA;
                        border-radius: 2px;
                        font-family: PingFangSC-Medium;
                        font-size: 13px;
                        color: rgba(@C0, 0.9);
                        text-align: center;
                        line-height: 32px;
                        font-weight: 500;
                        &:not(:nth-child(3n)) {
                            margin-right: 10px;
                        }
                        &.current {
                            background-color: @CB;
                            color: @CW;
                        }
                    }
                }
            }
            &.active {
                background: @CW;
                .select-wrapper {
                    display: block;
                }
                .icon {
                    width: 16px;
                    &::after {
                        background-position: -21px 0;
                    }
                }
            }
        }
    }
}