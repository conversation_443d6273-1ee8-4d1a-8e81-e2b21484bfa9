/* eslint-disable complexity */
/**
 * @file layout
 * <AUTHOR>
 */

import Head from 'next/head';
import Script from 'next/script';
import {PagePropsObj} from '@common/interface/common';
import {PORTAL_COMMON_EN_URL, PORTAL_COMMON_URL, navSeoData} from '@common/constant/variableConst';
import {useOnMount} from '@baidu/bce-hooks';
import {Ioc} from '@baidu/bce-decorators';
import {UEnvService} from '@common/services/env';
import {WeirwoodErrorBoundary} from '@common/components/WeirwoodErrorBoundary/WeirwoodErrorBoundary';
import {UUserService} from '@common/services/userinfo';
import {cloneElement, ReactElement} from 'react';

const defaultHeadOption: LayoutPropsObj['headOption'] = {
    title: '百度智能云-云智一体深入产业',
    keywords: '百度智能云,云服务器,物联网,域名注册,百度云,云计算,云数据库,云主机,云存储,对象存储,百度云cdn,AI人工智能,云智一体,大模型,文心一言,文心大模型',
    description: '百度智能云致力于为企业和开发者提供全球领先的人工智能、大数据和云计算服务，加速产业智能化转型升级',
    favicon: 'https://bce.bdstatic.com/img/favicon.ico',
};

const defaultOptions: PortalCommonOptionsObj = {
    showPcFooter: true,
    showWapFooter: true,
    showPcFixedBox: true,
    showWapFixedBox: true,
    showHeader: true,
    headerTransparent: false,
};

export function Layout(props: LayoutPropsObj) {
    const {
        portalCommonOption,
        children,
        isMobile,
        isEn,
        headOption,
        hidePortalCommon,
    } = props;
    const {
        title = defaultHeadOption.title,
        keywords = defaultHeadOption.keywords,
        description = defaultHeadOption.description,
        favicon = defaultHeadOption.favicon,
    } = headOption || defaultHeadOption;

    const options: PortalCommonOptionsObj = {
        ...defaultOptions,
        loginSuccess: info => {
            Ioc(UUserService).dispatch(info);
        },
        ...portalCommonOption,
    };
    const defaultEnv = Ioc(UEnvService).isProdSandbox ? '_sandbox' : '';
    useOnMount(() => {
        const portalCommonScript = document.querySelector('script[src*="portal-common"]');
        if (portalCommonScript || hidePortalCommon) {
            return;
        }
        const script = document.createElement('script');
        script.addEventListener('load', () => {
            if (window.require) {
                (window.require as any)(['portalCommon'], (portalCommon: {
                    setup: (options: PortalCommonOptionsObj) => void;
                }) => {
                    portalCommon.setup(options);
                });
            } else {
                window.portalCommon.setup(options);
            }
        });
        const s = document.getElementsByTagName('script')[0];
        const portalCommonUrlMap = isEn ? PORTAL_COMMON_EN_URL : PORTAL_COMMON_URL;
        script.src = portalCommonUrlMap[['cloudtest', 'localhost'].find(item => location.host.includes(item)) ? 'sandbox' : 'online'];
        script.defer = true;
        setTimeout(() => {
            s.parentNode.insertBefore(script, s);
        }, isMobile ? 200 : 0);
    });

    return (
        <>
            <Head>
                <title>{title}</title>
                <link rel="shortcut icon" href={favicon} type="image/x-icon" />
                <meta httpEquiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
                <meta name="baidu-site-verification" content="codeva-WdReimoQXv" />
                <meta name="360-site-verification" content="a28659ee618a3ad4fe3f566fedcf2a23" />
                <meta name="sogou_site_verification" content="1WgYtnMFf3" />
                <meta name="shenma-site-verification" content="6de355ac393346d8f9218cbbd7b3f966_1654776643" />
                <meta name="keywords" content={keywords} />
                <meta name="description" content={description} />
                <meta name="renderer" content="webkit" />
                <meta
                    name="viewport"
                    content={isMobile ? 'width=device-width, user-scalable=0, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0' : 'width=1280'}
                />
                {
                    options.showHeader && !hidePortalCommon && !isEn
                    && <link rel="stylesheet" href={`https://bce.bdstatic.com/portal-server/common/header/portal-default${defaultEnv}.css`} />
                }
                {
                    // eslint-disable-next-line @next/next/no-sync-scripts
                    isMobile && <script src="https://bce.bdstatic.com/portal-server/common/rem.js" />
                }
            </Head>
            {
                options.showHeader && !hidePortalCommon && !isEn && (
                    <Script
                        strategy="beforeInteractive"
                        id="headerDefault"
                        // eslint-disable-next-line max-len
                        src={`https://bce.bdstatic.com/portal-server/common/header/portal-default${defaultEnv}.js${
                            options.headerTransparent ? '?theme=transparent' : ''
                        }`}
                    />
                )
            }
            <header id="add-header-html-ddd" style={{width: 0, height: 0, overflow: 'hidden'}}>
                <nav>
                    <ul>
                        {
                            navSeoData.map(item => (
                                <li key={item.text}>
                                    <a href={item.link}>{item.text}</a>
                                </li>
                            ))
                        }
                    </ul>
                </nav>
            </header>
            <WeirwoodErrorBoundary>
                {
                    cloneElement(children)
                }
            </WeirwoodErrorBoundary>
        </>
    );
}

interface LayoutPropsObj extends PagePropsObj {
    children: ReactElement;
}
