/**
 * @file 放大图片
 */

import {useStateRef} from '@baidu/bce-hooks';
import {UModalMask} from '../UModalMask/UModalMask';

import styles from './main.module.less';

export const UScaleImg = (props: {
    maskClosable?: boolean;
    src: string;
    // onOk?: () => void;
    onCancel?: () => void;
}) => {
    const [show, setShow] = useStateRef<boolean>(true);
    const cancelHandler = () => {
        setShow(false);
        props.onCancel && props.onCancel();
    };
    return (
        <UModalMask className={styles['mask']} show={show} maskClosable={props.maskClosable} onClose={cancelHandler}>
            <div className={styles['image-wrapper']} onClick={cancelHandler}>
                <img
                    src={props.src}
                    alt="scale-img"
                />
            </div>
        </UModalMask>
    );
};
