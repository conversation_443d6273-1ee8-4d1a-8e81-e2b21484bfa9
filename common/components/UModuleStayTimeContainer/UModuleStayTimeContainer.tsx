/**
 * @file 模块停留时长上报容器
 */

import {useModuleStayTime} from '@common/hooks/useModuleStayTime';
import {cloneElement, useRef} from 'react';

export const UModuleStayTimeContainer = (props: {
    category: string; // 埋点category
    name: string; // 埋点name
    children: JSX.Element;
    /** 是否直接使用child的顶级元素 默认false */
    useRootDom?: boolean;
    className?: string;
}) => {
    const {category, name, useRootDom, className = ''} = props;
    const ref = useRef<HTMLDivElement>(null);
    useModuleStayTime({ref, category, name});
    return useRootDom ? (
        cloneElement(props.children, {
            ref,
        })
    ) : (
        <div ref={ref} className={className}>
            {props.children}
        </div>
    );
};
