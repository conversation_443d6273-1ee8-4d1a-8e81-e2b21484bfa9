/**
 * @file 自动吸顶，可以覆盖吸顶的样式
 * <AUTHOR>
 */

import {isUndefined} from '@baidu/bce-helper';
import {getElementTop, getHeaderNavHeight} from '@common/helper/page';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {CSSProperties, useRef, cloneElement} from 'react';

const defStyle: CSSProperties = {position: 'fixed', left: 0, top: 0};

/**
 * 自动吸顶
 */
export function UStickyContainer(props: {
    children: JSX.Element;
    defaultStyle?: CSSProperties; // 默认吸顶样式
    fixedStyle?: CSSProperties; // 覆盖吸顶样式
    startFixed?: number; // 修正开始吸顶的位置
    onScrollHandler?: (scrollTop: number) => void; // 监听滚动
    fixedTop?: number; // 开始吸顶的位置(不包含header导航高度)
    fixedHiddenHeader?: boolean; // 是否隐藏header
}) {
    const childRef = useRef<HTMLElement>(null);
    const [style, setStyle, styleRef] = useStateRef<CSSProperties>(null);
    const {children, fixedStyle, startFixed = 0, onScrollHandler, defaultStyle, fixedTop, fixedHiddenHeader = false} = props;
    const getFixedTopWithHeader = (fixedTop: number) => {
        return fixedTop + getHeaderNavHeight(window.innerWidth);
    };
    useOnMount(() => {
        let childTop = isUndefined(fixedTop) ? getElementTop(childRef.current) + startFixed : getFixedTopWithHeader(fixedTop);
        // 初始导航高度
        let preHeaderNavHeight = getHeaderNavHeight(window.innerWidth);
        let newHeaderNavHeight = preHeaderNavHeight;

        const dist = getElementTop(childRef.current) - getElementTop(childRef.current.parentElement);
        handleScroll();
        function handleScroll() {
            const newChildTop = getElementTop(childRef.current.parentElement) + dist + startFixed;
            if (isUndefined(fixedTop) && childTop !== newChildTop) {
                childTop = newChildTop;
            }
            const scrollTop = document.documentElement.scrollTop;
            if (childTop <= scrollTop && !styleRef.current) {
                setStyle({...defStyle, ...defaultStyle});
                // 如果此时需要隐藏header
                fixedHiddenHeader && window.portalCommon && window.portalCommon?.hideHeaderOption({hideHeader: true});
            } else if (childTop > scrollTop && styleRef.current) {
                setStyle(null);
                fixedHiddenHeader && window.portalCommon && window.portalCommon?.hideHeaderOption({hideHeader: false});
            }
            onScrollHandler && onScrollHandler(scrollTop);
        }
        function handleResize() {
            newHeaderNavHeight = getHeaderNavHeight(window.innerWidth);
            if (preHeaderNavHeight === newHeaderNavHeight) {
                return;
            }
            // 处理childTop高度改变 重新计算
            childTop = isUndefined(fixedTop) ? (
                childTop = getElementTop(childRef.current.parentElement)
                - getElementTop(document.getElementById('__next'))
                + newHeaderNavHeight
                + startFixed
                + dist
            ) : getFixedTopWithHeader(fixedTop);
            preHeaderNavHeight = newHeaderNavHeight;
        }
        window.addEventListener('scroll', handleScroll);
        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('scroll', handleScroll);
            window.removeEventListener('resize', handleResize);
        };
    });
    return (
        <>
            {
                cloneElement(
                    children,
                    {ref: childRef, style: {...style, ...fixedStyle}}
                )
            }
        </>
    );
}

