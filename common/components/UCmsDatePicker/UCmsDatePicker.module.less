@import '@styles/variables.less';

.date-picker {
    position: relative;
    box-sizing: border-box;
    width: 100%; // width: 470px;
    height: 36px;
    padding: 0 8px;
    background: #fff;
    border: 1px solid rgba(34,34,34,0.16);
    border-radius: 4px;
    outline: none;
    box-shadow: none;
    :global {
        .ant-picker-suffix svg {
            display: block;
        }
        .ant-picker-input:hover .ant-picker-suffix {
            display: none;
        }
    }
    &:hover,
    &:global(.ant-picker-focused) {
        border-color: @CB;
    }
}

.date-picker-popup {
    :global {
        .ant-picker-panel-container {
            box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
            border-radius: 6px;
        }
        .ant-picker-date-panel {
            width: 268px;
        }
        .ant-picker-header {
            > button {
                color: #5a5a5a;
                &:hover {
                    color: @CB;
                }
            }
            .ant-picker-header-view button {
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: @C0;
                font-weight: 400;
                &:hover {
                    color: @CB;
                }
            }
        }
        .ant-picker-date-panel .ant-picker-body {
            padding: 12px;
            font-size: 12px;
            .ant-picker-content {
                display: flex;
                flex-direction: column;
                width: 244px;
                thead {
                    display: block;
                    tr {
                        display: flex;
                        width: 100%;
                        justify-content: space-between;
                        th {
                            width: 28px;
                            height: 28px;
                            line-height: 28px;
                        }
                    }
                }
                tbody {
                    tr {
                        display: flex;
                        justify-content: space-between;
                        margin-top: 8px;
                    }
                }
                .ant-picker-cell {
                    .ant-picker-cell-inner {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        box-sizing: border-box;
                        width: 28px;
                        height: 28px;
                        border: 1px solid #fff;
                        border-radius: 2px;
                        font-family: PingFangSC-Regular;
                        font-size: 14px;
                        line-height: 24px;
                        transition: none;
                    }
                    &:hover .ant-picker-cell-inner {
                        background: rgba(36,104,242,0.08) !important;
                        border: none;
                    }
                    &.ant-picker-cell-today .ant-picker-cell-inner {
                        border-color: @CB;
                        background: #fff !important;
                        &::before {
                            display: none;
                        }
                    }
                    &.ant-picker-cell-selected .ant-picker-cell-inner {
                        color: #fff;
                        border-color: @CB;
                        background: @CB !important;
                    }
                }
            }
        }
        .ant-picker-body .ant-picker-cell {
            &:hover .ant-picker-cell-inner  {
                background: rgba(36,104,242,0.08) !important;
            }
            &.ant-picker-cell-selected .ant-picker-cell-inner  {
                background: @CB !important;
            }
        }
    }
}