import {useOnMount, useStateRef, useOnUpdate} from '@baidu/bce-hooks';
import {useScrollBind} from '@common/hooks/useScrollBind';
import {CSSProperties, useRef, useState} from 'react';
import {getElementTop, getScrollTop, setScrollTop} from '@common/helper/page';
import {throttle} from 'lodash';
import {GetFuncType} from '@common/interface/common';
import {HEADER_NAV_HEIGHT_MAP} from '@common/constant/variableConst';
import cn from 'classnames';

import styles from './main_m.module.less';

const SCROLL_LEFT_BOUNDARY = 50;
const COVER_WIDTH = 40;
const HEADER_HEIGHT = HEADER_NAV_HEIGHT_MAP.wap;
const MARGIN_BETWEEN = 20;

type SubNavItem = {
    name: string;
};
type SubNavProps = {
    subNavData: SubNavItem[];
    subNavDataPc?: SubNavItem[];
    scrollBindOption?: GetFuncType<typeof useScrollBind>;
    hashHandlerFn?: (hash: string) => void;
};

export const USubNavWap = ({subNavData, subNavDataPc, scrollBindOption, hashHandlerFn}: SubNavProps) => {
    const [tabbarActive, setTabbarActive, tabbarActiveRef] = useStateRef(false);
    const [showTabbar, setShowTabbar] = useState(false);
    const [average, setAverage] = useState(false);
    const [specialLi, setSpecialLi] = useState(false);
    const [prevCoverVisible, setPrevCoverVisible] = useState(false);
    const [current, setCurrent] = useScrollBind(scrollBindOption || {
        itemSelector: '.module',
        fixTop: -40,
        scrollUpHeaderFixTop: HEADER_HEIGHT,
        throttleDelay: 0,
    });
    const [lineStyle, setLineStyle] = useState<CSSProperties>(null);
    const ref = useRef<HTMLDivElement>(null);
    const subNavListEle = useRef<HTMLUListElement>(null);
    const headerTop = useRef(0);
    const scrollTopPrev = useRef(0);
    const [fixedStyle, setFixedStyle] = useState<CSSProperties>({top: 0});

    const handleLineMove = (index: number) => {
        const curLi: HTMLLIElement = subNavListEle.current.querySelector(`li:nth-child(${index + 1})`);
        const width = curLi.offsetWidth;
        const left = curLi.offsetLeft + (curLi.offsetWidth - width) / 2;
        setLineStyle({width, left});
    };

    const checkDistribution = () => {
        const len = subNavData?.length || 0;
        if (len > 4) {
            setShowTabbar(true);
        } else if (len === 4) {
            // 当导航为4个时，有两种适配方式
            // 1、采用700宽度均分适配（tab文字2～5个字） 2、当文字总宽度超出最大值时，选项横向排布展示，可滑动
            const liData: NodeListOf<HTMLElement> = subNavListEle.current.querySelectorAll('li:not(:last-child)');
            // 根据375的设计稿计算实际间隔尺寸
            let total = window.innerWidth * (MARGIN_BETWEEN * (len - 1) / 375);
            liData.forEach(liElement => {
                total += liElement.offsetWidth;
            });
            if (total > subNavListEle.current.offsetWidth) {
                setSpecialLi(true);
            } else {
                setAverage(true);
            }
        } else {
            setAverage(true);
        }
        // 执行line
        setTimeout(() => {
            handleLineMove(current);
        }, 100);
    };

    useOnUpdate(() => {
        const curTabEle: HTMLDivElement = subNavListEle.current.querySelector(`li:nth-child(${current + 1})`);
        const subNavListEleW = subNavListEle.current.offsetWidth;
        if (subNavListEle.current.scrollLeft > curTabEle.offsetLeft - COVER_WIDTH) {
            subNavListEle.current.scrollTo({left: Math.max(curTabEle.offsetLeft - COVER_WIDTH, 0), behavior: 'smooth'});
        } else if (subNavListEle.current.scrollLeft < curTabEle.offsetLeft - subNavListEleW + curTabEle.offsetWidth + COVER_WIDTH) {
            subNavListEle.current.scrollTo({left: curTabEle.offsetLeft - subNavListEleW + curTabEle.offsetWidth + COVER_WIDTH, behavior: 'smooth'});
        }
        handleLineMove(current);
    }, [current]);

    useOnMount(() => {
        if (location.hash) {
            // 判断当前是否存在index
            const data = decodeURIComponent(location.hash).slice(1);
            let index = subNavData.findIndex(item => item.name === data);
            if (index < 0 && subNavDataPc) {
                // 判断hash是否存在于PC数据中
                index = subNavDataPc.findIndex(item => item.name === data);
            }
            index >= 0 && setTimeout(() => setCurrent(index), 400);
            if (index < 0 && hashHandlerFn) {
                hashHandlerFn(data);
            }
        }
        checkDistribution();
        const subNavTop = getElementTop(ref.current);
        const handleWindowScroll = throttle(() => {
            // 滚动时，收起展开的二级导航
            tabbarActiveRef.current && setTabbarActive(false);
            // 吸顶逻辑
            const scrollTop = getScrollTop();
            const cloudHeaderEle: HTMLDivElement = document.querySelector('.cloud-header-wrapper-wap');
            if (cloudHeaderEle) {
                if (scrollTop < scrollTopPrev.current) {
                    // 下拉
                    if (scrollTop < subNavTop - HEADER_HEIGHT) {
                        headerTop.current = 0;
                        setFixedStyle({top: 0});
                    } else {
                        headerTop.current = Math.min(0, headerTop.current + scrollTopPrev.current - scrollTop);
                        setFixedStyle(pre => ({
                            top: Math.min((pre.top as number) + scrollTopPrev.current - scrollTop, HEADER_HEIGHT),
                            position: 'fixed',
                        }));
                    }
                } else if (scrollTop < subNavTop - HEADER_HEIGHT) {
                    headerTop.current = 0;
                    setFixedStyle({top: 0});
                } else {
                    // 下滚
                    headerTop.current = Math.max(-HEADER_HEIGHT, headerTop.current + scrollTopPrev.current - scrollTop);
                    setFixedStyle(pre => ({
                        top: Math.max((pre.position === 'fixed' ? (pre.top as number) : 54) + scrollTopPrev.current - scrollTop, 0),
                        position: 'fixed',
                    }));
                }
                if (headerTop.current === -HEADER_HEIGHT) {
                    cloudHeaderEle.style.boxShadow = 'none';
                } else {
                    cloudHeaderEle.style.boxShadow = '';
                }
                cloudHeaderEle.style.top = `${headerTop.current}px`;
            }
            scrollTopPrev.current = scrollTop;
        }, 10);
        setTimeout(() => {
            setScrollTop(getScrollTop() + 1);
        }, 500);
        // 滑动页面后要收起下拉面板
        window.addEventListener('scroll', handleWindowScroll);
        return () => {
            window.removeEventListener('scroll', handleWindowScroll);
        };
    });
    return (
        <div className={styles['sub-nav-wrapper']} ref={ref}>
            <div
                id="sub-nav-wap"
                className={styles['sub-nav']}
                style={fixedStyle}
            >
                <div className={cn(styles['tab-bar'], showTabbar && styles['show'], tabbarActive && styles['active'])}>
                    {!tabbarActive && prevCoverVisible && <span className={styles['prev-cover']} />}
                    <span
                        className={styles['icon']}
                        onClick={() => {
                            setTabbarActive(prev => !prev);
                        }}
                    />
                    <div className={styles['select-wrapper']}>
                        <span>请选择</span>
                        <ul>
                            {
                                subNavData.map((item, index) => (
                                    <li
                                        key={item.name}
                                        className={current === index ? styles['current'] : ''}
                                        onClick={() => {
                                            if (index !== current) {
                                                setCurrent(index);
                                                setTabbarActive(false);
                                                history.pushState({index}, '', `${location.pathname}${location.search}#${subNavData[index].name}`);
                                            }
                                        }}
                                    >
                                        {item.name}
                                    </li>
                                ))
                            }
                        </ul>
                    </div>
                </div>
                <div className={styles['sub-nav-list-wrapper']}>
                    <ul
                        className={cn(styles['sub-nav-list'], average && styles['average'], specialLi && styles['special'])}
                        ref={subNavListEle}
                        onScroll={throttle(() => {
                            setPrevCoverVisible(subNavListEle.current.scrollLeft > SCROLL_LEFT_BOUNDARY);
                        }, 40)}
                    >
                        {
                            subNavData.map((item, index) => (
                                <li
                                    key={item.name}
                                    className={cn(index === current && styles['current'])}
                                    onClick={() => {
                                        if (index !== current) {
                                            setCurrent(index);
                                            history.pushState({index}, '', `${location.pathname}${location.search}#${subNavData[index].name}`);
                                        }
                                    }}
                                >
                                    {item.name}
                                </li>
                            ))
                        }
                        <li className={styles['line']} style={lineStyle} />
                    </ul>
                </div>
            </div>
        </div>
    );
};
