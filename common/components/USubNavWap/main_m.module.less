@import '@styles/variables.less';

@navHeight: 44px;

.sub-nav-wrapper {
    height: @navHeight;
    width: 100%;
}

.sub-nav {
    height: @navHeight;
    width: 100%;
    position: relative;
    z-index: 10;
    padding: 0 12.5px;
    background: #FFFFFF;
    box-shadow: 0 4px 16px 0 rgba(218,221,231,0.70);
}

.sub-nav-list-wrapper {
    height: @navHeight;
    overflow: hidden;
}

.sub-nav-list {
    position: relative;
    display: flex;
    justify-content: flex-start;
    flex-wrap: nowrap;
    overflow-x: scroll;
    height: @navHeight + 10px;
    li {
        flex-shrink: 0;
        line-height: @navHeight;
        font-family: PingFangSC-Medium;
        color: @C9;
        font-size: 14px;
        font-weight: 500;
        transition: color 0.3s ease-out;
        -webkit-tap-highlight-color: transparent;
        -webkit-tap-highlight-color: rgba(0,0,0,0);

        + li {
            margin-left: 20px;
        }
        &:nth-last-child(2) {
            margin-right: 30px;
        }
        &.current {
            color: @CB;
        }
    }
    .line {
        width: 0;
        height: 2px;
        background: @CB;
        margin: 0;
        position: absolute;
        left: 0;
        bottom: 10px;
        transition: all 0.3s ease-out;
    }
    &.average {
        justify-content: space-around;
        li {
            margin-right: 0;
            margin-left: 0;
            &:nth-last-child(2) {
                margin-right: 0;
            }
        }
    }
    &.special {
        li {
            margin-right: 0;
            &:nth-last-child(2) {
                margin-right: 0;
            }
        }
    }
}

.tab-bar {
    position: absolute;
    display: none;
    left: 0;
    top: 0;
    z-index: 10;
    width: 375px;
    box-shadow: 0 4px 16px 0 rgba(218, 221, 231, 0.70);
    .prev-cover {
        position: absolute;
        left: 12px;
        top: 0;
        height: 42px;
        width: 42px;
        background-image: linear-gradient(to left, rgba(@CW, 0) 0%, @CW 71%);
    }
    .icon {
        position: absolute;
        right: 12.5px;
        top: 0;
        height: 42px;
        width: 42px;
        background-image: linear-gradient(to right, rgba(@CW, 0) 0%, @CW 71%);
        &::after {
            content: '';
            position: absolute;
            right: 0;
            top: 14px;
            width: 16px;
            height: 16px;
            background: url(https://bce.bdstatic.com/p3m/common-service/uploads/icon_arrow_home_wap_5fc9bd2.png) no-repeat;
            background-size: auto 16px;
            background-position: 0 0;
        }
    }
    .select-wrapper {
        display: none;
        span {
            display: block;
            padding-left: 12.5px;
            padding-top: 11px;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: rgba(@C0, 0.9);
            line-height: 22px;
            font-weight: 400;
        }
        ul {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            padding: 0 12.5px 16px;
            li {
                margin-top: 10px;
                width: 110px;
                height: 32px;
                background: #F2F6FA;
                border-radius: 2px;
                font-family: PingFangSC-Medium;
                font-size: 13px;
                color: rgba(@C0, 0.9);
                text-align: center;
                line-height: 32px;
                font-weight: 500;
                &:not(:nth-child(3n)) {
                    margin-right: 10px;
                }
                &.current {
                    background-color: @CB;
                    color: @CW;
                }
            }
        }
    }
    &.active {
        background: @CW;
        .select-wrapper {
            display: block;
        }
        .icon {
            width: 16px;
            &::after {
                background-position: -21px 0;
            }
        }
    }
    &.show {
        display: block;
    }
}
    