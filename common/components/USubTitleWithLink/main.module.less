@import '@styles/variables.less';
.desc {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 1180px;
    margin: 12px auto 0;
    &, span {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: rgba(34, 34, 34, 0.9);
        line-height: 24px;
        font-weight: 400;
    }
    a {
        display: inline-block;
        vertical-align: middle;
        margin-left: 8px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #2468F2;
        line-height: 24px;
        font-weight: 400;
        position: relative;
        padding-right: 20px;
        &::after {
            content: "";
            position: absolute;
            right: 0;
            top: 4px;
            width: 16px;
            height: 16px;
            background: url(https://bce.bdstatic.com/p3m/common-service/uploads/arrow-hover_5c62c57.svg) no-repeat 100%/contain;
            transition: right .3s ease-out;
        }
        &:hover {
            text-decoration: none;
            &::after {
                right: -8px;
            }
        }
    }
    &.white {
        span, a {
            color: @CW;
        }
        a {
            &::after {
                background-image: url(https://bce.bdstatic.com/p3m/common-service/uploads/arrow_icon_8bbc66a.png);
            }
        }
    }
}

@media (max-width: @MobileWidth) {
    .desc {
        width: auto;
        margin: 0.8rem 1.5rem 0;
        text-align: center;

        &,
        span {
            display: block;
            font-size: 1.3rem;
            line-height: 2.1rem;
        }

        span+a {
            margin-top: 0.4rem;
        }

        a {
            margin-left: 0;
            position: relative;
            font-size: 1.3rem;
            line-height: 2.1rem;
            padding-right: 1.6rem;

            &::after {
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 1.4rem;
                height: 1.4rem;
            }

            &:hover {
                &::after {
                    right: 0;
                }
            }
        }
    }
}