import {SubTitleObj} from '@common/interface/blocks';
import classNames from 'classnames';
import style from './main.module.less';

// 产品组件-子标题
export const USubTitleWithLink = (props: {
    subTitle?: string | SubTitleObj;
    title?: string;
    type?: 'white' | 'black';
}) => {
    const {subTitle, title, type} = props;
    if (!subTitle) {
        return null;
    }
    if (typeof subTitle === 'string') {
        return (
            <p className={classNames(style.desc, style[type])}>
                {subTitle}
            </p>
        );
    }
    return (
        (subTitle.content || !subTitle.link.disabled && subTitle.link.text) && (
            <p className={classNames(style.desc, style[type])}>
                {subTitle.content && (<span>{subTitle.content}</span>)}
                {!subTitle.link.disabled && subTitle.link.text && (
                    <a
                        href={subTitle.link.href}
                        target="_blank"
                        data-track-category={title}
                        data-track-name="副标题"
                        data-track-value={subTitle.link.text}
                    >
                        {subTitle.link.text}
                    </a>
                )}
            </p>
        )
    );
};
