/**
 * @file 懒加载video
 * <AUTHOR>
 */

import {useOnMount, useOnUpdate} from '@baidu/bce-hooks';
import {CSSProperties, DetailedHTMLProps, useRef, useState, VideoHTMLAttributes} from 'react';

import styles from './main.module.less';

type VideoProps = Omit<DetailedHTMLProps<VideoHTMLAttributes<HTMLVideoElement>, HTMLVideoElement>, 'src'>;

const defaultVideoProps: VideoProps = {
    loop: true,
    muted: true,
    autoPlay: true,
};

export const ULazyLoadVideo = (props: {
    src: string;
    videoProps?: VideoProps;
    previewImage?: string;
    className?: string;
    wrapperStyle?: CSSProperties;
    option?: IntersectionObserverInit;
    lazyCallback?: () => void;
}) => {
    const ref = useRef<HTMLDivElement>(null);
    const [visible, setVisible] = useState(false);
    const [src, setSrc] = useState('');
    const [previewImage, setPreviewImage] = useState('');
    useOnUpdate(() => {
        if (visible) {
            setSrc(props.src);
            setPreviewImage(props.previewImage);
        }
    }, [props.src, visible, props.previewImage]);
    useOnMount(() => {
        let observerable: IntersectionObserver = null;
        try {
            observerable = new IntersectionObserver(entries => {
                entries.forEach(item => {
                    if (item.isIntersecting) {
                        const target = item.target as HTMLDivElement;
                        setVisible(true);
                        props.lazyCallback && props.lazyCallback();
                        observerable.unobserve(target);
                    }
                });
            }, {
                rootMargin: '0px 0px 256px 0px',
                ...props.option,
            });
            observerable.observe(ref.current);
        } catch (e) {
            setSrc(props.src);
            props.lazyCallback && props.lazyCallback();
        }
        return () => {
            ref.current && observerable && observerable.unobserve(ref.current);
        };
    });
    return (
        <div
            ref={ref}
            className={`${props.className || ''} ${styles['u-lazy-video']}`}
            style={props.wrapperStyle}
        >
            <div style={{backgroundImage: previewImage ? `url(${previewImage})` : 'none'}} />
            <video {...defaultVideoProps} {...props.videoProps} src={src} />
        </div>
    );
};
