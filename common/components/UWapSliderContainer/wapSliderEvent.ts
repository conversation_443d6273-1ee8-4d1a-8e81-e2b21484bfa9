/**
 * @file 移动端滑动事件
 */
export class SlideEvent {
    el: HTMLElement = null;
    handle: (currentIndex: number, argv: SlideEvent) => void = null;
    num: number = 0;
    currentIndex: number = 0;
    timer: number = null;
    startX: number = 0;
    startY: number = 0;
    x: number = 0;
    y: number = 0;
    banLoop: boolean = false;
    isMove: boolean = false;
    duration: number = 3000;
    constructor(
        el: HTMLElement,
        length: number,
        handle: (currentIndex: number, argv: SlideEvent) => void,
        options: {
            banLoop?: boolean;
            currentIndex?: number;
            duration?: number;
        } = {}
    ) {
        this.el = el; // 绑定事件的list type:JqueryElement
        this.handle = handle; // 除基础事件操作外的自定义操作
        this.num = length; // 列表长度
        this.currentIndex = options.currentIndex || 0; // 当前展示项
        this.timer = null;
        this.startX = 0;
        this.startY = 0;
        this.x = 0;
        this.y = 0;
        this.banLoop = options.banLoop; // 控制循环和自动播放，若为ture 则不会循环和自动播放
        this.isMove = false;
        this.duration = options.duration || 3000;
    }

    animation(direction: string) {
        // 判断滑动方向 & 判断是否还能滑动
        if (!direction || direction === 'left'
            && !(this.banLoop && this.currentIndex === this.num - 1)) {
            this.currentIndex++;
        } else if (direction === 'right' && !(this.banLoop && this.currentIndex === 0)) {
            this.currentIndex--;
        }

        // 边界值处理
        if (this.currentIndex < 0) {
            this.currentIndex = this.num - 1;
        } else if (this.currentIndex > this.num - 1) {
            this.currentIndex = 0;
        }

        // 自定义动画
        this.handle(this.currentIndex, this);
    }

    init() {
        if (!this.banLoop) {
            this.timer = setInterval(this.animation.bind(this), this.duration);
        }

        this.bind();
    }

    touchEndOrCancel() {
        const deltaX = this.x - this.startX;
        const deltaY = this.y - this.startY;

        // 判断为水平滑动，竖直滑动不处理，且水平移动距离超过20px
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 20 && this.isMove) {
            let direction = 'left';
            if (deltaX > 20) {
                direction = 'right';
            }
            this.isMove = false;
            this.animation(direction);
            // 重新开始一轮
            if (!this.banLoop) {
                clearInterval(this.timer);
                this.timer = setInterval(this.animation.bind(this), this.duration);
            }
        }
    }

    bind() {
        this.el.addEventListener('touchstart', e => {
            const touches = e.targetTouches && e.targetTouches[0] || {screenX: 0, screenY: 0};
            this.startX = touches.screenX;
            this.startY = touches.screenY;
            this.isMove = false;
        });

        this.el.addEventListener('touchmove', e => {
            const touches = e.targetTouches && e.targetTouches[0] || {screenX: 0, screenY: 0};
            this.x = touches.screenX;
            this.y = touches.screenY;
            this.isMove = true;
            // 如果是水平滑动，禁止浏览器的返回和前进操作
            const deltaX = this.x - this.startX;
            const deltaY = this.y - this.startY;
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                e.preventDefault();
            }
        });

        this.el.addEventListener('touchend', () => {
            this.touchEndOrCancel();
        });
        this.el.addEventListener('touchcancel', () => {
            this.touchEndOrCancel();
        });
    }
}
