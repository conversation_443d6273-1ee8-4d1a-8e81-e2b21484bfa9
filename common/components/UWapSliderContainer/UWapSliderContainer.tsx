/**
 * @file 移动端滑动切换容器
 * <AUTHOR>
 */
import {useEffect, useRef} from 'react';
import {SlideEvent} from './wapSliderEvent';

export function UWapSliderContainer(props: {
    className?: string;
    handleSlider: (currentIndex: number, argv: SlideEvent) => void;
    children: JSX.Element | JSX.Element[];
    listLength: number;
    options?: {
        banLoop?: boolean;
        currentIndex?: number;
        duration?: number;
    };
}) {
    const {className, children, listLength, handleSlider, options} = props;
    const sliderRef = useRef(null);
    useEffect(() => {
        if (listLength) {
            const slider = new SlideEvent(sliderRef.current, listLength, handleSlider, options);
            slider.init();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [listLength]);
    return (
        <div className={className} ref={sliderRef}>
            {children}
        </div>
    );
}
