/* eslint-disable */

import {GroupBuyBccItemObj, GroupBuyCdnItemObj, GroupBuyBosItemObj, GroupBuyLssItemObj, GroupBuyTmsItemObj,
    GroupBuyAipageItemObj, GroupBuyCbsItemObj, GroupBuyNatItemObj, GroupBuyEipItemObj} from '@common/interface/groupBuy';
import {productAllBos, CapacityBosMap, CapacityBosEnum} from '@components/groupBuy/GroupItemBos/model';
import {productAllBcc} from '@components/groupBuy/GroupItemBcc/model';
import {productAllCdn, CapacityCdnMap, CapacityCdnEnum} from '@components/groupBuy/GroupItemCDN/model';
import {lssAllProducts, CapacityLssMap, CapacityLssEnum} from '@components/groupBuy/GroupItemLSS/model';
import {productAllTms} from '@components/groupBuy/GroupItemTms/model';
import {aipageAllProducts} from '@components/groupBuy/GroupItemAipage/model';
import {productAllCbs} from '@components/groupBuy/GroupItemCbs/model';
import {displayNameNatArr, DurationNatMap, productAllNat} from '@components/groupBuy/GroupItemNat/model';
import {displayNameEipEumn, DurationEipMap, productAllEip} from '@components/groupBuy/GroupItemEip/model';
import {GroupPurchaseProductPriceParams} from '@common/interface/groupPurchase';

export type GroupBuyItem = GroupBuyBccItemObj | GroupBuyCdnItemObj | GroupBuyBosItemObj | GroupBuyLssItemObj | GroupBuyTmsItemObj 
| GroupBuyAipageItemObj | GroupBuyCbsItemObj | GroupBuyNatItemObj | GroupBuyEipItemObj;

type GroupBuyKey = keyof GroupBuyBccItemObj | keyof GroupBuyCdnItemObj | keyof GroupBuyBosItemObj | keyof GroupBuyLssItemObj
| keyof GroupBuyTmsItemObj | keyof GroupBuyAipageItemObj | keyof GroupBuyCbsItemObj | keyof GroupBuyNatItemObj | keyof GroupBuyEipItemObj;
type ServiceType = 'BCC' | 'BOS' | 'CDN' | 'LSS' | 'TMS' | 'AIPAGE' | 'CBS' | 'NAT' | 'EIP' | 'ICR' | 'AI_TE' | 'SPEECH' | 'OCR' | 'GENERAL_OCR' | 'FACE';

export interface IndexMergeParamsObj {
    displayName: string;
    desc?: string;
    link?: string;
    serviceType: ServiceType;
    apiId?: string;
    params?: Array<{
        key: GroupBuyKey; // 下拉参数组合
        transform?: (data: string | number) => string; // 转换数据
    }>;
    filter?: Array<{ // 根据多个key和value过滤数据
        key: GroupBuyKey;
        value: string;
    }>;
    products?: GroupBuyItem[];
    dataList?: Array<{
        label: string,
        formValue: any;
        data: GroupPurchaseProductPriceParams[];
    }>;
};

export interface IndexMergePurchaceItem {
    type: 'merge' | 'single' | 'merge-new';
    title: string;
    desc: string;
    titleTag?: string;
    tags?: Array<{
        text: string;
        color: 'red' | 'grey';
    }>;
    // 单个产品
    keys?: string[]; // 要选择的参数
    options?: Array<{ // 表格
        [props: string]: string | number;
        priceFlag?: string;
        price: string | number;
        unit?: string;
        deletePrice?: string;
        href: string;
        mobileHref?: string;
    }>;
    // 多个产品的参数
    mergeParams?: IndexMergeParamsObj[];
    // 多个产品的初始显示价格
    initialPrice?: {
        priceBefore?: string;
        price: string | number;
        priceAfter?: string;
        linePrice?: string | number;
    },
}

export interface MergePurchaceModuleObj {
    category: string;
    desc: string;
    list: IndexMergePurchaceItem[];
}

export interface IndexMergePurchaceModelObj {
    title: string;
    subTitle: string;
    modules: MergePurchaceModuleObj[];
}

const monTrans = (v: string | number) => `-${v}个月`;

export const indexMergePurchaceModel: IndexMergePurchaceModelObj = {
    title: '精选应用场景产品推荐',
    subTitle: '基于实际业务场景，为您精心推荐的爆款产品、产品组合购买',
    // btnText: '查看全部产品',
    // btnHref: 'https://cloud.baidu.com/products/index.html',
    modules: [{
        category: '企业服务',
        desc: '满足企业初创阶段的业务需求',
        list: [{
            type: 'single',
            title: '云上公司注册',
            desc: '园区税收优惠政策，全程线上操作，方便快捷，快至当天下证',
            tags: [{
                text: '一站式创业服务',
                color: 'red',
            }, {
                text: '快速出证',
                color: 'grey',
            }],
            keys: ['注册园区', '拿证时间'],
            options: [
                {
                    '注册园区': '上海园区',
                    '拿证时间': '快至3-7天',
                    '服务类型': null,
                    'priceFlag': '￥',
                    'price': 1,
                    'unit': '/年起',
                    'deletePrice': '￥680/年起',
                    'href': 'https://console.bce.baidu.com/cbs/#/park/order/submit?stepStart=2&comType=LIMITED_COMPANY&area=CP0004&taxpayerType=SMALL_SCALE_TAXPAYER&trace=20211118_gw_qifu&track=20211118_gw_qifu&pageResource=20211118_gw_qifu&pageSource=20211118_gw_qifu'
                },
                {
                    '注册园区': '天津园区',
                    '拿证时间': '快至3-7天',
                    '服务类型': null,
                    'priceFlag': '￥',
                    'price': 1,
                    'unit': '/年起',
                    'deletePrice': '￥680/年起',
                    'href': 'https://console.bce.baidu.com/cbs/#/park/order/submit?stepStart=2&comType=LIMITED_COMPANY&area=CP0003&taxpayerType=SMALL_SCALE_TAXPAYER&trace=20211118_gw_qifu&track=20211118_gw_qifu&pageResource=20211118_gw_qifu&pageSource=20211118_gw_qifu'
                },
                {
                    '注册园区': '广州园区',
                    '拿证时间': '快至3-7天',
                    '服务类型': null,
                    'priceFlag': '￥',
                    'price': 1,
                    'unit': '/年起',
                    'deletePrice': '￥680/年起',
                    'href': 'https://console.bce.baidu.com/cbs/#/park/order/submit?stepStart=2&comType=LIMITED_COMPANY&area=CP0002&taxpayerType=SMALL_SCALE_TAXPAYER&trace=20211118_gw_qifu&track=20211118_gw_qifu&pageResource=20211118_gw_qifu&pageSource=20211118_gw_qifu'
                },
                {
                    '注册园区': '杭州园区',
                    '拿证时间': '快至3-7天',
                    '服务类型': null,
                    'priceFlag': '￥',
                    'price': 1,
                    'unit': '/年起',
                    'deletePrice': '￥680/年起',
                    'href': 'https://console.bce.baidu.com/cbs/#/park/order/submit?stepStart=2&comType=LIMITED_COMPANY&area=CP0001&taxpayerType=SMALL_SCALE_TAXPAYER&trace=20211118_gw_qifu&track=20211118_gw_qifu&pageResource=20211118_gw_qifu&pageSource=20211118_gw_qifu'
                },
                {
                    '注册园区': '中山园区',
                    '拿证时间': '快至3-7天',
                    '服务类型': null,
                    'priceFlag': '￥',
                    'price': 1,
                    'unit': '/年起',
                    'deletePrice': '￥680/年起',
                    'href': 'https://console.bce.baidu.com/cbs/#/park/order/submit?stepStart=2&comType=LIMITED_COMPANY&area=CP0000&taxpayerType=SMALL_SCALE_TAXPAYER&trace=20211118_gw_qifu&track=20211118_gw_qifu&pageResource=20211118_gw_qifu&pageSource=20211118_gw_qifu'
                },
                {
                    '注册园区': '青岛园区',
                    '拿证时间': '快至3-7天',
                    '服务类型': null,
                    'priceFlag': '￥',
                    'price': 1,
                    'unit': '/年起',
                    'deletePrice': '￥680/年起',
                    'href': 'https://console.bce.baidu.com/cbs/#/park/order/submit?stepStart=2&comType=LIMITED_COMPANY&area=CP0005&taxpayerType=SMALL_SCALE_TAXPAYER&trace=20211118_gw_qifu&track=20211118_gw_qifu&pageResource=20211118_gw_qifu&pageSource=20211118_gw_qifu'
                },
                {
                    '注册园区': '重庆园区',
                    '拿证时间': '快至3-7天',
                    '服务类型': null,
                    'priceFlag': '￥',
                    'price': 1,
                    'unit': '/年起',
                    'deletePrice': '￥680/年起',
                    'href': 'https://console.bce.baidu.com/cbs/#/park/order/submit?stepStart=2&comType=LIMITED_COMPANY&area=CP0006&taxpayerType=SMALL_SCALE_TAXPAYER&trace=20211118_gw_qifu&track=20211118_gw_qifu&pageResource=20211118_gw_qifu&pageSource=20211118_gw_qifu'
                },
                {
                    '注册园区': '温州园区',
                    '拿证时间': '快至3-7天',
                    '服务类型': null,
                    'priceFlag': '￥',
                    'price': 1,
                    'unit': '/年起',
                    'deletePrice': '￥680/年起',
                    'href': 'https://console.bce.baidu.com/cbs/#/park/order/submit?stepStart=2&comType=LIMITED_COMPANY&area=CP0007&taxpayerType=SMALL_SCALE_TAXPAYER&trace=20211118_gw_qifu&track=20211118_gw_qifu&pageResource=20211118_gw_qifu&pageSource=20211118_gw_qifu'
                },
                {
                    '注册园区': '南京园区',
                    '拿证时间': '快至3-7天',
                    '服务类型': null,
                    'priceFlag': '￥',
                    'price': 1,
                    'unit': '/年起',
                    'deletePrice': '￥680/年起',
                    'href': 'https://console.bce.baidu.com/cbs/#/park/order/submit?stepStart=2&comType=LIMITED_COMPANY&area=CP0008&taxpayerType=SMALL_SCALE_TAXPAYER&trace=20211118_gw_qifu&track=20211118_gw_qifu&pageResource=20211118_gw_qifu&pageSource=20211118_gw_qifu'
                }
            ],
        }, {
            type: 'single',
            title: '公司注册',
            desc: '企业工商注册服务，覆盖全国主要城市',
            tags: [{
                text: '快速下照',
                color: 'red',
            }, {
                text: '9大核心产业园',
                color: 'grey',
            }],
            keys: ['公司性质', '纳税人类型', '服务类型'],
            options: [
                {
                    "公司性质": "有限公司",
                    "纳税人类型": "小规模纳税人",
                    "服务类型": "公司注册",
                    "priceFlag": "￥",
                    "price": 9.9,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://cloud.baidu.com/product/crs#/"
                },
                {
                    "公司性质": "有限公司",
                    "纳税人类型": "小规模纳税人",
                    "服务类型": "公司注册超值套餐",
                    "priceFlag": "￥",
                    "price": 729,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://cloud.baidu.com/product/crs#/"
                },
                {
                    "公司性质": "有限公司",
                    "纳税人类型": "小规模纳税人",
                    "服务类型": "公司注册无忧套餐",
                    "priceFlag": "￥",
                    "price": 9999,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://cloud.baidu.com/product/crs#/"
                },
                {
                    "公司性质": "有限公司",
                    "纳税人类型": "一般纳税人",
                    "服务类型": "公司注册",
                    "priceFlag": "￥",
                    "price": 9.9,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://cloud.baidu.com/product/crs#/"
                },
                {
                    "公司性质": "有限公司",
                    "纳税人类型": "一般纳税人",
                    "服务类型": "公司注册超值套餐",
                    "priceFlag": "￥",
                    "price": 729,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://cloud.baidu.com/product/crs#/"
                },
                {
                    "公司性质": "有限公司",
                    "纳税人类型": "一般纳税人",
                    "服务类型": "公司注册无忧套餐",
                    "priceFlag": "￥",
                    "price": 14999,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://cloud.baidu.com/product/crs#/"
                }
            ],
        }, {
            type: 'single',
            title: '代理记账',
            desc: '云会计代理记账，品质服务，安心代账，帮您轻松完成企业财务税务管理',
            tags: [{
                text: '专家委托服务',
                color: 'grey',
            }, {
                text: '减本增效',
                color: 'grey',
            }],
            keys: ['纳税人类型', '年开票额'],
            options: [
                {
                    "纳税人类型": "小规模纳税人",
                    "年开票额": "零申报",
                    "priceFlag": "￥",
                    "price": 1199,
                    "unit": "/年",
                    "deletePrice": "￥1999/年",
                    "href": "https://cloud.baidu.com/product/bookkeeping#/"
                },
                {
                    "纳税人类型": "小规模纳税人",
                    "年开票额": "非零申报",
                    "priceFlag": "￥",
                    "price": 2399,
                    "unit": "/年",
                    "deletePrice": "￥2799/年",
                    "href": "https://cloud.baidu.com/product/bookkeeping#/"
                },
                {
                    "纳税人类型": "一般纳税人",
                    "年开票额": "零申报",
                    "priceFlag": "￥",
                    "price": 1199,
                    "unit": "/年",
                    "deletePrice": "￥1999/年",
                    "href": "https://cloud.baidu.com/product/bookkeeping#/"
                },
                {
                    "纳税人类型": "一般纳税人",
                    "年开票额": "非零申报",
                    "priceFlag": "￥",
                    "price": 5299,
                    "unit": "/年",
                    "deletePrice": "￥5999/年",
                    "href": "https://cloud.baidu.com/product/bookkeeping#/"
                }
            ],
        }, {
            type: 'single',
            title: '商标智能注册',
            desc: '自助办理，快速递交，性价比首选，针对有一定商标申请经验的用户',
            tags: [{
                text: '申报快至1分钟',
                color: 'grey',
            }, {
                text: '百万好标即买即用',
                color: 'grey',
            }],
            keys: ['商标类型'],
            options: [
                {
                    "商标类型": "文字商标",
                    "拿证时间": null,
                    "priceFlag": "￥",
                    "price": 270,
                    "unit": "/首年起",
                    "deletePrice": "￥318/年起",
                    "href": "https://console.bce.baidu.com/tms/#/tms/create/self?pageResource=PORTAL_001"
                },
                {
                    "商标类型": "图形商标",
                    "拿证时间": null,
                    "priceFlag": "￥",
                    "price": 270,
                    "unit": "/年起",
                    "deletePrice": "￥318/年起",
                    "href": "https://console.bce.baidu.com/tms/#/tms/create/self?pageResource=PORTAL_001"
                },
                {
                    "商标类型": "文字图形组合商标",
                    "拿证时间": null,
                    "priceFlag": "￥",
                    "price": 270,
                    "unit": "/年起",
                    "deletePrice": "￥318/年起",
                    "href": "https://console.bce.baidu.com/tms/#/tms/create/self?pageResource=PORTAL_001"
                }
            ],
        }, {
            type: 'single',
            title: '域名注册',
            desc: '百余款域名后缀随心选，新用户注册COM域名仅需33元',
            tags: [{
                text: '首购特惠',
                color: 'red',
            }, {
                text: '百余款域名后缀',
                color: 'grey',
            }],
            keys: ['域名后缀', '购买时长'],
            options: [
                {
                    "域名后缀": ".xyz",
                    "购买时长": "1年",
                    "priceFlag": "￥",
                    "price": 13,
                    "unit": "/首年起",
                    "deletePrice": "￥119/年",
                    "href": "https://cloud.baidu.com/product/bcd/search.html"
                },
                {
                    "域名后缀": ".com",
                    "购买时长": "1年",
                    "priceFlag": "￥",
                    "price": 33,
                    "unit": "/首年起",
                    "deletePrice": "￥80/年",
                    "href": "https://cloud.baidu.com/product/bcd/search.html"
                },
                {
                    "域名后缀": ".net",
                    "购买时长": "1年",
                    "priceFlag": "￥",
                    "price": 66,
                    "unit": "/首年起",
                    "deletePrice": "￥100/年",
                    "href": "https://cloud.baidu.com/product/bcd/search.html"
                },
                {
                    "域名后缀": ".ltd",
                    "购买时长": "1年",
                    "priceFlag": "￥",
                    "price": 10,
                    "unit": "/首年起",
                    "deletePrice": "￥80/年",
                    "href": "https://cloud.baidu.com/product/bcd/search.html"
                }
            ],
        }, {
            type: 'single',
            title: '企信直达号',
            desc: '企业的官方信息专属样式优先展示，成为企业在百度上的专属品牌“商号”',
            tags: [{
                text: '双重认证',
                color: 'red',
            }, {
                text: '搜索结果优先展示',
                color: 'grey',
            }],
            keys: ['产品选型', '购买时长'],
            options: [
                {
                    "产品选型": "标准版",
                    "购买时长": "1年",
                    "priceFlag": "￥",
                    "price": 9960,
                    "unit": "/年",
                    "deletePrice": "￥12800/年",
                    "href": "https://console.bce.baidu.com/bcd/?_=1669785640169#/bcd/ec/choose"
                },
                {
                    "产品选型": "全网版",
                    "购买时长": "1年",
                    "priceFlag": "￥",
                    "price": 6960,
                    "unit": "/年",
                    "deletePrice": "￥8800/年",
                    "href": "https://console.bce.baidu.com/bcd/?_=1669785640169#/bcd/ec/choose"
                }
            ],
        }],
    }, {
        category: '企业上云',
        desc: '为企业提供各阶段多场景上云服务',
        list: [{
            type: 'single',
            title: '云服务器',
            desc: '为您提供超高效费比的高性能云服务器',
            tags: [{
                text: '1年8.3折',
                color: 'red',
            }, {
                text: '高性价比',
                color: 'grey',
            }],
            keys: ['型号', '规格', '系统盘'],
            options: [
                {
                    "型号": "通用型g4",
                    "规格": "2核8G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 210.76,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=8&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "通用型g4",
                    "规格": "4核16G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 401.52,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=16&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "通用型g5",
                    "规格": "2核8G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 266.17,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=8&instanceType=34",
                    "mobileHref": null
                },
                {
                    "型号": "通用型g5",
                    "规格": "4核16G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 512.35,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=16&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "计算型c4",
                    "规格": "2核4G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 161.04,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=4&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "计算型c4",
                    "规格": "4核8G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 302.08,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=8&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "密集计算型ic5",
                    "规格": "2核2G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 136.18,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=2&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "密集计算型ic5",
                    "规格": "4核4G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 252.36,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=4&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "内存型m4",
                    "规格": "2核16G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 310.2,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=16&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "内存型m4",
                    "规格": "4核32G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 600.4,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=32&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "内存型m5",
                    "规格": "2核16G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 342.83,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=16&instanceType=34",
                    "mobileHref": null
                },
                {
                    "型号": "内存型m5",
                    "规格": "4核32G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 665.67,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=32&instanceType=34",
                    "mobileHref": null
                }
            ],
        }, {
            type: 'single',
            title: '对象存储',
            desc: '支持标准、低频、冷和归档存储等多种存储类型满足多场景的存储需求',
            tags: [{
                text: '6个月8.3折',
                color: 'red',
            }, {
                text: '稳定可靠',
                color: 'grey',
            }],
            keys: ['存储类型', '规格'],
            options: [
                {
                    "存储类型": "标准存储包",
                    "规格": "1TB",
                    "priceFlag": "￥",
                    "price": 550,
                    "unit": "/6个月",
                    "deletePrice": "",
                    "href": "https://console.bce.baidu.com/bos/#/bos/package/create"
                },
                {
                    "存储类型": "低频存储包",
                    "规格": "1TB",
                    "priceFlag": "￥",
                    "price": 364.54,
                    "unit": "/6个月",
                    "deletePrice": "",
                    "href": "https://console.bce.baidu.com/bos/#/bos/package/create"
                },
                {
                    "存储类型": "冷存储包",
                    "规格": "1TB",
                    "priceFlag": "￥",
                    "price": 163.18,
                    "unit": "/6个月",
                    "deletePrice": "",
                    "href": "https://console.bce.baidu.com/bos/#/bos/package/create"
                },
                {
                    "存储类型": "归档存储包",
                    "规格": "1TB",
                    "priceFlag": "￥",
                    "price": 70,
                    "unit": "/6个月",
                    "deletePrice": "",
                    "href": "https://console.bce.baidu.com/bos/#/bos/package/create"
                },
                {
                    "存储类型": "外网下行流量包",
                    "规格": "1TB",
                    "priceFlag": "￥",
                    "price": 2455,
                    "unit": "/6个月",
                    "deletePrice": "",
                    "href": "https://console.bce.baidu.com/bos/#/bos/package/create"
                }
            ],
        }, {
            type: 'single',
            title: '云数据库RDS',
            desc: '高性能、高可靠关系型云数据库服务',
            tags: [{
                text: '1年8.3折',
                color: 'red',
            }, {
                text: '稳定可靠',
                color: 'grey',
            }],
            keys: ['数据库类型', '机型', '存储磁盘'],
            options: [
                {
                    "数据库类型": "MySQL",
                    "机型": "单机基础版",
                    "存储磁盘": "50GB",
                    "priceFlag": "￥",
                    "price": 89.5,
                    "unit": "/月",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/rds/#/rds/create"
                },
                {
                    "数据库类型": "MySQL",
                    "机型": "双机高可用版",
                    "存储磁盘": "5GB",
                    "priceFlag": "￥",
                    "price": 123.8,
                    "unit": "/月",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/rds/#/rds/create"
                },
                {
                    "数据库类型": "SQL Server",
                    "机型": "单机基础版",
                    "存储磁盘": "50GB",
                    "priceFlag": "￥",
                    "price": 1202,
                    "unit": "/月",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/rds/#/rds/create"
                },
                {
                    "数据库类型": "SQL Server",
                    "机型": "双机高可用版",
                    "存储磁盘": "50GB",
                    "priceFlag": "￥",
                    "price": 2000,
                    "unit": "/月",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/rds/#/rds/create"
                },
                {
                    "数据库类型": "PostgreSQL",
                    "机型": "单机基础版",
                    "存储磁盘": "50GB",
                    "priceFlag": "￥",
                    "price": 117,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/rds/#/rds/create"
                },
                {
                    "数据库类型": "PostgreSQL",
                    "机型": "双机高可用版",
                    "存储磁盘": "5GB",
                    "priceFlag": "￥",
                    "price": 123.8,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/rds/#/rds/create"
                }
            ],
        }, {
            type: 'single',
            title: '内容分发网络',
            desc: '自建海量优质节点，提高用户访问网站资源的响应速度，有效提升用户体验',
            tags: [{
                text: '全球海量资源',
                color: 'red',
            }, {
                text: '覆盖25+运营商',
                color: 'grey',
            }],
            keys: ['规格', '数量'],
            options: [
                {
                    "规格": "100 GB",
                    "数量": "1个",
                    "priceFlag": "￥",
                    "price": 18,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/cdn/#/cdn/package/create"
                },
                {
                    "规格": "500 GB",
                    "数量": "1个",
                    "priceFlag": "￥",
                    "price": 90,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/cdn/#/cdn/package/create"
                },
                {
                    "规格": "1 TB",
                    "数量": "1个",
                    "priceFlag": "￥",
                    "price": 180,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/cdn/#/cdn/package/create"
                },
                {
                    "规格": "10 TB",
                    "数量": "1个",
                    "priceFlag": "￥",
                    "price": 1800,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/cdn/#/cdn/package/create"
                },
                {
                    "规格": "50 TB",
                    "数量": "1个",
                    "priceFlag": "￥",
                    "price": 7650,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/cdn/#/cdn/package/create"
                },
                {
                    "规格": "200 TB",
                    "数量": "1个",
                    "priceFlag": "￥",
                    "price": 21600,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/cdn/#/cdn/package/create"
                },
                {
                    "规格": "1 PB",
                    "数量": "1个",
                    "priceFlag": "￥",
                    "price": 108000,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/cdn/#/cdn/package/create"
                }
            ],
        }, {
            type: 'single',
            title: '弹性公网',
            desc: '为用户访问公网提供IP地址和公网带宽，灵活匹配业务变更，增加用户使用弹性',
            tags: [{
                text: '部署灵活',
                color: 'grey',
            }, {
                text: '节省成本',
                color: 'grey',
            }],
            keys: ['流量包规格', '套餐'],
            options: [
                {
                    "流量包规格": "10GB",
                    "套餐": "全时",
                    "priceFlag": "￥",
                    "price": 7.2,
                    "unit": "/月",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/eip/?_=1491789772489#/eip/eiptp/create"
                },
                {
                    "流量包规格": "50GB",
                    "套餐": "全时",
                    "priceFlag": "￥",
                    "price": 36,
                    "unit": "/月",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/eip/?_=1491789772489#/eip/eiptp/create"
                },
                {
                    "流量包规格": "100GB",
                    "套餐": "全时",
                    "priceFlag": "￥",
                    "price": 72,
                    "unit": "/月",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/eip/?_=1491789772489#/eip/eiptp/create"
                },
                {
                    "流量包规格": "500GB",
                    "套餐": "全时",
                    "priceFlag": "￥",
                    "price": 360,
                    "unit": "/月",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/eip/?_=1491789772489#/eip/eiptp/create"
                },
                {
                    "流量包规格": "1TB",
                    "套餐": "全时",
                    "priceFlag": "￥",
                    "price": 737.28,
                    "unit": "/月",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/eip/?_=1491789772489#/eip/eiptp/create"
                },
                {
                    "流量包规格": "5TB",
                    "套餐": "全时",
                    "priceFlag": "￥",
                    "price": 3686.4,
                    "unit": "/月",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/eip/?_=1491789772489#/eip/eiptp/create"
                }
            ],
        }, {
            type: 'single',
            title: '智能云解析',
            desc: '稳定、高效、安全灵活的内网和公网DNS解析以及流量调度服务',
            tags: [{
                text: '稳定可靠',
                color: 'grey',
            }, {
                text: '安全智能',
                color: 'grey',
            }],
            keys: ['产品版本'],
            options: [
                {
                    "产品版本": "普惠版",
                    "priceFlag": "￥",
                    "price": 99,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/dns/#/dns/order/create"
                },
                {
                    "产品版本": "企业版",
                    "priceFlag": "￥",
                    "price": 468,
                    "unit": "/年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/dns/#/dns/order/create"
                }
            ],
        }],
    }, {
        category: '企业建站',
        desc: '域名注册到上云必备，一站购齐',
        list: [{
            type: 'single',
            title: '域名注册服务',
            desc: '百余款域名后缀随心选，新用户注册COM域名仅需33元',
            tags: [{
                text: '首购特惠',
                color: 'red',
            }, {
                text: '百余款域名后缀',
                color: 'grey',
            }],
            keys: ['域名后缀', '购买时长'],
            options: [
                {
                    "域名后缀": ".xyz",
                    "购买时长": "1年",
                    "priceFlag": "￥",
                    "price": 13,
                    "unit": "/首年起",
                    "deletePrice": "￥119/年",
                    "href": "https://cloud.baidu.com/product/bcd/search.html"
                },
                {
                    "域名后缀": ".com",
                    "购买时长": "1年",
                    "priceFlag": "￥",
                    "price": 33,
                    "unit": "/首年起",
                    "deletePrice": "￥80/年",
                    "href": "https://cloud.baidu.com/product/bcd/search.html"
                },
                {
                    "域名后缀": ".net",
                    "购买时长": "1年",
                    "priceFlag": "￥",
                    "price": 66,
                    "unit": "/首年起",
                    "deletePrice": "￥100/年",
                    "href": "https://cloud.baidu.com/product/bcd/search.html"
                },
                {
                    "域名后缀": ".ltd",
                    "购买时长": "1年",
                    "priceFlag": "￥",
                    "price": 10,
                    "unit": "/首年起",
                    "deletePrice": "￥80/年",
                    "href": "https://cloud.baidu.com/product/bcd/search.html"
                }
            ],
        }, {
            type: 'single',
            title: 'SSL证书',
            desc: '助力企业打造高安全高可信站点',
            tags: [{
                text: '免部署',
                color: 'red',
            }, {
                text: '收录加速',
                color: 'grey',
            }],
            keys: ['证书品牌', '规格', '保护域名'],
            options: [
                {
                    "证书品牌": "BaiduTrust",
                    "规格": "域名型DV",
                    "保护域名": "1个",
                    "priceFlag": "￥",
                    "price": 880,
                    "unit": "/年",
                    "deletePrice": "￥1680/年",
                    "href": "https://console.bce.baidu.com/cas/#/cas/apply/create~brand=BAIDUTRUST&certType=DV&productType=SINGLE"
                },
                {
                    "证书品牌": "BaiduTrust",
                    "规格": "企业型OV",
                    "保护域名": "1个",
                    "priceFlag": "￥",
                    "price": 1980,
                    "unit": "/年",
                    "deletePrice": "￥3800/年",
                    "href": "https://console.bce.baidu.com/cas/#/cas/apply/create~brand=BAIDUTRUST&certType=OV&productType=SINGLE"
                },
                {
                    "证书品牌": "BaiduTrust",
                    "规格": "增强型EV",
                    "保护域名": "1个",
                    "priceFlag": "￥",
                    "price": 4980,
                    "unit": "/年",
                    "deletePrice": "￥8000/年",
                    "href": "https://console.bce.baidu.com/cas/#/cas/apply/create~brand=BAIDUTRUST&certType=EV&productType=SINGLE"
                }
            ],
        }, {
            type: 'single',
            title: '云虚拟主机',
            desc: '零基础站长轻松一站式搞定应用部署',
            tags: [{
                text: '高性价比',
                color: 'grey',
            }, {
                text: '快速收录',
                color: 'grey',
            }],
            keys: ['主机地域', '机型', '套餐'],
            options: [
                {
                    "主机地域": "内地",
                    "机型": "标准虚机",
                    "套餐": "优选型BC03",
                    "priceFlag": "￥",
                    "price": 359,
                    "unit": "/年",
                    "deletePrice": "￥399/年",
                    "href": "https://console.bce.baidu.com/bch/?_=1670999651981#/bch/create/create~packageId=903"
                },
                {
                    "主机地域": "内地",
                    "机型": "标准虚机",
                    "套餐": "高配型BC04",
                    "priceFlag": "￥",
                    "price": 499,
                    "unit": "/年",
                    "deletePrice": "￥599/年",
                    "href": "https://console.bce.baidu.com/bch/?_=1670999651981#/bch/create/create~packageId=904"
                },
                {
                    "主机地域": "内地",
                    "机型": "标准虚机",
                    "套餐": "豪华型BC05",
                    "priceFlag": "￥",
                    "price": 799,
                    "unit": "/年",
                    "deletePrice": "￥999/年",
                    "href": "https://console.bce.baidu.com/bch/?_=1670999651981#/bch/create/create~packageId=905"
                },
                {
                    "主机地域": "内地",
                    "机型": "商务安全主机",
                    "套餐": "商务SC01",
                    "priceFlag": "￥",
                    "price": 1066.92,
                    "unit": "/年",
                    "deletePrice": "￥1088.64/年",
                    "href": "https://console.bce.baidu.com/bch/#/bch/create/create~packageId=1000&series=SECURITY"
                },
                {
                    "主机地域": "内地",
                    "机型": "商务安全主机",
                    "套餐": "商务SC02",
                    "priceFlag": "￥",
                    "price": 1585.84,
                    "unit": "/年",
                    "deletePrice": "￥1688.64/年",
                    "href": "https://console.bce.baidu.com/bch/#/bch/create/create~packageId=1002&series=SECURITY"
                },
                {
                    "主机地域": "内地",
                    "机型": "商务安全主机",
                    "套餐": "商务SC03",
                    "priceFlag": "￥",
                    "price": 2204.76,
                    "unit": "/年",
                    "deletePrice": "￥2388.64/年",
                    "href": "https://console.bce.baidu.com/bch/#/bch/create/create~packageId=1003&series=SECURITY"
                },
                {
                    "主机地域": "内地",
                    "机型": "商务安全主机",
                    "套餐": "商务SC04",
                    "priceFlag": "￥",
                    "price": 3004.76,
                    "unit": "/年",
                    "deletePrice": "￥3988.64/年",
                    "href": "https://console.bce.baidu.com/bch/#/bch/create/create~packageId=1004&series=SECURITY"
                },
                {
                    "主机地域": "香港",
                    "机型": "标准虚机",
                    "套餐": "优选型BC03",
                    "priceFlag": "￥",
                    "price": 479,
                    "unit": "/年",
                    "deletePrice": "",
                    "href": "https://console.bce.baidu.com/bch/#/bch/create/create~packageId=903&region=hkg"
                },
                {
                    "主机地域": "香港",
                    "机型": "标准虚机",
                    "套餐": "高配型BC04",
                    "priceFlag": "￥",
                    "price": 719,
                    "unit": "/年",
                    "deletePrice": "",
                    "href": "https://console.bce.baidu.com/bch/#/bch/create/create~packageId=904&region=hkg"
                },
                {
                    "主机地域": "香港",
                    "机型": "标准虚机",
                    "套餐": "豪华型BC05",
                    "priceFlag": "￥",
                    "price": 1199,
                    "unit": "/年",
                    "deletePrice": "",
                    "href": "https://console.bce.baidu.com/bch/#/bch/create/create~packageId=905&region=hkg"
                }
            ],
        }, {
            type: 'single',
            title: '轻量应用服务器',
            desc: '大大降低企业和个人上云成本',
            tags: [{
                text: '1年8.3折',
                color: 'red',
            }, {
                text: '高性价比',
                color: 'grey',
            }],
            keys: ['套餐类型', '规格', '时长'],
            options: [
                {
                    "套餐类型": "标准型套餐",
                    "规格": "1核1G 3Mbps",
                    "时长": "1个月",
                    "priceFlag": "￥",
                    "price": 42,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ls/?_=1670996951717#/ls/instance/create"
                },
                {
                    "套餐类型": "标准型套餐",
                    "规格": "1核2G 4Mbps",
                    "时长": "1个月",
                    "priceFlag": "￥",
                    "price": 80,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ls/?_=1670996951717#/ls/instance/create"
                },
                {
                    "套餐类型": "标准型套餐",
                    "规格": "2核4G 6Mbps",
                    "时长": "1个月",
                    "priceFlag": "￥",
                    "price": 130,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ls/?_=1670996951717#/ls/instance/create"
                },
                {
                    "套餐类型": "标准型套餐",
                    "规格": "2核8G 8Mbps",
                    "时长": "1个月",
                    "priceFlag": "￥",
                    "price": 240,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ls/?_=1670996951717#/ls/instance/create"
                },
                {
                    "套餐类型": "存储型套餐",
                    "规格": "1核2G 3Mbps",
                    "时长": "1个月",
                    "priceFlag": "￥",
                    "price": 60,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ls/?_=1670996951717#/ls/instance/create"
                },
                {
                    "套餐类型": "存储型套餐",
                    "规格": "2核4G 4Mbps",
                    "时长": "1个月",
                    "priceFlag": "￥",
                    "price": 102,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ls/?_=1670996951717#/ls/instance/create"
                },
                {
                    "套餐类型": "存储型套餐",
                    "规格": "2核8G 5Mbps",
                    "时长": "1个月",
                    "priceFlag": "￥",
                    "price": 135,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ls/?_=1670996951717#/ls/instance/create"
                }
            ],
        }, {
            type: 'single',
            title: '云服务器',
            desc: '为您提供超高效费比的高性能云服务器',
            tags: [{
                text: '1年8.3折',
                color: 'red',
            }, {
                text: '高性价比',
                color: 'grey',
            }],
            keys: ['型号', '规格', '系统盘'],
            options: [
                {
                    "型号": "通用型g4",
                    "规格": "2核8G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 210.76,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=8&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "通用型g4",
                    "规格": "4核16G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 401.52,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=16&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "通用型g5",
                    "规格": "2核8G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 266.17,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=8&instanceType=34",
                    "mobileHref": null
                },
                {
                    "型号": "通用型g5",
                    "规格": "4核16G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 512.35,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=16&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "计算型c4",
                    "规格": "2核4G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 161.04,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=4&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "计算型c4",
                    "规格": "4核8G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 302.08,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=8&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "密集计算型ic5",
                    "规格": "2核2G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 136.18,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=2&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "密集计算型ic5",
                    "规格": "4核4G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 252.36,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=4&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "内存型m4",
                    "规格": "2核16G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 310.2,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=16&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "内存型m4",
                    "规格": "4核32G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 600.4,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=32&instanceType=13",
                    "mobileHref": null
                },
                {
                    "型号": "内存型m5",
                    "规格": "2核16G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 342.83,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=2&memory=16&instanceType=34",
                    "mobileHref": null
                },
                {
                    "型号": "内存型m5",
                    "规格": "4核32G",
                    "系统盘": "20GB",
                    "priceFlag": "￥",
                    "price": 665.67,
                    "unit": "/月起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/bcc/#/bcc/instance/create?cpu=4&memory=32&instanceType=34",
                    "mobileHref": null
                }
            ],
        }, {
            type: 'single',
            title: '智能门户AIPage',
            desc: '八合一站点，享多项百度搜索优势权益',
            tags: [{
                text: '八站合一',
                color: 'grey',
            }, {
                text: '收录加速',
                color: 'grey',
            }],
            keys: ['产品系列', '产品版本', '搭建方式'],
            options: [
                {
                    "产品系列": "全网版",
                    "产品版本": "基础版",
                    "搭建方式": "自助搭建",
                    "priceFlag": "￥",
                    "price": 298,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "全网版",
                    "产品版本": "展示版",
                    "搭建方式": "自助搭建",
                    "priceFlag": "￥",
                    "price": 980,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "全网版",
                    "产品版本": "展示版",
                    "搭建方式": "定制搭建",
                    "priceFlag": "￥",
                    "price": 4980,
                    "unit": "/首年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "全网版",
                    "产品版本": "官网版",
                    "搭建方式": "自助搭建",
                    "priceFlag": "￥",
                    "price": 1980,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "全网版",
                    "产品版本": "官网版",
                    "搭建方式": "定制搭建",
                    "priceFlag": "￥",
                    "price": 9980,
                    "unit": "/首年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "全网版",
                    "产品版本": "营销版",
                    "搭建方式": "自助搭建",
                    "priceFlag": "￥",
                    "price": 3980,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "全网版",
                    "产品版本": "营销版",
                    "搭建方式": "定制搭建",
                    "priceFlag": "￥",
                    "price": 15980,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "小程序版",
                    "产品版本": "基础版",
                    "搭建方式": "自助搭建",
                    "priceFlag": "￥",
                    "price": 178,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "小程序版",
                    "产品版本": "展示版",
                    "搭建方式": "自助搭建",
                    "priceFlag": "￥",
                    "price": 580,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "小程序版",
                    "产品版本": "展示版",
                    "搭建方式": "定制搭建",
                    "priceFlag": "￥",
                    "price": 1880,
                    "unit": "/首年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "小程序版",
                    "产品版本": "官网版",
                    "搭建方式": "自助搭建",
                    "priceFlag": "￥",
                    "price": 1280,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "小程序版",
                    "产品版本": "官网版",
                    "搭建方式": "定制搭建",
                    "priceFlag": "￥",
                    "price": 3880,
                    "unit": "/首年",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "小程序版",
                    "产品版本": "营销版",
                    "搭建方式": "自助搭建",
                    "priceFlag": "￥",
                    "price": 2580,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                },
                {
                    "产品系列": "小程序版",
                    "产品版本": "营销版",
                    "搭建方式": "定制搭建",
                    "priceFlag": "￥",
                    "price": 6580,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/aipage/#/site/create"
                }
            ],
        }],
    }, {
        category: '企业智能',
        desc: '助力企业智能化升级',
        list: [{
            type: 'single',
            title: '文字识别',
            desc: '多语种、高精度的文字检测与识别服务',
            tags: [{
                text: '70+项细分识别能力',
                color: 'grey',
            }, {
                text: '准确率98%+',
                color: 'grey',
            }],
            keys: ['服务类型', '接口名称', '规格'],
            options: [
                {
                    "服务类型": "通用场景OCR",
                    "接口名称": "通用文字识别（标准版）",
                    "规格": "1 万次",
                    "priceFlag": "￥",
                    "price": 50,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?fromai=1#/ai/ocr/purchaseAll/index~apiId=27"
                },
                {
                    "服务类型": "卡证OCR",
                    "接口名称": "身份证识别",
                    "规格": "1 万次",
                    "priceFlag": "￥",
                    "price": 200,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?fromai=1#/ai/ocr/purchaseAll/index~apiId=27"
                },
                {
                    "服务类型": "交通场景OCR",
                    "接口名称": "驾驶证识别",
                    "规格": "1 万次",
                    "priceFlag": "￥",
                    "price": 400,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?fromai=1#/ai/ocr/purchaseAll/index~apiId=27"
                },
                {
                    "服务类型": "财务票据OCR",
                    "接口名称": "增值税发票识别",
                    "规格": "1 万次",
                    "priceFlag": "￥",
                    "price": 480,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?fromai=1#/ai/ocr/purchaseAll/index~apiId=27"
                }
            ],
        }, {
            type: 'single',
            title: '语音识别',
            desc: '流式端到端语音语言一体化建模方法',
            tags: [{
                text: '识别准确率达98%',
                color: 'grey',
            }],
            keys: ['免费调用量', '有效期', '次数包'],
            options: [
                {
                    "免费调用量": " 200万次/企业账号",
                    "有效期": "1年",
                    "次数包": "100万次",
                    "priceFlag": "￥",
                    "price": 2400,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?fromai=1#/ai/speech/overview/index"
                },
                {
                    "免费调用量": " 200万次/企业账号",
                    "有效期": "1年",
                    "次数包": "1000万次",
                    "priceFlag": "￥",
                    "price": 19200,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?fromai=1#/ai/speech/overview/index"
                },
                {
                    "免费调用量": " 200万次/企业账号",
                    "有效期": "1年",
                    "次数包": "1亿次",
                    "priceFlag": "￥",
                    "price": 144000,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?fromai=1#/ai/speech/overview/index"
                },
                {
                    "免费调用量": " 200万次/企业账号",
                    "有效期": "1年",
                    "次数包": "10亿次",
                    "priceFlag": "￥",
                    "price": 960000,
                    "unit": "/年起",
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?fromai=1#/ai/speech/overview/index"
                }
            ],
        }, {
            type: 'single',
            title: '人脸识别',
            desc: '基于百度专业的深度学习算法和海量数据训练，拥有毫秒级识别响应能力',
            tags: [{
                text: '毫秒级识别响应',
                color: 'grey',
            }],
            keys: ['资源包类型', '规格'],
            options: [
                {
                    "资源包类型": "人脸检测",
                    "规格": "10万次",
                    "priceFlag": "￥",
                    "price": 38,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/#/ai/face/overview/index"
                },
                {
                    "资源包类型": "人脸对比",
                    "规格": "10万次",
                    "priceFlag": "￥",
                    "price": 48,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/#/ai/face/overview/index"
                },
                {
                    "资源包类型": "人脸搜索",
                    "规格": "10万次",
                    "priceFlag": "￥",
                    "price": 48,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/#/ai/face/overview/index"
                },
                {
                    "资源包类型": "人脸搜索M：N识别",
                    "规格": "10万次",
                    "priceFlag": "￥",
                    "price": 48,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/#/ai/face/overview/index"
                },
                {
                    "资源包类型": "人脸注册",
                    "规格": "10万次",
                    "priceFlag": "￥",
                    "price": 38,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/#/ai/face/overview/index"
                },
                {
                    "资源包类型": "人脸更新",
                    "规格": "10万次",
                    "priceFlag": "￥",
                    "price": 38,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/#/ai/face/overview/index"
                }
            ],
        }, {
            type: 'single',
            title: '图像识别',
            desc: '10余项高精度识图能力',
            tags: [{
                text: '定制化识图',
                color: 'grey',
            }, {
                text: '10万+物体和场景识别',
                color: 'grey',
            }],
            keys: ['规格', 'QPS限制', '有效期'],
            options: [
                {
                    "规格": "1万次",
                    "QPS限制": 10,
                    "有效期": "一年",
                    "priceFlag": "￥",
                    "price": 29,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=&fromai=1#/ai/imagerecognition/overview/index"
                },
                {
                    "规格": "10万次",
                    "QPS限制": 10,
                    "有效期": "一年",
                    "priceFlag": "￥",
                    "price": 270,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=&fromai=1#/ai/imagerecognition/overview/index"
                },
                {
                    "规格": "50万次",
                    "QPS限制": 10,
                    "有效期": "一年",
                    "priceFlag": "￥",
                    "price": 1250,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=&fromai=1#/ai/imagerecognition/overview/index"
                },
                {
                    "规格": "100万次",
                    "QPS限制": 10,
                    "有效期": "一年",
                    "priceFlag": "￥",
                    "price": 2200,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=&fromai=1#/ai/imagerecognition/overview/index"
                },
                {
                    "规格": "500万次",
                    "QPS限制": 10,
                    "有效期": "一年",
                    "priceFlag": "￥",
                    "price": 9500,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=&fromai=1#/ai/imagerecognition/overview/index"
                },
                {
                    "规格": "1000万次",
                    "QPS限制": 10,
                    "有效期": "一年",
                    "priceFlag": "￥",
                    "price": 17000,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=&fromai=1#/ai/imagerecognition/overview/index"
                }
            ],
        }, {
            type: 'single',
            title: '文本翻译',
            desc: '提供200+语种互译的在线文本翻译服务',
            tags: [{
                text: '日均响应千亿字符请求',
                color: 'grey',
            }],
            keys: ['免费测试量', '有效期', '字符包'],
            options: [
                {
                    "免费测试量": "最高1000万字符",
                    "有效期": "12个月",
                    "字符包": "1000万字符",
                    "priceFlag": "￥",
                    "price": 450,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=1626169856745#/ai/machinetranslation/overview/index"
                },
                {
                    "免费测试量": "最高1000万字符",
                    "有效期": "12个月",
                    "字符包": "5000万字符",
                    "priceFlag": "￥",
                    "price": 2100,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=1626169856745#/ai/machinetranslation/overview/index"
                },
                {
                    "免费测试量": "最高1000万字符",
                    "有效期": "12个月",
                    "字符包": "1亿字符",
                    "priceFlag": "￥",
                    "price": 4000,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=1626169856745#/ai/machinetranslation/overview/index"
                }
            ],
        }, {
            type: 'single',
            title: '文本审核',
            desc: '基于自然语言理解、深度学习等技术',
            tags: [{
                text: '多种语义模型',
                color: 'grey',
            }, {
                text: '万级敏感词库',
                color: 'grey',
            }],
            keys: ['审核类型', '有效期', '次数包'],
            options: [
                {
                    "审核类型": "文本审核",
                    "有效期": "1年",
                    "次数包": "50万次",
                    "priceFlag": "￥",
                    "price": 750,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=1671027114367#/ai/antiporn/buyPackage/index~apiId=362&status=1"
                },
                {
                    "审核类型": "文本审核",
                    "有效期": "1年",
                    "次数包": "300万次",
                    "priceFlag": "￥",
                    "price": 4200,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=1671027114367#/ai/antiporn/buyPackage/index~apiId=362&status=1"
                },
                {
                    "审核类型": "文本审核",
                    "有效期": "1年",
                    "次数包": "500万次",
                    "priceFlag": "￥",
                    "price": 6500,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=1671027114367#/ai/antiporn/buyPackage/index~apiId=362&status=1"
                },
                {
                    "审核类型": "文本审核",
                    "有效期": "1年",
                    "次数包": "1000万次",
                    "priceFlag": "￥",
                    "price": 12000,
                    "unit": null,
                    "deletePrice": null,
                    "href": "https://console.bce.baidu.com/ai/?_=1671027114367#/ai/antiporn/buyPackage/index~apiId=362&status=1"
                }
            ],
        }],
    }],
};

export const indexAllProducts: {
    [props: string]: GroupBuyItem[];
} = {
    BCC: productAllBcc,
    BOS: productAllBos,
    CDN: productAllCdn,
    LSS: lssAllProducts,
    TMS: productAllTms,
    AIPAGE: aipageAllProducts,
    CBS: productAllCbs,
    NAT: productAllNat,
    EIP: productAllEip.filter(item => item.displayName !== displayNameEipEumn['增强型BGP']),
};

export const defaultIndexBccJingxiang = {
    "imageId": "m-AfH5u6IE",
    "imageType": "common",
    "osType": "linux",
    "osVersion": "6.8 x86_64 (64bit)",
};

export const defaultIndexCbsCity = {
    label: '北京',
    value: 'Beijing',
    code: '110100',
};

export const enterpriseServiceTypes: ServiceType[] = ['LSS'];

export const indexMergePurchaceAbtestId = '42897445d09448dd92e9724963f77790';

export const indexMoreRecommend: MergePurchaceModuleObj = {
    category: '更多推荐',
    desc: '更多应用场景，助力企业数字化转型',
    list: [{
        type: 'merge',
        title: '企业上云场景',
        titleTag: '热销',
        tags: [{
            text: '企业上云必备',
            color: 'red',
        }, {
            text: '成本节约',
            color: 'grey',
        }],
        desc: 'BCC+BOS+CDN一站购齐，轻松上云',
        mergeParams: [{
            displayName: '云服务器g4',
            serviceType: 'BCC',
            desc: '基于第二代英特尔® 至强® 可扩展处理器，适于对计算能力较高的场景',
            link: 'https://cloud.baidu.com/product/bcc.html',
            params: [{
                key: 'cpu',
                transform: v => `${v}核`,
            }, {
                key: 'memory',
                transform: v => `${v}G`,
            }, {
                key: 'duration',
                transform: monTrans,
            }],
            filter: [{
                key: 'specName',
                value: '通用型g4',
            }],
        }, {
            displayName: 'BOS标准存储包',
            serviceType: 'BOS',
            desc: '适用于移动应用、网站视频、数据分析等场景',
            link: 'https://cloud.baidu.com/doc/BOS/s/Bk24ffn89',
            params: [{
                key: 'capacity',
                transform: v => CapacityBosMap[v as CapacityBosEnum],
            }, {
                key: 'duration',
                transform: monTrans,
            }],
            filter: [{
                key: 'displayName',
                value: 'BOS(标准存储包)',
            }],
        }, {
            displayName: 'CDN流量包',
            serviceType: 'CDN',
            desc: '全球拥有2800+节点，中国境内拥有2300+节点',
            link: 'https://cloud.baidu.com/product/cdn.html',
            params: [{
                key: 'capacity',
                transform: v => CapacityCdnMap[v as CapacityCdnEnum],
            }, {
                key: 'duration',
                transform: monTrans,
            }],
            filter: [{
                key: 'displayName',
                value: '流量包',
            }],
        }],
        initialPrice: {
            priceBefore: '￥',
            price: '323.76',
            priceAfter: '',
            linePrice: '',
        },
    }, {
        type: 'merge',
        title: '网站加速场景',
        desc: '大大降低带宽资源消耗，提高用户访问应用资源的响应速度，提升用户浏览体验',
        tags: [{
            text: '节约带宽成本',
            color: 'red',
        }, {
            text: '海量节点',
            color: 'grey',
        }],
        mergeParams: [{
            displayName: 'CDN流量包',
            serviceType: 'CDN',
            desc: '全球拥有2800+节点，中国境内拥有2300+节点',
            link: 'https://cloud.baidu.com/product/cdn.html',
            params: [{
                key: 'capacity',
                transform: v => CapacityCdnMap[v as CapacityCdnEnum],
            }, {
                key: 'duration',
                transform: monTrans,
            }],
            filter: [{
                key: 'displayName',
                value: '流量包',
            }],
        }, {
            displayName: '云服务器g4',
            serviceType: 'BCC',
            desc: '基于第二代英特尔® 至强® 可扩展处理器，适于对计算能力较高的场景',
            link: 'https://cloud.baidu.com/product/bcc.html',
            params: [{
                key: 'cpu',
                transform: v => `${v}核`,
            }, {
                key: 'memory',
                transform: v => `${v}G`,
            }, {
                key: 'duration',
                transform: monTrans,
            }],
            filter: [{
                key: 'specName',
                value: '通用型g4',
            }],
        }],
        initialPrice: {
            priceBefore: '￥',
            price: '269.76',
            priceAfter: '',
            linePrice: '',
        },
    }, {
        type: 'merge',
        title: '公网访问场景',
        desc: '灵活匹配业务变更，增加公网访问弹性',
        tags: [{
            text: '企业上云必备',
            color: 'red',
        }, {
            text: '节约成本',
            color: 'grey',
        }],
        mergeParams: [{
            displayName: '云服务器g4',
            serviceType: 'BCC',
            desc: '基于第二代英特尔® 至强® 可扩展处理器，适于对计算能力较高的场景',
            link: 'https://cloud.baidu.com/product/bcc.html',
            params: [{
                key: 'cpu',
                transform: v => `${v}核`,
            }, {
                key: 'memory',
                transform: v => `${v}G`,
            }, {
                key: 'duration',
                transform: monTrans,
            }],
            filter: [{
                key: 'specName',
                value: '通用型g4',
            }],
        }, {
            displayName: 'NAT网关',
            serviceType: 'NAT',
            desc: '支持 IP 地址转换的网络云服务，为私有网络提供访问Internet服务',
            link: 'https://cloud.baidu.com/product/nat.html',
            params: [{
                key: 'displayName',
                transform: v => `${displayNameNatArr.find(item => item.value === v).text}型-`,
            }, {
                key: 'duration',
                transform: v => DurationNatMap[v as keyof typeof DurationNatMap],
            }],
        }, {
            displayName: '弹性公网IP',
            serviceType: 'EIP',
            desc: '为用户提供公网带宽服务',
            link: 'https://cloud.baidu.com/product/eip.html',
            params: [{
                key: 'displayName',
                transform: v => `${(v as string).slice(0, 3)}-`,
            }, {
                key: 'duration',
                transform: v => DurationEipMap[v as keyof typeof DurationEipMap],
            }],
        }],
        initialPrice: {
            priceBefore: '￥',
            price: '606.76',
            priceAfter: '',
            linePrice: '',
        },
    }, {
        type: 'merge',
        title: '企业应用存储场景',
        desc: '用于图片视频等大文件的存储，降低存储成本，满足企业应用对存储灵活购买的需求',
        tags: [{
            text: '降低存储成本',
            color: 'red',
        }, {
            text: '智能存储',
            color: 'grey',
        }],
        mergeParams: [{
            displayName: '云服务器m4',
            serviceType: 'BCC',
            desc: '基于第二代英特尔® 至强® 可扩展处理器，适于对计算能力较高的场景',
            link: 'https://cloud.baidu.com/product/bcc.html',
            params: [{
                key: 'cpu',
                transform: v => `${v}核`,
            }, {
                key: 'memory',
                transform: v => `${v}G`,
            }, {
                key: 'duration',
                transform: monTrans,
            }],
            filter: [{
                key: 'specName',
                value: '内存型m4',
            }],
        }, {
            displayName: 'BOS标准存储包',
            serviceType: 'BOS',
            desc: '适用于移动应用、网站视频、数据分析等场景',
            link: 'https://cloud.baidu.com/doc/BOS/s/Bk24ffn89',
            params: [{
                key: 'capacity',
                transform: v => CapacityBosMap[v as CapacityBosEnum],
            }, {
                key: 'duration',
                transform: monTrans,
            }],
            filter: [{
                key: 'displayName',
                value: 'BOS(标准存储包)',
            }],
        }],
        initialPrice: {
            priceBefore: '￥',
            price: '407.2',
            priceAfter: '',
            linePrice: '',
        },
    }, {
        type: 'merge',
        title: '音视频场景组合',
        desc: '依托百度实时转码与全球分发能力，提供稳定流畅、低延迟的一站式音视频存储服务',
        tags: [{
            text: '企业专享',
            color: 'red',
        }, {
            text: '全球分发能力',
            color: 'grey',
        }],
        mergeParams: [{
            displayName: 'LSS流量包',
            serviceType: 'LSS',
            desc: '提供稳定流畅、低延迟、支持高并发的一站式智能直播云服务。',
            link: 'https://cloud.baidu.com/product/lss.html',
            params: [{
                key: 'capacity',
                transform: v => CapacityLssMap[v as CapacityLssEnum],
            }, {
                key: 'duration',
                transform: monTrans,
            }],
            filter: [{
                key: 'displayName',
                value: '流量包',
            }],
        }, {
            displayName: 'CDN流量包',
            serviceType: 'CDN',
            desc: '全球拥有2800+节点，中国境内拥有2300+节点',
            link: 'https://cloud.baidu.com/product/cdn.html',
            params: [{
                key: 'capacity',
                transform: v => CapacityCdnMap[v as CapacityCdnEnum],
            }, {
                key: 'duration',
                transform: monTrans,
            }],
            filter: [{
                key: 'displayName',
                value: '流量包',
            }],
        }],
        initialPrice: {
            priceBefore: '￥',
            price: '232',
            priceAfter: '',
            linePrice: '',
        },
    }, {
        type: 'merge',
        title: '公司初创场景',
        desc: '公司注册、工商注册、建站一步到位，满足客户在企业初创阶段的业务需求',
        tags: [{
            text: '初创企业必备',
            color: 'red',
        }, {
            text: '专业服务',
            color: 'grey',
        }],
        mergeParams: [{
            displayName: '工商财税',
            serviceType: 'CBS',
            desc: '工商财税一站式服务，助您省心省力开公司',
            link: 'https://cloud.baidu.com/product/cbs?trace=cbsnav&pageResource=cbsnav',
            params: [{
                key: 'displayName',
            }],
            filter: [],
        }, {
            displayName: '智能门户AIpage',
            serviceType: 'AIPAGE',
            desc: '八合一站点，享多项百度搜索优势权益',
            link: 'https://cloud.baidu.com/product/aipage.html',
            params: [{
                key: 'displayName',
            }, {
                key: 'capacity',
            }, {
                key: 'duration',
                transform: v => `-${v}年`,
            }],
            filter: [],
        }],
        initialPrice: {
            priceBefore: '￥',
            price: '2179',
            priceAfter: '',
            linePrice: '',
        },
    }],
}