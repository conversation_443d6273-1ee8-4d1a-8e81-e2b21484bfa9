
import {ReactNode, useRef, useState} from 'react';
import {useOnMount, useOnUpdate} from '@baidu/bce-hooks';
import cn from 'classnames';
import {USelect} from '@common/components/USelect/USelect';
import {uniqWith} from 'lodash';
import {UMessage} from '@common/components/UMessage/UMessage';
import {Ioc} from '@baidu/bce-decorators';
import {useCheckUserinfo} from '@common/hooks/useCheckUserinfo';
import {GROUP_BUY, GROUP_PURCHASE, REAL_NAME_URL} from '@common/constant/variableConst';
import {netService, UDynamicService} from '@baidu/bce-services';
import {UConfirm} from '@common/components/UConfirm/UConfirm';
import {urlConst} from '@common/constant/urlConst';
import Big from 'big.js';
import {ProductPriceItem} from '@common/interface/groupBuy';
import {UFeMonitorExposure} from '@common/components/UFeMonitorExposure/UFeMonitorExposure';
import {sendMonitor} from '@common/helper/page';
import {isMobile, replaceOfString} from '@baidu/bce-helper';
import {GroupPurchaseProductPriceParams} from '@common/interface/groupPurchase';
import {ProductPriceDetailObj} from '@common/interface/page';
import {
    IndexMergeParamsObj, IndexMergePurchaceItem, indexAllProducts, GroupBuyItem,
    enterpriseServiceTypes, indexMergePurchaceAbtestId,
} from './model';
import {transPriceParams} from './transPriceParams';

const {Option} = USelect;

type SingleOptionType = Array<string | number>;

type PurchaceCardProps = IndexMergePurchaceItem & {
    category: string;
    styles: {
        readonly [key: string]: string;
    };
    moduleCategory: string;
};

// 生成单个产品购买选项的列表
const genOptions = (curOption: IndexMergePurchaceItem['options'][0], props: IndexMergePurchaceItem) => {
    const selectOptions: {
        [props: string]: SingleOptionType;
    } = {};
    props.keys.forEach((key, index) => {
        if (!selectOptions[key]) {
            selectOptions[key] = [];
        }
        props.options.forEach(item => {
            let flag = true;
            props.keys.forEach((k, idx) => {
                if (k !== key && item[k] !== curOption[k] && idx < index) {
                    flag = false;
                }
            });
            if (flag && !selectOptions[key].includes(item[key])) {
                selectOptions[key].push(item[key]);
            }
        });
    });
    return selectOptions;
};

// 根据需要筛选的内容生成唯一商品列表
const genProductList = (config: IndexMergeParamsObj) => {
    const productList = config.filter?.length ? (indexAllProducts[config.serviceType]).filter(item => (
        config.filter.every(filter => item[filter.key as keyof typeof item] === filter.value)
    )) : indexAllProducts[config.serviceType];
    return uniqWith(productList, (v1, v2) => (
        config.params.every(item => v1[item.key as keyof typeof v1] === v2[item.key as keyof typeof v2])
    ));
};

// 组合购选择器生成label
const genSelectLabel = (pro: GroupBuyItem, params: IndexMergeParamsObj['params']) => {
    return params.reduce((res, param) => {
        const v = pro[param.key as keyof typeof pro];
        return (`${res}${param.transform ? param.transform(v) : v}`);
    }, '');
};

// 打开实名confirm弹窗
const openRealNameConfirm = (content: string) => {
    Ioc(UDynamicService).open({
        component: UConfirm,
        props: {
            title: '温馨提示',
            content,
            okText: '前往实名',
            hideCancel: true,
            maskClosable: true,
            onOk: () => {
                window.open(REAL_NAME_URL.online, '_blank');
            },
        },
    });
};

// 基础卡片
const BaseCard = (
    props: PurchaceCardProps
    & {
        onHandleBuy: () => void;
        selectSlot: ReactNode;
        priceDetail: IndexMergePurchaceItem['initialPrice'];
        category: string;
        wrapper?: HTMLElement;
        children?: ReactNode;
        cardRef?: React.Ref<HTMLDivElement>;
    }
) => {
    const {styles, moduleCategory, cardRef} = props;
    return (
        <div className={cn(styles['card-inner'], styles[props.type])} ref={cardRef}>
            <div className={styles['title-wrap']}>
                <h3>{props.title}</h3>
                {props.titleTag && <span>{props.titleTag}</span>}
            </div>
            <p className={cn(styles['desc'], 'desc')}>{props.desc}</p>
            {props.selectSlot}
            {
                props.tags && (
                    <ul className={styles['tags']}>
                        {
                            props.tags.map(item => (
                                <li
                                    key={item.text}
                                    className={styles[item.color]}
                                >
                                    {item.text}
                                </li>
                            ))
                        }
                    </ul>
                )
            }
            <div className={styles['price-wrapper']}>
                {
                    props.priceDetail.price === '计算中' ? (
                        <span className={styles['price-caculating']}>计算中</span>
                    ) : (
                        <>
                            {
                                props.priceDetail.priceBefore && (
                                    <span className={styles['price-before']}>{props.priceDetail.priceBefore}</span>
                                )
                            }
                            <span className={styles['price']}>{props.priceDetail.price}</span>
                            {
                                props.priceDetail.priceAfter && (
                                    <span className={styles['price-after']}>{props.priceDetail.priceAfter}</span>
                                )
                            }
                            {
                                props.priceDetail.linePrice && (
                                    <span className={styles['line-price']}>{props.priceDetail.linePrice}</span>
                                )
                            }
                        </>
                    )
                }
            </div>
            <UFeMonitorExposure
                category={`${moduleCategory}&${indexMergePurchaceAbtestId}`}
                name={props.category}
                value={`${props.title}-立即购买&2`}
            >
                <div className={styles['buy-btn']} onClick={props.onHandleBuy}>
                    <span>立即购买</span>
                </div>
            </UFeMonitorExposure>
            {props.children}
        </div>
    );
};

// 单个产品购买卡片
export const IndexSinglePurchaceCard = (props: PurchaceCardProps) => {
    const {styles, moduleCategory} = props;
    const [curOption, setCurOption] = useState(props.options[0]);
    const [dropdownMatchSelectWidth, setDropdownMatchSelectWidth] = useState(false);
    const [selectOptions, setSelectOptions] = useState<{
        [props: string]: SingleOptionType;
    }>(() => genOptions(curOption, props));

    const handleSelectChange = (key: string, v: string) => {
        const tempOption = {...curOption};
        tempOption[key] = v;
        for (let i = props.keys.length - 1; i >= 0; i--) {
            const keys = props.keys.slice(0, i + 1);
            const tempList = props.options.filter(item => keys.every(key => item[key] === tempOption[key]));
            if (tempList.length) {
                setCurOption(tempList[0]);
                setSelectOptions(() => genOptions(tempList[0], props));
                break;
            }
        }
    };

    const handleBuy = () => {
        sendMonitor({
            category: `${moduleCategory}&${indexMergePurchaceAbtestId}`,
            name: props.category,
            value: `${props.title}-立即购买&2`,
            action: 'click',
        });
        window.open(curOption.href, '_blank');
    };

    useOnMount(() => {
        setDropdownMatchSelectWidth(isMobile());
    });

    return (
        <BaseCard
            {...props}
            selectSlot={(
                <div className={styles['card-select']}>
                    {
                        props.keys.map(key => (
                            <div key={key} className={styles['card-select-item']}>
                                <span className={styles['select-label']}>{key}</span>
                                {
                                    selectOptions[key].length > 1 ? (
                                        <USelect
                                            className={styles['select']}
                                            value={curOption[key]}
                                            onChange={v => {
                                                handleSelectChange(key, v);
                                            }}
                                            dropdownMatchSelectWidth={dropdownMatchSelectWidth}
                                        >
                                            {
                                                selectOptions[key].map(item => (
                                                    <Option key={item} value={item}>{item}</Option>
                                                ))
                                            }
                                        </USelect>
                                    ) : (
                                        <span className={styles['select-one-option']}>{selectOptions[key][0]}</span>
                                    )
                                }
                            </div>
                        ))
                    }
                </div>
            )}
            priceDetail={{
                priceBefore: curOption.priceFlag,
                price: curOption.price,
                priceAfter: curOption.unit,
                linePrice: curOption.deletePrice,
            }}
            onHandleBuy={handleBuy}
        />
    );
};

// 组合购买卡片
export const IndexMergePurchaceCard = (props: PurchaceCardProps) => {
    const {styles, moduleCategory} = props;
    const [productConfigs] = useState<IndexMergeParamsObj[]>(() => props.mergeParams.map(item => ({
        ...item,
        products: genProductList(item),
    })));
    const [productList, setProductList] = useState(productConfigs.map(item => item.products[0]));
    const [checkedList, setCheckedList] = useState(productConfigs.map(() => true));
    const [curLabel, setCurLabel] = useState(-1);
    const [priceDetail, setPriceDetail] = useState(props.initialPrice);
    const [dropdownMatchSelectWidth, setDropdownMatchSelectWidth] = useState(false);
    const timer = useRef(null);
    const tipWrapper = useRef(null);
    const [, , loginHandler, checkUserTypeHandler] = useCheckUserinfo({
        isImmediately: false,
        maskClosable: true,
    });
    const handleBuy = () => {
        loginHandler(info => {
            if (info.hasLogin) {
                checkUserTypeHandler(userType => {
                    if (userType === 'NONE') {
                        openRealNameConfirm('本产品仅限实名用户，如需购买请完成实名认证');
                        return;
                    }
                    if (userType !== 'ENTERPRISE' && productList.some(item => enterpriseServiceTypes.includes(item.serviceType))) {
                        openRealNameConfirm('本产品仅限企业实名用户，如需购买请完成企业实名');
                        return;
                    }
                    sessionStorage.setItem(GROUP_BUY, JSON.stringify({
                        productList,
                        checkedList,
                    }));
                    sendMonitor({
                        category: `${moduleCategory}&${indexMergePurchaceAbtestId}`,
                        name: props.category,
                        value: `${props.title}-立即购买&2`,
                        action: 'click',
                    });
                    window.open('/groupBuy', '_self');
                });
            } else {
                UMessage({
                    type: 'info',
                    content: '请先登录哦~',
                    wrapper: tipWrapper.current,
                });
            }
        });
    };
    const handleSelectChange = (idx: number, v: GroupBuyItem) => {
        setPriceDetail({price: '计算中'});
        setProductList(pre => {
            const newValue = [...pre];
            newValue[idx] = v;
            return newValue;
        });
    };
    const handleCheckBoxChange = (idx: number) => {
        const newValue = [...checkedList];
        newValue[idx] = !newValue[idx];
        if (newValue.every(item => !item)) {
            UMessage({
                type: 'warning',
                content: '请最少选择1款产品',
                wrapper: tipWrapper.current,
            });
            return;
        }
        setPriceDetail({price: '计算中'});
        setCheckedList(newValue);
    };
    const clearTimer = () => {
        if (timer.current) {
            clearTimeout(timer.current);
            timer.current = null;
        }
    };
    const handleMounseEnterLabel = (idx: number) => {
        clearTimer();
        timer.current = setTimeout(() => {
            setCurLabel(idx);
        }, 300);
    };

    useOnUpdate(() => {
        const configs = transPriceParams(productList, checkedList);
        Promise.all(configs.map(item => {
            const signAuth = item.signAuth.replace(/[^a-zA-Z]/g, '');
            return netService.get(replaceOfString(urlConst.GET_PRODUCT_PRICE_FROM_BOS, {
                ':parentServiceType': item.parentServiceType,
                ':serviceType': item.serviceType,
                ':signAuth': signAuth,
            }));
        })).then(data => {
            const newPriceDet: IndexMergePurchaceItem['initialPrice'] = (data as any as ProductPriceItem[]).reduce((pre, item) => ({
                ...pre,
                price: new Big(pre.price).add(item.currentPrice).toNumber(),
                linePrice: new Big(pre.linePrice).add(item.originPrice).toNumber(),
            }), {
                price: 0,
                linePrice: 0,
                priceBefore: '￥',
            });
            if (newPriceDet.price < newPriceDet.linePrice) {
                newPriceDet.linePrice = `￥${newPriceDet.linePrice}`;
            } else {
                delete newPriceDet.linePrice;
            }
            setPriceDetail(newPriceDet);
        }).catch(() => {
            UMessage({
                type: 'error',
                content: '价格获取异常',
                wrapper: tipWrapper.current,
            });
        });
    }, [productList, checkedList]);

    useOnMount(() => {
        setDropdownMatchSelectWidth(isMobile());
    });

    return (
        <BaseCard
            {...props}
            selectSlot={(
                <div className={styles['card-select']}>
                    {
                        productConfigs.map((item, index) => (
                            <div key={item.displayName} className={styles['card-select-item']}>
                                <label className={styles['checkbox-wrap']}>
                                    <input
                                        type="checkbox"
                                        checked={checkedList[index]}
                                        onChange={() => handleCheckBoxChange(index)}
                                    />
                                    <span></span>
                                </label>
                                <span
                                    className={styles['select-label']}
                                    onMouseEnter={() => handleMounseEnterLabel(index)}
                                    onMouseLeave={() => {
                                        clearTimer();
                                        curLabel > -1 && setCurLabel(-1);
                                    }}
                                >{item.displayName}
                                </span>
                                <div className={cn(styles['hover-detail'], curLabel === index ? styles.active : '')}>
                                    <div className={styles['hover-inner']}>
                                        <h4>{item.displayName}</h4>
                                        <p>
                                            {item.desc}
                                            <a href={item.link} target="_blank">查看详情<span></span></a>
                                        </p>
                                    </div>
                                </div>
                                <USelect
                                    className={styles['select']}
                                    defaultValue={0}
                                    onChange={idx => handleSelectChange(index, item.products[idx])}
                                    options={item.products.map((pro, idx) => ({
                                        value: idx,
                                        label: genSelectLabel(pro, item.params),
                                    }))}
                                    dropdownMatchSelectWidth={dropdownMatchSelectWidth}
                                />
                            </div>
                        ))
                    }
                </div>
            )}
            priceDetail={priceDetail}
            onHandleBuy={handleBuy}
        >
            <div ref={tipWrapper} className={styles['tip-wrapper']}></div>
        </BaseCard>
    );
};

type PriceType = {
    priceBefore?: string;
    price: string | number;
    priceAfter?: string;
    linePrice?: string | number;
};

export const IndexMergePurchaceCardNew = (props: PurchaceCardProps) => {
    const {styles, moduleCategory} = props;
    const [productList, setProductList] = useState(props.mergeParams.map(item => item.dataList[0].data));
    const [formValueList, setFormValueList] = useState(props.mergeParams.map(item => item.dataList[0].formValue));
    const [checkedList, setCheckedList] = useState(props.mergeParams.map(() => true));
    const [priceList, setPriceList] = useState<PriceType[]>([
        {price: 0, priceBefore: '¥'},
        {price: 0, priceBefore: '¥'},
        {price: 0, priceBefore: '¥'},
    ]);
    const [curLabel, setCurLabel] = useState(-1);
    const [priceDetail, setPriceDetail] = useState<PriceType>({price: '计算中'});
    const [dropdownMatchSelectWidth, setDropdownMatchSelectWidth] = useState(false);
    const timer = useRef(null);
    const tipWrapper = useRef(null);
    const ref = useRef<HTMLDivElement>(null);
    const [, , loginHandler, checkUserTypeHandler] = useCheckUserinfo({
        isImmediately: false,
        maskClosable: true,
    });
    const handleBuy = () => {
        loginHandler(info => {
            if (info.hasLogin) {
                checkUserTypeHandler(userType => {
                    if (userType === 'NONE') {
                        openRealNameConfirm('本产品仅限实名用户，如需购买请完成实名认证');
                        return;
                    }
                    sessionStorage.setItem(GROUP_PURCHASE, JSON.stringify(props.mergeParams.map((item, idx) => ({
                        serviceType: item.serviceType,
                        selected: checkedList[idx],
                        product: formValueList[idx],
                    }))));
                    sendMonitor({
                        category: `${moduleCategory}&${indexMergePurchaceAbtestId}`,
                        name: props.category,
                        value: `${props.title}-立即购买&2`,
                        action: 'click',
                    });
                    location.href = '/group-purchase';
                });
            } else {
                UMessage({
                    type: 'info',
                    content: '请先登录哦~',
                    wrapper: tipWrapper.current,
                });
            }
        });
    };
    const getPrice = (v: GroupPurchaseProductPriceParams[]) => {
        return new Promise<ProductPriceDetailObj[]>((resolve, reject) => {
            netService.post<{data: GroupPurchaseProductPriceParams[]}, ProductPriceDetailObj[]>(
                urlConst.POST_PRODUCT_PRICE,
                {data: v}
            ).then(data => {
                if (data.success && data.status === 200) {
                    resolve(data.result);
                } else {
                    reject(new Error('获取价格失败'));
                }
            });
        });
    };
    const caculatePrice = (newPriceList: PriceType[], newCheckedList: boolean[]) => {
        const newPriceDet = newPriceList.reduce((res, item, index) => (
            newCheckedList[index] ? ({
                ...res,
                price: new Big(res.price).add(item.price).toNumber(),
            }) : res
        ), {
            price: 0,
            linePrice: 0,
            priceBefore: '¥',
        });
        if (newPriceDet.price < newPriceDet.linePrice) {
            newPriceDet.linePrice = `￥${newPriceDet.linePrice}`;
        } else {
            delete newPriceDet.linePrice;
        }
        setPriceDetail(newPriceDet);
    };
    const handleSelectChange = (idx: number, v: IndexMergeParamsObj['dataList'][0]) => {
        if (priceDetail.price === '计算中') {
            UMessage({
                type: 'warning',
                content: '询价中，请稍后',
                wrapper: tipWrapper.current,
            });
            return;
        }
        const newValue = [...productList];
        newValue[idx] = v.data;
        setProductList(newValue);
        setFormValueList(pre => {
            pre[idx] = v.formValue;
            return [...pre];
        });
        setPriceDetail({price: '计算中'});
        // 根据v获取一下价格，然后setPriceList, 如果checkedList[idx]为true，setPriceDetail
        getPrice(v.data).then(data => {
            const thePrice = data.reduce((res, item) => ({
                ...res,
                price: new Big(res.price).add(item.price).toNumber(),
            }), {
                price: 0,
                linePrice: 0,
                priceBefore: '¥',
            });
            const newPriceList = [...priceList];
            newPriceList[idx] = thePrice;
            caculatePrice(newPriceList, checkedList);
            setPriceList(newPriceList);
        });
    };
    const handleCheckBoxChange = (idx: number) => {
        if (priceDetail.price === '计算中') {
            UMessage({
                type: 'warning',
                content: '询价中，请稍后',
                wrapper: tipWrapper.current,
            });
            return;
        }
        const newValue = [...checkedList];
        newValue[idx] = !newValue[idx];
        if (newValue.every(item => !item)) {
            UMessage({
                type: 'warning',
                content: '请最少选择1款产品',
                wrapper: tipWrapper.current,
            });
            return;
        }
        setCheckedList(newValue);
        caculatePrice(priceList, newValue);
    };
    const clearTimer = () => {
        if (timer.current) {
            clearTimeout(timer.current);
            timer.current = null;
        }
    };
    const handleMounseEnterLabel = (idx: number) => {
        clearTimer();
        timer.current = setTimeout(() => {
            setCurLabel(idx);
        }, 300);
    };

    useOnMount(() => {
        setDropdownMatchSelectWidth(isMobile());
        let observerable: IntersectionObserver = null;
        const getAllPrice = () => {
            try {
                Promise.all(productList.map(item => getPrice(item))).then(data => {
                    const newPriceList: PriceType[] = data.map(item => item.reduce((r, p) => (
                        {
                            ...r,
                            price: new Big(r.price).add(p.price).toNumber(),
                        }
                    ), {
                        price: 0,
                        linePrice: 0,
                        priceBefore: '¥',
                    }));
                    caculatePrice(newPriceList, checkedList);
                    setPriceList(newPriceList);
                });
            } catch (error) {
                UMessage({
                    type: 'error',
                    content: '网络错误，请稍后重试',
                    wrapper: tipWrapper.current,
                });
                console.error(error);
            }
        };
        try {
            observerable = new IntersectionObserver(entries => {
                entries.forEach(item => {
                    if (item.isIntersecting) {
                        const target = item.target as HTMLDivElement;
                        getAllPrice();
                        observerable.unobserve(target);
                    }
                });
            }, {
                rootMargin: '0px 0px 256px 0px',
            });
            observerable.observe(ref.current);
        } catch (e) {
            getAllPrice();
        }
        return () => {
            ref.current && observerable && observerable.unobserve(ref.current);
        };
    });

    return (
        <BaseCard
            {...props}
            selectSlot={(
                <div className={styles['card-select']}>
                    {
                        props.mergeParams.map((item, index) => (
                            <div key={item.displayName} className={styles['card-select-item']}>
                                <label className={styles['checkbox-wrap']}>
                                    <input
                                        type="checkbox"
                                        checked={checkedList[index]}
                                        onChange={() => handleCheckBoxChange(index)}
                                    />
                                    <span></span>
                                </label>
                                <span
                                    className={styles['select-label']}
                                    onMouseEnter={() => handleMounseEnterLabel(index)}
                                    onMouseLeave={() => {
                                        clearTimer();
                                        curLabel > -1 && setCurLabel(-1);
                                    }}
                                >{item.displayName}
                                </span>
                                <div className={cn(styles['hover-detail'], curLabel === index ? styles.active : '')}>
                                    <div className={styles['hover-inner']}>
                                        <h4>{item.displayName}</h4>
                                        <p>
                                            {item.desc}
                                            <a href={item.link} target="_blank">查看详情<span></span></a>
                                        </p>
                                    </div>
                                </div>
                                <USelect
                                    className={styles['select']}
                                    defaultValue={0}
                                    onChange={idx => handleSelectChange(index, item.dataList[idx])}
                                    options={item.dataList.map((pro, idx) => ({
                                        value: idx,
                                        label: pro.label,
                                    }))}
                                    dropdownMatchSelectWidth={dropdownMatchSelectWidth}
                                />
                            </div>
                        ))
                    }
                </div>
            )}
            priceDetail={priceDetail}
            onHandleBuy={handleBuy}
            cardRef={ref}
        >
            <div ref={tipWrapper} className={styles['tip-wrapper']}></div>
        </BaseCard>
    );
};
