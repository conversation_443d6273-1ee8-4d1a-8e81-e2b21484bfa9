/* eslint-disable max-statements */
/* eslint-disable complexity */
/**
 * @file 处理价格参数
 * <AUTHOR>
 */

import {configIdBosMap, DisplayNameBosEumn} from '@components/groupBuy/GroupItemBos/model';
import moment from 'moment';
import {configGroupIdBCC, configIdCDS, configIdEIP} from '@components/groupBuy/GroupItemBcc/model';
import {DisplayNameTmsEumn} from '@components/groupBuy/GroupItemTms/model';
import {versionAipageMap} from '@components/groupBuy/GroupItemAipage/model';
import {ComTypeCbsEumn} from '@components/groupBuy/GroupItemCbs/model';
import {displayNameEipEumn} from '@components/groupBuy/GroupItemEip/model';
import {genGroupBuySignAuth} from '@common/helper/page';
import {defaultIndexBccJingxiang, defaultIndexCbsCity, GroupBuyItem} from './model';

export const transPriceParams = (productList: GroupBuyItem[], checkedList: boolean[]) => {
    const checkedProList = productList.filter((_, i) => checkedList[i]);
    const params: any[] = [];
    checkedProList.forEach(item => {
        if (item.serviceType === 'BCC') {
            [{
                'serviceType': 'BCC',
                'parentServiceType': 'BCC',
                'configId': configGroupIdBCC,
                'configGroupId': configGroupIdBCC,
                'duration': item.duration,
                'region': item.region,
                'timeUnit': item.timeUnit,
                'count': 1, // 数量默认1
                'specs': [
                    {
                        'name': 'cpu',
                        'value': '1',
                        'scale': item.cpu,
                    },
                    {
                        'name': 'memory',
                        'value': '1g',
                        'scale': item.memory,
                    },
                    {
                        'name': 'spec',
                        'value': item.spec,
                        'scale': 1,
                    },
                    {
                        'name': 'imageId',
                        'value': defaultIndexBccJingxiang.imageId, // 镜像默认用 Ubuntu 22.04 LTS amd64 (64bit)
                        'scale': 1,
                    },
                    {
                        'name': 'imageType',
                        'value': 'common',
                        'scale': 1,
                    },
                    {
                        'name': 'osType',
                        'value': defaultIndexBccJingxiang.osType, // Ubuntu 22.04 LTS amd64 (64bit) 的操作系统
                        'scale': 1,
                    },
                    {
                        'name': 'osVersion',
                        'value': defaultIndexBccJingxiang.osVersion,
                        'scale': 1,
                    },
                ],
            }, {
                'serviceType': 'EIP',
                'parentServiceType': 'BCC',
                'configId': configIdEIP,
                'configGroupId': configGroupIdBCC,
                'duration': item.duration,
                'region': item.region,
                'timeUnit': item.timeUnit,
                'count': 1,
                'specs': [
                    {'name': 'bandwidth', 'value': item.bandwidth, 'scale': 1},
                ],
            }, {
                'serviceType': 'CDS',
                'parentServiceType': 'BCC',
                'configId': configIdCDS,
                'configGroupId': configGroupIdBCC,
                'duration': item.duration,
                'region': item.region,
                'timeUnit': item.timeUnit,
                'count': 1,
                'specs': [
                    {'name': 'size', 'value': item.size, 'scale': 1},
                ],
            }].forEach(i => params.push(i));
        }
        if (item.serviceType === 'BOS') {
            const isStandardStorage = item.displayName === DisplayNameBosEumn['BOS(标准存储包)'];
            const capacityValue = isStandardStorage ? item.capacity : `${parseInt(item.capacity, 10) * item.duration}${item.capacity.slice(-1)}`;
            params.push({
                serviceType: 'BOS',
                parentServiceType: 'BOS',
                displayName: item.displayName,
                configId: configIdBosMap[item.displayName],
                configGroupId: configIdBosMap[item.displayName],
                region: item.region,
                duration: item.duration,
                count: 1,
                timeUnit: 'MONTH',
                specs: [
                    {'name': 'capacity', 'value': capacityValue, 'scale': 1},
                ],
                resourceActiveTime: moment(new Date()).format(),
            });
        }
        if (item.serviceType === 'CDN') {
            params.push({
                serviceType: 'CDN',
                parentServiceType: 'CDN',
                configId: 'b6e00d6dfcd61547880884b10e146f26',
                configGroupId: 'b6e00d6dfcd61547880884b10e146f26',
                region: 'global',
                timeUnit: 'MONTH',
                displayName: item.displayName,
                duration: item.duration,
                specs: [
                    {name: 'capacity', value: item.capacity, scale: 1},
                ],
                capacity: item.capacity,
                count: 1,
            });
        }
        if (item.serviceType === 'LSS') {
            params.push({
                serviceType: 'LSS',
                parentServiceType: 'LSS',
                configId: 'aca4e2b3a70be41e0b4ba65c2359684c',
                configGroupId: 'aca4e2b3a70be41e0b4ba65c2359684c',
                region: 'global',
                timeUnit: 'MONTH',
                displayName: 'LSS（音视频直播）',
                duration: item.duration,
                specs: [
                    {
                        name: 'subServiceType',
                        value: 'default',
                        scale: 1,
                    },
                    {
                        name: 'capacity',
                        value: item.capacity,
                        scale: 1,
                    },
                    {
                        name: 'deductPolicy',
                        value: 'UpDownStream_Outbytes',
                        scale: 1,
                    },
                ],
            });
        }
        if (item.serviceType === 'TMS') {
            const configId = item.personRole === DisplayNameTmsEumn['专家辅助注册']
                ? '35a5d4d915da3e1e33557fe6b5340de5'
                : 'bcd75cda68823f61143b4a0cde8f89fb';
            const specs = item.personRole === DisplayNameTmsEumn['专家辅助注册']
                ? [{
                    name: 'tmsServiceType',
                    value: 'AUXILIARY_REGISTRATION',
                    scale: 1,
                }, {
                    name: 'AUXILIARY_REGISTRATION',
                    value: '1',
                    scale: 1,
                }, {
                    name: 'subServiceType',
                    value: 'trademark registration',
                    scale: 1,
                }]
                : [{
                    name: 'subServiceType',
                    value: 'trademark registration',
                    scale: 1,
                }, {
                    name: 'GUARANTEE_REGISTRATION',
                    value: '1',
                    scale: 1,
                }, {
                    name: 'tmsServiceType',
                    value: 'GUARANTEE_REGISTRATION',
                    scale: 1,
                }];
            params.push({
                serviceType: 'TMS',
                parentServiceType: 'TMS',
                displayName: '商标注册',
                configId,
                configGroupId: configId,
                timeUnit: 'DAY',
                region: 'global',
                duration: 1,
                specs,
            });
        }
        if (item.serviceType === 'AIPAGE') {
            const version: string = item.displayName + item.capacity;
            params.push({
                serviceType: 'AIPAGE',
                parentServiceType: 'AIPAGE',
                configId: 'e8166c2537986313d81058458fb21e2c',
                configGroupId: 'e8166c2537986313d81058458fb21e2c',
                region: 'global',
                timeUnit: 'YEAR',
                duration: item.duration,
                specs: [
                    {name: versionAipageMap[version], value: 1, scale: 1},
                ],
            });
        }
        if (item.serviceType === 'CBS') {
            params.push(item.displayName === '代理记账' ? {
                serviceType: 'CBS',
                parentServiceType: 'CBS',
                displayName: '代理记账',
                configId: '910e24d2c3030317d7a0d597d3196073',
                configGroupId: '910e24d2c3030317d7a0d597d3196073',
                region: 'global',
                timeUnit: 'YEAR',
                duration: item.duration,
                count: 1,
                specs: [{
                    name: `${defaultIndexCbsCity.value}_bkprys`,
                    value: `${item.personRole}-${item.couponType}`,
                    scale: 1,
                }, {
                    name: 'subServiceType',
                    value: 'bookkeeping',
                    scale: 1,
                }, {
                    name: 'area',
                    value: defaultIndexCbsCity.code,
                    scale: null,
                }, {
                    name: 'productType',
                    value: 'BKP',
                    scale: null,
                }, {
                    name: 'taxpayerType',
                    value: item.personRole,
                    scale: null,
                }, {
                    name: 'invoiceAmount',
                    value: item.couponType,
                    scale: null,
                }],
            } : {
                serviceType: 'CBS',
                parentServiceType: 'CBS',
                displayName: '公司注册',
                configId: '8dc33f37bf5e0f3f8c51605f070146e5',
                configGroupId: '8dc33f37bf5e0f3f8c51605f070146e5',
                region: 'global',
                timeUnit: 'DAY',
                duration: item.duration,
                count: 1,
                specs: [{
                    name: 'comType',
                    value: item.comType,
                    scale: null,
                }, {
                    name: 'subServiceType',
                    value: item.subServiceType,
                    scale: 1,
                }, {
                    name: 'taxpayerType',
                    value: item.personRole,
                    scale: null,
                }, {
                    name: 'area',
                    value: defaultIndexCbsCity.code,
                    scale: null,
                }, {
                    name: defaultIndexCbsCity.value,
                    value: `${item.comType === ComTypeCbsEumn['集团公司'] ? item.comType : `${item.comType}-${item.personRole}`}`,
                    scale: 1,
                }, {
                    name: 'productType',
                    value: 'CRS',
                    scale: null,
                }],
            });
        }
        if (item.serviceType === 'NAT') {
            params.push({
                serviceType: 'NAT',
                parentServiceType: 'NAT',
                configId: '5466bfd3242ed39c485caa4715c65d4b',
                configGroupId: '5466bfd3242ed39c485caa4715c65d4b',
                timeUnit: 'MONTH',
                region: item.region,
                duration: item.duration,
                count: 1,
                specs: [
                    {name: 'nat_gateway_scale', value: item.displayName, scale: 1},
                ],
            });
        }
        if (item.serviceType === 'EIP') {
            const configId = item.displayName === displayNameEipEumn['标准型BGP']
                ? '77c5fe42db9c6c57fde0e434c5e09f4e'
                : 'f0529e9a26a45be2e31f40902da38d1a';
            const configGroupId = item.displayName === displayNameEipEumn['标准型BGP']
                ? '77c5fe42db9c6c57fde0e434c5e09f4e'
                : 'f0529e9a26a45be2e31f40902da38d1a';
            const specs = item.displayName === displayNameEipEumn['标准型BGP']
                ? [{
                    name: 'bandwidth',
                    value: '1m',
                    scale: 1,
                }]
                : [{
                    name: 'bandwidth',
                    value: '100m',
                    scale: 1,
                }, {
                    name: 'subServiceType',
                    value: 'BGP_S',
                    scale: 1,
                }];
            params.push({
                serviceType: 'EIP',
                parentServiceType: 'EIP',
                configId,
                configGroupId,
                region: item.region,
                timeUnit: 'MONTH',
                duration: item.duration,
                specs,
                count: 1,
            });
        }
    });
    params.forEach(item => {
        item.signAuth = genGroupBuySignAuth(item);
    });
    return params;
};
