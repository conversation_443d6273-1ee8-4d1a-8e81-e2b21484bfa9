/**
 * @file 解决方案联系我们
 * <AUTHOR>
 */

import {isMobile} from '@baidu/bce-helper';
import {BOS_CDN_ORIGIN_URL} from '@common/constant/variableConst';
import {useOnMount} from '@baidu/bce-hooks';
import {useState} from 'react';

import styles from './main.module.less';

export function ProAndSolutionContact() {
    const [showVideo, setShowVideo] = useState(false);
    useOnMount(() => {
        !isMobile() && setShowVideo(true);
    });
    return (
        <div className={styles.contact}>
            <div className={styles['video-wrapper']}>
                <div className={styles.bg}></div>
                {
                    showVideo ? <video
                        autoPlay
                        loop
                        muted
                        src={`${BOS_CDN_ORIGIN_URL}/portal-cloud-server/video/contact_white.mp4`}
                    /> : null
                }
            </div>
            <div className={`container ${styles.content}`}>
                <h3>立即联系您的专属顾问</h3>
                <p>免费咨询百度智能云专属顾问，为您量身定制产品推荐方案</p>
                <a
                    href="https://cloud.baidu.com/survey/connect-us.html"
                    target="_blank"
                    data-track-category="页面最下方联系我们"
                    data-track-name="立即联系您的专属顾问"
                    data-track-value="立即咨询"
                >
                    立即咨询
                </a>
            </div>
        </div>
    );
}
