@import '../../../../styles/variables.less';

.contact {
    position: relative;
    width: 100%;
    height: 374px;
    box-sizing: border-box;
    padding-top: 40px;
    overflow: hidden;
    
    .content {
        position: relative;
        z-index: 1;
    }
    h3 {
        padding-top: 87px;
        font-family: PingFangSC-Medium;
        font-size: 28px;
        color: #FFFFFF;
        line-height: 40px;
        font-weight: 500;
    }
    p {
        margin-top: 16px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 24px;
    }
    a {
        display: block;
        width: 136px;
        height: 40px;
        margin-top: 40px;
        background: #FFFFFF;
        border-radius: 4px;
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: @CB;
        text-align: center;
        line-height: 40px;
        font-weight: 500;
    }
    .video-wrapper {
        position: absolute;
        z-index: 0;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        .bg {
            position: relative;
            z-index: 1;
            width: 100%;
            height: 100%;
            background: url(https://bce.bdstatic.com/p3m/common-service/uploads/img_contact_us_b9482fc.png) no-repeat center;
            background-size: auto 100%;
        }

        video {
            position: absolute;
            z-index: 1;
            height: 100%;
            width: auto;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            top: 0;
        }
        &::after {
            content: '';
            position: absolute;
            width: 100%;
            left: 0;
            top: 39px;
            bottom: 0;
            background-color: #2c73f3;
        }
    }
}