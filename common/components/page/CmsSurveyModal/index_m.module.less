@import '@styles/variables.less';
@import '@styles/mixin.less';

.dialog-form-wap {
    --max-height: calc(100vh - 21.1rem);
    position: relative;
    width: 100%;
    max-height: var(--max-height);

    .dialog-header {
        position: relative;
        width: 100%;
        height: 4.8rem;
        padding: 1.25rem 0;
        text-align: center;
        box-sizing: border-box;

        .dialog-title {
            font-family: PingFangSC-Medium;
            font-size: 1.5rem;
            color: #151b26;
            text-align: center;
            line-height: 2.3rem;
            font-weight: 500;
        }
        .close {
            position: absolute;
            right: 1rem;
            top: 1.2rem;
            width: 2.4rem;
            height: 2.4rem;
            background: url(https://bce.bdstatic.com/p3m/common-service/uploads/icon-close_77cc15a.png) no-repeat 50%/cover;
            background-size: 2.4rem 2.4rem;
        }
    }

    .dialog-body {
        position: relative;
        padding-top: 1.2rem;
        padding-right: 0.4rem;
        display: flex;
        flex-direction: column;
        max-height: calc(var(--max-height) - 4.8rem);
        box-sizing: border-box;
        .form-container {
            width: 100%;
            flex: 1;
            overflow-y: scroll;
            overflow-x: hidden;
            padding: 0 0.45rem 0 1.25rem;
            margin: 0 auto;
            box-sizing: border-box;
            border-bottom: none;

            scrollbar-width: thin;
            scrollbar-color: #CECFD1 @CW; // firefox第一个滚轮颜色，第二个滚动条背景色

            &::-webkit-scrollbar {
                width: 0.4rem;
            }

            &::-webkit-scrollbar-thumb {
                background-color: #D8D8D8;
                border-radius: 0.2rem;
            }

            :global {
                .ant-form-item {
                    margin-bottom: 2rem;
                    &.ant-form-item-has-error {
                        margin-bottom: 1.6rem;
                    }
                    &:last-child {
                        margin-bottom: 0;
                    }
                }
                // 消除antd自带的间距影响
                .ant-form-item-margin-offset {
                    display: none;
                }
                // 标题
                .ant-form-item-label {
                    flex: 1;
                    text-align: left;
                    padding: 0 0 0.8rem;
                    label {
                        display: block;
                        position: relative;
                        min-height: 2.4rem;
                        font-family: PingFangSC-Regular;
                        font-size: 1.5rem;
                        color: @C9;
                        line-height: 2.4rem;
                        font-weight: 400;
                        text-align: left;
                    }
                }

                .ant-form-item-control {
                    .ant-input {
                        width: 100%;
                        height: 4.4rem;
                        background: @CW;
                        border: 0.1rem solid rgba(@C0, .12);
                        border-radius: 0.2rem;

                        box-sizing: border-box;
                        padding: 1.15rem 1.2rem;
                        font-family: PingFangSC-Regular;
                        font-size: 1.3rem;
                        color: @C0;
                        line-height: 2.1rem;
                        font-weight: 400;
                        outline: none;
                        caret-color: #2468f2;

                        &::placeholder {
                            font-family: PingFangSC-Regular;
                            font-size: 14px;
                            color: rgba(34,34,34,0.40);
                            line-height: 24px;
                            font-weight: 400;
                        }
                        &:hover, &:focus {
                            box-shadow: none !important;
                            border-color: @CB;
                        }
                        &.ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless) {
                            border-color: @CR;
                            &:hover {
                                border-color: @CR;
                            }
                            // 报错时focus清除报错信息 不行
                        }
                    }

                    .ant-radio-group {
                        color: @C0;
                        .ant-radio-wrapper {
                            display: inline-flex;
                            min-height: 36px;
                            align-items: center;
                            margin-right: 16px;
                            font-family: PingFangSC-Regular;
                            font-size: 14px;
                            color: @C0;
                            line-height: 24px;
                            font-weight: 400;
                            .ant-radio {
                                top: 0;
                                &:hover {
                                    .ant-radio-inner {
                                        border-color: @CB;
                                    }
                                }
                                .ant-radio-inner {
                                    box-shadow: none;
                                    transition: none;
                                    &::after {
                                        border-color: @CB;
                                        background-color: @CB;
                                    }
                                    &:hover {
                                        border-color: @CB;
                                    }
                                }
                                &.ant-radio-checked{
                                    &::after {
                                        border-color: @CB;
                                        animation: none;
                                    }
                                    .ant-radio-inner {
                                        border-color: @CB;
                                    }
                                }
                            }
                        }
                    }

                    .ant-select {
                        font-family: PingFangSC-Regular;
                        font-size: 13px;
                        .ant-select-selector {
                            box-shadow: none;
                            border: 1px solid rgba(@C0, 0.16);
                            border-radius: 2px;
                            height: 44px;
                            padding: 0 12px;
                            input {
                                height: 100%;
                            }
                        }
                        .ant-select-selection-search {
                            left: 12px;
                        }
                        .ant-select-selection-placeholder, .ant-select-selection-item {
                            line-height: 42px;
                        }
                    }

                    .ant-form-item-extra,
                    .ant-form-item-explain {
                        margin-top: 0.4rem;
                        font-family: PingFangSC-Regular;
                        font-size: 1.4rem;
                        letter-spacing: 0;
                        line-height: 2.2rem;
                        font-weight: 400;
                        color: @C4;
                        transition: none;
                    }

                    .ant-form-item-explain-error {
                        color: @CR;
                    }
                }

                .summit-dropdown-pop {
                    padding: 4px 0;
                    .ant-select-item {
                        position: relative;
                        font-family: PingFangSC-Regular;
                        font-size: 13px;
                        color: @C0;
                        line-height: 44px;
                        font-weight: 400;
                        padding: 0 12px;
                        &::after {
                            content: '';
                            position: absolute;
                            right: 12px;
                            top: 50%;
                            transform: translateY(-50%);
                            width: 16px;
                            height: 16px;
                            border-radius: 50%;
                            border: 1px solid rgba(@C0, 0.08);
                        }
                    }
                    .ant-select-item-option-selected {
                        background-color: @CW;
                        &::after {
                            background: url(https://bce.bdstatic.com/p3m/common-service/uploads/radio_8459532.png) no-repeat center / cover;
                            border: none;
                        }
                    }
                }
            }
        }
        .form-submit {
            width: 100%;
            box-sizing: border-box;
            background-color: @CW;
            padding: 1.6rem 0.85rem calc(1.6rem + env(safe-area-inset-bottom)) 1.25rem;

            .submit-btn {
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 4.4rem;
                margin: 0;
                padding: 1.1rem 0;
                display: block;
                border-radius: 0.4rem;
                background: #2468F2;

                font-family: PingFangSC-Medium;
                font-size: 1.5rem;
                text-align: center;
                line-height: 2.3rem;
                font-weight: 500;
                letter-spacing: 0;
                color: @CW;
            }
        }
        .dialog-result {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: @CW;
            padding: 0 2.4rem 8.5rem;
            box-sizing: border-box;

            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .dialog-result-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .dialog-result-img {
                    width: 7.4rem;
                    height: 7.4rem;
                    background: no-repeat center/cover;
                    background-size: 100% auto;
                }
                h3 {
                    margin-top: 2.2rem;
                    font-family: PingFangSC-Medium;
                    font-size: 1.5rem;
                    color: #151B26;
                    text-align: center;
                    line-height: 2.3rem;
                    font-weight: 500;
                }
                .sub-title {
                    margin-top: 1rem;
                    font-family: PingFangSC-Regular;
                    font-size: 1.3rem;
                    color: rgba(21, 27, 38, .9);
                    text-align: center;
                    line-height: 2.2rem;
                    font-weight: 400;
                }
            }
            .count-down {
                text-align: center;

                .count-down-text {
                    display: none;
                    min-height: 2rem;
                    font-family: PingFangSC-Regular;
                    font-size: 1.4rem;
                    color: @C7;
                    text-align: center;
                    line-height: 2rem;
                    font-weight: 400;

                    &.show {
                        display: inline;
                    }

                    a {
                        display: inline-flex;
                        align-items: center;
                        color: @CB;

                        .jump-icon {
                            width: 1rem;
                            height: 1rem;
                            margin-left: .4rem;
                            transition: transform 0.3s ease-out;

                            > div {
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                width: 100%;
                                height: 100%;
                                
                                svg {
                                    width: 100%;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}