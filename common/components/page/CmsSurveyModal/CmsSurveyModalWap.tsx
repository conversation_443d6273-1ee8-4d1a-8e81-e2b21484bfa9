import {Popup} from 'antd-mobile';
import {Form} from 'antd';
import {ReactSVG} from 'react-svg';
import cn from 'classnames';
import {SummitFormDataObj, SummitFormDataType, SummitResultDataObj} from '@common/interface/summit';
import {useState, useEffect, MouseEventHandler, useCallback} from 'react';
import {useCmsSurveyApply} from '@common/hooks/page/useCmsSurvey';
import {CmsSurveyForm} from '@components/cms/CmsSurveyForm/CmsSurveyForm';
import {CmsSurveyPageDataObj} from '@common/interface/cmsSurvey';
import {useSurveyQualificationFb} from '@common/hooks/page/useSurveyQualificationFb';
import {SUMMIT_RESULT_ICON} from '@common/constant/variableConst';
import {findParentNode, sendMonitor} from '@common/helper/page';
import {debounce} from 'lodash';
import {useCountDownClose} from './CmsSurveyModal';
import style from './index_m.module.less';

export const CmsSurveyModalWap = ({data, formData, visible, trackCategory, toggleFormVisible, submitDataHandler, successDialogPrompt}: {
    data?: CmsSurveyPageDataObj;
    formData: SummitFormDataObj;
    visible?: boolean;
    trackCategory?: string;
    toggleFormVisible: (show?: boolean) => void;
    submitDataHandler?: (formData: SummitFormDataType) => SummitFormDataType; // 提交表单前自定义数据处理逻辑
    successDialogPrompt?: (formData: SummitFormDataType) => SummitResultDataObj;
}) => {
    const [form] = Form.useForm();
    const [successDialog, setSuccessDialog] = useState<boolean>(false); // 展示成功返回
    const {submit} = useCmsSurveyApply({pageData: data});
    const {timeRemaining} = useCountDownClose({
        open: successDialog,
        dialogVisible: visible,
        duration: 5,
        onClose: () => {
            toggleFormVisible(false);
        },
    });
    const {resultInfo, setResultInfo, qualificationFun} = useSurveyQualificationFb();
    const showResult = successDialog && resultInfo?.type;

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const submitFn = useCallback(debounce(() => {
        form.validateFields().then((formData: {[props: string]: string | string[]}) => {
            if (submitDataHandler) {
                formData = submitDataHandler(formData); // 提交表单前自定义数据处理逻辑
            }
            submit({formData}).then(res => {
                // 打开成功弹窗，根据内容自定义提示语
                qualificationFun({
                    response: res,
                    successFun: () => {
                        if (successDialogPrompt) {
                            const {type = 'success', title = '感谢您对本次论坛的关注！', desc = ''} = successDialogPrompt(formData);
                            setResultInfo({type, title, desc});
                            return;
                        }
                        setResultInfo({
                            type: 'success',
                            title: '感谢您对本次论坛的关注！',
                            desc: '活动开始前，我们会以短信的形式提醒您收看线上直播',
                        });
                    },
                });
                setSuccessDialog(true);
                sendMonitor({
                    category: trackCategory || '表单弹窗-wap',
                    name: '表单弹窗-提交结果返回',
                    value: res?.result?.message || res?.result?.code?.toString() || '正常返回',
                });
            }).catch(() => {
                setResultInfo({
                    type: 'error',
                    title: '网络问题',
                    desc: '网络问题，请稍后重试！',
                });
                sendMonitor({
                    category: trackCategory || '表单弹窗-wap',
                    name: '表单弹窗-提交结果返回',
                    value: 'error-网络问题',
                });
            });
        }).catch(e => {
            const firstErrorId = e.errorFields[0].name[0];
            setTimeout(() => document.getElementById(firstErrorId)?.scrollIntoView(), 20);
        });
    }, 300), []);

    const handleFormClick: MouseEventHandler<HTMLFormElement> = e => {
        const currentItem = findParentNode(e.target as HTMLElement, 'ant-form-item');
        currentItem && e.currentTarget.scrollTo({top: currentItem.offsetTop - 20, behavior: 'smooth'});
    };

    useEffect(() => {
        // 关闭弹窗时，如果 form 有 error 状态, 取消这些状态
        if (!showResult && !visible) {
            form.setFields(Object.keys(form.getFieldsValue()).map(name => ({name, errors: null})));
        }
    }, [form, visible, showResult]);

    return (
        <Popup
            visible={visible}
            onMaskClick={() => toggleFormVisible(false)}
            bodyStyle={{
                borderTopLeftRadius: '1.6rem',
                borderTopRightRadius: '1.6rem',
                maxHeight: 'calc(100vh - 21.1rem)',
            }}
        >
            <div className={style['dialog-form-wap']} onClick={e => e.stopPropagation()}>
                <div className={style['dialog-header']}>
                    <h3 className={style['dialog-title']}>{formData.title}</h3>
                    <div className={style.close} onClick={() => toggleFormVisible(false)}></div>
                </div>
                <div className={style['dialog-body']}>
                    <CmsSurveyForm
                        data={data}
                        formProps={{
                            form,
                            className: style['form-container'],
                            layout: 'vertical',
                            onClick: handleFormClick,
                        }}
                        protocolText="勾选提交即表示您同意百度智能云及其授权的合作伙伴获取如上信息，并通过您填写的联系方式与您联系，为您提供大会及百度智能云信息。"
                    />
                    <div className={style['form-submit']}>
                        <div
                            className={style['submit-btn']}
                            onClick={submitFn}
                            data-track-action="click"
                            data-track-category={trackCategory || '表单弹窗-wap'}
                            data-track-name="表单弹窗-提交"
                            data-track-value={formData.btnText}
                        >{formData.btnText}
                        </div>
                    </div>
                    {showResult && (
                        <div className={style['dialog-result']}>
                            <div className={style['dialog-result-content']}>
                                <div
                                    className={style['dialog-result-img']}
                                    style={{backgroundImage: `url(${SUMMIT_RESULT_ICON[resultInfo.type]})`}}
                                >
                                </div>
                                <h3>{resultInfo.title}</h3>
                                <span className={style['sub-title']}>{resultInfo.desc}</span>
                            </div>
                            <div className={style['count-down']}>
                                {resultInfo.jumpUrl ? (
                                    <span className={cn(style['count-down-text'], timeRemaining > 0 && style.show)}>
                                        {timeRemaining}s后将为您跳转到指定页面，
                                        <a
                                            id="result-jump-link"
                                            href={resultInfo.jumpUrl}
                                            target={resultInfo.jumpUrl || '_self'}
                                        >
                                            {resultInfo.btnText || '立即跳转'}
                                            <ReactSVG
                                                className={style['jump-icon']}
                                                src="https://bce.bdstatic.com/p3m/common-service/uploads/arrow_right_blue_d0dd0df.svg"
                                            />
                                        </a>
                                    </span>
                                ) : (
                                    <span className={cn(style['count-down-text'], timeRemaining > 0 && style.show)}>
                                        {timeRemaining}s后本弹窗将自动关闭
                                    </span>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </Popup>
    );
};
