import {Button, Form} from 'antd';
import {ReactSVG} from 'react-svg';
import cn from 'classnames';
import {SummitFormDataObj, SummitFormDataType, SummitResultDataObj} from '@common/interface/summit';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {useCallback, useEffect, useRef, useState} from 'react';
import {UModalMask} from '@common/components/UModalMask/UModalMask';
import {CmsSurveyForm} from '@components/cms/CmsSurveyForm/CmsSurveyForm';
import {useCmsSurveyApply} from '@common/hooks/page/useCmsSurvey';
import {CmsSurveyPageDataObj} from '@common/interface/cmsSurvey';
import {useSurveyQualificationFb} from '@common/hooks/page/useSurveyQualificationFb';
import {SUMMIT_RESULT_ICON} from '@common/constant/variableConst';
import {sendMonitor} from '@common/helper/page';
import {debounce} from 'lodash';
import style from './index.module.less';

interface CountDownCloseOptions {
    open: boolean;
    duration: number;
    dialogVisible: boolean;
    onClose: () => void;
}

export const useCountDownClose = (options: CountDownCloseOptions) => {
    const {open, duration, onClose, dialogVisible} = options;
    const [timeRemaining, setTimeRemaining, timeRemainingRef] = useStateRef(duration);
    const timer = useRef(null);

    // 还在倒计时的时候弹窗被关闭, 停止倒计时
    const lastDialogVisible = useRef(dialogVisible);
    if (timer.current !== null && lastDialogVisible.current && !dialogVisible && timeRemaining) {
        clearInterval(timer.current);
        setTimeout(() => setTimeRemaining(0), 0);
    }
    lastDialogVisible.current = dialogVisible;

    useEffect(() => {
        if (open && timer.current === null) {
            timer.current = setInterval(() => {
                const remain = timeRemainingRef.current - 1;
                setTimeRemaining(remain);
                if (remain <= 0) {
                    document.getElementById('result-jump-link')?.click();
                    onClose();
                    clearInterval(timer.current);
                }
            }, 1000);
        }
    }, [setTimeRemaining, timeRemainingRef, onClose, open]);

    return {timeRemaining};
};

export const CmsSurveyModal = ({data, formData, visible, trackCategory, toggleFormVisible, submitDataHandler, successDialogPrompt}: {
    data: CmsSurveyPageDataObj; // 从cms侧获取的表单数据
    formData: SummitFormDataObj;
    visible?: boolean;
    trackCategory?: string;
    toggleFormVisible: (show?: boolean) => void;
    submitDataHandler?: (formData: SummitFormDataType) => SummitFormDataType;
    successDialogPrompt?: (formData: SummitFormDataType) => SummitResultDataObj;
}) => {
    const [form] = Form.useForm();
    const [successDialog, setSuccessDialog] = useState<boolean>(false); // 展示成功返回
    const {submit} = useCmsSurveyApply({pageData: data});
    const {timeRemaining} = useCountDownClose({
        open: successDialog,
        dialogVisible: visible,
        duration: 5,
        onClose: () => toggleFormVisible(false),
    });
    const {resultInfo, setResultInfo, qualificationFun} = useSurveyQualificationFb();
    const showResult = successDialog && resultInfo?.type;
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const submitFn = useCallback(debounce(() => {
        form.validateFields().then((formData: SummitFormDataType) => {
            if (submitDataHandler) {
                formData = submitDataHandler(formData); // 提交表单前自定义数据处理逻辑
            }
            submit({formData}).then(res => {
                // 打开成功弹窗，根据内容自定义提示语
                qualificationFun({
                    response: res,
                    successFun: () => {
                        if (successDialogPrompt) {
                            const {type = 'success', title = '感谢您对本次论坛的关注！', desc = ''} = successDialogPrompt(formData);
                            setResultInfo({type, title, desc});
                            return;
                        }
                        setResultInfo({
                            type: 'success',
                            title: '感谢您对本次论坛的关注！',
                            desc: '活动开始前，我们会以短信的形式提醒您收看线上直播',
                        });
                    },
                });
                setSuccessDialog(true);
                sendMonitor({
                    category: trackCategory || '表单弹窗',
                    name: '表单弹窗-提交结果返回',
                    value: res?.result?.message || res?.result?.code?.toString() || '正常返回',
                });
            }).catch(() => {
                setResultInfo({
                    type: 'error',
                    title: '网络问题',
                    desc: '网络问题，请稍后重试！',
                });
                sendMonitor({
                    category: trackCategory || '表单弹窗',
                    name: '表单弹窗-提交结果返回',
                    value: 'error-网络问题',
                });
            });
        }).catch(e => {
            // Form 的 scrollToFirstError 不生效 (可能跟这个表单嵌在 modal 里有关), 这里手动 scroll 一下
            const firstErrorId = e.errorFields[0].name[0];
            // 加一个小延迟, 为了让最底部的 error msg 渲染出来之后再 scroll, 否则滚动距离不太够
            setTimeout(() => document.getElementById(firstErrorId)?.scrollIntoView(), 20);
        });
    }, 300), [form, qualificationFun, setResultInfo, submit, trackCategory]);

    useEffect(() => {
        // 关闭弹窗时，如果 form 有 error 状态, 取消这些状态
        if (!showResult && !visible) {
            form.setFields(Object.keys(form.getFieldsValue()).map(name => ({name, errors: null})));
        }
    }, [form, visible, showResult]);

    useOnMount(() => {
        (window as any).form = form;
    });

    return (
        <UModalMask
            keepModule
            className={style['dialog-wrapper']}
            show={visible}
            onClose={() => toggleFormVisible(false)}
        >
            <div
                className={cn(style['dialog-form'], {[style['dialog-form-result']]: showResult})}
                style={formData?.formBg ? {backgroundImage: `url(${formData.formBg})`} : null}
                onClick={e => e.stopPropagation()}
            >
                <div className={style['dialog-header']}>
                    <h3 className={style['dialog-title']}>{formData.title}</h3>
                    <div
                        className={style.close}
                        onClick={() => toggleFormVisible(false)}
                    >
                    </div>
                </div>
                <div className={style['dialog-body']}>
                    {/* 成功提示 或 报名表单 */}
                    <div className={cn(style['dialog-body-form'], {[style.show]: !showResult})}>
                        <CmsSurveyForm
                            data={data}
                            formProps={{
                                form,
                                className: style['form-container'],
                                layout: 'horizontal',
                                labelCol: {span: 8},
                                labelAlign: 'left',
                            }}
                            protocolText="勾选提交即表示您同意百度智能云及其授权的合作伙伴获取如上信息，并通过您填写的联系方式与您联系，为您提供大会及百度智能云信息。"
                        />
                        <div className={style['form-submit']}>
                            <Button
                                className={style['submit-btn']}
                                onClick={submitFn}
                                data-track-action="click"
                                data-track-category={trackCategory || '表单弹窗'}
                                data-track-name="表单弹窗-提交"
                                data-track-value={formData.btnText}
                            >{formData.btnText}
                            </Button>
                        </div>
                    </div>
                    {showResult && (
                        <div className={style['dialog-result']}>
                            <div className={style['dialog-result-content']}>
                                <div
                                    className={style['dialog-result-img']}
                                    style={{backgroundImage: `url(${SUMMIT_RESULT_ICON[resultInfo.type]})`}}
                                >
                                </div>
                                <h3>{resultInfo.title}</h3>
                                <span className={style['sub-title']}>{resultInfo.desc}</span>
                            </div>
                            <div className={style['count-down']}>
                                {resultInfo.jumpUrl ? (
                                    <span className={cn(style['count-down-text'], timeRemaining > 0 && style.show)}>
                                        {timeRemaining}s后将为您跳转到指定页面，
                                        <a
                                            id="result-jump-link"
                                            href={resultInfo.jumpUrl}
                                            target={resultInfo.jumpUrl || '_self'}
                                        >
                                            {resultInfo.btnText || '立即跳转'}
                                            <ReactSVG
                                                className={style['jump-icon']}
                                                src="https://bce.bdstatic.com/p3m/common-service/uploads/arrow_right_blue_d0dd0df.svg"
                                            />
                                        </a>
                                    </span>
                                ) : (
                                    <span className={cn(style['count-down-text'], timeRemaining > 0 && style.show)}>
                                        {timeRemaining}s后本弹窗将自动关闭
                                    </span>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </UModalMask>
    );
};
