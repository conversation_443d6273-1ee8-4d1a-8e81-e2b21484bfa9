@import '@styles/variables.less';
@import '@styles/mixin.less';
@error-color: #F33E3E;

.dialog-wrapper {
    cursor: default;
    transition: opacity 0.3s ease-out, z-index 0.3s ease-out;

    .dialog-form {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 520px;
        background: #FFFFFF url('https://bce.bdstatic.com/p3m/common-service/uploads/dialog-bg_0a44810.png') no-repeat top;
        background-size: 100% auto;
        border-radius: 6px;
        padding: 24px 8px 24px 24px;
        box-sizing: border-box;

        &.dialog-form-result {
            height: 378px;

            .dialog-body {
                position: absolute;
                left: 0;
                right: 0;
                top: 120px;
            }
        }

        .dialog-header {
            position: relative;
            width: 100%;
            height: 28px;
            .dialog-title {
                font-family: PingFangSC-Medium;
                font-size: 18px;
                color: #151B26;
                line-height: 28px;
                font-weight: 600;
            }
            .close {
                cursor: pointer;
                position: absolute;
                top: 2px;
                right: 16px;
                width: 24px;
                height: 24px;
                background: url(https://bce.bdstatic.com/p3m/common-service/uploads/icon_close_5f14e89.png) no-repeat 50%/cover;
                background-position: -29px 0;
            }
        }

        .dialog-body {
            position: relative;
            display: flex;
            flex-direction: column;

            .dialog-body-form {
                display: none;
                margin-top: 24px;

                &.show {
                    display: block;
                }

                .form-container {
                    width: 100%;
                    max-height: calc(80vh - 160px);
                    flex: 1;
                    overflow-y: scroll;
                    box-sizing: border-box;
                    padding: 0 8px 0 0;
                    margin: 0 auto 20px;
                    border-bottom: none;

                    scrollbar-width: thin;
                    scrollbar-color: #CECFD1 #FFFFFF; // firefox第一个滚轮颜色，第二个滚动条背景色
    
                    &::-webkit-scrollbar {
                        width: 8px;
                    }
    
                    &::-webkit-scrollbar-thumb {
                        background-color: #CECFD1;
                        border-radius: 4px;
                    }
    
                    :global {
                        .ant-form-item {
                            margin-bottom: 20px;
                            &:last-child {
                                margin-bottom: 0;
                            }
                            &.ant-form-item-has-error {
                                margin-bottom: 8px;

                                &:has(.ant-radio-group) .ant-form-item-row {
                                    align-items: flex-start;
                                }
                            }
                        }
                        // 消除antd自带的间距影响
                        .ant-form-item-margin-offset {
                            display: none;
                        }
                        .ant-form-item-label {
                            flex: 1;
                            text-align: left;
                            max-width: 148px;
                            margin-right: 8px;
                            label {
                                height: 100%;
                                max-height: 36px;
                                font-family: PingFangSC-Regular;
                                font-size: 14px;
                                color: #222222;
                                line-height: 24px;
                                font-weight: 400;
                                text-align: left;
                            }
                        }
                        .ant-form-item-control {
                            .ant-input {
                                box-sizing: border-box;
                                margin: 0;
                                font-variant: tabular-nums;
                                list-style: none;
                                font-feature-settings: 'tnum';
                                position: relative;
                                display: inline-block;
                                min-width: 0;
                                transition: all 0.3s;
                    
                                width: 100%;
                                background: #fff;
                                border: 1px solid rgba(34,34,34,.16);
                                border-radius: 4px;
                                
                                font-family: PingFangSC-Regular;
                                font-size: 14px;
                                color: @C0;
                                line-height: 24px;
                                font-weight: 400;
    
                                outline: none;
                                appearance: none;
                                -moz-appearance: none;
                                -webkit-appearance: none;
                                padding: 6px 8px;
                                height: 36px;
                                line-height: 34px;
                                box-sizing: border-box;
                                text-overflow: ellipsis;
                    
                                &::placeholder {
                                    font-family: PingFangSC-Regular;
                                    font-size: 14px;
                                    color: rgba(34,34,34,0.40);
                                    line-height: 24px;
                                    font-weight: 400;
                                }
                                &:hover, &:focus {
                                    box-shadow: none !important;
                                    border-color: @CB;
                                }
                                &.ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless) {
                                    border-color: @error-color;
                                    &:hover {
                                        border-color: @error-color;
                                    }
                                    // 报错时focus清除报错信息 不行
                                }
                            }
    
                            .ant-select {
                                &.ant-select-focused.ant-select-open {
                                    .ant-select-selector {
                                        border-color: @CB;
                                    }
                                    .ant-select-arrow {
                                        transform: rotate(180deg);
                                    }
                                    &.ant-select-multiple .ant-select-selector .ant-select-selection-overflow::after {
                                        transform: translateY(-50%) rotate(180deg);
                                    }
                                    
                                }
                                .ant-select-selector {
                                    border-color: rgba(34,34,34,.16);
                                    border-radius: 4px;
                                    box-shadow: none;
                                    height: 36px;
                                    &:hover {
                                        border-color: @CB;
                                    }
                                    .ant-select-selection-item,
                                    .ant-select-selection-placeholder {
                                        line-height: 34px;
                                    }
                                    .ant-select-selection-placeholder {
                                        color: @C4;
                                    }
                                    .ant-select-selection-search-input {
                                        height: 100%;
                                    }
                                }
                                .ant-select-arrow {
                                    right: 8px;
                                    width: 16px;
                                    height: 16px;
                                    margin-top: -8px;
                                    transform: rotate(0);
                                    transition: transform .3s ease-out;
                                    > span {
                                        width: 16px;
                                        height: 16px;
                                        background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/arrow-down_c8dc68d.svg') center / cover;
                                        > svg {
                                            display: none;
                                        }
                                    }
                                }
                                .ant-select-dropdown {
                                    &.summit-dropdown-pop {
                                        background: #FFFFFF;
                                        box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
                                        border-radius: 4px;
    
                                        .ant-select-item {
                                            min-height: 40px;
                                            padding: 8px 12px;
    
                                            font-family: PingFangSC-Regular;
                                            font-size: 14px;
                                            color: #222222;
                                            line-height: 24px;
                                            font-weight: 400;
                                            .ellipsis();
    
                                            &.ant-select-item-option-active {
                                                background-color: rgba(@CB, .08);
                                            }
                                        }
                                        .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
                                            background-color: rgba(@CB, .08);
                                        }
                                        .ant-select-item-option-disabled {
                                            color: @C4;
                                        }
                                    }
                                    &.summit-checkbox-pop {
                                        background: #FFFFFF;
                                        box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
                                        border-radius: 4px;
                                        .ant-select-item {
                                            min-height: 40px;
                                            padding: 8px 12px;
    
                                            font-family: PingFangSC-Regular;
                                            font-size: 14px;
                                            color: #222222;
                                            line-height: 24px;
                                            font-weight: 400;
                                            .ellipsis();
    
                                            &.ant-select-item-option-active {
                                                background-color: @CW;
                                            }
                                        }
                                        .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
                                            background-color: @CW;
                                        }
                                    }
                                }
                                // 下拉框多选
                                &.ant-select-multiple {
                                    .ant-select-selector {
                                        padding: 4px;
                                        height: auto;
                                        // 多选时，内容包裹部分
                                        .ant-select-selection-overflow {
                                            position: relative;
                                            padding-right: 24px;
                                            &::after {
                                                content: '';
                                                position: absolute;
                                                top: 50%;
                                                right: 3px;
                                                width: 16px;
                                                height: 16px;
                                                transform: translateY(-50%) rotate(0deg);
                                                transition: transform 0.3s ease-out;
                                                background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/arrow-down_c8dc68d.svg') center/cover;
                                            }
                                            .ant-select-selection-item {
                                                font-family: PingFangSC-Regular;
                                                font-size: 14px;
                                                color: #222222;
                                                line-height: 24px;
                                                font-weight: 400;
                                                min-height: 26px;
    
                                                .ant-select-selection-item-remove {
                                                    display: inline-flex;
                                                    align-items: center;
                                                }
                                            }
                                        }
                                    }
                                    .ant-select-dropdown.summit-dropdown-pop {
                                        .ant-select-item-option {
                                            position: relative;
                                            font-family: PingFangSC-Regular;
                                            font-size: 14px;
                                            color: #222222;
                                            line-height: 24px;
                                            font-weight: 400;
                                            padding: 8px 12px 8px 36px;
    
                                            .ant-select-item-option-state {
                                                display: none;
                                            }
                                            &::before {
                                                content: '';
                                                position: absolute;
                                                left: 12px;
                                                top: 12px;
                                                width: 16px;
                                                height: 16px;
                                                border: 1px solid rgba(34,34,34,0.16);
                                                border-radius: 2px;
                                                background: no-repeat #FFFFFF center/cover;
                                            }
                                            &:hover {
                                                border-color: @CB;
                                                background: rgba(36, 104, 242, .08);
                                            }
                                            &.ant-select-item-option-selected {
                                                &::before {
                                                    border: none;
                                                    background-image: url(https://bce.bdstatic.com/p3m/common-service/uploads/icon_checkbox_selcted_fade2fa.svg);
                                                }
                                            }
                                            &.ant-select-item-option-disabled {
                                                color: @C4;
                                                &::before {
                                                    background-color: #F2F6FA;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
    
                            .ant-form-item-control-input {
                                min-height: auto;
                                .ant-form-item-control-input-content {
                                    line-height: 1;
                                }
                            }
                            .ant-radio-group {
                                color: @C0;
                                .ant-radio-wrapper {
                                    display: inline-flex;
                                    // min-height: 36px;
                                    align-items: center;
                                    margin-right: 16px;
                                    font-family: PingFangSC-Regular;
                                    font-size: 14px;
                                    color: #222222;
                                    line-height: 24px;
                                    font-weight: 400;
                                    .ant-radio {
                                        top: 0;
                                        &:hover {
                                            .ant-radio-inner {
                                                border-color: @CB;
                                            }
                                        }
                                        .ant-radio-inner {
                                            box-shadow: none;
                                            transition: none;
                                            &::after {
                                                border-color: @CB;
                                                background-color: @CB;
                                            }
                                            &:hover {
                                                border-color: @CB;
                                            }
                                        }
                                        &.ant-radio-checked{
                                            &::after {
                                                border-color: @CB;
                                                animation: none;
                                            }
                                            .ant-radio-inner {
                                                border-color: @CB;
                                            }
                                        }
                                    }
                                }
                            }
                            .ant-form-item-extra,
                            .ant-form-item-explain {
                                margin-top: 4px;
                                font-family: PingFangSC-Regular;
                                font-size: 14px;
                                letter-spacing: 0;
                                line-height: 24px;
                                font-weight: 400;
                                color: @C4;
                                transition: none;
                            }
    
                            .ant-form-item-explain-error {
                                color: @error-color;
                            }
                        }
                    }
                }
                .form-submit {
                    width: 100%;
                    padding-right: 16px;
                    box-sizing: border-box;
                    text-align: center;
                    font-size: 0;
    
                    .submit-btn {
                        cursor: pointer;
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        box-sizing: border-box;
                        height: 40px;
                        min-width: 260px;
                        padding: 0px 22px;
                        border-radius: 4px;
                        background-color: @CB;
                        border: none;
                        box-shadow: none;
                        &:hover {
                            transition: all .3s ease-out;
                            background-color: #528eff;
                        }
                        > span {
                            color: #FFFFFF;
                            font-family: PingFangSC-Medium;
                            font-size: 16px;
                            text-align: center;
                            line-height: 26px;
                            font-weight: 400;
                        }
                    }
                }
            }

            .dialog-result {
                background: @CW;
                display: flex;
                flex-direction: column;

                .dialog-result-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    .dialog-result-img {
                        width: 74px;
                        height: 74px;
                        margin-bottom: 24px;
                        background: no-repeat center/cover;
                        background-size: 100% auto;
                    }
                    > h3 {
                        font-family: PingFangSC-Medium;
                        font-size: 16px;
                        color: #151B26;
                        text-align: center;
                        line-height: 26px;
                        font-weight: 500;
                    }
                    .sub-title {
                        margin-top: 4px;
                        opacity: 0.9;
                        font-family: PingFangSC-Regular;
                        font-size: 14px;
                        color: #151B26;
                        text-align: center;
                        line-height: 24px;
                        font-weight: 400;
                    }
                }

                .count-down {
                    margin: 20px auto 12px;
                    height: 20px;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    color: rgba(34, 34, 34, 0.7);
                    text-align: center;
                    line-height: 20px;
                    font-weight: 400;

                    .count-down-text {
                        display: none;

                        &.show {
                            display: inline;
                        }

                        a {
                            display: inline-flex;
                            align-items: center;
                            color: @CB;
    
                            &:hover {
                                color: @CLB;
                                .jump-icon {
                                    transform: translateX(4px);
                                }
                            }
    
                            .jump-icon {
                                width: 8px;
                                height: 8px;
                                margin-left: 4px;
                                transition: transform 0.3s ease-out;
    
                                > div {
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    width: 100%;
                                    height: 100%;
                                    
                                    svg {
                                        width: 100%;
                                    }
                                }
    
                            }
                        }
                    }
                }
            }
        }
    }
}
