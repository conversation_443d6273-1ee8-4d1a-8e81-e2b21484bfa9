/**
 * @file 产品和解决方案聚合页的搜索框
 * <AUTHOR>
 */

import {useOnMount} from '@baidu/bce-hooks';
import {uniqBy, uniqWith, isEqual} from 'lodash';
import {useState} from 'react';
import cn from 'classnames';
import {ProductsMarketDataObj} from '@components/ProductsIndex/ProductsIndexMarketing/ProductsIndexMarketing';
import {sendMonitor} from '@common/helper/page';

import styles from './main.module.less';

export const ProAndSolutionSearch = (
    {allData, hotProduct, HISTORY, pageType}:
    {allData: ProAndSolutionIndexSearchDataObj[], hotProduct?: ProductsMarketDataObj['hotProduct'], HISTORY: string, pageType: 'product' | 'solution'}
) => {
    const [searchHistory, setSearchHistory] = useState<ProAndSolutionIndexSearchDataObj[]>([]);
    const [searchResult, setSearchResult] = useState<ProAndSolutionIndexSearchDataObj[]>([]);
    const [keyword, setKeyword] = useState('');
    useOnMount(() => {
        setSearchHistory(JSON.parse(localStorage.getItem(HISTORY)) || []);
    });

    const handleSearch = (v: string) => {
        const value = v.trim().toUpperCase();
        if (keyword !== value) {
            setKeyword(value);
        }
        if (!value) {
            searchResult.length && setSearchResult([]);
            return;
        }
        const res = uniqWith(allData.filter(item =>
            item.name.toUpperCase().includes(value)
            || (item.pinyin && item.pinyin.toUpperCase().includes(value))
            || (item.abbr && item.abbr.toUpperCase().includes(value))
        ), isEqual);
        setSearchResult(uniqBy(res, 'name'));
    };

    return (
        <div className={cn(styles.search, pageType === 'product' && styles.products)}>
            <input
                onChange={e => handleSearch(e.target.value)}
                type="text"
                placeholder={`请输入您想搜索的${pageType === 'solution' ? '解决方案' : '产品'}`}
            />
            <span className={styles.icon}></span>
            <div className={styles['result-wrapper']}>
                <ul className={styles.result}>
                    {
                        (keyword ? searchResult : searchHistory).map(item => (
                            <li
                                key={item.name}
                                onClick={() => {
                                    const newHis = searchHistory.filter(his => his.name !== item.name);
                                    newHis.unshift(item);
                                    setSearchHistory(newHis.slice(0, 5));
                                    localStorage.setItem(HISTORY, JSON.stringify(newHis.slice(0, 5)));
                                    sendMonitor({
                                        category: `${pageType === 'product' ? '产品' : '方案'}聚合页banner`,
                                        name: '搜索框',
                                        value: item.name,
                                        action: 'click',
                                    });
                                    window.open(item.link, '_blank');
                                }}
                            >
                                {item.name}
                            </li>
                        ))
                    }
                    {
                        keyword && !searchResult.length && (
                            <li className={styles['empty-result']}>没有找到结果，请重新输入</li>
                        )
                    }
                </ul>
            </div>
            {
                hotProduct && (
                    <div className={styles['hot-product']}>
                        <span>{hotProduct.title}：</span>
                        {
                            hotProduct.list?.map((item, index) => item && !item.disabled && (
                                <a
                                    // eslint-disable-next-line react/no-array-index-key
                                    key={index}
                                    href={item.href}
                                    target="_blank"
                                    data-track-category="产品聚合页营销位"
                                    data-track-name={hotProduct.title}
                                    data-track-value={item.text}
                                >
                                    {item.text}
                                </a>
                            ))
                        }
                    </div>
                )
            }
        </div>
    );
};

export interface ProAndSolutionIndexSearchDataObj {
    name: string;
    link: string;
    pinyin: string;
    abbr: string;
}
