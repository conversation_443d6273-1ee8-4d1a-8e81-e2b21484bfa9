@import '@styles/variables.less';
@import '@styles/mixin.less';

.base-text() {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: @C9;
    text-align: justify;
    line-height: 24px;
    font-weight: 400;
}
.search {
    position: relative;
    width: 610px;
    height: 50px;
    margin-top: 19px;
    input {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding-left: 24px;
        border: 0;
        outline: 0;
        border-bottom: 1px solid rgba(@C0, 0.16);
        background-color: transparent;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        font-weight: 400;
        color: @C0;
        &::placeholder {
            color: rgba(@C0, 0.6);
        }
        &:focus {
            & + .icon {
                background-position: 0 0;
            }
            & ~ .result-wrapper {
                display: block;
            }
        }
    }
    .icon {
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -8px;
        width: 16px;
        height: 16px;
        background: url(https://bce.bdstatic.com/p3m/common-service/uploads/search_6945eb6.png) no-repeat;
        background-size: auto 16px;
        background-position: -21px 0;
    }
    .result-wrapper {
        position: relative;
        z-index: 2;
        display: none;
        padding-top: 8px;
        width: 610px;
        &:hover {
            display: block;
        }
        .result {
            background-color: #FFFFFF;
            border-radius: 4px;
            max-height: 260px;
            box-shadow: 0 6px 16px 2px rgba(7,12,20,0.08);
            overflow: auto;
            li {
                height: 40px;
                padding-left: 16px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: @C0;
                line-height: 40px;
                cursor: pointer;
                &:not(.empty-result):hover {
                    background-color: rgba(82, 142, 255, 0.08);
                    color: @CB;
                }
            }
            .empty-result {
                color: rgba(@C0, 0.4);
                cursor: text;
            }
        }
    }

    &.products {
        width: auto;
        height: 42px;
        margin-top: 40px;
        input {
            width: 466px;
            padding-left: 36px;
            border: 1px solid @CW;
            border-radius: 4px;
            background-color: @CW;
        }
        .icon {
            left: 12px;
        }
        .result-wrapper {
            position: absolute;
            width: 466px;
        }
        .hot-product {
            margin-top: 12px;
            > span {
                background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/fire-r@3x_1f852fc.svg') left center;
                background-size: 16px 16px;
                padding-left: 20px;
                margin-right: -8px;
                .base-text();
            }
            > a {
                .base-text();
                margin-left: 12px;
                transition: color 0.3s ease-out;
                &:hover {
                    color: @CB;
                }
            }
        }
    }
}
