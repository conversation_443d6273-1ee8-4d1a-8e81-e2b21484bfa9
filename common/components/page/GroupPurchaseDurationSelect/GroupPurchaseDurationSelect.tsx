import {USelect, USelectProps} from '@common/components/USelect/USelect';
import {GroupPurchaseDurationSelectProps} from '@common/interface/groupPurchase';
import {useEffect, useState} from 'react';
import style from './GroupPurchaseDurationSelect.module.less';

function GroupPurchaseDurationSelectCom(props: GroupPurchaseDurationSelectProps) {
    const {options, value, onChange} = props;
    const [optionsItem, setOptionsItem] = useState<USelectProps['options']>([]);
    useEffect(() => {
        if (options.length) {
            const optionResult = options.map(item => {
                const intVal = parseInt(item, 10);
                const unitVal = item.endsWith('Y') ? 'Y' : 'M';
                return {
                    value: item,
                    label: unitVal === 'Y' ? `${intVal}年` : `${intVal}个月`,
                };
            });
            setOptionsItem(optionResult);
            const target = optionResult.find(item => item.value === value);
            if (!target) {
                onChange(optionResult[0].value, optionResult[0]);
            }
        }
    }, [onChange, options, value]);
    return (
        <USelect
            style={{width: '100px'}}
            className={style['duration-select']}
            {...props}
            options={optionsItem}
        />
    );
}

type GroupPurchaseDurationSelectType = typeof GroupPurchaseDurationSelectCom & {
    getDurationValue?: (value: string) => {
        durationInt: number;
        timeUnit: 'MONTH' | 'YEAR';
        label: string;
    };
};

const GroupPurchaseDurationSelect = GroupPurchaseDurationSelectCom as GroupPurchaseDurationSelectType;

GroupPurchaseDurationSelect.getDurationValue = (val: string) => {
    const durationInt = parseInt(val, 10);
    const timeUnit = val.endsWith('M') ? 'MONTH' : 'YEAR';
    return {
        durationInt,
        timeUnit,
        label: timeUnit === 'MONTH' ? `${durationInt}个月` : `${durationInt}年`,
    };
};


export {GroupPurchaseDurationSelect};
