import style from './main.module.less';

type ListItemObj = {
    title: string;
    id: string;
};

export function DeveloperAsideQuestionList(props: {
    list: ListItemObj[];
}) {
    return (
        <ul className={style['aside-question-ul']}>
            {
                props.list.map(item => {
                    return (
                        <li key={item.id}>
                            <a
                                target="_blank"
                                href={`/ask/${item.id}`}
                            >
                                {item.title}
                            </a>
                        </li>
                    );
                })
            }
        </ul>
    );
}
