.aside-question-ul {
    padding: 0px 23px 0px 30px;
    margin: 20px 4px 0px 0;
    max-height: 314px;
    overflow: auto;
    /*控制整个滚动条*/
    &::-webkit-scrollbar {
        background: #EDF3F7;
        width: 3px;
        background-clip: padding-box;
    }

    // /*滚动条两端方向按钮*/
    // &::-webkit-scrollbar-button {
    //     background-color: pink;
    // }

    /*滚动条中间滑动部分*/
    &::-webkit-scrollbar-thumb {
        width: 3px;
        height: 206px;
        background-color: #2468F2;
    }

    // /*滚动条右下角区域*/
    // &::-webkit-scrollbar-corner {
    //     background-color: red;
    // }
    li {
        margin-bottom: 12px;
        color: #191A24;
        position: relative;
        cursor: pointer;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        letter-spacing: 0;
        line-height: 24px;
        font-weight: 400;
        a {
            color: #191A24;
        }
        &:last-child {
            margin-bottom: 0;
        }
        &:hover {
            a {
                color: #2468F2;
            }
            &::before {
                background: #2468F2;
            }
        }
        &::before {
            content: '';
            width: 4px;
            background: #191A24;
            height: 4px;
            border-radius: 50%;
            position: absolute;
            left: -12px;
            top: 10px;
        }
    }
}