@import '@styles/variables.less';

.coupon-list-wap {
    ul li {
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 146px;
        padding-right: 28px;
        margin-bottom: 20px;
        overflow: hidden;
        position: relative;
        align-items: center;
        &:last-child {
            margin-bottom: 0;
        }
        &::before,
        &::after {
            display: block;
            content: '';
            width: 22px;
            height: 22px;
            background-color: #F2F6FA;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: -11px;

        }
        &::after {
            left: auto;
            right: -11px;
        }
        &.checked {
            .check-radio {
                opacity: 1;
                background: @CB;
                border: none;
                background-color: #fff;
                background: url(https://bce.bdstatic.com/portal-cloud-server/images/bcc-order/icon_checkbox_circle.png) no-repeat center 100%/100%;
            }
        }
        &.disabled.no-checked {
            .coupon-amount,
            .coupon-amount i,
            .coupon-limit,
            .coupon-name,
            .coupon-time {
                color: rgba(34, 34, 34, .4)!important;
            }
            .check-radio {
                background: #F2F2F3;
                opacity: 1;
                border: 2px solid rgba(34, 34, 34, 0.12);
            }

        }
        .check-radio {
             width: 40px;
             height: 40px;
             border: 2px solid rgba(34, 34, 34, 1);   
             opacity: 0.24;
             border-radius: 50%;
             position: absolute;
             top: 50%;
             right: 28px;
             transform: translateY(-50%);
        }
        .coupon-val {
            width: 226px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-right: 2px dashed #dddd;
            .coupon-limit {
                height: 40px;
                margin-top: 3px;
                line-height: 40px;
                font-family: PingFangSC-Regular;
                font-size: 24px;
                color: rgba(34, 34, 34, .7);
                font-weight: 400;
            }
            // 折扣券
            &.discount {
                .coupon-amount {
                    color: #F33E3E;
                    font-family: PingFangSC-Medium;
                    font-size: 32px;
                    height: 56px;
                    line-height: 56px;
                    margin-bottom: 4px;
                    i {
                        font-family: PingFangSC-Medium;
                        font-size: 48px;
                    }
                    i.discount-text {
                        margin-left: 4px;
                        font-size: 26px;
                    }
                }

            }
            // 满减券
            &.cash-full,
            &.common {
                .coupon-amount {
                    margin-left: -5px;
                    color: #F33E3E;
                    font-family: PingFangSC-Medium;
                    font-size: 48px;
                    font-weight: 500;
                    i.unit {
                        font-size: 28px;
                        vertical-align: middle;
                    }
                    i.amount-int {
                        font-size: 44px;
                        line-height: 56px;
                    }
                    i.amount-float {
                        font-size: 28px;
                    }
                }
            }
            // 通用券
            // &.common {
            //     .coupon-amount {
            //         color: #F33E3E;
            //         font-family: PingFangSC-Medium;
            //         font-size: 48px;
            //         font-weight: 500;
            //         i {
            //             font-size: 28px;
            //             vertical-align: text-bottom;
            //         }
            //     }
            // }
        }
        .coupon-desc {
            flex: 1;
            padding-left: 24px;
            span {
                display: block;
                letter-spacing: 0;
                color: #222222;
                &.coupon-name {
                    font-family: PingFangSC-Medium;
                    height: 44px;
                    line-height: 44px;
                    font-weight: 500;
                    font-size: 28px;
                    margin-bottom: 8px;
                    width: 328px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                &.coupon-time {
                    font-size: 24px;
                    font-weight: 400;
                    color: rgba(34, 34, 34, .7);
                    height: 40px;
                    line-height: 40px;
                }
            }
        }
    }
}