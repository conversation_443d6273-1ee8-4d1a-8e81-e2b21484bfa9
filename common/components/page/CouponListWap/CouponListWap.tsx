/* eslint-disable complexity */
import moment from 'moment';
import classn from 'classnames';
import Big from 'big.js';
import {useEffect, useState} from 'react';
import {ProductCouponObj} from '@common/interface/page';
import {getCouponTypeAndDesc} from '@common/helper/page';
import style from './index_750m.module.less';

export function CouponListWap(props: {
    list: ProductCouponObj[];
    selectId: number;
    onSelect: (item: ProductCouponObj, couponType: ReturnType<typeof getCouponTypeAndDesc>) => void;
}) {
    const [currentItem, setCurrentItem] = useState<ProductCouponObj>(null);
    useEffect(() => {
        setCurrentItem(props.list.find(item => item.id === props.selectId));
    }, [props.list, props.selectId]);
    return (
        <div className={style['coupon-list-wap']}>
            <ul>
                {
                    props.list.map(item => {
                        const couponInfo = getCouponTypeAndDesc(item);
                        const isDisabled = couponInfo.isApportionmentAmount && item.balance <= 0 && item.id !== props.selectId;
                        const balanceToShowArr = item.balanceToShow.split('.');
                        return (
                            <li
                                key={item.id}
                                onClick={() => {
                                    if (isDisabled) {
                                        return;
                                    }
                                    const isCancel = currentItem?.id === item.id;
                                    if (isCancel) {
                                        setCurrentItem(null);
                                        props.onSelect(null, null);
                                    } else {
                                        setCurrentItem(item);
                                        props.onSelect(item, couponInfo);
                                    }
                                }}
                                className={classn(
                                    item.id === currentItem?.id ? style['checked'] : style['no-checked'],
                                    isDisabled ? style['disabled'] : style['normal']
                                )}
                            >
                                {
                                    // 折扣券
                                    item.couponType === 'DISCOUNT_COUPON' && (
                                        <div className={classn(style['coupon-val'], style.discount)}>
                                            <span className={style['coupon-amount']}>
                                                <i>{item.amountOrDiscount.slice(0, 1)}</i>
                                                {item.amountOrDiscount.slice(1, -1)}
                                                <i className={style['discount-text']}>折</i>
                                            </span>
                                            <span className={style['coupon-limit']}>
                                                {/* 有最大抵扣显示最大抵扣 */}
                                                {/* 无最大抵扣显示代金券限制条件 */}
                                                {
                                                    item?.effectArgsMap?.upperBoundAmount
                                                        ? `最大抵扣${new Big(item.effectArgsMap.upperBoundAmount).toFixed(0)}`
                                                        : (
                                                            item?.conditionArgsMap?.productTotalPrice
                                                                ? `满${item.conditionArgsMap.productTotalPrice}可用`
                                                                : '无门槛'
                                                        )
                                                }
                                            </span>
                                        </div>
                                    )
                                }
                                {
                                    // 满减券
                                    item.couponType === 'CASH_COUPON' && item.conditionArgsMap && (
                                        <div className={classn(style['coupon-val'], style['cash-full'])}>
                                            <span className={style['coupon-amount']}>
                                                <i className={style.unit}>￥</i>
                                                <i className={style['amount-int']}>{balanceToShowArr[0]}</i>
                                                {
                                                    Number(balanceToShowArr[1]) > 0 && (
                                                        <i className={style['amount-float']}>.{balanceToShowArr[1]}</i>
                                                    )
                                                }
                                            </span>
                                            <span className={style['coupon-limit']}>
                                                {/* 发券平台只能配置 整单满或者单产品满 这两个只能存在一个 */}
                                                {
                                                    `满${item.conditionArgsMap.productTotalPrice || item.conditionArgsMap.price}可用`
                                                }
                                            </span>
                                        </div>
                                    )

                                }
                                {
                                    // 通用券、其他券
                                    couponInfo.type === 'common' && (
                                        <div className={classn(style['coupon-val'], style['common'])}>
                                            <span className={style['coupon-amount']}>
                                                <i className={style.unit}>￥</i>
                                                <i className={style['amount-int']}>{balanceToShowArr[0]}</i>
                                                {
                                                    Number(balanceToShowArr[1]) > 0 && (
                                                        <i className={style['amount-float']}>.{balanceToShowArr[1]}</i>
                                                    )
                                                }
                                            </span>
                                        </div>
                                    )
                                }
                                <div className={style['coupon-desc']}>
                                    <span className={style['coupon-name']}>{couponInfo.name}-{item.id}-{item.balance}</span>
                                    <span className={style['coupon-time']}>{moment(item.endTime).format('YYYY-MM-DD')}到期</span>
                                </div>
                                <div className={style['check-radio']}></div>
                            </li>
                        );
                    })
                }
            </ul>
        </div>
    );
}
