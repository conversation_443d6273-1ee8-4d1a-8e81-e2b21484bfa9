/**
 * @file PreviewOperation.tsx 退出预览、复制分享链接等操作
 * <AUTHOR>
 */

import {message} from 'antd';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';

import styles from './main.module.less';

export const PreviewOperation = ({isPreview, pageId}: {
    isPreview: boolean;
    pageId: string;
}) => {

    const handleExistPreviewMode = () => {
        netService.get(urlConst.EXIST_PREVIEW_MODE).then(() => {
            window.location.href = window.location.origin + window.location.pathname;
        });
    };

    const handleCopyPreviewLink = () => {
        navigator.clipboard.writeText(`${location.origin}/api/cloud-server/cms-preview?pageId=${pageId}`);
        message.success('复制成功');
    };

    return isPreview ? (
        <div className={styles['preview-operation']}>
            <input
                className={styles['preview-btn']}
                type="button"
                value="退出预览模式"
                onClick={handleExistPreviewMode}
            />
            <input
                className={styles['copy-btn']}
                type="button"
                value="复制预览链接"
                onClick={handleCopyPreviewLink}
            />
        </div>
    ) : null;
};
