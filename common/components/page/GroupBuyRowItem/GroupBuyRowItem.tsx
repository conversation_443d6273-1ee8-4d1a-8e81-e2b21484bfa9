import classNames from 'classnames';
import {CSSProperties, useRef} from 'react';
import {useOnMount} from '@baidu/bce-hooks';
import {Ioc} from '@baidu/bce-decorators';
import {UEventEmitter} from '@baidu/bce-services';
import style from './GroupBuyRowItem.module.less';

let count = 0;
let maxWidth = 0;
const event = Ioc(UEventEmitter);
export function GroupBuyRowItem(props: {
    label: string;
    children: JSX.Element;
    className?: string;
    labelWidth?: number;
    hasBorder?: boolean;
    style?: CSSProperties;
}) {
    const ref = useRef<HTMLSpanElement>(null);
    useOnMount(() => {
        const width = ref.current.offsetWidth;
        count++;
        event.on('group-buy-row-item-label-width', e => {
            maxWidth = Math.max(e, maxWidth);
            ref.current.style.width = `${maxWidth}px`;
        });

        event.emit('group-buy-row-item-label-width', width);
        return () => {
            count = count - 1;
            if (count <= 0) {
                event.destroy();
            }
        };
    });
    return (
        <div
            className={
                classNames(
                    style['group-buy-row-item'],
                    props.hasBorder ? style['has-border'] : '',
                    props.className
                )
            }
            style={props.style}
        >
            <span ref={ref} className={classNames(style.label, 'group-buy-row-item-label')}>{props.label}</span>
            <div className={style.content}>
                {props.children}
            </div>
        </div>
    );
}
