import {Form, FormProps, Progress, Tooltip} from 'antd';
import {useMemo, useState} from 'react';
import cn from 'classnames';
import {CampaignCardItemObj, MonitorTrackConfig, PURCHASE_LINK_MAP, getCampaignTitleTag} from '@common/helper/campaign';
import {useCheckActivateUserInfo} from '@common/hooks/page/useCheckActivateUser';
import {InfoCircleOutlined} from '@ant-design/icons';
import {UDialog} from '@common/components/UDialog/UDialog';
import {REAL_NAME_URL} from '@common/constant/variableConst';
import {Ioc} from '@baidu/bce-decorators';
import {UEnvService} from '@common/services/env';
import styles from './mainNew_1200.module.less';

interface CampaignInfoTagInfo {
    campaignTag: string;
    campaignPrice: number | string;
    catalogPrice: number | string;
    buyCountLimit: number | string;
    discountRatio: number;
}

interface CampaignCardCommonProps {
    serviceType: string;
    cardTitle: string;
    cardTitleLink?: string; // 卡片标题链接
    curProduct: CampaignCardItemObj;
    formProps: FormProps;
    formFields: Array<{
        label: string;
        name: string;
        component: JSX.Element;
    }>;
    basicInfo?: {
        desc?: string; // 描述
        descInfoList?: string[]; // 表单下方的描述列表
        killProgress?: number; // 抢购进度条
    };
    addonInfo?: {
        title: string; // 附加信息标题
        onChange: (value: any) => void; // 附加信息变更回调
    };
    btnTrackConfig?: MonitorTrackConfig;
}

const CampaignCardTop = (props: {
    className?: string;
    cardTitle: string;
    cardTitleLink?: string;
    desc?: string;
    campaignInfo: CampaignInfoTagInfo;
}) => {
    const {cardTitle, cardTitleLink = '', desc, campaignInfo} = props;
    return (
        <div className={props.className}>
            <div className={styles['campaign-card-head']}>
                {cardTitleLink ? (
                    <a
                        className={styles['campaign-card-title-link']}
                        href={cardTitleLink}
                        target="_blank"
                    >
                        <h2 className={styles['campaign-card-title']}>{cardTitle}</h2>
                    </a>
                ) : (<h2 className={styles['campaign-card-title']}>{cardTitle}</h2>)}
                <span className={styles['campaign-card-title-tip']}>{cardTitle}</span>
            </div>
            {desc && <p className={styles['campaign-card-desc']}>{desc}</p>}
            {/* 折扣信息 */}
            {campaignInfo?.discountRatio || campaignInfo?.buyCountLimit ? (
                <div className={styles['campaign-card-hot-tag']}>
                    {campaignInfo.discountRatio && <span className={styles['campaign-card-tag-red']}>{campaignInfo?.discountRatio}折</span>}
                    {+campaignInfo.buyCountLimit > 0 ? <span>限购{campaignInfo?.buyCountLimit}</span> : <span>不限</span>}
                </div>
            ) : null}
        </div>
    );
};

// 附加信息
const CampaignCardAddon = (props: {
    title: string;
    value: boolean;
    onChange: (value: boolean) => void;
}) => {
    const {title, value = false, onChange} = props;
    const [checked, setChecked] = useState(value);
    const handleChange = (value: boolean) => {
        setChecked(value);
        onChange(value);
    };
    return (
        <div className={styles['campaign-card-addon']}>
            <label>
                <input
                    type="checkbox"
                    checked={checked}
                    onChange={e => handleChange(e.target.checked)}
                />
                <i />
            </label>
            <p
                className={styles['campaign-card-addon-title']}
                // eslint-disable-next-line react/no-danger
                dangerouslySetInnerHTML={{__html: title}} // bca-disable-line
                onClick={() => handleChange(!checked)}
            />
        </div>
    );
};

// 秒杀进度条
const CampaignCardKillProgress = (props: {killProgress: number}) => {
    const {killProgress} = props;
    return (
        <div className={styles['campaign-card-progress']}>
            <p className={styles['progress-text']}>已抢购<span>{killProgress}%</span></p>
            <Progress
                className={styles['campaign-card-progress-bar']}
                percent={killProgress}
                showInfo={false}
                size="small"
                strokeColor="linear-gradient(90deg, rgba(243,62,62,0) 0%, rgba(243,62,62,1) 100%)"
                trailColor="rgba(230,235,243,0.43)"
            />
        </div>
    );
};

const checkDialog = ({title, content, okText, cb}: {
    title: string;
    content: string;
    okText: string;
    cb?: () => void;
}) => {
    UDialog.open({
        title: title,
        content: content,
        okText: okText,
        maskClosable: true,
        showCloseIcon: true,
        zIndex: 10000,
        onOk: close => {
            close();
            cb && cb();
        },
    });
};

// Campaign卡片内容：
// 头部：标题（链接可选）、描述（可选）、折扣信息、角标信息
// 主体部分：
// - 价格展示、选配区（文本展示、下拉框、数字输入框、日期框）、描述列表信息(可选)
// - 购买按钮、加购复选框(可选)、抢购进度条(可选)
export const CampaignCardCommon = (props: CampaignCardCommonProps) => {
    const {serviceType, cardTitle, cardTitleLink = '', curProduct, formProps, formFields, basicInfo = {}, btnTrackConfig} = props;
    const {desc, descInfoList = [], killProgress} = basicInfo;
    const [cardHover, setCardHover] = useState(false);
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const isSandbox = Ioc(UEnvService).isProdSandbox;

    const campaignInfo: CampaignInfoTagInfo = useMemo(() => {
        const {campaignPrice = '', catalogPrice = '', buyCountLimit = '', newUserLimit = '', realNameLimit = ''} = curProduct?.campaignInfo || {};
        const campaignTag = getCampaignTitleTag(Number(newUserLimit), Number(realNameLimit)) || '';
        return {
            campaignTag,
            campaignPrice,
            catalogPrice,
            buyCountLimit,
            discountRatio: campaignPrice && (+catalogPrice > +campaignPrice) ? (Math.round(+campaignPrice / +catalogPrice * 100) / 10) : null,
        };
    }, [curProduct]);

    const btnSuccessCb = (href: string) => {
        window.open(href, '_blank');
    };

    const genPurchaseLink = (product: CampaignCardItemObj) => {
        return `/campaign/order.html${location.search}${PURCHASE_LINK_MAP[serviceType]}${product.campaignInfo?.campaignId}`;
    };

    const handlerPurchase = () => {
        // 1. 判断登录状态
        // 2. 跳转购买（生成链接）
        const link = genPurchaseLink(curProduct);
        // 获取用户状态
        if (userinfo?.hasLogin) {
            window.open(link, '_blank');
        } else {
            verifyHandler().then(code => {
                // code表示当前已完成状态
                if (code === 0) {
                    // 均正常
                    btnSuccessCb(link);
                } else if (code === 201) {
                    handlerPurchase(); // 已激活，再次判断是否需要实名
                } else if (code === 204) {
                    // 未实名处理
                    checkDialog({
                        title: '温馨提示',
                        content: '对不起，您还未实名认证，请完成实名认证后再参与活动',
                        okText: '前往实名认证',
                        cb: () => btnSuccessCb(REAL_NAME_URL[isSandbox ? 'sandbox' : 'online']),
                    });
                } else {
                    checkDialog({
                        title: '未知错误',
                        content: '未知错误，请稍后重试',
                        okText: '返回活动',
                    });
                }
            }).catch(err => {
                console.error(err);
                checkDialog({
                    title: '未知错误',
                    content: '未知错误，请稍后重试',
                    okText: '返回活动',
                });
            });
        }
    };

    return (
        <div
            className={cn(styles['campaign-card'], !curProduct && styles.loading, cardHover && styles['card-hover'])}
            onMouseEnter={() => setCardHover(true)}
            onMouseLeave={() => setCardHover(false)}
        >
            {campaignInfo.campaignTag && <span className={cn(styles['campaign-card-head-tag'], styles[campaignInfo.campaignTag])} />}
            <CampaignCardTop
                className={styles['campaign-card-top']}
                cardTitle={cardTitle}
                cardTitleLink={cardTitleLink}
                desc={desc}
                campaignInfo={campaignInfo}
            />
            <div className={styles['campaign-card-info']}>
                <div className={styles['campaign-card-config']}>
                    <div className={styles['campaign-card-price']}>
                        <span className={styles['price-discount']}><span>¥</span>{campaignInfo?.campaignPrice}</span>
                        {(+campaignInfo?.catalogPrice > +campaignInfo?.campaignPrice) && (
                            <span className={styles['price-original']}>¥&nbsp;{campaignInfo?.catalogPrice}</span>
                        )}
                    </div>
                    <div className={styles['campaign-card-body']}>
                        <Form
                            colon={false}
                            {...formProps}
                            className={cn(styles['campaign-card-form'], formProps.className)}
                        >
                            {formFields.map((item: any) => (
                                <Form.Item
                                    key={item.name}
                                    label={item.tooltip ? (
                                        <>{item.label}<Tooltip title={item.tooltip} placement="top"><InfoCircleOutlined /></Tooltip></>
                                    ) : item.label}
                                    name={item.name}
                                >
                                    {item.component}
                                </Form.Item>
                            ))}
                        </Form>
                        {descInfoList.length > 0 && (
                            <div className={styles['campaign-card-desc-info']}>
                                {descInfoList.map((item, index) => (
                                    <p key={index.toString()} className={styles['campaign-card-desc-info-item']}>{item}</p>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
                <div className={styles['campaign-card-footer']}>
                    {props.addonInfo && <CampaignCardAddon {...props.addonInfo} value={false} />}
                    {killProgress && <CampaignCardKillProgress killProgress={killProgress} />}
                    <div className={styles['campaign-card-btn-wrapper']}>
                        <div
                            className={styles['campaign-card-btn']}
                            onClick={handlerPurchase}
                            data-track-action="click"
                            data-track-category={serviceType}
                            data-track-name={cardTitle}
                            // 当前表单的项的所有value值
                            data-track-value={btnTrackConfig?.value || '立即购买'}
                        >
                            <span>立即购买</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
