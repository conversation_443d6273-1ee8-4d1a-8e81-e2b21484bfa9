@import '@styles/variables.less';
@import '@styles/mixin.less';

// tag样式
@comp_new_user_tag: "https://bce.bdstatic.com/p3m/common-service/uploads/comp_new_user_tag-new_6c66630.svg";
@comp_user_tag: "https://bce.bdstatic.com/p3m/common-service/uploads/comp_user_tag-new_faeebe3.svg";
@com_product_first_tag: "https://bce.bdstatic.com/p3m/common-service/uploads/comp_product_first_tag-new_3fcb0d9.svg";
@new_user_tag: "https://bce.bdstatic.com/p3m/common-service/uploads/new_user_tag-new_502e4aa.svg";
@product_first_tag: "https://bce.bdstatic.com/p3m/common-service/uploads/product_first_tag-new_175df9a.svg";

.campaign-card {
    position: relative;
    border-radius: 12px;
    background-color: @CW;
    border: 1px solid rgba(47,58,78,0.16);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &.card-hover,
    &:hover {
        border-color: @CB;
    }

    // TODO：loading态
    // &.loading {
    //     background-color: pink;
    // }
    .campaign-card-head-tag {
        position: absolute;
        top: -1px;
        right: -1px;
        display: inline-block;
        background: no-repeat top right/100% auto;
        width: 100PX;
        height: 30PX;

        &.product_first_tag {
            background-image: url(@product_first_tag);
        }
        &.comp_new_user_tag {
            background-image: url(@comp_new_user_tag);
        }
        &.new_user_tag {
            background-image: url(@new_user_tag);
        }
        &.comp_user_tag {
            background-image: url(@comp_user_tag);
        }
        &.com_product_first_tag {
            background-image: url(@com_product_first_tag);
        }
    }
    .campaign-card-top {
        position: relative;
        padding: 24px 24px 0;
        .campaign-card-head {
            width: 100%;
            position: relative;
            .campaign-card-title {
                display: inline-block;
                width: 100%;
                font-family: PingFangSC-Semibold;
                font-size: 16px;
                color: #091221;
                letter-spacing: 0;
                line-height: 24px;
                font-weight: 600;
                .ellipsis();
            }
            .campaign-card-title-link {
                position: relative;
                display: inline-flex;
                align-items: center;
                width: 100%;
                .campaign-card-title {
                    width: auto;
                }
                .campaign-card-title-icon {
                    content: '';
                    display: inline-block;
                    flex: min(16px, 20PX) 0 0;
                    width: min(16px, 20PX);
                    height: min(16px, 20PX);
                    margin-left: 2px;
                    position: relative;
                    left: 0;
                    color: #091221;
                    transform: rotate(90deg);
                    transition: left 0.3s ease-out;
                    svg {
                        width: 100%;
                        height: 100%;
                    }
                }
                &:hover {
                    .campaign-card-title {
                        color: @CB;
                    }
                    .campaign-card-title-icon  {
                        color: @CB;
                        left: 4px;
                    }
                }
            }
            .campaign-card-title-tip {
                visibility: hidden;
                position: absolute;
                z-index: 2;
                left: 100%;
                top: 26px;
                transform: translateX(-50%);
                opacity: 0;
                display: block;
                padding: 0 8PX;
                border: 1px solid rgba(47,58,78,0.12);
                border-radius: 4px;
                background: #FFFFFF;
                font-family: PingFangSC-Medium;
                font-size: 12PX;
                color: rgba(34, 34, 34, .54);
                letter-spacing: 0;
                line-height: 20PX;
                font-weight: 500;
                white-space: nowrap;
                transition: opacity 0.3s ease-out;
            }
            &:hover {
                .campaign-card-title-tip {
                    visibility: visible;
                    opacity: 1;
                }
            }
        }
        // TODO: desc OLD
        .campaign-card-desc {
            margin-top: 2px;
            color: @C7;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: min(12px, 14PX);
            line-height: 20px;
            letter-spacing: 0px;
            text-align: left;
            .ellipsis2(2);
        }
        .campaign-card-hot-tag {
            display: block;
            margin-top: 8px;
            span {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                padding: 2px 6px;
                background: rgba(216, 216, 216, .25);
                border-radius: 4px;

                font-family: PingFangSC-Regular;
                font-size: min(12px, 14PX);
                color: rgba(43,48,75,0.64);
                letter-spacing: 0;
                line-height: min(12px, 14PX);
                font-weight: 400;
                &.campaign-card-tag-red {
                    color: #F33E3E;
                    background: rgba(243,62,62,0.1);;
                }
                & + span {
                    margin-left: 8px;
                }
            }
        }
    }

    .campaign-card-info {
        padding: 16px 24px 20px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .campaign-card-config {
            .campaign-card-price {
                font-size: 0;
                position: relative;
                padding-bottom: 16px;
                &::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 1px;
                    background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/dashed_bc8efc8.png') center/cover;
                }
                .price-discount {
                    font-family: FZJUNH_CUJW--GB1-0;
                    font-size: 22px;
                    color: #F33E3E;
                    line-height: 28px;
                    font-weight: 400;
                    vertical-align: text-bottom;
                    > span {
                        display: inline-block;
                        margin-right: 4px;
                        font-family: PingFangSC-Semibold;
                        font-size: 16px;
                        color: #F33E3E;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 20px;
                        font-weight: 500;
                    }
                }
                .price-original {
                    display: block;
                    margin-top: 4px;
                    font-family: PingFangSC-Regular;
                    font-size: min(12px, 14PX);
                    color: @C4;
                    line-height: 20PX;
                    font-weight: 400;
                    text-decoration: line-through;
                }
            }
        }
        .campaign-card-body {
            margin: 16px 0 20px;
            .campaign-card-form {
                :global {
                    .ant-form-item {
                        margin-bottom: 10px;

                        .ant-form-item-label {
                            height: 18px;
                            & > label {
                                display: inline-block;
                                width: 64px;
                                height: 18px;
                                color: rgba(34,34,34,.7);
                                font-family: PingFangSC-Regular;
                                font-weight: 400;
                                font-size: min(12px, 14PX);
                                line-height: 18px;
                                letter-spacing: 0px;
                                text-align: left;
                                .ellipsis();

                                // TODO: anticon OLD
                                & > .anticon {
                                    font-size: min(12px, 14PX);
                                    position: relative;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    margin-left: 2PX;
                                    &.ant-form-item-tooltip {
                                        color: @C4;
                                        cursor: pointer;
                                        margin-inline-start: 2PX;
                                    }
                                    &:hover,
                                    &.ant-tooltip-open {
                                        color: @CB;
                                    }
                                }
                            }
                        }

                        .ant-form-item-control-input {
                            height: 18px;
                            min-height: auto;
                            .ant-form-item-control-input-content {
                                color: #091221;
                                font-family: PingFangSC-Medium;
                                font-weight: 600;
                                font-size: min(12px, 14PX);
                                line-height: 18px;
                                letter-spacing: 0px;
                                text-align: left;
                            }
                        }
                    }
                }
            }
            // TODO: desc-info OLD
            .campaign-card-desc-info {
                .campaign-card-desc-info-item {
                    position: relative;
                    padding-left: min(22px, 34PX);
                    color: #091221;
                    font-family: PingFangSC-Regular;
                    font-weight: 400;
                    font-size: min(12px, 14PX);
                    line-height: min(22px, 34PX);
                    letter-spacing: 0px;
                    text-align: left;
                    .ellipsis();
                    &::before {
                        position: absolute;
                        content: '';
                        left: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        width: min(16px, 18PX);
                        height: min(16px, 18PX);
                        background: no-repeat url('https://bce.bdstatic.com/p3m/common-service/uploads/checked_c7b878a.svg') center/cover;
                    }
                    & + & {
                        margin-top: min(12px, 20PX);
                    }
                }
            }
        }

        .campaign-card-footer {
            // TODO: addon OLD
            .campaign-card-addon {
                cursor: pointer;
                position: relative;
                display: flex;
                margin-bottom: min(24px, 36PX);
            
                label {
                    width: min(16px, 24PX);
                    height: min(16px, 24PX);
                    margin-right: min(2px, 6PX);
                    cursor: pointer;
                    position: relative;
                    top: min(2px, 3PX);
                    flex-shrink: 0;
            
                    input[type="checkbox"] {
                        display: none;
            
                        &:checked + i {
                            border-color: @CB;
                            background: @CB url('https://bce.bdstatic.com/portal-cloud-server/images/index2022/merge_purchace/icon_checkbox.svg') no-repeat 50%/12px 12px;
                        }
                    }
            
                    i {
                        display: block;
                        width: 100%;
                        height: 100%;
                        border: 1px solid @C4;
                        border-radius: min(2px, 3PX);
                        &:hover {
                            border-color: @CB;
                        }
                    }
                }

                .campaign-card-addon-title {
                    color: #091221;
                    font-family: PingFangSC-Regular;
                    font-weight: 400;
                    font-size: min(12px, 14PX);
                    line-height: min(20px, 30PX);
                    letter-spacing: 0px;
                    text-align: left;
                }
            }
            // TODO: progress OLD
            .campaign-card-progress {
                display: flex;
                align-items: center;
                margin-bottom: min(8px, 12PX);
                .progress-text {
                    min-width: min(72px, 98PX);
                    &, & > span {
                        height: min(18px, 27PX);
                        display: inline-block;
                        color: rgba(34,34,34,0.6);
                        font-family: PingFangSC-Medium;
                        font-weight: 600;
                        font-size: min(12px, 14PX);
                        line-height: min(18px, 27PX);
                        letter-spacing: 0px;
                        text-align: left;
                    }
                    & > span {
                        color: #091221;
                    }
                }
                .campaign-card-progress-bar {
                    flex: 1;
                    height: min(18px, 27PX);
                    font-size: min(12px, 14PX);
                    :global {
                        .ant-progress-success-bg, .ant-progress-bg {
                            height: min(6px, 9PX)!important;
                        }
                    }
                }
            }
            .campaign-card-btn-wrapper {
                width: calc(100% - 2PX);
                display: flex;
                align-items: center;
                justify-content: center;
                .campaign-card-btn {
                    width: 100%;
                    max-width: 170px;
                    padding: 8PX 0;
                    border: 1px solid #2468F2;
                    border-radius: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: @CW;
                    cursor: pointer;
                    > span {
                        display: inline-block;
                        font-family: PingFangSC-Medium;
                        font-size: min(14px, 18PX);
                        color: @CB;
                        line-height: min(20px, 24PX);
                        font-weight: 500;
                    }
                    &:hover {
                        background-color: @CB;
                        > span {
                            color: @CW;
                        }
                    }
                }
            }
        }
    }
}

:global {
    // tooltip OLD
    .ant-tooltip {
        .ant-tooltip-inner {
            color: rgb(0,0,0);
            font-family: PingFangSC-regular;
            padding: 8px 12px;
            font-weight: 400;
            font-size: 12px;
            line-height: 20px;
            letter-spacing: 0px;
            text-align: left;
            background-color: @CW;
            border-radius: 4px;
        }
        .ant-tooltip-arrow {
            bottom: 1px;
        }
        .ant-tooltip-arrow-content {
            --antd-arrow-background-color: @CW;
        }
    }
}