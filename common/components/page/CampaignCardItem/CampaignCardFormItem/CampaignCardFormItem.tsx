import {USelect, USelectProps} from '@common/components/USelect/USelect';
import {InputNumber, InputNumberProps} from 'antd';
import {UDatePicker} from '@common/components/UDatePicker/UDatePicker';
import moment from 'moment';
import {ReactSVG} from 'react-svg';
import {ReactNode} from 'react';
import styles from './mainNew_1200.module.less';

export const UCampSingleText = (props: {content: ReactNode}) => {
    return <div className={styles['camp-single-text']}>{props.content}</div>;
};

// Select 组件: 实例
// {
//     label: '时长',
//     name: 'duration',
//     component: <UCampSelectInput
//         options={durationOption.map(item => ({
//             label: `${item.slice(0, -1)}` + TIME_UNIT_MAP[item.slice(-1)],
//             value: item,
//         }))}
//     />,
// },
export const UCampSelectInput = (props: USelectProps) => {
    const options = props.options || [];
    return options?.length > 1 ? (
        <USelect
            {...props}
            className={styles['camp-select-input']}
            popupClassName={styles['camp-select-input-dropdown']}
            listItemHeight={22} // antd4 下拉框每一项的高度小于24px时，需要设置该值
            suffixIcon={(
                <ReactSVG
                    className="camp-select-input-icon"
                    src="https://bce.bdstatic.com/p3m/common-service/uploads/arrow_f36a1a8.svg"
                />
            )}
        />
    ) : <UCampSingleText content={options[0]?.label} />;
};

// NumberInput 组件
// {
//     label: '购买数量',
//     name: 'count',
//     component: <UCampNumberInput
//         min={1}
//         max={Number(curProduct?.campaignInfo?.buyCountLimit || 0) > 0 ? Number(curProduct.campaignInfo?.buyCountLimit) : null}
//     />,
// },
export const UCampNumberInput = (props: InputNumberProps) => {
    const {min = 1, max} = props;
    return min < max ? (
        <InputNumber
            bordered={false}
            defaultValue={min}
            controls={{
                upIcon: <ReactSVG
                    className="camp-number-input-up"
                    src="https://bce.bdstatic.com/p3m/common-service/uploads/mini-arrow_6fccfab.svg"
                />,
                downIcon: <ReactSVG
                    className="camp-number-input-down"
                    src="https://bce.bdstatic.com/p3m/common-service/uploads/mini-arrow_6fccfab.svg"
                />,
            }}
            size="small"
            formatter={value => (value ? `${value}`.replace(/\D/g, '') : min.toString())}
            {...props}
            className={styles['camp-number-input']}
        />
    ) : <UCampSingleText content={min} />;
};

// datePicker 组件
// {
//     label: '生效日期',
//     tooltip: '限购数量为1-10台，超出限购数量请联系客服购买',
//     name: 'date',
//     component: <UCampDatePicker />,
// },
export const UCampDatePicker = () => {
    return (
        <UDatePicker
            className={styles['camp-date-picker']}
            dropdownClassName={styles['camp-date-picker-popup']}
            bordered={false}
            showToday={false}
            allowClear={false}
            inputReadOnly
            disabledDate={current => current && current < moment().startOf('day')}
        />
    );
};
