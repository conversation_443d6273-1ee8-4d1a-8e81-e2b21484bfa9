@import '@styles/variables.less';
@import '@styles/mixin.less';
// 单行文本
.camp-single-text {
    padding-left: 8px; // 与下拉框文本对齐
    color: #091221;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: min(12px, 16PX);
    line-height: 18px;
    letter-spacing: 0px;
    text-align: right;
    .ellipsis();
}

// select
.camp-select-input {
    // 基础调整
    &:global(.ant-select) {
        :global {
            .ant-select-arrow {
                right: 4px;
                color: @C4;
                .camp-select-input-icon {
                    width: 12px;
                    height: 12px;
                    font-size: 12px;
                    color: @C4;
                    transform: rotate(180deg);
                    svg {
                        width: 12px;
                        height: 12px;
                    }
                }
            }
            .ant-select-selector {
                height: 20px;
                border: none;
                padding: 0 0 0 4px;
                background-color: rgba(@CB, .06);
                border-radius: 2px;

                .ant-select-selection-item, .ant-select-selection-search-input {
                    height: 20px;
                    padding-right: 20px;
                    font-family: PingFangSC-Regular;
                    font-size: min(12px, 16PX);
                    line-height: 20px;
                    color: #091221;
                    text-align: right;
                    font-weight: 400;
                }
            }
        }
        &:global(.ant-select-open .camp-select-input-icon) {
            transform: rotate(0deg);
        }
    }
    // 悬浮边框不变色
    &:hover {
        &:global(.ant-select) {
            :global {
                .ant-select-selector {
                   border-color: transparent !important;
                }
                .camp-select-input-icon {
                    color: @CB;
                }
            }
        }
    }
}

// select 下拉框
.camp-select-input-dropdown {
    padding: 4px 0;
    border-radius: 2px;
    // width: max-content !important; // 重置下拉框宽度
    // min-width: unset !important; // 重置下拉框宽度
    // max-width: 158px !important; // 重置下拉框宽度
    :global {
        .ant-select-item {
            min-height: 22px;
            padding: 2px 20px 2px 4px;
            font-size: min(12px, 16PX);
            line-height: 18px;
            font-weight: 400;
            text-align: right;
            color: @C7;
            &.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
                color: #091221;
                background-color: @CW;
            }
            &.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
                color: @CB;
                background-color: @CW;
                &:hover {
                    color: @CB;
                }
            }
        }
        .rc-virtual-list-scrollbar {
            width: 7px !important;
            .rc-virtual-list-scrollbar-thumb {
                width: 3px !important;
                border-radius: 2px !important;
            }
        }
    }
}

// 数字输入框 OLD
.camp-number-input {
    &:global(.ant-input-number) {
        width: 100%;
        :global {
            .camp-number-input-up,
            .camp-number-input-down {
                width: 100%;
                height: 100%;
                color: @C4;
                font-size: min(10px, 12PX);
                &:hover {
                    color: @CB;
                }
                > div {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    svg {
                        width: min(10px, 12PX);
                        height: auto;
                    }
                }
            }
            .camp-number-input-down  {
                transform: rotate(180deg);
            }
            .ant-input-number-handler-wrap {
                opacity: 1 !important;
                width: min(10px, 12PX);
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                .ant-input-number-handler {
                    border-left: none;
                    width: 100%;
                    flex: 1;
                    .ant-input-number-handler-up-inner,
                    .ant-input-number-handler-down-inner {
                        font-size: min(10px, 12PX);
                        height: 100%;
                        width: 100%;
                        right: 0;
                        margin-top: 0;
                        &:hover {
                            color: @CB;
                        }
                    }
                    .ant-input-number-handler-up-inner {
                        top: unset;
                        bottom: 0;
                        border: none;
                        position: relative;
                        &::after {
                            content: '';
                            position: absolute;
                            bottom: 0;
                            width: 100%;
                            height: 1px;
                            background-color: rgba(167,167,167,0.2);
                        }
                    }
                    &.ant-input-number-handler-up-disabled,
                    &.ant-input-number-handler-down-disabled {
                        .camp-number-input-up,
                        .camp-number-input-down {
                            color: rgba(34,34,34,.2);
                        }
                    }
                    &:hover {
                        height: auto !important;
                        color: @CB;
                    }
                }
            }
            .ant-input-number-input-wrap {
                .ant-input-number-input {
                    height: 18px;
                    padding: 0;
                    .camp-single-text();
                }
            }
        }
    }
}

// 日期 OLD
.camp-date-picker {
    padding: 0 0;
    width: 100%;
    height: 18px;
    border: none;
    &:hover {
        border: none;
    }
    &:global(.ant-picker) {
        :global {
            .ant-picker-input {
                height: 18px;
                padding: 0;
                >input {
                    .camp-single-text();
                    &::placeholder {
                        color: @C4;
                        font-family: PingFangSC-Regular;
                        font-weight: 400;
                    }
                }
            }
            .ant-picker-suffix {
                color: @C4;
                pointer-events: all;
                font-size: min(10px, 12PX);
                margin-left: 4PX;
                &:hover {
                    color: @CB;
                }
            }
        }
    }
}
.camp-date-picker-popup {
    &:global(.ant-picker-dropdown) {
        :global {
            .ant-picker-header {
                button {
                    color: #091221;
                    line-height: 32px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                }
                .ant-picker-header-view {
                    line-height: 32px;
                }
            }
            .ant-picker-suffix {
                color: @C7;
            }
        }
    }
}