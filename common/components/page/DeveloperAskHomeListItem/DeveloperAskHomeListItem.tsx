import {utcToBJ} from '@baidu/bce-helper';
import {UShowMoreContainer} from '@common/components/UShowMoreContainer/UShowMoreContainer';
import {QuestionListItemObj} from '@common/interface/page';
import style from './main.module.less';

export function DeveloperAskHomeListItem({item}: {item: QuestionListItemObj}) {
    return (
        <li className={style['ask-li']} key={item.id}>
            <a
                target="_blank"
                href={`/ask/${item.id}`}
                className={style['ask-li-a']}
            >
                <div className={style['title-content']}>
                    <p
                        className={style.title}
                        // eslint-disable-next-line react/no-danger
                        dangerouslySetInnerHTML={{__html: item.title}} // bca-disable-line
                    />
                    <div className={style['ask-time']}>
                        <span className={style.time}>{utcToBJ(item.createdAt)}</span>
                        <span className={style.view}>{item.viewCount + 500}</span>
                    </div>
                    <div className={style['answer-num-box']}>
                        <span className={style.num}>{item.answerNum}</span>
                        <span className={style.text}>回答</span>
                    </div>
                </div>
            </a>
            {
                item.answer?.htmlContent && (
                    <div className={style['ask-content']}>
                        <UShowMoreContainer boundaryHeight={200} btnBg="rgb(248 250 255)">
                            <div className={style['answer-content']}>
                                <span className={style.nickname}>
                                    {item.answer.nickname}：
                                </span>
                                <div
                                    id="u-editor"
                                    className="markdown-body editormd-preview-container"
                                    // eslint-disable-next-line react/no-danger
                                    dangerouslySetInnerHTML={{__html: item.answer.htmlContent}}
                                />
                            </div>
                        </UShowMoreContainer>
                    </div>
                )
            }
        </li>
    );
}
