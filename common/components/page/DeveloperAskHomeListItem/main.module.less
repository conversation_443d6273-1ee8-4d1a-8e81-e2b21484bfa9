.ask-li {
    padding: 15px 0;
    border-bottom: 1px solid #e9eef3;
    .ask-li-a {
        color: #191a24;
    }
    .title-content {
        position: relative;
        &:hover {
            .title {
                color: #2468f2;
            }
        }
        .title {
            font-size: 16px;
            font-weight: 500;
            min-height: 16px;
            cursor: pointer;
        }
        .ask-time {
            font-size: 12px;
            color: #758096;
            height: 24px;
            line-height: 24px;
            margin-top: 4px;
            .time,
            .view {
                padding-left: 18px;
                background-image: url(https://bce.bdstatic.com/developer-static/imgs/common/time.svg);
                background-repeat: no-repeat;
                background-size: 12px auto;
                background-position: 0;
                margin-right: 10px;
            }
            .view {
                background-image: url(https://bce.bdstatic.com/developer-static/imgs/common/eye.svg);
                background-size: 16px auto;
            }
        }
        .answer-num-box {
            width: 46px;
            height: 46px;
            position: absolute;
            right: 0;
            top: 0px;
            background-color: rgba(36, 104, 242, 0.05);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            span {
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #2468F2;
                letter-spacing: 0;
                text-align: center;
                font-weight: 400;
                &.num {
                    margin-bottom: 6px;
                }
            }
        }
    }
    .ask-content {
        margin-top: 8px;
        padding: 15px 20px;
        background-color: rgba(36, 104, 242, 0.03);
        .nickname {
            color: #8E97AC;
            font-size: 12px;
        }
        .answer-content {
            color: #8E97AC;
        }
        :global {
            #u-editor.editormd-preview-container {
                background-color: transparent;
                padding: 0;
            }
        }
    }
}