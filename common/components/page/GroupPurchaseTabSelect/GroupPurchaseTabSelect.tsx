/* eslint-disable complexity */
import {memo, useState, useEffect, Fragment} from 'react';
import cx from 'classnames';
import {GroupPurchaseTabSelectProps, GroupPurchaseTabSelectPropsData} from '@common/interface/groupPurchase';
import {orderBy} from 'lodash';
import styles from './index.module.less';

export const GroupPurchaseTabSelect = memo((props: GroupPurchaseTabSelectProps) => {
    const {data: propsData = [], onChange, className, defaultValue, value, isOrder} = props;
    const [activeValue, setActiveValue] = useState<string | number>(value || defaultValue || propsData?.[0]?.value);
    const [data, setData] = useState(propsData);

    const handleLiClick = (item: GroupPurchaseTabSelectPropsData[0]) => {
        if (item.value !== activeValue) {
            setActiveValue(item.value);
            onChange && onChange(item.value, item);
        }
    };

    useEffect(() => {
        if (propsData && propsData.length > 0) {
            let orderData = propsData;
            if (isOrder) {
                orderData = orderBy(propsData.map(item => ({...item, valueOrder: +item.value})), ['valueOrder']);
            }
            setData(orderData);
            const item = orderData.find(item => item.value === activeValue);
            setActiveValue(item ? item.value : orderData[0].value);
            !item && onChange && onChange(orderData[0].value, orderData[0]);
        }
    }, [activeValue, isOrder, onChange, propsData]);

    return (
        <div
            className={cx(className, styles['tabselect-container'])}
        >
            <div className={styles.wrapper}>
                <ul>
                    {
                        data.map(item => (
                            <li
                                className={cx({[styles.active]: item.value === activeValue})}
                                key={item.value}
                                onClick={() => handleLiClick(item)}
                            >
                                {item.label}
                            </li>
                        ))
                    }
                </ul>
            </div>
            {
                data.map(item => (
                    <Fragment key={item.value}>
                        {
                            item.desc && activeValue === item.value && (
                                <div
                                    className={styles.desc}
                                >
                                    {item.desc}
                                </div>
                            )
                        }
                    </Fragment>
                ))
            }
        </div>
    );
}, (prev, next) => {
    if (next.data.length <= 0) {
        return true;
    }
    if (prev.value || next.value) {
        return (prev.value === next.value)
        && (JSON.stringify(prev.data) === JSON.stringify(next.data));
    }
    return (prev.defaultValue === next.defaultValue)
        && (JSON.stringify(prev.data) === JSON.stringify(next.data));
});

