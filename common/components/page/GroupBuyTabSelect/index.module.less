@import '@styles/variables.less';

.tabselect-container {
    &.has-border-bottom {
        padding-bottom: 20px;
        border-bottom: 1px solid @C04; 
    }
    .wrapper {
        display: flex;
        align-items: center;
    
        ul {
            display: flex;
            flex: 1;
            flex-wrap: wrap;
    
            li {
                box-sizing: border-box;
                height: 32px;
                min-width: 72px;
                padding: 6px 20px;
                border: 1px solid @C08;
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #222222;
                text-align: center;
                line-height: 20px;
                font-weight: 400;
                cursor: pointer;

                &:nth-child(n+2) {
                    margin-left: -1px;
                    border-left-color: transparent;
                }

                &:first-child {
                    border-radius: 4px 0 0 4px;
                }
                
                &:last-child {
                    border-radius: 0 4px 4px 0;
                }
                
                &:only-child {
                    border-radius: 4px;
                }

                &:hover {
                    border-color: @CB;
                    color: @CB;
                }
            }
        }

        .active {
            color: @CB;
            border-color: @CB !important;
        }
    }

    .desc {
        margin-top: 4px;
        height: 20px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: @C4;
        line-height: 20px;
        font-weight: 400;
    }
}