import {memo, useState, useEffect, Fragment} from 'react';
import {useOnMount} from '@baidu/bce-hooks';
import cx from 'classnames';
import {GroupBuyTabSelectProps} from '@common/interface/groupBuy';
import styles from './index.module.less';

export const GroupBuyTabSelect = memo((props: GroupBuyTabSelectProps) => {
    const {data, onChange, className, defaultValue, value} = props;
    const [activeValue, setActiveValue] = useState<string>(value || defaultValue || data[0]?.value);

    const handleLiClick = (item: {
        text: string;
        value: string;
    }) => {
        if (item.value !== activeValue) {
            setActiveValue(item.value);
            onChange && onChange(item.value);
        }
    };

    useOnMount(() => {
        onChange && onChange(activeValue);
    });

    useEffect(() => {
        if (activeValue) {
            if (data.every(item => item.value !== activeValue)) {
                setActiveValue(data[0]?.value);
                onChange && onChange(data[0]?.value);
            } else {
                onChange && onChange(activeValue);
            }
        }
    }, [activeValue, data, onChange]);

    useEffect(() => {
        value && setActiveValue(value);
    }, [value]);

    return (
        <div
            className={cx(className, styles['tabselect-container'])}
        >
            <div className={styles.wrapper}>
                <ul>
                    {
                        data.map(item => (
                            <li
                                className={cx({[styles.active]: item.value === activeValue})}
                                key={Math.random().toString()}
                                onClick={() => handleLiClick(item)}
                            >
                                {item.text}
                            </li>
                        ))
                    }
                </ul>
            </div>
            {
                data.map(item => (
                    <Fragment key={Math.random().toString()}>
                        {
                            item.desc && activeValue === item.value && (
                                <div
                                    className={styles.desc}
                                >
                                    {item.desc}
                                </div>
                            )
                        }
                    </Fragment>
                ))
            }
        </div>
    );
}, (prev, next) => {
    if (next.data.length <= 0) {
        return true;
    }
    if (prev.value || next.value) {
        return (prev.value === next.value)
        && (JSON.stringify(prev.data) === JSON.stringify(next.data));
    }
    return (prev.defaultValue === next.defaultValue)
        && (JSON.stringify(prev.data) === JSON.stringify(next.data));
});

