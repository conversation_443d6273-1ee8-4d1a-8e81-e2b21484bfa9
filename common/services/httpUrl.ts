/* eslint-disable max-lines */
import {Injectable} from '@baidu/bce-decorators';
import {BOS_CDN_ORIGIN_URL, BOS_ORIGIN_URL} from '@common/constant/variableConst';
import {UHttpConfig} from '../config/http';
import {UEnvService} from './env';

@Injectable()
export class httpUrlService {
    constructor(
        private httpConfig: UHttpConfig,
        private env: UEnvService
    ) {}

    private get cloudBos() {
        return this.httpConfig.cloudBosUrl;
    }

    private get developerBos() {
        return this.httpConfig.developerBosUrl;
    }

    private get developerBosByEnv() {
        return this.httpConfig.developerBosUrlByEnv;
    }

    private get bosPreFix() {
        return this.httpConfig.bosUrlPrefix;
    }

    private get portalServer() {
        return this.httpConfig.portalServerUrl;
    }

    private get portal() {
        return this.httpConfig.portalUrl;
    }

    private get developer() {
        return this.httpConfig.developerUrl;
    }

    // console-hub
    private get consoleHub() {
        return this.httpConfig.consoleHubUrl;
    }

    // 产品价格bos地址，判断跟cloudBos不同
    private productPriceBosUrl = this.httpConfig.productPriceBosUrl;

    // 文档subIndex页bos地址，判断跟cloudBos不同
    private docSubIndexBosUrl = this.httpConfig.docSubIndexBosUrl;
    private docSubIndexEnBosUrl = this.httpConfig.docSubIndexEnBosUrl;

    // cloud地址
    private cloudUrl = this.httpConfig.cloudUrl;

    // 判断表单是否提交
    get GET_SURVEY_SUBMIT_STATUS() {
        return '/api/portal/common/survey/:campaignId/submit_status';
    }

    // 首页-新banner数据
    get INDEX_BANNER2022() {
        return `${this.cloudBos}/index/${this.bosPreFix}banner2022.js`;
    }
    // 首页-营销位数据
    get INDEX_MARKET_POS2022() {
        return `${this.cloudBos}/index/${this.bosPreFix}marketPos2022.js`;
    }
    // 首页移动端营销位
    get INDEX_MARKET_POS_WAP2023() {
        return `${this.cloudBos}/index/${this.bosPreFix}marketPosWap2023.js`;
    }
    get INDEX_PRODUCT2022() {
        return `${this.cloudBos}/index/${this.bosPreFix}product2022.js`;
    }
    // 首页组合购模块数据
    get INDEX_GROUP_BUY() {
        return `${BOS_ORIGIN_URL}/portal-cloud-server/group-purchase/indexGroupBuy.json`;
    }
    // 获取企业服务中心数据（2022 新版）
    get INDEX_ENTERPRISE2022() {
        return `${this.cloudBos}/index/${this.bosPreFix}enterprise2022.js`;
    }
    get INDEX_ACTIVITY2022() {
        return `${this.cloudBos}/index/${this.bosPreFix}activity2022.js`;
    }
    // 获取开发者中心数据
    get GET_DEVELOPER() {
        return `${this.cloudBos}/index/${this.bosPreFix}developer.js`;
    }
    // 获取能力体验中心类型数据
    get GET_EXPERIENCE() {
        return `${this.cloudBos}/portal_experience/${this.bosPreFix}experience.js`;
    }
    // 获取产品价格导航
    get GET_PRODUCT_PRICE_NAV() {
        return `${this.cloudBos}/product-price/${this.bosPreFix}nav.js`;
    }
    // 获取新闻资讯
    get GET_INDEX_NEWS() {
        return `${this.cloudBos}/index/${this.bosPreFix}news.js`;
    }
    // 获取热门搜索数据
    get GET_HOT_SEARCH() {
        return `${this.cloudBos}/portal_search/${this.bosPreFix}productUnion.js`;
    }
    // 获取导航数据
    get GET_PORTAL_COMMON_NAV() {
        return `${this.cloudBos}/portal-common/${this.bosPreFix}nav.js`;
    }
    // 获取cms页面路径
    get GET_CMS_PAGE_PATH() {
        return `${this.portalServer}/api/cms/path`;
    }
    // 获取cms页面数据
    get GET_CMS_PAGE_DATA() {
        if (this.env.isDev) {
            return 'http://gzbh-sandbox144-store-5117.gzbh:8266/api/cms/data';
        }
        return `${this.portalServer}/api/cms/data`;
    }
    // 退出预览模式
    get EXIST_PREVIEW_MODE() {
        return '/api/cloud-server/clear-preview';
    }
    // 获取sdk页面数据
    get GET_DOC_SDK_DATA() {
        return `${this.cloudBos}/doc/${this.bosPreFix}sdk.js`;
    }
    // 获取最佳实践搜索数据
    get GET_PRACTICE_SEARCH() {
        return '/api/practice/search';
    }
    // 获取最佳实践列表
    get GET_PRACTICE_LIST() {
        return `${this.portal}/api/practice`;
    }
    // 获取最佳实践分类
    get GET_PRACTICE_CATEGORY() {
        return `${this.portal}/api/practice/category`;
    }

    // 获取最佳实践详情
    get GET_PRACTICE_DETAIL() {
        return `${this.portal}/api/practice/:id/detail`;
    }
    // 增加浏览次数
    get VIEW_PRACTICE_DETAIL() {
        return '/api/practice/:id/view';
    }
    // 获取文档中心数据
    get GET_DOC_INDEX() {
        return `${this.cloudBos}/doc/${this.bosPreFix}nav.js`;
    }
    // 获取文档产品index页sidebar数据
    get GET_DOC_SUB_SIDEBAR() {
        return `${this.docSubIndexBosUrl}/:path/config.json`;
    }
    get GET_DOC_SUB_SIDEBAR_EN() {
        return `${this.docSubIndexEnBosUrl}/:path/config.json`;
    }
    // 获取文档产品index页侧边导航栏数据
    get GET_DOC_SUB_NAV() {
        return `${this.cloudBos}/:path/${this.bosPreFix}nav.js`;
    }
    // 获取文档产品index页PDF链接
    get GET_DOC_PDF() {
        return `${this.portalServer}/doc/:repo/pdf_settings`;
    }
    // 获取文档产品index页OrgName
    get GET_DOC_ORG_NAME() {
        return `${this.portal}/api/doc/org/:id`;
    }
    get POST_DOC_SEARCH() {
        return `${this.portal}/api/doc/search`;
    }
    get GET_DOC_OPERATION_DATA() {
        return `${this.portal}/api/doc/operation_info`;
    }
    // 获取新闻资讯详情
    get GET_NEWS_DETAIL() {
        return `${this.portal}/api/news`;
    }
    // 获取新闻资讯列表
    get GET_NEWS_LIST() {
        return `${this.portal}/api/news_list`;
    }
    // 获取新闻资讯热门新闻等内容
    get GET_NEWS_LIST_HOT_NEWS() {
        return `${this.cloudBos}/index/${this.bosPreFix}news.js`;
    }

    // 获取开发者用户权限
    get GET_DEVELOPER_USER_CURRENT() {
        return `${this.portal}/api/live/current`;
    }
    // 直播报名
    get LIVE_JOIN() {
        return `${this.developer}/portal/live/sign_up`;
    }
    // 获取直播详情
    get GET_LIVE_DETAIL() {
        return `${this.developer}/portal/live/:id`;
    }
    // 评论列表
    get LIVE_COMMENT() {
        return `${this.developer}/portal/live/comment`;
    }
    // 发表评论
    get PUBLISH_LIVE_COMMENT() {
        return `${this.portal}/api/live/comment`;
    }
    // 删除评论
    get DELETE_LIVE_COMMENT() {
        return `${this.portal}/api/live/delete_comment`;
    }
    // 查询已经删除的评论
    get LIVE_COMMENT_DEL() {
        return `${this.developer}/portal/live/deleted_comment/:liveId`;
    }
    // 直播历史评论
    get LIVE_COMMENT_HISTORY() {
        return `${this.developer}/portal/live/comments/history`;
    }

    // 问题列表
    get GET_QUESTION_LIST() {
        return `${this.developer}/questions`;
    }

    // 问题详情
    get GET_QUESTION_DETAIL() {
        return `${this.developer}/question/:id`;
    }

    // 相关问题
    get GET_QUESTION_RELATED() {
        return `${this.developer}/question/:id/related`;
    }

    // 问题的回答
    get GET_QUESTION_ANSWER() {
        return `${this.developer}/question/:id/answers`;
    }

    // 热门问题
    get GET_QUESTION_HOT_QUESTION() {
        return `${this.developerBos}/question/${this.bosPreFix}hot_question.json`;
    }

    // 搜索主题列表
    get GET_THEME_LIST() {
        return `${this.portal}/api/theme/:word/keywords`;
    }

    // 长尾关键词详情
    get GET_SEO_TAIL_WORD() {
        return `${this.portal}/api/theme/keyword/:id`;
    }

    // SEO核心关键词
    GET_SEO_CORE_WORDS = `${BOS_ORIGIN_URL}/portal-cloud-server/dep/core-keywords.json`;

    // 搜索
    get PORTAL_SEARCH() {
        return `${this.portal}/api/portalsearch`;
    }

    // 解决方案聚合页营销位
    get GET_SOLUTION_INDEX_MARKET_POS() {
        return `${this.cloudBos}/solution/${this.bosPreFix}marketingPosition.js`;
    }

    // 产品聚合页营销位
    get GET_PRODUCT_INDEX_MARKET_POS() {
        return `${this.cloudBos}/product/${this.bosPreFix}marketingPosition.js`;
    }

    // 产品聚合页营销位2023
    get GET_PRODUCT_INDEX_MARKET_POS_2023() {
        return `${this.cloudBos}/product/${this.bosPreFix}marketingPosition2023.js`;
    }

    // 文档首页营销位
    get GET_DOC_INDEX_MARKET_POS() {
        return `${this.cloudBos}/doc/${this.bosPreFix}marketingPosition.js`;
    }

    // 文档首页营销位 - 2025 新版
    get GET_DOC_INDEX_MARKET_POS_2025() {
        return `${this.cloudBos}/doc/${this.bosPreFix}marketingPosition2025.js`;
    }

    // 推荐产品
    get GET_PRODUCT_RECOMMEND() {
        return `${this.portal}/api/feed/similar_product`;
    }

    // BCD获取tld
    get GET_BCD_PORTAL_TLD() {
        return '/api/bcd/open/portal/recommend?from=portalAvailableTld';
    }

    // BCD领取证书代金券
    get POST_BCD_REVICE_CAS_COUPON() {
        return '/api/sme/coupon/issue-coupon';
    }

    // BCD判断是否是新用户
    get POST_BCD_IS_NEW_USER() {
        return '/api/sme/user/activity/isNewUser';
    }

    // BCD获取证书活动信息
    get GET_BCD_ACTIVITY_INFO() {
        return '/api/sme/user/activity/info';
    }

    // BCD获取热门推荐
    get GET_BCD_PORTAL_HOT() {
        return '/api/bcd/open/portal/recommend?from=portalRecommendTld';
    }

    // BCD获取交易数据
    get GET_BCD_PORTAL_TRAN() {
        return '/api/bcd/open/portal/recommend?from=portalRecommendDomain';
    }

    // BCD获取域名详情
    get GET_BCD_PORTAL_DETAIL() {
        return '/api/bcd/open/domain/detail';
    }

    // BCD获取域名详情
    get GET_BCD_PKG_LIST() {
        return `${this.consoleHub}/api/bcd/open/resource/package/sale`;
    }

    // BCD获取公告列表
    get GET_BCD_NOTICE_LIST() {
        return `${this.consoleHub}/api/bcd/open/notice/query`;
    }

    // BCD获取公告详情
    get GET_BCD_NOTICE_DETAIL() {
        return `${this.consoleHub}/api/bcd/open/notice/:id`;
    }

    // BCD获取推荐SUFFIX
    get GET_BCD_RECOMMEND_SUFFIX() {
        return `${this.portal}/api/bcd/whois/suffix_recommend`;
    }

    // BCD委托购买提交表单
    get GET_BCD_PURCHASE_DATA() {
        return '/api/bcd/open/portal/anonymous/agent-consult';
    }

    // BCD获取验证码
    get GET_BCD_SEND_SMSCODE() {
        return '/api/bcd/open/portal/agent-demand/anonymous-verification';
    }

    // BLA获取城市、行业静态数据
    get GET_BLA_STATIC_DATA() {
        return '/api/crs/static/data';
    }

    // BLA行业数据
    get GET_BLA_QUALIFICATION_DATA() {
        return '/api/bla/qualification/industries';
    }

    // BLA活动列表
    get GET_BLA_ACTIVITIES() {
        return '/api/bla/portal/activities';
    }

    // BLA导航列表
    get GET_BLA_NAVS() {
        return '/api/bla/portal/navigation';
    }

    // BLA获取资质信息
    get GET_BLA_QUALIFICATIONS() {
        return '/api/bla/portal/qualifications';
    }

    // BLA获取专区信息
    get GET_BLA_SECTIONS() {
        return '/api/bla/portal/section';
    }

    // BLA获取关联产品推荐
    get GET_BLA_OTHERS() {
        return '/api/bla/portal/recommendation';
    }

    // BLA获取数藏活动页资质
    get GET_BLA_NFT_QUALIFICATIONS() {
        return '/api/bla/portal/nft/qualifications';
    }

    // BLA获取数藏活动页banner
    get GET_BLA_NFT_BANNER() {
        return '/api/bla/portal/nft/banner';
    }

    // BLA资质详情
    get GET_BLA_QUALIFICATIONS_QUERY() {
        return '/api/bla/qualification/query';
    }

    // CBS发送验证码
    get GET_CBS_SEND_SMSCODE() {
        return '/api/crs/verification/sms';
    }

    // BLA发送验证码
    get GET_BLA_SEND_SMSCODE() {
        return '/api/bla/requirements/verify';
    }

    // CBS提交需求
    get GET_CBS_SUBMIT_DEMAND() {
        return '/api/crs/demand/anonymous';
    }

    // BLA提交需求
    get GET_BLA_SUBMIT_DEMAND() {
        return '/api/bla/requirements/anonymous';
    }

    // 微信分享接口
    get GET_WX_SHARE() {
        return `${this.portal}/api/wx_signature`;
    }

    // TMS获取交易商标列表
    get GET_TMS_GET_TRANSACTION_LIST() {
        return '/api/tms/transaction/v2/goods/list';
    }

    // TMS查询是否可以领取代金券
    get GET_TMS_SEARCH_COUPON() {
        return '/api/sme/coupon?judge';
    }

    // TMS领取代金券
    get GET_TMS_RECEIVE_COUPON() {
        return '/api/sme/coupon/promotion?acquireCoupon';
    }

    // TMS查询商标
    get GET_TMS_GET_TRADE_LIST() {
        return `${this.consoleHub}/api/tms/query/list`;
    }

    // TMS获取商标详情
    get GET_TMS_GET_TRADE_DETAIL() {
        return `${this.consoleHub}/api/tms/query/detail`;
    }

    // 获取商标交易信息
    get GET_TMS_GET_TRANSACTION_INFO() {
        return `${this.consoleHub}/api/tms/transaction/goods/query-list`;
    }

    // 获取服务器时间
    get GET_TMS_SERVICE_TIMS() {
        return `${this.consoleHub}/api/tms/query/time`;
    }
    // 获取搜索置顶数据
    get GET_SEARCH_TOP() {
        return `${this.portal}/api/search/top`;
    }
    get GET_DEFAULT_SEARCH_TOP() {
        return `${this.cloudBos}/portal_search/${this.bosPreFix}recommend.js`;
    }
    get GET_SEARCH_TYPECOUNT() {
        return `${this.portal}/api/search/type`;
    }

    // 获取排行榜页跑马灯数据
    get GET_RANK_NEWS_TICKER() {
        return `${this.portal}/api/rank/news_ticker`;
    }

    // 获取文章主页tag
    get GET_ARTICLE_HOME_TAGS() {
        return `${this.developerBos}/home/<USER>
    }
    // 获取文章页最新活动
    get GET_NEW_ACTIVITYS_DATA() {
        return `${this.developerBos}/article/${this.bosPreFix}newest_activity.json`;
    }

    // 获取文章页最热文章
    get GET_HOT_ARTICLES_DATA() {
        return `${this.developerBos}/article/${this.bosPreFix}hottest_article.json`;
    }

    // 获取全部文章列表
    get GET_ARTICLE_ALL_LIST() {
        return `${this.developer}/article/list`;
    }

    // 获取文章详情
    get GET_ARTICLE_DETAIL() {
        return `${this.developer}/article/:id`;
    }

    // 获取文章评论列表
    get GET_COMMENT() {
        return `${this.developer}/article/:id/comment`;
    }

    // 查看文章关联tag
    get GET_TAGS_OF_ARTICLE() {
        return `${this.developer}/article/:id/tag`;
    }

    // 获取文章详情页的营销位ID
    get GET_ARTICLE_MARKETING_POS() {
        return `${this.portal}/api/seo/article/:id/operation_ids`;
    }
    // 获取文章详情页内容开始位置营销位(:env/ 将在用户侧进行环境处理展示pre_data或者data)
    GET_ARTICLE_BEGINNING_MARKET_POS = `${this.developerBosByEnv}/article/:env/begining_market_seo_pos.json`;
    // 获取文章详情页弹出营销位数据
    GET_ARTICLE_POPOVER_MARKET_POS = `${this.developerBosByEnv}/article/:env/popover_market_seo_pos.json`;
    // 获取文章详情页内容开始位置产品卡片营销位
    GET_ARTICLE_PRODUCT_CARD_MARKET_POS = `${this.developerBosByEnv}/article/:env/product_card_market_seo_pos.json`;
    // 获取文章详情页文章底部营销位
    GET_ARTICLE_AI_BOTTOM_MARKET_POS = `${this.developerBos}/article/:env/ai_bottom_market_pos.json`;
    // 获取文章详情页右侧营销位
    GET_ARTICLE_RIGHT_MARKET_POS = `${this.developerBos}/article/:env/right_market_pos.json`;

    // 活动
    get GET_ACTIVITY() {
        return `${this.developerBos}/home/<USER>
    }
    // 活动聚合页banner
    get GET_ACTIVITY_BANNER() {
        return `${this.developerBos}/activity/${this.bosPreFix}index_banner.json`;
    }
    // 活动聚合页topic
    get GET_ACTIVITY_TOPIC() {
        return `${this.developerBos}/activity/${this.bosPreFix}index_topic.json`;
    }

    // 活动聚合 日历
    get ACTIVITY_INDEX_CALENDAR() {
        return `${this.developer}/campaign/calendar`;
    }

    // 获取活动标签（分类）
    get ACTIVITY_INDEX_TAGS() {
        return `${this.developer}/campaign/tags`;
    }

    // 活动页列表
    get ACTIVITY_INDEX_LIST() {
        return `${this.developer}/campaign`;
    }

    // BCC操作系统和镜像
    get POST_GROUP_BUY_BCC_IMAGES() {
        return `${this.portal}/api/bcc/image/list/forCreateInstance`;
    }

    // 组合购询价
    get GROUP_BUY_PRICES() {
        return `${this.portal}/api/merge_purchase/prices`;
    }

    // 组合购下单
    get GROUP_BUY_TOPAY() {
        return `${this.portal}/api/merge_purchase/orders`;
    }

    // 判断用户实名与否和用户类型
    get ACCOUNT_REAL_NAME_TYPE() {
        return `${this.portal}/api/account/real_name/type`;
    }

    // 组合购 CBS 服务区域
    get GET_CBS_REGION() {
        return `${this.portal}/api/crs/static/data`;
    }

    // 组合购 获取短信验证码
    get POST_GROUPBUY_AUTHCODE() {
        return `${this.portal}/api/merge_purchase/auth_code`;
    }

    // 组合购 验证短信验证码
    get POST_GROUPBUY_AUTHCODE_VERIFY() {
        return `${this.portal}/api/merge_purchase/auth_code/verify`;
    }

    // 组合购 获取vpc列表
    get GET_VPC_LIST() {
        return `${this.portal}/api/merge_purchase/vpc_list`;
    }

    // 组合购 获取资源分组id
    get GET_RESOURCE_GROUP_ID() {
        return `${this.portal}/api/res/group`;
    }

    // 组合购 对应region的可用区和可用区下的套餐资源
    get GET_INSTANCE_FLAVORSPEC() {
        return `${this.portal}/api/bcc/instance/flavorSpec`;
    }

    // 组合购 获取打标商品
    get GET_GROUP_PURCHASE_PRODUCTS() {
        return `${this.portal}/api/price/config`;
    }

    // 优惠聚合页 获取打标商品 - 无登陆限制
    get GET_GROUP_PURCHASE_PRODUCTS_NO_LOGIN() {
        return `${this.portal}/api/price/config/cache`;
    }

    // 产品价格bos地址
    get GET_PRODUCT_PRICE_FROM_BOS() {
        return `${this.productPriceBosUrl}/product-price/:parentServiceType-:serviceType/:signAuth.json`;
    }

    // 提交表单-不需要登录
    get POST_SURVEY_NO_NEED_LOGIN() {
        // 返回无需登录的申请调查问卷的API路径
        return `${this.portal}/api/survey/no_need_login/apply`;
    }

    // 提交表单-需要登录
    get POST_SURVEY_NEED_LOGIN() {
        return `${this.portal}/api/survey/need_login/apply`;
    }
    // 提交峰会表单
    get POST_SURVEY_SUMMIT() {
        return `${this.portal}/api/survey_summit/apply`;
    }

    // 获取产品页dom
    get POST_PRODUCT_TPL() {
        if (this.env.isDev) {
            return 'http://gzbh-sandbox144-store-5117.gzbh:8266/api/cms/tpl';
        }
        return `${this.portalServer}/api/cms/tpl`;
    }

    get POST_PRODUCT_PRICE() {
        return `${this.portal}/api/price`;
    }

    // 组合购产品询价
    get POST_MERGE_PRODUCT_PRICE() {
        return `${this.portal}/api/price/merge-product`;
    }

    // 获取视频中心类型信息数据
    get POST_VIDEO_CENTER_TYPE() {
        return `${this.portal}/api/video-center/type`;
    }

    // 获取视频中心视频列表数据
    get GET_VIDEO_CENTER_LIST() {
        return `${this.portal}/api/video-center/list`;
    }

    // 获取视频中心详情页视频数据
    get POST_VIDEO_DETAIL() {
        return `${this.portal}/api/video-center/video`;
    }

    // 视频中心：上传视频已观看记录
    get POST_VIDEO_DETAIL_VIEWED() {
        return `${this.portal}/api/video-center/views`;
    }

    // 视频中心：上传视频点击Like
    get POST_VIDEO_DETAIL_LIKES() {
        return `${this.portal}/api/video-center/likes`;
    }

    // 视频中心：上传视频点击dislike
    get POST_VIDEO_DETAIL_DISLIKES() {
        return `${this.portal}/api/video-center/dislikes`;
    }

    // 视频中心：post详情页进入页面记录用户信息,put修改用户反馈
    get POST_VIDEO_DETAIL_USERINFO() {
        return `${this.portal}/api/video-center/user-info`;
    }

    // 获取用户状态-是否登录、是否激活、是否实名及实名类型
    get GET_ACCOUNT_STATUS() {
        return `${this.portal}/api/account/status`;
    }

    // 获取文心一言数据
    get GET_WENXIN_DATA() {
        return `${this.cloudBos}/wenxin/${this.bosPreFix}data.js`;
    }

    // 白皮书-行业白皮书列表数据
    get HANDBOOK_WHITEBOOK() {
        return `${this.cloudBos}/portal_aggregation/${this.bosPreFix}white_book.js`;
        // portal_aggregation/white_book
    }
    // 白皮书-产品手册列表数据
    get HANDBOOK_PRODUCT() {
        return `${this.cloudBos}/portal_aggregation/${this.bosPreFix}product_handbook.js`;
    }

    // 获取官网直播详情
    get GET_CLOUD_LIVE() {
        return `${this.portal}/api/cloud-live/:liveId`;
    }
    // 直播观看量 POST => 观看量+1, GET => 获取观看量
    get CLOUD_LIVE_VIEW() {
        return '/api/cloud-live/:liveId/view';
    }
    // 发送消息
    get POST_CLOUD_LIVE_MESSAGE() {
        return '/api/cloud-live/msg';
    }

    // 验证优惠券状态
    get POST_COUPON_PRECONDITION() {
        return '/api/yunying/coupon/precondition';
    }
    // 领取优惠券
    get POST_COUPON_APPLY() {
        return '/api/yunying/coupon/apply';
    }
    // 根据 ServiceType 获取商品 SKU 列表
    get GET_PURCHASE_PRODUCT() {
        return `${this.portal}/api/price/config`;
    }
    // 获取产品可用的代金券 使用bfe转发到console
    get POST_COUPON_AVAIL() {
        return `${this.portal}/api/coupon/avail`;
    }
    // 移动端产品下单
    get POST_PURCHASE_ORDER_MOBILE() {
        return `${this.portal}/api/merge_purchase/orders/mobile`;
    }
    // 获取cms配置的 券组数据
    get GET_COUPON_GROUP_CMS() {
        return `${this.cloudBos}/product/${this.bosPreFix}coupon_group.js`;
    }
    // 获取ai聚合页数据
    get GET_AGGREGATE_AI_DATA() {
        return `${this.cloudBos}/portal_aggregation/${this.bosPreFix}ai_data.js`;
    }
    // 获取峰会页面数据
    get GET_SUMMIT_PAGE_DATA() {
        return `${this.cloudBos}/summit/:path/${this.bosPreFix}data.json`;
    }
    // 获取峰会数据
    get GET_SUMMIT_PAGE_DATA_NEW() {
        return `${this.cloudBos}/summit/${this.bosPreFix}:path.js`;
    }
    // 获取爱企查数据
    get GET_SEARCH_COMPANY() {
        return `${this.portal}/api/survey_summit/search_company`;
    }
    // UC账号绑定PASS
    get POST_UC_BIND_PASS() {
        return '/api/cloud-live/uc_bind_pass';
    }
    // 获取console和p3m对应产品subserviceType展示的中文名称和apiid
    get GET_PRODUCT_API_ID() {
        return `${BOS_CDN_ORIGIN_URL}/portal-cloud-server/group-purchase/product-map/:product.json`;
    }
    // 获取合作伙伴课程
    POST_PARTNER_COURSE = `${this.portal}/api/partner/course`;
    // 判断用户是否是合作伙伴，或者企业组织的主账号是伙伴的
    GET_ACCOUT_PARTNER = `${this.portal}/api/partner/exam/qualify`;
    // 获取合作伙伴考试结果
    POST_PARTNER_EXAM_RESULT = `${this.portal}/api/partner/exam/result`;
    // 获取可以/已经报名的考试
    POST_PARTNER_EXAM = `${this.portal}/api/partner/exam`;
    // 提交考试报名表单
    POST_PARTNER_EXAM_APPLY = `${this.portal}/api/partner/exam/apply`;
    // 获取考试信息
    GET_PARTNER_EXAM_DETAIL = `${this.portal}/api/partner/exam/:examId`;
    // 通过examId获取考试报名信息
    GET_PARTNER_EXAM_INFO_APPLY = `${this.portal}/api/partner/exam_info/:examId`;
    // 获取峰会直播流状态
    GET_SUMMIT_LIVE_STATUS = `${this.portal}/api/summit/live/status`;
    // 获取峰会直播推荐卡片数据
    GET_SUMMIT_RECOMMEND_CARD = `${this.cloudBos}/summit/${this.bosPreFix}recommend.js`;

    // 校验课程或者考试的前置课程是否已经学完，传examId或者courseId
    POST_CHECK_PRE_COURSE = `${this.portal}/api/video-center/preCourseCheck`;

    // 判断千帆试用开通状态
    get GET_QIANFAN_TRIAL_STATUS() {
        return `${this.portal}/api/qianfan/check_protocol`;
    }
    // 请求千帆试用
    get POST_QIANFAN_TRIAL_APPLY() {
        return `${this.portal}/api/qianfan/agree_protocol`;
    }
    // 千帆对话
    POST_QIANFAN_CHAT = `${this.portal}/api/qianfan/chat`;
    // 获取千帆模型数据
    GET_QIANFAN_MODEL_DATA = `${this.portal}/api/qianfan/models`;
    // 获取千帆bos数据
    GET_QIANFAN_TRIAL_DATA = `${BOS_ORIGIN_URL}/portal-cloud-server/qianfan/${this.bosPreFix}trial.json`;

    // 获取优惠聚合页数据
    SEARCH_CAMPAIGN_LIST = `${this.portal}/api/price/activity_list`;
    // SEARCH_CAMPAIGN_LIST = 'http://172.18.80.45:8666/api/price/activity_list';
    SEARCH_CAMPAIGN_DETAIL = `${this.portal}/api/price/activity_detail`;
    // SEARCH_CAMPAIGN_DETAIL = 'http://172.18.80.45:8666/api/price/activity_detail';

    // 推送产品页激活线索到crm
    POST_PUSH_LEADS_TO_CRM = '/api/portal/leads';

    // 价格计算器导航
    get GET_PRICE_CALCULATOR_NAV() {
        return `${this.cloudBos}/calculator/${this.bosPreFix}nav.js`;
    }

    // 价格定价页面全量数据
    get GET_PRICE_ALL_DATA() {
        return `${this.cloudBos}/product-price/${this.bosPreFix}newNav.js`;
    }
    // 价格定价页面全量数据
    get GET_PRICE_ALL_BANNER() {
        return `${this.cloudBos}/product-price/${this.bosPreFix}banner.js`;
    }

    get GET_REGION_LIST() {
        return `${this.cloudUrl}/api/region/list`;
    }
    /** ocr价格计算器 - 获取产品&价格信息 */
    get GET_OCR_PRODUCT() {
        return `${this.cloudUrl}/bce/chargeproduct/get`;
        // return 'https://iapi.baidu-int.com/m1/345790-0-default/bce/chargeproduct/get';
    }
    // 价格计算器 - 千帆询价
    get GET_QIANFAN_PRICE() {
        return `${this.portal}/api/calculator/qianfan/order/price`;
    }
    get GET_REGION_LIST_NEW() {
        return `${this.cloudUrl}/api/region/region_list`;
    }
    get GET_EIP_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }
    get GET_BLS_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }
    get GET_BEC_FLAVOR() {
        return `${this.cloudUrl}/api/calculator/bec/flavor`;
    }
    get GET_BEC_PRICE() {
        return `${this.cloudUrl}/api/calculator/bec/price`;
    }

    get GET_BCC_FLAVOR_LIST() {
        return `${this.cloudUrl}/api/calculator/bccFlavor`;
    }

    get GET_BCC_PRICE() {
        return `${this.cloudUrl}/api/calculator/bcc/instance/price`;
    }

    POST_BCC_PRICE_V2 = `${this.cloudUrl}/api/calculator/bcc/instance/priceV2`;

    get GET_BOS_FLAVOR() {
        return `${this.cloudUrl}/api/calculator/bos/flavor`;
    }
    get GET_BOS_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }
    get GET_PALO_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }
    get GET_PALO_FLAVOR_LIST() {
        return `${this.cloudUrl}/api/calculator/palo/module/listWithSlot`;
    }

    get GET_BMR_FLAVOR_LIST() {
        return `${this.cloudUrl}/api/calculator/bmr/flavor`;
    }

    get GET_BMR_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }

    // 价格计算器 - 加入清单
    get ADD_CALCULATOR_LIST() {
        return `${this.portal}/api/calculator/addCalculatorList`;
    }
    // 价格计算器 - 获取清单
    get GET_CALCULATOR_LIST() {
        return `${this.portal}/api/calculator/getCalculatorList`;
    }
    // 价格计算器 - 删除清单
    get DELETE_CALCULATOR_LIST() {
        return `${this.portal}/api/calculator/delCalculatorList`;
    }
    // 价格计算器 - 更新清单
    get UPDATE_CALCULATOR_LIST() {
        return `${this.portal}/api/calculator/updateCalculatorList`;
    }
    // RDS 价格计算器 - 查询 package config
    get RDS_PACKAGE_CONFIG() {
        return `${this.cloudUrl}/api/rds/package/config`;
    }
    // RDS 价格计算器 - 批量询价接口
    get RDS_BATCH_QUERY_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }
    // SCS 价格计算器 - 查询 package config
    get SCS_PACKAGE_CONFIG() {
        return `${this.cloudUrl}/api/scs/package/config`;
    }
    // SCS 价格计算器 - 批量询价接口
    get SCS_BATCH_QUERY_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }
    // DWZ 价格计算器 - 查询 询价接口
    get DWZ_QUERY_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }
    // VDB 价格计算器 - 批量询价接口
    get VDB_BATCH_QUERY_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }
    // GaiaDB 价格计算器 - 查询 节点规格
    get GAIADB_PACKAGE_CONFIG() {
        return `${this.cloudUrl}/api/gaiadb/package/config`;
    }
    // GaiaDB 价格计算器 - 批量询价接口
    get GAIADB_BATCH_QUERY_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }
    get GET_PFS_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }
    // CFS 价格计算器 - 批量询价接口
    get GET_CFS_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }
    /* ai加速营 */
    // 获取报名人数
    GET_AI_ACCELERATE_JOIN = `${this.portal}/api/accelerator/nums`;
    // 获取报名状态
    GET_AI_ACCELERATE_JOIN_STATUS = `${this.portal}/api/accelerator/status`;
    GET_PARTNER_VIDEO_QUALIFY = `${this.portal}/api/partner/portal/video/qualify`;
    // 下载数据记录
    GET_PARTNER_DOWNLOAD_URL = `${this.portal}/api/partner/data/download_record`;
    // 合作伙伴查询
    GET_PARTNER_QUERY_URL = `${this.portal}/api/partner/portal/query`;
    // 获取课程状态
    GET_AI_ACCELERATE_COURSE = `${this.portal}/api/accelerator/course`;
    // 获取页面数据
    GET_AI_ACCELERATE_DATA = `${this.cloudBos}/ai_training_camp/${this.bosPreFix}:id.js`;
    // 获取推广活动基本信息（未登录）
    POST_AI_INVITATION_INFO = `${this.portal}/api/partner/invitation/info`;
    // 获取用户的推广活动数据（登录）
    POST_AI_INVITATION_USER_INFO = `${this.portal}/api/partner/invitation/user_info`;
    // 根据accountID获取用户昵称
    GET_ACCOUNT_LOGIN_NAME = `${this.portal}/api/account/loginName`;
    // 提交分享次数统计
    POST_AI_INVITATION_COUNT = `${this.portal}/api/partner/invitation/count`;
    // ai加速营人传人活动，重新绑定邀请人
    POST_AI_ACCELERATE_UPDATE_INVITER = '/api/partner/invitation/update_inviter_info';

    // 获取ai demo 数据
    GET_AI_DEMO_DATA = '/platform/page/customDemo';
    // 问题反馈
    POST_AI_DEMO_FEEDBACK = '/platform/red/logserver/api/v1/badcase/add';
    // 印章识别
    POST_OCR_SEAL = '/platform/demo/red/rest/2.0/ocr/v1/seal';
    // 数字识别
    POST_OCR_NUMBERS = '/platform/demo/red/rest/2.0/ocr/v1/numbers';
    // 智能票据识别
    POST_OCR_MULTIPLE_INVOICE = '/platform/demo/red/rest/2.0/ocr/v1/multiple_invoice';
    // 增值税发票识别
    POST_OCR_VAT_INVOICE = '/platform/demo/red/rest/2.0/ocr/v1/vat_invoice';
    // 获取文档捉虫活动页数据
    GET_DOC_TYPO_DATA = `${BOS_ORIGIN_URL}/portal-cloud-server/doc/${this.bosPreFix}typo.json`;
    // aidemo
    POST_AIDEMO = '/aidemo';
    // 车型检测
    POST_VEHICLE_CAR = '/platform/demo/red/rest/2.0/image-classify/v1/car';
    // 地标识别
    POST_IMAGERECOGNITION_LANDMARK = '/platform/demo/red/rest/2.0/image-classify/v1/landmark';
    // 果蔬识别
    POST_IMAGERECOGNITION_INGREDIENT = '/platform/demo/red/rest/2.0/image-classify/v1/classify/ingredient';
    // 车辆检测
    POST_VEHICLE_DETECT = '/platform/demo/red/rest/2.0/image-classify/v1/vehicle_detect';
    // 网约车行程单识别
    POST_OCR_ONLINE_TAXI_ITINERARY = '/platform/demo/red/rest/2.0/ocr/v1/online_taxi_itinerary';
    // 文档识别
    POST_OCR_DOC_PARSER = '/platform/demo/xmind/red/xmind/parser';
    // 图像清晰度增强
    POST_IMG_DEF_ENHANCE = '/platform/demo/red/rest/2.0/image-process/v1/image_definition_enhance';
    // 图像去雾
    POST_IMG_DEHAZE = '/platform/demo/red/rest/2.0/image-process/v1/dehaze';
    // 图像对比度增强
    POST_IMG_CONTRAST_ENHANCE = '/platform/demo/red/rest/2.0/image-process/v1/contrast_enhance';
    // 图像拉伸恢复
    POST_IMG_STRETCH_RESTORE = '/platform/demo/red/rest/2.0/image-process/v1/stretch_restore';
    // 图像色彩增强
    POST_IMG_COLOR_ENHANCE = '/platform/demo/red/rest/2.0/image-process/v1/color_enhance';
    // 黑白图像上色
    POST_IMG_COLOURIZE = '/platform/demo/red/rest/2.0/image-process/v1/colourize';
    // 人像动漫化
    POST_IMG_SELFIE_ANIME = '/platform/demo/red/rest/2.0/image-process/v1/selfie_anime';
    // 图像风格转换
    POST_IMG_STYLE_TRANS = '/platform/demo/red/rest/2.0/image-process/v1/style_trans';
    // 图片无损放大
    POST_IMG_QUALITY_ENHANCE = '/platform/demo/red/rest/2.0/image-process/v1/image_quality_enhance';
    // 图像修复
    POST_IMG_INPAINTING = '/platform/demo/red/rest/2.0/image-process/v1/inpainting';

    // 首页行业支持模块
    GET_HOME_BANNER_2024 = `${this.cloudBos}/index/${this.bosPreFix}banner2024.js`;
    GET_HOME_BANNER_2024_V2 = `${this.cloudBos}/index/${this.bosPreFix}banner2024-v2.js`;
    GET_HOME_BANNER_2024_V3 = `${this.cloudBos}/index/${this.bosPreFix}banner2024-v3.js`;
    GET_HOME_MARKET_POS_2024 = `${this.cloudBos}/index/${this.bosPreFix}marketPosition2024.js`;
    GET_HOME_INDUSTRY_SUPPORT2024 = `${this.cloudBos}/index/${this.bosPreFix}industrySupport2024.js`;
    GET_HOME_ABILITY_SUPPORT_2024 = `${this.cloudBos}/index/${this.bosPreFix}abilitySupport2024.js`;
    GET_HOME_PRODUCT_2024 = `${this.cloudBos}/index/${this.bosPreFix}productNew2024.js`;
    GET_HOME_ENTERPRISE_2024 = `${this.cloudBos}/index/${this.bosPreFix}case2024.js`;
    GET_HOME_DA_MO_XING_2024 = `${this.cloudBos}/index/${this.bosPreFix}daMoXing2024.js`;

    // DeepSeek
    // GET_DEEP_SEEK_BANNER = `${this.cloudBos}/deepseek/${this.bosPreFix}banner.js`;
    GET_DEEP_SEEK_APP = `${this.cloudBos}/deepseek/${this.bosPreFix}app.js`;
    GET_DEEP_SEEK_APP_BUILDER = `${this.cloudBos}/deepseek/${this.bosPreFix}appBuilder.js`;
    GET_DEEP_SEEK_BAI_GE = `${this.cloudBos}/deepseek/${this.bosPreFix}baigeai.js`;
    GET_DEEP_SEEK_MODEL_BUILDER = `${this.cloudBos}/deepseek/${this.bosPreFix}modelBuilder.js`;
    GET_DEEP_SEEK_BAR = `${this.cloudBos}/deepseek/${this.bosPreFix}bottomBar.js`;
    GET_DEEP_SEEK_PRODUCTS = `${this.cloudBos}/deepseek/${this.bosPreFix}products.js`;
    GET_DEEP_SEEK_PRACTICE = `${this.cloudBos}/deepseek/${this.bosPreFix}practice.js`;
    GET_DEEP_SEEK_YI_TI_JI = `${this.cloudBos}/deepseek/${this.bosPreFix}yitiji.js`;
    GET_DEEP_SEEK_BANNER = `${this.cloudBos}/deepseek/${this.bosPreFix}bannerNew.js`;
    GET_DEEP_SEEK_BAI_GE_YI_TI_JI = `${this.cloudBos}/deepseek/${this.bosPreFix}bageyitiji.js`;

    // 千帆开发者中心
    GET_QIANFAN_DEV_CENTER_BANNER = `${this.cloudBos}/qianfan_dev-center_home/${this.bosPreFix}banner.js`;
    GET_QIANFAN_DEV_CENTER_MODEL_SERVICE = `${this.cloudBos}/qianfan_dev-center_home/${this.bosPreFix}modelService.js`;
    GET_QIANFAN_DEV_CENTER_SCENE_COMPONENT = `${this.cloudBos}/qianfan_dev-center_home/${this.bosPreFix}sceneComponent.js`;
    GET_QIANFAN_DEV_CENTER_TOOLS = `${this.cloudBos}/qianfan_dev-center_home/${this.bosPreFix}tools.js`;
    GET_QIANFAN_DEV_CENTER_BEST_PRACTICE = `${this.cloudBos}/qianfan_dev-center_home/${this.bosPreFix}bestPractice.js`;
    GET_QIANFAN_DEV_CENTER_VIDEOS = `${this.cloudBos}/qianfan_dev-center_home/${this.bosPreFix}videos.js`;
    GET_QIANFAN_DEV_CENTER_LOGOS = `${this.cloudBos}/qianfan_dev-center_home/${this.bosPreFix}logos.js`;
    GET_QIANFAN_DEV_CENTER_JOIN_US = `${this.cloudBos}/qianfan_dev-center_home/${this.bosPreFix}joinUs.js`;

    get GET_LS_FLAVOR_LIST() {
        // 返回 API 请求的路径
        return `${this.cloudUrl}/api/calculator/ls/flavor`;
    }

    get GET_LS_PRICE() {
        return `${this.cloudUrl}/api/calculator/ls/price`;
    }

    // ai加速器活动2024-活动1学习进度接口
    get GET_AI_ACCELERATE_2024_CAMPAIGN1_PROGRESS() {
        return '/api/portal/partner/activity/learning_process/:campaignId';
    }
    // ai加速器活动2024-活动3查看邀请人数
    get GET_AI_ACCELERATE_2024_CAMPAIGN3_INVITE_TOTAL() {
        return '/api/portal/partner/activity/invite_total/:campaignId';
    }
    // ai加速器活动2024-活动3邀请
    get POST_AI_ACCELERATE_2024_CAMPAIGN3_INVITE() {
        return '/api/portal/partner/activity_invite';
    }
    // ai加速器活动2024-活动3助力状态
    get GET_AI_ACCELERATE_2024_CAMPAIGN3_HELPED() {
        return '/api/portal/partner/activity_invite/help_status/:campaignId';
    }
    // ai加速器活动2024-活动4获取总参与人数
    get GET_AI_ACCELERATE_2024_CAMPAIGN4_TOTAL() {
        return '/api/portal/partner/activity/register_total/:campaignId';
    }
    // ai加速器活动2024-活动4获取最近10条参与人手机
    get GET_AI_ACCELERATE_2024_CAMPAIGN4_RECENT() {
        return '/api/portal/partner/activity/recently_registered/:campaignId';
    }
    // ai加速器活动2024-活动4获取调用量前20名
    get GET_AI_ACCELERATE_2024_CAMPAIGN4_TOP20() {
        return '/api/portal/partner/activity/qianfan_invocation_rank';
    }

    get GET_BES_SLOT() {
        return `${this.cloudUrl}/api/calculator/bes/module/listWithSlot`;
    }

    get GET_BES_LOGSTASH_SLOT() {
        return `${this.cloudUrl}/api/calculator/logstash/module/listWithSlot`;
    }

    get GET_BES_PRICE() {
        return `${this.cloudUrl}/api/calculator/getPrice/batch`;
    }

    get POST_SCENE_SEARCH() {
        return `${this.portal}/api/case_center/search`;
    }
    get POST_CASE_CENTER() {
        return `${this.portal}/api/case_center/scene_industry_data`;
    }
    GET_CASE_BANNER = `${this.cloudBos}/case/${this.bosPreFix}banner.js`;
    GET_CASE_INNOVATIVE_VIDEO = `${this.cloudBos}/case/${this.bosPreFix}innovativeVideo.js`;

    // CDS 价格计算器 快照询价
    POST_CDS_SNAPSHOT_PRICE = `${this.cloudUrl}/api/calculator/snapshot/price`;
    // CDS 价格计算器 通用存储容量包询价
    POST_CDS_GSCP_PRICE = `${this.cloudUrl}/api/calculator/gscp/price`;
    // CDS 价格计算器 专属集群询价
    POST_CDS_DEDICATED_PRICE = `${this.cloudUrl}/api/calculator/dedicated/price`;
    // CDS 价格计算器 查询专属集群容量
    POST_CDS_CAPACITY = `${this.cloudUrl}/api/calculator/dedicated/capacity`;
    // CDS 价格计算器 获取专属集群类型
    POST_CLUSTER_TYPE = `${this.cloudUrl}/api/calculator/dedicated/resource`;
    // 最佳实践文档-左侧类别列表
    GET_BEST_PRACTICE_TYPE = `${this.portal}/api/doc/best_practice/type`;
    // 最佳实践文档-右侧摘要列表
    GET_BEST_PRACTICE_LIST_ALL= `${this.portal}/api/doc/best_practice/list`;
    GET_BEST_PRACTICE_LIST= `${this.portal}/api/doc/best_practice/list/:typeId`;
}
