/**
 * @file 公共的获取数据的方法
 */

import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {logger} from 'server/utils/logger';

// 获取cms页面数据
type GetCmsPageDataParams = {path?: string, pageId?: string};
export async function getCmsPageData<T>(params: GetCmsPageDataParams) {
    let data: T = null;
    try {
        logger.info({msg: '获取 CMS 页面数据', params});
        const res = await netService.get<GetCmsPageDataParams, T>(urlConst.GET_CMS_PAGE_DATA, params);
        data = res.result;
    } catch (err) {
        logger.error({msg: '获取 CMS 页面数据失败', params, err});
    }
    return data;
}
