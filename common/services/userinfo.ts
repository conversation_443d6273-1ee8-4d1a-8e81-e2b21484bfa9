/**
 * @file 用户信息
 * <AUTHOR>
 */

import {Injectable} from '@baidu/bce-decorators';

type HandlerFn = (info: UserinfoObj) => void;

@Injectable()
export class UUserService {
    private _userinfo: UserinfoObj = null;

    private handlers: HandlerFn[] = [];

    get userinfo() {
        return this._userinfo;
    }
    subscribe(fn: HandlerFn) {
        this.handlers.push(fn);
    }

    dispatch(data: UserinfoObj) {
        this._userinfo = data;
        this.handlers.forEach(item => item(data));
    }
}
