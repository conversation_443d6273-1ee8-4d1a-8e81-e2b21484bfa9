import {AxiosResponse, AxiosRequestConfig} from 'axios';
import {Injectable} from '@baidu/bce-decorators';
import {ResponseObj} from '@baidu/bce-services';

@Injectable()
export class UNetInterceptor implements UInterceptor {
    async response(config: AxiosResponse<ResponseObj<never>>) {
        return config;
    }
    async request(config: AxiosRequestConfig) {
        return config;
    }
}
export interface UInterceptor {
    response: (response: AxiosResponse) => Promise<AxiosResponse>;
    request: (config: AxiosRequestConfig) => Promise<AxiosRequestConfig>;
}
