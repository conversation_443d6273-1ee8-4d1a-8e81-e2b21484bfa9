import {Injectable} from '@baidu/bce-decorators';
import {NextIncomingMessage} from 'next/dist/server/request-meta';

@Injectable()
export class UEnvService {
    private req: NextIncomingMessage;

    init(req: NextIncomingMessage) {
        this.req = req;
    }

    get isProd() {
        return process.env.NODE_ENV === 'production';
    }

    // 为什么这样判断？
    // 本地有可能访问沙盒的数据，需要模仿沙盒的环境，所以不用 process.env.NODE_ENV 去做判断
    get isDev() {
        return !this.isProdOnline && !this.isProdSandbox;
    }

    // 正式线上环境
    get isProdOnline(): boolean {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return IS_BUILD_ONLINE === 'true';
    }

    get isProdSandbox(): boolean {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return IS_BUILD_ONLINE === 'false';
    }


    get isNotProductOnlineAndGetSourceFromCmsSandbox() {
        if (this.isServer) {
            return !this.isProdOnline && this.req && this.req.url.includes('source=cms_sandbox');
        } else {
            return !this.isProdOnline && location.href.includes('source=cms_sandbox');
        }
    }

    get isClient() {
        try {
            return Boolean(window);
        } catch (e) {
            return false;
        }
    }

    get isServer() {
        return !this.isClient;
    }
}
