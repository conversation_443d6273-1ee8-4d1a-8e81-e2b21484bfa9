import {UNetService} from '@baidu/bce-services';
import {Injectable} from '@baidu/bce-decorators';
import {NextIncomingMessage} from 'next/dist/server/request-meta';
import {UNetInterceptor} from '../services/netInterceptor';
import {UEnvService} from '../services/env';

@Injectable()
export class USetUp {
    constructor(
        private env: UEnvService,
        private uNetInterceptor: UNetInterceptor
    ) {}
    initServer(req: NextIncomingMessage) {
        // 主要是source=cms_sandbox
        this.env.init(req);
    }
    initClient() {
        this.initNet();
    }
    initNet() {
        UNetService.setInterceptors([
            this.uNetInterceptor,
        ]);
        UNetService.setWithCredentials(true);
    }
}
