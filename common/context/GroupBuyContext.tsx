import {createContext, useCallback, useEffect, useMemo, useState, useRef} from 'react';
import {Form, FormInstance} from 'antd';
import Big from 'big.js';
import {useStateRef} from '@baidu/bce-hooks';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {getAntiParam, genGroupBuySignAuth} from '@common/helper/page';
import {ProductPriceItem, ShowConfigItem, ShowConfigData} from '@common/interface/groupBuy';

interface GroupBuyCtx {
    prices: ProductPriceItem[];
    totalPrices: ProductPriceItem[];
    pricesLoading: boolean;
    handlePriceLoading: (v: boolean) => void;
    configs: any[];
    handleConfigsChange: (config: {
        id: number;
        allParamsReady: boolean;
        data: any[];
    }) => void;
    showConfigs: ShowConfigData[][];
    handleShowConfigsChange: (showConfig: ShowConfigItem) => void;
    defaultConfigs: any[];
    phoneNumber: string;
    handlePhoneNumberChange: (obj: {
        phone: string;
        code: string;
    }) => void;

    allForm: FormInstance<any>;
}

export const GroupBuyContext = createContext<GroupBuyCtx>({
    prices: [],
    pricesLoading: false,
    handlePriceLoading: null,
    totalPrices: [],
    configs: [],
    handleConfigsChange: null,
    showConfigs: [],
    handleShowConfigsChange: null,
    defaultConfigs: [],
    phoneNumber: '',
    handlePhoneNumberChange: null,

    allForm: null,
});

export const GroupBuyContextProvider = (props: {
    children: React.ReactNode;
    defaultConfigs: any[];
}) => {

    // 所有产品组件需要校验参数的form
    const [allForm] = Form.useForm();

    // 首页拿到的数据作为默认值
    const {defaultConfigs} = props;
    // 询价返回的产品单价
    const [prices, setPrices] = useState<ProductPriceItem[]>([]);

    /**
     * 部分产品询价参数需要等其他接口返回，configs会多次变化造成多次询价，价格闪动问题，
     * 因此构造paramsReadyinfos，等参数都ready了再询价
     */
    const [paramsReadyinfos, setParamsReadyinfos, paramsReadyinfosRef] = useStateRef<boolean[]>([]);
    const canGetPrices = useMemo(() => paramsReadyinfos.every(item => item), [paramsReadyinfos]);

    const [configs, setConfigs, configsRef] = useStateRef<any[]>([]);
    const [showConfigs, setShowConfigs, showConfigsRef] = useStateRef<ShowConfigData[][]>([]);
    const [pricesLoading, setPriceLoading] = useState(true);
    const debounceRef = useRef(null);
    const [phoneNumber, setPhoneNumber] = useState<string>('');

    const handlePriceLoading = (v: boolean) => setPriceLoading(v);

    const handlePhoneNumberChange = useCallback((obj: {
        phone: string;
        code: string;
    }) => {
        setPhoneNumber(obj?.phone);
    }, []);

    // 每个产品 算上数量的价格
    const totalPrices = useMemo(() => {
        if (pricesLoading) {
            return [];
        }
        const priceWithCount = prices.map(price => {
            const count = configs.flat().find(config => config.configId === price.configId)?.count || 1;
            return {
                ...price,
                originPrice: new Big(price.originPrice || 0).times(count).toNumber(),
                currentPrice: new Big(price.currentPrice || 0).times(count).toNumber(),
                priceDetail: [],
            };
        });

        // bcc的特殊逻辑 把bcc自身的价格加上绑定产品的价格
        const bccGroups = priceWithCount.filter(item => item.parentServiceType === 'BCC');
        const pricesWithoutBcc = priceWithCount.filter(item => item.parentServiceType !== 'BCC');
        if (bccGroups.length > 0) {
            const bccPriceObj = priceWithCount.find(item => item.serviceType === 'BCC');
            const {originPrice, currentPrice} = bccGroups.reduce((pre, cur) => {
                return {
                    originPrice: new Big(pre.originPrice || 0).add(cur.originPrice).toNumber(),
                    currentPrice: new Big(pre.currentPrice || 0).add(cur.currentPrice).toNumber(),
                };
            }, {originPrice: 0, currentPrice: 0});
            // bcc需要展示绑定商品的价格，所以构造priceDetail
            // 为了逻辑的通用性，这里的originPrice和currentPrice代表的是整个bcc的价格（包括绑定商品）
            const newBccPriceObj = {
                ...bccPriceObj,
                originPrice,
                currentPrice,
                priceDetail: [...bccGroups],
            };
            return [...pricesWithoutBcc, newBccPriceObj];
        }
        return priceWithCount;

    }, [configs, prices, pricesLoading]);

    // 维护询价参数
    const handleConfigsChange = useCallback((config: {
        id: number;
        allParamsReady: boolean;
        data: any[];
    }) => {
        const {id, data, allParamsReady} = config;
        // id是组件遍历时的索引，对应在询价参数中的顺序
        const newConfigs = [...configsRef.current];
        newConfigs[id] = data;
        setConfigs(newConfigs);
        const newParamsReadyinfos = [...paramsReadyinfosRef.current];
        newParamsReadyinfos[id] = allParamsReady;
        setParamsReadyinfos(newParamsReadyinfos);
    }, [configsRef, paramsReadyinfosRef, setConfigs, setParamsReadyinfos]);

    // 维护展示参数
    const handleShowConfigsChange = useCallback((showConfig: ShowConfigItem) => {
        const newShowConfigs = [...showConfigsRef.current];
        const {id, data} = showConfig;
        newShowConfigs[id] = data;
        setShowConfigs(newShowConfigs);

    }, [setShowConfigs, showConfigsRef]);

    const getPrices = useCallback(async () => {
        setPriceLoading(true);
        // 询价下单参数需要拍平一下
        const newConfigs = configs.flat();
        newConfigs.forEach(item => {
            item.signAuth = genGroupBuySignAuth(item);
        });
        netService.post<any, ProductPriceItem[], null>(
            urlConst.GROUP_BUY_PRICES,
            newConfigs,
            null,
            {
                headers: {
                    'Acs-Token': await getAntiParam(),
                },
            }
        ).then(res => {
            if (res.success) {
                // 返回的result，可能会有null
                if (res.result.every(item => item)) {
                    setPriceLoading(false);
                    setPrices(res.result);
                }
            }
        });
    }, [configs]);

    useEffect(() => {
        if (configs.length < 1) {
            return;
        }
        if (!canGetPrices) {
            return;
        }
        if (debounceRef.current) {
            clearTimeout(debounceRef.current);
            debounceRef.current = null;
        }
        debounceRef.current = setTimeout(() => {
            getPrices();
        }, 100);
    }, [canGetPrices, configs, getPrices]);

    return (
        <GroupBuyContext.Provider
            value={{
                prices,
                configs,
                totalPrices,
                pricesLoading,
                handlePriceLoading,
                handleConfigsChange,
                showConfigs,
                handleShowConfigsChange,
                defaultConfigs,
                phoneNumber,
                handlePhoneNumberChange,

                allForm,
            }}
        >
            <Form
                form={allForm}
                autoComplete="off"
            >
                {props.children}
            </Form>
        </GroupBuyContext.Provider>
    );
};
