/**
 * @file 获取和设置页面滚动的位置，并且绑定动画
 */

import {MutableRefObject, useRef} from 'react';
import {getElementTop, getScrollTop, setScrollTop} from '@common/helper/page';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {isFunction} from '@baidu/bce-helper';
import {throttle} from 'lodash';

/**
 * 获取和设置页面滚动的位置，并且绑定动画
 * @param option itemSelector 需要绑定的元素, throttleDelay 节流的 delay 时长, duration 滚动动画的时间, defaultIndex 默认位置, fixTop 修正到顶部的距离, scrollUpHeaderFixTop向上滚动时位置修正
 * @returns current 当前位置的index, setCurrent 设置当前位置的index
 */
export const useScrollBind: (option: {
    itemSelector: string;
    throttleDelay?: number;
    duration?: number;
    defaultIndex?: number;
    fixTop?: number | (() => number);
    defaultStep?: number; // 默认滚动步长，设置该属性后，动画的时间会随着滚动距离变化
    scrollUpHeaderFixTop?: number; // 页面向上滚动时锚点位置修正(该场景需要将throttleDelay时长设置为0)
    getItemTopList?: () => number[]; // 每个模块的临界值
    findCurrentIndex?: (topList: number[]) => number; // 自定义滚动时当前index
}) => [
    number,
    (i: number) => void,
    MutableRefObject<number>,
] = option => {
    const {
        itemSelector, throttleDelay = 40, duration = 300, defaultIndex = 0, fixTop = 0, scrollUpHeaderFixTop = 0,
        getItemTopList, findCurrentIndex, defaultStep,
    } = option;
    const [current, setCurrentIndex, curentRef] = useStateRef(defaultIndex);
    const timerRef = useRef(null);
    const scrollTopPrev = useRef(0);
    const getTopList = () => {
        if (getItemTopList) {
            const itemTopList = getItemTopList();
            return itemTopList;
        }
        const contentItems = document.querySelectorAll(itemSelector);
        const topList = Array.prototype.map.call(contentItems, (ele: HTMLElement) => {
            const ft: number = (isFunction(fixTop) ? (fixTop as (() => number))() : fixTop as number);
            return Math.floor(getElementTop(ele) + ft);
        });
        return topList as number[];
    };
    useOnMount(() => {
        const scrollHandler = throttle(() => {
            const scrollTop = Math.ceil(getScrollTop());
            let temp = 0;
            if (findCurrentIndex) {
                temp = findCurrentIndex(getTopList());
            } else {
                // 若页面向上滚动，且包含导航高度修正 => currentIndex的位置需要去除导航高度
                const scrollUpFixTop = scrollTop < scrollTopPrev.current ? scrollUpHeaderFixTop : 0;
                getTopList().forEach((top, i) => {
                    if (scrollTop >= top - scrollUpFixTop || i === 0) {
                        temp = i;
                    }
                });
            }
            setCurrentIndex(temp);
            scrollTopPrev.current = scrollTop;
        }, throttleDelay);
        window.addEventListener('scroll', scrollHandler);
        return () => {
            window.removeEventListener('scroll', scrollHandler);
        };
    });
    const setCurrent = (i: number) => {
        if (timerRef.current) {
            window.cancelAnimationFrame(timerRef.current);
            timerRef.current = null;
        }
        const topList = getTopList();
        const distance = topList[i] - getScrollTop();
        // 当传入了默认步长时，使用默认的；
        // 否则默认用户显示器刷新率为 60 Hz, 步长 = 总距离 / 总帧数
        const step = defaultStep ? (distance > 0 ? defaultStep : -defaultStep) : distance / (duration / (1000 / 60));
        // 页面上滚, 锚点位置需去除导航高度
        const headerDistanceFix = distance < 0 ? scrollUpHeaderFixTop : 0;

        const scrollToTarget = () => {
            const scrollTop = getScrollTop();
            // 内容视口
            if (Math.abs(topList[i] - scrollTop) < Math.abs(step)) {
                setScrollTop(topList[i] - headerDistanceFix);
                window.cancelAnimationFrame(timerRef.current);
                return;
            }
            setScrollTop(step + scrollTop);
            timerRef.current = window.requestAnimationFrame(scrollToTarget);
        };

        timerRef.current = window.requestAnimationFrame(scrollToTarget);
    };
    return [
        current,
        setCurrent,
        curentRef,
    ];
};
