import {useEffect, useState} from 'react';

// 服务端初始化时返回0，首屏渲染可能会有问题
// 客户端最好使用useEffect + useState处理screenWidth的关联值变化，而不是useMemo
export function useScreenWidth() {
    const [width, setWidth] = useState(() => {
        if (typeof window === 'undefined') {
            return 0;
        } else {
            return window.innerWidth;
        }
    });

    useEffect(() => {
        const handleResize = () => setWidth(window.innerWidth);
        handleResize();
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return width;
}
