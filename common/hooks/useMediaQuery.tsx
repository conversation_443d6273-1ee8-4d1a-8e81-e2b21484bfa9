/**
 * @description useMediaQuery, 支持服务端渲染，监听媒体查询，根据屏幕宽度返回布尔值
 * @param query 媒体查询字符串
 * @param defaultValue 默认值
 * @returns {boolean} 是否匹配媒体查询
 * @example const isMobile = useMediaQuery('(max-width: 767px)');
 * @example const isTablet = useMediaQuery('(min-width: 768px) and (max-width: 1199px)');
 */

import {useEffect, useLayoutEffect, useState} from 'react';

type UseMediaQueryOptions = {
    defaultValue?: boolean;
    initializeWithValue?: boolean;
};
const IS_SERVER = typeof window === 'undefined';

export const useIsomorphicLayoutEffect = IS_SERVER ? useLayoutEffect : useEffect;

export function useMediaQuery(
    query: string,
    {
        defaultValue = false,
        initializeWithValue = false,
    }: UseMediaQueryOptions = {}
): boolean {
    const getMatches = (query: string): boolean => {
        if (IS_SERVER) {
            return defaultValue;
        }
        return window.matchMedia(query).matches;
    };

    const [matches, setMatches] = useState<boolean>(initializeWithValue);

    function handleChange() {
        setMatches(getMatches(query));
    }

    useIsomorphicLayoutEffect(() => {
        const matchMedia = window.matchMedia(query);
        handleChange();

        matchMedia.addEventListener('change', handleChange);
        return () => {
            matchMedia.removeEventListener('change', handleChange);
        };
    }, [query]);

    return matches;
}
