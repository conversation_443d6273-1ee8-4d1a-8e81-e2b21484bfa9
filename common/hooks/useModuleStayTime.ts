/**
 * @file 模块停留时长上报
 */

import {useOnMount} from '@baidu/bce-hooks';
import {sendMonitor} from '@common/helper/page';
import {MutableRefObject, useEffect, useRef} from 'react';

export const useModuleStayTime = (option: {
    ref: MutableRefObject<HTMLElement>;
    category: string;
    name: string;
}) => {
    const {ref, category, name} = option;
    const trackDataRef = useRef({category, name});
    useEffect(() => {
        trackDataRef.current = {category, name};
    }, [category, name]);
    useOnMount(() => {
        if (!ref.current) {
            throw new Error('ref.current is null');
        }
        const height = ref.current.offsetHeight;
        let time = 0;
        let timer: NodeJS.Timeout = null;
        let enterThreshold = 0.7; // 进入阈值
        let leaveThreshold = 0.4; // 离开阈值
        if (height > window.innerHeight) { // 模块高度大于屏幕高度
            enterThreshold = window.innerHeight * 0.7 / height;
            leaveThreshold = window.innerHeight * 0.4 / height;
        }

        // 清空计时器
        const clearTimer = () => {
            if (timer) {
                clearInterval(timer);
                timer = null;
                time = 0;
            }
        };

        // 上报停留时长
        const sendStayTime = () => {
            sendMonitor({
                ...trackDataRef.current,
                value: String(time),
                action: 'staytime',
            });
            clearTimer();
        };

        const observer = new IntersectionObserver(entries => {
            const intersectionRatio = entries[0].intersectionRatio;
            // 离开的时候上报
            if (intersectionRatio < leaveThreshold) {
                if (time >= 1) {
                    sendStayTime();
                } else {
                    clearTimer();
                }
            }
            // 出现的时候开始计时，并且超过600s之后，直接上报
            if (intersectionRatio >= enterThreshold) {
                clearTimer();
                timer = setInterval(() => {
                    time++;
                    if (time >= 600) {
                        sendStayTime();
                    }
                }, 1000);
            }
        }, {
            threshold: [enterThreshold, leaveThreshold],
        });
        observer.observe(ref.current);
        return () => {
            observer.unobserve(ref.current);
        };
    });
};
