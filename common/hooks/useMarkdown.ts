/**
 * @file markdown有些效果要js处理
 * <AUTHOR>
 */

import {useEffect} from 'react';

export const useMarkdown = (mdSelector: string, deps: any[] = []) => {
    useEffect(() => {
        const mds: NodeListOf<HTMLTableElement> = document.querySelectorAll(mdSelector);
        mds.forEach(md => {
            const tables = md.querySelectorAll('table');
            const mdW = md.offsetWidth;
            tables.forEach(table => {
                if (table.offsetWidth > mdW) {
                    table.classList.add('block-table');
                }
            });
        });
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, deps);
};
