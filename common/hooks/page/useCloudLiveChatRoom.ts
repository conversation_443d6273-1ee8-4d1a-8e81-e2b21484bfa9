/**
 * @file 官网直播聊天室hook
 */

import {ChangeEventHandler, useCallback, useEffect, useRef, useState} from 'react';
import IM from '@baidu/im-jssdk/dist/im.min';
import {getCookie} from '@baidu/bce-helper';
import {TextAreaRef} from 'antd/lib/input/TextArea';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {useCheckUserinfo} from '@common/hooks/useCheckUserinfo';
import {Ioc} from '@baidu/bce-decorators';
import {UEnvService} from '@common/services/env';
import {throttle} from 'lodash';
import {CloudLiveObj} from '@common/interface/page';
import {UUserService} from '@common/services/userinfo';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {getAntiParam} from '@common/helper/page';

const env = Ioc(UEnvService);

interface MsgObj {
    content: {
        text: {
            name: string;
            'content_body': {
                text: string;
            };
            type: string;
        };
    };
    msgid: string;
}

type MsgParamsObj = {
    imRoomId: number;
    msg: string;
    passId?: string;
};

const msgMaxLength = 50;

const encryptName = (name: string) => {
    const len = Math.min(name.length - 1, 5);
    return [name[0], ...Array.from({length: len}).fill('*')].join('') + '：';
};

export const useCloudLiveChatRoom = (liveDetail: CloudLiveObj, isShow?: boolean) => {
    const {imRoomId} = liveDetail;
    const [msg, setMsg] = useState('');
    const currentListRef = useRef<MsgObj[]>([]);
    const [messageList, setMessageList] = useState<MsgObj[]>([]);
    const [newMsgLength, setNewMsgLength, newMsgLengthRef] = useStateRef<number>(0);
    const [reachMaxLength, setReachMaxLength] = useState(false);
    const [userInfo, , loginHandler, , userInfoRef] = useCheckUserinfo({
        isCheckUserType: true,
        isImmediately: false,
    });
    const clientRef = useRef(null);
    const textareaRef = useRef<TextAreaRef>(null);
    const messageListRef = useRef<HTMLDivElement>(null);
    const isShowRef = useRef(isShow);

    // input消息
    const handleMsgChange: ChangeEventHandler<HTMLTextAreaElement> = e => {
        const val = e.target.value;
        setMsg(val);
        if (val.length >= msgMaxLength) {
            !reachMaxLength && setReachMaxLength(true);
            setTimeout(() => {
                setReachMaxLength(false);
            }, 1000);
        }
    };

    // 点击输入框区域
    const handleClickInputArea = () => {
        loginHandler(info => {
            if (info.hasLogin) {
                // 如果是UC登录，并且没有绑定过pass-id，进行绑定
                const cookie = getCookie() as {[props: string]: string};
                if (cookie['bceAccountName']?.includes('UC') && !cookie[`pass-id_${cookie['__cas__id__285']}`]) {
                    netService.post(urlConst.POST_UC_BIND_PASS);
                }
                textareaRef.current?.focus();
            }
        });
    };

    // 消息列表回到底部
    const messageListToBottom = () => {
        messageListRef.current && setTimeout(() => {
            messageListRef.current.scrollTo({top: messageListRef.current.scrollHeight, behavior: 'smooth'});
        }, 0);
    };

    const postMessage = async (params: MsgParamsObj) => {
        // 调用后端接口发送消息
        netService.post(urlConst.POST_CLOUD_LIVE_MESSAGE, params, null, {
            headers: {
                'Acs-Token': await getAntiParam(),
            },
        });
    };

    // 发送消息
    const handleSendMessage = () => {
        if (!msg.trim()) {
            return;
        }
        const params: MsgParamsObj = {
            imRoomId,
            msg,
        };
        const cookie = getCookie() as {[props: string]: string};
        if (cookie['bceAccountName']?.includes('UC')) {
            const passId = cookie[`pass-id_${cookie['__cas__id__285']}`];
            if (passId) {
                params.passId = passId;
            }
        }
        imRoomId && postMessage(params);
        const infoItem: MsgObj = {
            content: {
                text: {
                    type: '0',
                    'content_body': {
                        text: msg,
                    },
                    name: userInfo.displayName, // 自己的用户名
                },
            },
            msgid: `${new Date().getTime()}`,
        };
        setMsg('');
        currentListRef.current.push(infoItem);
        setMessageList([...currentListRef.current]);
        messageListToBottom();
    };

    // 初始化im
    const initIm = useCallback((info: UserinfoObj) => {
        if (clientRef.current || !imRoomId) {
            return;
        }
        clientRef.current = new IM({
            appId: ********, // 移动开发者中心申请的APPID
            deviceType: 3, // web：3
            accountType: info?.hasLogin && (getCookie('bceAccountName') as string)?.includes('PASSPORT') ? 7 : 10, // 登陆态：7 未登陆：10 仅柠檬未登录传6
            appVersion: 'pc', // 默认写死
            sdkVersion: '7450001', // 默认写死
            BAIDUID: getCookie('BAIDUID') as string, // cookie：BAIDUID
            domain: env.isDev ? 'http://rd-im-server.bcc-szth.baidu.com:8111' : 'https://pim.baidu.com',
            lcpDomain: env.isDev ? 'http://rd-im-server.bcc-szth.baidu.com:8089' : 'https://pim.baidu.com',
        }, {
            chat: true, // 默认为true， 支持私信功能
            session: false, // 是否需要集成会话列表功能
            signaling: false, // 是否需要集成信令功能
            roomChat: true, // 是否需要集成聊天室功能
        });
        clientRef.current.roomCount = -30;
        clientRef.current.onConnect(() => {
            clientRef.current.enterLiveChatRoom({
                'mcast_id': Number(imRoomId),
            }).then((res: any) => {
                if (res.err_code) {
                    console.info('进入房间出错', res.msg);
                    return;
                }
                // 初始化部分聊天信息
                clientRef.current.initRoomChat().then((chatInfoRes: any) => {
                    currentListRef.current = chatInfoRes.list.filter((item: MsgObj) => item.content.text.type === '0');
                    setMessageList([...currentListRef.current]);
                    messageListToBottom();
                }).catch((err: any) => {
                    console.info('初始化房间出错', err);
                });
            }).catch((err: any) => console.info('接口出错', err?.data?.error_msg));
        });
        // 接收消息
        clientRef.current.onRoomMessage((item: {normal: MsgObj[], system: any}) => {
            const normalInfo = item.normal;
            const systemInfo = item.system;
            systemInfo.forEach((systemItem: any) => {
                if (systemItem.content.text.type === '106') { // 删除某一条消息
                    const msgids = systemItem.content.text.data.msgids;
                    currentListRef.current = currentListRef.current.filter((item: any) => {
                        return msgids.indexOf(String(item.msgid)) === -1;
                    });
                    setMessageList([...currentListRef.current]);
                }
            });
            // 处理普通消息
            const filterNormalInfo = normalInfo.filter(item => (
                item.content.text.name !== userInfoRef.current.displayName
                && !currentListRef.current.find(msg => String(msg.msgid) === String(item.msgid))
            ));
            if (filterNormalInfo.length) {
                currentListRef.current = [...currentListRef.current, ...filterNormalInfo];
                if (messageListRef.current.scrollHeight > messageListRef.current.offsetHeight + messageListRef.current.scrollTop) {
                    setNewMsgLength(pre => pre + filterNormalInfo.length);
                } else {
                    messageListToBottom();
                }
                setMessageList([...currentListRef.current]);
            }
        });
    }, [imRoomId, setNewMsgLength, userInfoRef]);

    useEffect(() => {
        if (isShow && !isShowRef.current && !clientRef.current) {
            isShowRef.current = isShow;
            if (userInfo?.hasLogin) {
                initIm(userInfo);
            } else if (window.portalCommon) {
                window.portalCommon.userinfoInit(initIm);
            } else {
                Ioc(UUserService).subscribe(initIm);
            }
        }
    }, [initIm, isShow, userInfo]);

    useOnMount(() => {
        const handleLoginMessage = (e: MessageEvent<{loginSuccess: boolean}>) => {
            if (e.data && e.data.loginSuccess && (getCookie('bceAccountName') as string)?.includes('PASSPORT')) {
                window.location.reload();
            }
        };
        window.addEventListener('message', handleLoginMessage);
        window.addEventListener('beforeunload', () => {
            clientRef.current && clientRef.current.exitLiveChatRoom({'mcast_id': Number(imRoomId)});
        });
        const handleMessageListScroll = throttle(() => {
            if (newMsgLengthRef.current > 0) {
                const h = messageListRef.current.offsetHeight + messageListRef.current.scrollTop;
                let res = newMsgLengthRef.current;
                for (let i = newMsgLengthRef.current; i > 0; i--) {
                    const liEle: HTMLLIElement = messageListRef.current.querySelector(`li:nth-last-child(${i})`);
                    const msgLiDistance = liEle.offsetTop + liEle.offsetHeight;
                    if (h >= msgLiDistance) {
                        res = i - 1;
                    }
                }
                setNewMsgLength(res);
            }
        }, 40);
        messageListRef.current?.addEventListener('scroll', handleMessageListScroll);
        return () => {
            clientRef.current?.exitLiveChatRoom({'mcast_id': Number(imRoomId)}).then(() => {
                clientRef.current = null;
            });
            window.removeEventListener('message', handleLoginMessage);
            messageListRef.current?.removeEventListener('scroll', handleMessageListScroll);
        };
    });

    return {
        messageList,
        handleSendMessage,
        handleClickInputArea,
        handleMsgChange,
        userInfo,
        textareaRef,
        msg,
        encryptName,
        messageListRef,
        newMsgLength,
        messageListToBottom,
        msgMaxLength,
        reachMaxLength,
    };
};
