/* eslint-disable complexity */
// 用于验证表单提交后的接口返回 - 同v4 qualificationFun

// TODO: 201激活处理还没写
import {ResponseObj} from '@baidu/bce-services';
import {getLoginUrl, handleZh} from '@common/helper/page';
import {CmsSurveyResObj} from '@common/interface/cmsSurvey';
import {SummitResultDataObj} from '@common/interface/summit';
import {useState} from 'react';

export const useSurveyQualificationFb = () => {
    const [resultInfo, setResultInfo] = useState<SummitResultDataObj>(null); // 弹窗/返回提示信息

    const qualificationFun = ({response, successFun}: {
        response: ResponseObj<CmsSurveyResObj, null>;
        successFun: () => void;
        isSubmit?: boolean;
    }) => {
        if (response.success) {
            switch (response.result.code) {
                case 0:
                    successFun();
                    break;
                case 101:
                    setResultInfo({
                        type: 'warning',
                        title: handleZh('活动已过期'),
                        desc: response.result.message,
                    });
                    break;
                case 201:
                    // TODO: 未激活处理, handleActive() 这次用不到, 但是后续的需要确认这里的逻辑
                    setResultInfo({
                        type: 'warning',
                        title: handleZh('未激活'),
                        desc: handleZh('抱歉，您的账户未激活'),
                    });
                    break;
                case 202:
                    // AIGC 大赛报名问卷页面此处报错需要特殊文字, 活动结束后可以删除这一判断
                    if (location.pathname === '/survey/aigc-competition.html') {
                        setResultInfo({
                            type: 'warning',
                            title: handleZh('温馨提示'),
                            desc: '抱歉，您已经填过该调查问卷了。如需修改报名信息或有其他疑虑，请向*********************发送邮件。',
                            jumpUrl: '/',
                            btnText: handleZh('返回首页'),
                        });
                    } else {
                        setResultInfo({
                            type: 'warning',
                            title: handleZh('温馨提示'),
                            desc: handleZh('抱歉，您已经填过该调查问卷了'),
                            jumpUrl: '/',
                            btnText: handleZh('返回首页'),
                        });
                    }
                    break;
                case 204:
                    setResultInfo({
                        type: 'warning',
                        title: handleZh('未实名'),
                        desc: handleZh('对不起，您还未实名认证'),
                        jumpUrl: handleZh('https://console.bce.baidu.com/qualify/#/qualify/index'),
                        btnText: handleZh('前往实名认证'),
                        jumpTarget: '_blank',
                    });
                    break;
                case 200:
                    setResultInfo({
                        type: 'warning',
                        title: handleZh('未登录'),
                        desc: handleZh('抱歉，您还没有登录'),
                        jumpUrl: getLoginUrl(),
                        btnText: handleZh('马上登录'),
                    });
                    break;
                case 230:
                    setResultInfo({
                        type: 'warning',
                        title: handleZh('温馨提示'),
                        desc: handleZh('验证失败'),
                    });
                    break;
                case 231:
                    setResultInfo({
                        type: 'warning',
                        title: handleZh('未企业实名'),
                        desc: handleZh('仅限企业实名用户，请完成企业实名后再填写表单'),
                        jumpUrl: handleZh('https://console.bce.baidu.com/qualify/#/qualify/choices/company?state=restart'),
                        btnText: handleZh('去企业实名'),
                        jumpTarget: '_blank',
                    });
                    break;
                case 232:
                    setResultInfo({
                        type: 'warning',
                        title: handleZh('未企业实名'),
                        desc: handleZh('仅限企业实名用户，请完成企业实名后再填写表单'),
                        jumpUrl: handleZh('https://console.bce.baidu.com/qualify/#/qualify/change/company'),
                        btnText: handleZh('去企业实名'),
                        jumpTarget: '_blank',
                    });
                    break;
                case 233: {
                    const encodeCurrentUrl = encodeURIComponent(location.href.replace(location.hash, '')) + location.hash;
                    setResultInfo({
                        type: 'warning',
                        title: handleZh('不是主账号'),
                        desc: handleZh('仅限主账号填写，请切换到主账号登录'),
                        jumpUrl: `https://login.bce.baidu.com/?login_type=uc&from=leads:qianfan&redirect=${encodeCurrentUrl}`,
                        btnText: handleZh('去登录'),
                    });
                    break;
                }
                case 234:
                    setResultInfo({
                        type: 'info',
                        title: handleZh('审核中'),
                        desc: handleZh('您的申请目前在审核中，审核通过后会通知您，请耐心等待'),
                        // 目前只有千帆表单有审核中，以后要是有其他产品表单有这个状态，产品页链接应当由后端返回
                        jumpUrl: 'https://cloud.baidu.com/product/wenxinworkshop',
                        btnText: handleZh('返回产品页'),
                    });
                    break;
                case 235:
                    setResultInfo({
                        type: 'success',
                        title: handleZh('审核通过'),
                        desc: handleZh('您已申请通过，请前往控制台使用'),
                        jumpUrl: handleZh('https://console.bce.baidu.com/ai/?_=#/ai/wenxinworkshop/overview/index'),
                        btnText: handleZh('去使用'),
                    });
                    break;
                case 236:
                    setResultInfo({
                        type: 'info',
                        title: handleZh('申请未开放'),
                        desc: handleZh('目前表单还未开放申请，请耐心等待'),
                    });
                    break;
                case 301:
                    setResultInfo({
                        type: 'warning',
                        title: handleZh('温馨提示'),
                        desc: response.result.message,
                    });
                    break;
                case 303:
                    setResultInfo({
                        type: 'warning',
                        title: handleZh('温馨提示'),
                        desc: response.result.message,
                    });
                    break;
                case 216601:
                    setResultInfo({
                        type: 'error',
                        title: handleZh('温馨提示'),
                        desc: handleZh('身份证号和姓名匹配失败'),
                    });
                    break;
                case 222353:
                    setResultInfo({
                        type: 'error',
                        title: handleZh('温馨提示'),
                        desc: handleZh('身份证信息不正确'),
                    });
                    break;
                default:
                    setResultInfo({
                        type: 'error',
                        title: handleZh('网络问题'),
                        desc: handleZh('网络问题，请稍后重试'),
                        jumpUrl: '/',
                        btnText: handleZh('返回首页'),
                    });
            }
        // 按照现在的写法玉门关校验会在上一步useCmsSurvey中完成，故省略
        // }
        // else if (isSubmit && response.code === 'yumenguan check exception') {
        //     // 如果是请求的提交表单的接口，并且玉门关校验没通过
        //     $('#captcha').show();
        //     mmm.initVcode();
        } else {
            setResultInfo({
                type: 'error',
                title: handleZh('网络问题'),
                desc: handleZh('网络问题，请稍后重试'),
                jumpUrl: '/',
                btnText: handleZh('返回首页'),
            });
        }
    };

    return {resultInfo, setResultInfo, qualificationFun};
};
