import {useState} from 'react';
import {message} from 'antd';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {useCheckActivateUserInfo} from './useCheckActivateUser';

type CheckStatusCallbackFn = (trialType: boolean) => void;
type SendTrialRequestCallbackFn = (trialType: boolean) => void;

type QianFanTrialStatusReturn = [
    UserinfoObj,
    boolean,
    (cb?: CheckStatusCallbackFn) => void,
    (cb?: SendTrialRequestCallbackFn) => void,
];
/**
 * 千帆试用相关功能接口
 */
export const useQianfanTrialStatus = (): QianFanTrialStatusReturn => {
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({}); // userinfo.hasLogin: 登录状态
    const [trialType, setTrialType] = useState<boolean>(null); // 开通状态
    // const [isTrialEnd, setIsTrialEnd] = useState(false); // 试用结束状态

    // 确认千帆开通状态
    const checkQianfanOpenStatus = (cb?: CheckStatusCallbackFn) => {
        // 登录成功后，检查千帆开通状态
        verifyHandler().then(() => {
            // 请求试用开通状态
            netService.get<null, boolean>(urlConst.GET_QIANFAN_TRIAL_STATUS).then(res => {
                setTrialType(res.result);
                // 判断试用开通，执行回调逻辑
                cb && cb(res.result);
            }).catch(() => {
                setTrialType(false);
            });
        });
    };

    // 发送千帆试用开通请求
    const sendQianfanTrialRequest = (cb?: SendTrialRequestCallbackFn) => {
        netService.post<null, boolean>(urlConst.POST_QIANFAN_TRIAL_APPLY).then(res => {
            if (res.success) {
                setTrialType(true);
                // 开通成功，执行回调逻辑
                cb && cb(res.result);
            } else {
                message.error('开通失败，请稍后重试');
            }
        }).catch(err => {
            setTrialType(false);
            console.error(`开通失败，请重试${err}`);
        });
    };

    return [userinfo, trialType, checkQianfanOpenStatus, sendQianfanTrialRequest];
};
