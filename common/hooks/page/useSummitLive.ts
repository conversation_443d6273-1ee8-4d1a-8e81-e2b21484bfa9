/**
 * @file 峰会直播流公共hook
 * @desc safari直播使用cyberPlayer 4.3.3.1版本，safari移动端只能使用m3u8格式
 * @desc flv相对于m3u8会加载更快，但兼容性不行
 */
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {netService} from '@baidu/bce-services';
import {ACCESS_KEY} from '@common/components/Cyberplayer/Cyberplayer';
import {urlConst} from '@common/constant/urlConst';
import {sendMonitor} from '@common/helper/page';
import {CyberplayerApi} from '@common/interface/common';
import {
    SummitAgendaDataItemObj,
    SummitLiveDataObj, SummitLiveStreamObj, SummitLiveStreamStatusObj,
    SummitRecommendCardItemObj, SummitRecommendCardObj,
} from '@common/interface/summit';
import moment from 'moment';
import {useEffect, useMemo, useRef} from 'react';

/**
 * @param streamList 直播流列表
 */
export const useSummitLive = ({
    streamList, defaultCurrent, autostartRef, listenStartTime, listenEndTime,
    isMobile = false, showRecommend = false, isEnd = false, trackCategory,
}: {
    streamList: SummitLiveDataObj['list'];
    defaultCurrent?: number;
    listenStartTime: string; // 开始监听时间
    listenEndTime: string; // 结束监听时间
    autostartRef?: React.MutableRefObject<boolean>;
    isMobile?: boolean;
    showRecommend?: boolean; // 是否展示推荐卡片
    isEnd?: boolean; // 直播是否结束，若结束则不发送任何请求
    trackCategory?: string; // 埋点
}) => {
    // const playerRef = useRef<CyberplayerApi>(null);
    const [player, setPlayer, playerRef] = useStateRef<CyberplayerApi>(null);
    const [current, setCurrent, currentRef] = useStateRef(defaultCurrent || 0);
    const timer = useRef<NodeJS.Timeout>(null);

    const [liveStream, setLiveStream, liveStreamRef] = useStateRef<SummitLiveStreamObj[]>(() => streamList?.map(item => item.liveStream));
    const curLiveStream = useMemo(() => {
        return liveStream[current];
    }, [current, liveStream]);

    const [recommendData, setRecommendData, recommendDataRef] = useStateRef<SummitRecommendCardItemObj>(null); // 当前展示的推荐卡片

    // 获取直播流状态信息
    const getLiveStreamStatus = async () => {
        netService.post<null, SummitLiveStreamStatusObj[]>(urlConst.GET_SUMMIT_LIVE_STATUS)
            .then(data => {
                const liveResult = data.result;
                setLiveStream(pre => {
                    return pre.map(item => {
                        // 匹配直播流信息内容
                        const newLiveStatus = liveResult?.filter(itemResult => itemResult.stream === item.id)?.[0];
                        const curLiveStream = pre[currentRef.current];
                        // 正在播放的内容如果变更状态，且变更为直播中，则需手动更新播放器
                        if (
                            curLiveStream?.id === newLiveStatus?.stream // 当前直播流id匹配
                                && item.status !== newLiveStatus?.status // 当前直播流状态发生变更
                                && newLiveStatus?.status === 'ONGOING' // 当前直播流状态变更为直播中
                        ) {
                            playerRef.current?.remove();
                            // 若是当前播放器，需要进行重置且延迟操作，否则无法获取player或状态变更不生效
                            playerRef.current?.setup({
                                width: '100%',
                                height: '100%',
                                title: '',
                                stretching: 'uniform',
                                image: isMobile ? curLiveStream?.videoCoverM : curLiveStream?.videoCover,
                                file: isMobile ? (curLiveStream?.liveM || curLiveStream?.live) : curLiveStream?.live,
                                autostart: autostartRef?.current || false,
                                isLive: true,
                                ak: ACCESS_KEY,
                                errorTip: '',
                                controls: 'over',
                            });
                            playerRef.current?.on('play', () => {
                                playerRef.current.setVolume(50);
                                trackCategory && sendMonitor({
                                    action: 'click',
                                    category: trackCategory,
                                    name: '峰会直播流',
                                    value: '播放器-播放-' + curLiveStream.id,
                                });
                            });
                            playerRef.current?.on('pause', () => {
                                trackCategory && sendMonitor({
                                    action: 'click',
                                    category: trackCategory,
                                    name: '峰会直播流',
                                    value: '播放器-暂停-' + curLiveStream.id,
                                });
                            });
                        }
                        return {
                            ...item,
                            status: newLiveStatus?.status || item.status,
                        };
                    });
                });
            }).catch(err => {
                console.error(err);
            });
    };

    // 获取推荐卡片信息
    const displaySummitRecommend = async () => {
        // 当前直播流状态不为直播中时，不展示推荐卡片
        let data: SummitRecommendCardObj = null;
        if (liveStreamRef.current?.[currentRef.current]?.status === 'ONGOING') {
            try {
                data = await netService.get<any, SummitRecommendCardObj>(urlConst.GET_SUMMIT_RECOMMEND_CARD) as any;
                const {list} = data;
                // 找到对应直播流的推荐卡片 且 visible
                const {recStartIndex = 0, recEndIndex = list.length} = streamList[currentRef.current];
                const curList = list?.slice(recStartIndex, recEndIndex);
                const tempRecData = curList?.find(item => item.visible) || {} as SummitRecommendCardItemObj;
                // 若标题为空, 则隐藏推荐卡片
                if (!tempRecData.title?.trim()?.length) {
                    setRecommendData(null);
                } else if (tempRecData.title !== recommendDataRef.current?.title) {
                    setRecommendData(tempRecData);
                }
            } catch (e) {
                console.error(e);
                setRecommendData(null);
            }
        } else {
            setRecommendData(null);
        }
    };

    useOnMount(() => {
        // 若直播流已结束，则不进行任何请求操作
        if (!isEnd) {
            // 初始化进入，加载播放器之后获取一次直播流状态
            const timer2 = setInterval(() => {
                if (playerRef.current) {
                    clearInterval(timer2);
                    getLiveStreamStatus();
                    showRecommend && displaySummitRecommend();
                }
            }, 50);
            const now = moment();
            // 根据数据开始时间和结束时间进行轮询，更新页面数据状态
            const pollingTime = () => {
                timer.current = setInterval(() => {
                    // 不在监听时间范围，则停止监听
                    if (moment().isBetween(moment(listenStartTime), moment(listenEndTime))) {
                        getLiveStreamStatus();
                        showRecommend && displaySummitRecommend(); // 推荐卡片与直播流定时器保持一致
                    } else {
                        clearInterval(timer.current);
                        timer.current = null;
                    }
                }, 10000);
            };
            // start时间前为了防止用户不刷新页面，开启定时器
            if (now.isBefore(moment(listenStartTime))) {
                setTimeout(() => {
                    pollingTime();
                }, moment(listenStartTime).diff(now));
            } else if (now.isBetween(moment(listenStartTime), moment(listenEndTime))) {
                pollingTime();
            } else {
                // 不在监听时间范围内 & !isEnd => 回放生成中，关闭定时器，直播流状态为初始化时接口获取的状态
                timer.current && clearInterval(timer.current);
                timer.current = null;
            }
        }
        return () => {
            clearInterval(timer.current);
            timer.current = null;
        };
    });
    return {
        current, setCurrent, player, setPlayer,
        playerRef, liveStream, curLiveStream,
        recommendData,
    };
};

/**
 * useSummitVideoTimePoint： 根据时间点进行播放器联动
 * @param dataList agenda扁平化内容列表
 * @param player 播放器实例
 * @param indexChangeCb index变更回调函数
 */
export const useSummitVideoTimePoint = ({player, dataList, indexChangeCb}: {
    player: CyberplayerApi;
    dataList: SummitAgendaDataItemObj[];
    indexChangeCb?: (index: number) => void; // index变更回调
}) => {
    const [liveIndex, setLiveIndex, liveIndexRef] = useStateRef(-1); // 当前播放的index
    const isClickTriggerRef = useRef(false); // 是否是点击触发

    const handleSeek = (index: number) => {
        const curItem = dataList[index];
        // 无播放器 ｜ 无时间点 ｜ 非回放期间，不做处理
        if (!player || !curItem.videoTimePoint) {
            return;
        }
        setLiveIndex(index);
        isClickTriggerRef.current = true;
        player?.seek(Number(curItem.videoTimePoint));
    };

    // 时间轴滚动控制
    useEffect(() => {
        const onTimeUpdateHandler = () => {
            // 最后一个小于当前时间的item，即为当前播放的item
            const index = dataList.findLastIndex(item => Number(item.videoTimePoint) < player.getPosition());
            // 视频跳转到指定时间点后，重置isClickTriggerRef，且滚动swiper
            if (index !== -1 && isClickTriggerRef.current && liveIndexRef.current === index) {
                isClickTriggerRef.current = false;
            }
            // 如果是点击触发的滚动条滚动，则不触发index变更，也不需要触发indexChangeCb
            if (index !== -1 && liveIndexRef.current !== index && !isClickTriggerRef.current) {
                setLiveIndex(index);
                indexChangeCb && indexChangeCb(index);
            }
        };
        let timer: NodeJS.Timeout = null;
        const clearTimer = () => {
            clearInterval(timer);
            timer = null;
        };
        timer && clearTimer();
        if (player) {
            timer = setInterval(() => {
                onTimeUpdateHandler();
            }, 300);
        }
        return () => {
            timer && clearTimer();
        };
    }, [dataList, indexChangeCb, liveIndexRef, player, setLiveIndex]);

    return {liveIndex, handleSeek, setLiveIndex};
};
