/**
 * @file 合作伙伴考试报名-hook
 */

import {Form} from 'antd';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {getAllParamsFromUrl, replaceOfString} from '@baidu/bce-helper';
import {useState} from 'react';
import {urlConst} from '@common/constant/urlConst';
import {netService} from '@baidu/bce-services';
import {UDialog} from '@common/components/UDialog/UDialog';
import {useCheckUserinfo} from '@common/hooks/useCheckUserinfo';
import {ExamCoursePreCheckObj} from '@common/interface/page';

const openPreCourseTip = (unfinishedCourses: ExamCoursePreCheckObj['unfinishedCourses']) => {
    UDialog.open({
        title: '温馨提示',
        okText: '关闭',
        content: (
            (
                <div>
                    请先完成
                    {
                        unfinishedCourses.map(item => (
                            <a
                                key={item.courseId}
                                target="_blank"
                                href={`/partner/course-center/course.html?id=${item.courseId}`}
                                style={{color: '#2468F2'}}
                            >
                                《{item.courseName}》
                            </a>
                        ))
                    }
                    课程的学习
                </div>
            )
        ),
        onOk(close) {
            close();
        },
    });
};

export const usePartnerExamApply = () => {
    const [form] = Form.useForm();
    const [examName, setExamName] = useState('');
    const [examId, setExamId] = useState('');
    const [, setLoading, loadingRef] = useStateRef(false);
    const [unfinishedCourses, setUnfinishedCourses] = useState<ExamCoursePreCheckObj['unfinishedCourses']>([]);
    const [, , loginCallback] = useCheckUserinfo({
        isImmediately: true,
        isCheckUserType: false,
        maskClosable: false,
        loginCallback: info => {
            const query = getAllParamsFromUrl();
            const {id} = query;
            info.hasLogin && netService.get<null, boolean>(urlConst.GET_ACCOUT_PARTNER).then(res => {
                if (res.result) {
                    netService.get<null, {examName: string, preCourseIds?: string}>(
                        replaceOfString(urlConst.GET_PARTNER_EXAM_DETAIL, {':examId': id})
                    )
                        .then(res => {
                            if (res.success && res.result.examName) {
                                setExamName(res.result.examName);
                                if (res.result.preCourseIds) {
                                    netService.post<{
                                        examId: string;
                                    }, ExamCoursePreCheckObj>(urlConst.POST_CHECK_PRE_COURSE, {examId: id}).then(res => {
                                        if (res.success && !res.result.checkStatus && res.result.unfinishedCourses?.length) {
                                            setUnfinishedCourses(res.result.unfinishedCourses);
                                            openPreCourseTip(res.result.unfinishedCourses);
                                        }
                                    });
                                }
                            }
                        })
                        .catch(res => {
                            UDialog.open({
                                title: '温馨提示',
                                content: res?.error?.message ?? '获取考试信息失败',
                                okText: '返回首页',
                                onOk: () => {
                                    location.href = '/';
                                },
                            });
                        });
                } else {
                    UDialog.open({
                        title: '温馨提示',
                        content: '你不是合作伙伴，请申请成为合作伙伴后再访问',
                        okText: '立即申请',
                        onOk: () => {
                            location.href = '/partner/apply.html';
                        },
                    });
                }
            });
        },
    });
    const handleSubmit = () => {
        if (unfinishedCourses.length) {
            openPreCourseTip(unfinishedCourses);
            return;
        }
        loginCallback(() => {
            !loadingRef.current && form.validateFields().then((formData: {[props: string]: string | string[]}) => {
                setLoading(true);
                netService.post(urlConst.POST_PARTNER_EXAM_APPLY, {...formData, examId}).then(() => {
                    UDialog.open({
                        title: '温馨提示',
                        content: '报名成功，请等待审核',
                        okText: '返回学习中心',
                        maskClosable: false,
                        showCloseIcon: false,
                        onCancel: () => {
                            location.href = '/partner/study/center.html';
                        },
                        onOk: close => {
                            close();
                        },
                    });
                }).catch(res => {
                    UDialog.open({
                        title: '温馨提示',
                        content: res.error?.message ?? '报名失败，请稍后重试',
                        okText: '确认',
                        maskClosable: true,
                        showCloseIcon: true,
                        onOk: close => {
                            close();
                        },
                    });
                }).finally(() => {
                    setLoading(false);
                });
            });
        });
    };
    useOnMount(() => {
        const query = getAllParamsFromUrl();
        const {id} = query;
        if (!id) {
            location.href = '/';
        }
        setExamId(id);
    });
    return {
        examName,
        handleSubmit,
        form,
    };
};
