/**
 * @file 新官网直播页公共hook
 */

import {replaceOfString} from '@baidu/bce-helper';
import {useOnMount} from '@baidu/bce-hooks';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {CloudLiveObj} from '@common/interface/page';
import {MutableRefObject, useRef, useState} from 'react';
import moment from 'moment';
import {CyberplayerApi, CyberplayerConfig} from '@common/interface/common';
import {ACCESS_KEY} from '@common/components/Cyberplayer/Cyberplayer';
import {getAntiParam, isIE} from '@common/helper/page';

type LiveStatusType = 'BEFEORE_LIVE' | 'LIVING' | 'END_LIVE';

/**
 * 新官网直播页公共hook
 * @param liveDetail 直播详情
 * @returns [liveStatus, viewCount]
 */
export const useCloudLive: (liveDetail: CloudLiveObj) => [LiveStatusType, number, MutableRefObject<CyberplayerApi>]
= liveDetail => {
    const [liveStatus, setLiveStatus] = useState<LiveStatusType>(null);
    const [viewCount, setViewCount] = useState(0);
    const playerRef = useRef<CyberplayerApi>(null);
    useOnMount(() => {
        const curLiveStatus = moment().isBefore(moment(liveDetail.startTime)) ? 'BEFEORE_LIVE'
            : moment().isAfter(moment(liveDetail.endTime)) ? 'END_LIVE' : 'LIVING';
        setLiveStatus(curLiveStatus);
        let timer1: NodeJS.Timeout = null;
        let timer2: NodeJS.Timeout = null;
        if (curLiveStatus === 'BEFEORE_LIVE' && moment(liveDetail.startTime).diff(moment(), 'h') < 5) {
            timer1 = setTimeout(() => {
                setLiveStatus('LIVING');
                timer1 = null;
            }, moment(liveDetail.startTime).diff(moment()));
        }
        if ((curLiveStatus === 'BEFEORE_LIVE' || curLiveStatus === 'LIVING') && moment(liveDetail.endTime).diff(moment(), 'h') < 5) {
            timer2 = setTimeout(() => {
                setLiveStatus('END_LIVE');
                const conf: CyberplayerConfig = {
                    width: '100%', // 宽度，也可以支持百分比(不过父元素宽度要有)
                    height: '100%', // 高度，也可以支持百分比
                    title: '', // 标题
                    file: liveDetail.reviewUrl, // 播放地址
                    image: liveDetail.liveCover, // 预览图
                    autostart: false, // 是否自动播放
                    stretching: 'uniform', // 拉伸设置
                    repeat: false, // 是否重复播放
                    volume: 50, // 音量
                    controls: 'over', // controlbar是否显示
                    starttime: 0, // 视频开始播放时间点(单位s)，如果不设置，则可以从上次播放时间点续播
                    ak: ACCESS_KEY, // 公有云平台注册即可获得accessKey
                    primary: isIE() ? 'flash' : null,
                    flv: {
                        reconnecttime: 2,
                    },
                    isLive: false,
                    errorTip: '',
                };
                playerRef.current?.setup(conf);
                timer2 = null;
            }, moment(liveDetail.endTime).diff(moment()));
        }
        netService.get<null, {viewCount: number}>(replaceOfString(urlConst.CLOUD_LIVE_VIEW, {':liveId': liveDetail.liveId})).then(res => {
            if (res.success) {
                setViewCount(res.result.viewCount);
            }
        });

        const addViewCount = async () => {
            netService.post(
                replaceOfString(urlConst.CLOUD_LIVE_VIEW, {':liveId': liveDetail.liveId}),
                null,
                null,
                {
                    headers: {
                        'Acs-Token': await getAntiParam(),
                    },
                }
            );
        };

        // 延迟一会再发不影响数量的增加，但是可以等到paris加载完
        setTimeout(() => {
            addViewCount();
        }, 1000);
        return () => {
            if (timer1) {
                clearTimeout(timer1);
            }
            if (timer2) {
                clearTimeout(timer2);
            }
        };
    });
    return [liveStatus, viewCount, playerRef];
};
