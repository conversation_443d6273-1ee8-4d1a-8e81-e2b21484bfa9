/* eslint-disable complexity */
/**
 * @file cms表单hook
 */

import {genCmsSurveySubmitData, getAntiParam, getSurveyValidationObj, handleZh} from '@common/helper/page';
import {Block} from '@common/interface/blocks';
import {
    CmsSurveyApplyObj,
    CmsSurveyResObj,
    FormFieldsObj,
    SurverBlockObj,
    SurveyCheckboxObj,
    SurveyCompanyInputObj,
    SurveyDropdownObj,
    SurveyInputObj,
    SurveyLinkageObj,
    SurveyRadioObj,
    SurveyTextareaObj,
    SurveyDatePickerObj,
    SurveyUploadObj,
} from '@common/interface/cmsSurvey';
import {CmsPageDataObj} from '@common/interface/common';
// import {Checkbox, Input, Radio} from 'antd';
import {Input, Radio} from 'antd';
import {Rule} from 'antd/lib/form';
import {uniq, without} from 'lodash';
import {useMemo, useState} from 'react';
import {useOnUpdate} from '@baidu/bce-hooks';
import {netService} from '@baidu/bce-services';
import {urlConst} from '@common/constant/urlConst';
import {UMultipleSelect} from '@common/components/UMultipleSelect/UMultipleSelect';
import {UMultipleSelectWap} from '@common/components/UMultipleSelect/UMultipleSelectWap';
import {UCompanyInput} from '@common/components/UCompanyInput/UCompanyInput';
import {UCmsDatePicker} from '@common/components/UCmsDatePicker/UCmsDatePicker';
import {UTextarea} from '@common/components/UTextarea/UTextarea';
import {UUpload} from '@common/components/UUpload/UUpload';
import {V5G001} from '@components/cms/V5G001/V5G001';
import {V5G023} from '@components/cms/V5G023/V5G023';
import {CodePanel} from '@components/cms/CodePanel/CodePanel';
import {USurveyCheckBox} from '@common/components/USurveyCheckBox/USurveyCheckBox';
import {usePassMachineNew} from '../usePassMachineNew';

const ACTIVITY_SUCCESS_COMPONENT_LIST = ['V5G023'];
const CMS_PRODUCT_COMPONENT_MAP = {
    'V5G002': V5G001,
    'V5G023': V5G023,
    'CodePanel': CodePanel,
};

const getInputComponent = (res: FormFieldsObj, module: SurverBlockObj, rules: Rule[]) => {
    const {data: {render}} = (module as SurveyInputObj);
    render.validation && render.validation !== 'none' && render.validation !== 'invite'
        && rules.push(getSurveyValidationObj()[render.validation]);
    res.inputProps = {
        maxLength: render.length,
        placeholder: render.placeholder || '请输入',
        ...res.inputProps,
    };
    res.isHide = render.show === false; // 有些模块不需要显示，如统一社会信用代码
    res.Component = Input;
    return res;
};

const getTextareaComponent = (res: FormFieldsObj, module: SurverBlockObj, rules: Rule[]) => {
    const {data: {render}} = (module as SurveyTextareaObj);
    if (render.minLength) {
        rules.push({
            validator: (rule, value, cb) => {
                if (value && value.length < render.minLength) {
                    cb(`请最少输入${render.minLength}个字`);
                } else {
                    cb();
                }
            },
        });
    }
    res.inputProps = {
        minLength: render.minLength,
        maxLength: render.length,
        placeholder: render.placeholder || '请输入',
        resize: 'none',
        ...res.inputProps,
    };
    res.Component = UTextarea;
    return res;
};

const getCompanyInputComponent = (res: FormFieldsObj, module: SurverBlockObj) => {
    const {data: {render}} = (module as SurveyCompanyInputObj);
    res.inputProps = {
        campaignId: render.campaignId,
        getPopupContainer: (triggerNode: HTMLElement) => triggerNode.parentElement,
        popupClassName: 'summit-dropdown-pop',
        placeholder: render.placeholder || '请输入',
        ...res.inputProps,
    };
    res.Component = UCompanyInput;
    return res;
};

const getRadioComponent = (res: FormFieldsObj, module: SurverBlockObj) => {
    const {data: {render}} = (module as SurveyRadioObj);
    res.inputProps = {
        options: render.answer.map(i => ({
            label: i,
            value: i,
        })),
        ...res.inputProps,
    };
    res.Component = Radio.Group;
    return res;
};

const getCheckboxComponent = (res: FormFieldsObj, module: SurverBlockObj) => {
    const {data: {render}} = (module as SurveyCheckboxObj);
    res.formItemProps = {
        ...res.formItemProps,
        normalize: (value: string[]) => {
            return value.join(',');
        },
    };
    res.inputProps = {
        options: render.answer.map(i => ({
            label: i,
            value: i,
        })),
        answerRule: render.answerRule,
        className: render.multiLineStyle ? 'checkbox-multi-line' : '',
        ...res.inputProps,
    };
    res.Component = USurveyCheckBox;
    return res;
};

const getDropdownComponent = (res: FormFieldsObj, module: SurverBlockObj) => {
    const {data: {render}} = (module as SurveyDropdownObj);
    res.inputProps = {
        options: render.answer.map(i => ({
            label: i,
            value: i,
        })),
        placeholder: '请选择',
        getPopupContainer: (triggerNode: HTMLElement) => triggerNode.parentElement,
        popupClassName: 'summit-dropdown-pop',
        ...res.inputProps,
    };
    res.Component = UMultipleSelect;
    return res;
};

const getDropdownWapComponent = (res: FormFieldsObj, module: SurverBlockObj) => {
    const {data: {render}} = (module as SurveyDropdownObj);
    res.inputProps = {
        options: render.answer.map(i => ({
            label: i,
            value: i,
        })),
        placeholder: '请选择',
        getPopupContainer: (triggerNode: HTMLElement) => triggerNode.parentElement,
        popupClassName: 'summit-dropdown-pop',
        ...res.inputProps,
    };
    res.Component = UMultipleSelectWap;
    return res;
};

const getDatePickerComponent = (res: FormFieldsObj, module: SurverBlockObj) => {
    const {data: {render}} = (module as SurveyDatePickerObj);
    res.inputProps = {
        placeholder: render.placeholder || '请选择日期',
        getPopupContainer: (triggerNode: HTMLElement) => triggerNode.parentElement,
        ...res.inputProps,
    };
    res.Component = UCmsDatePicker;
    return res;
};

const getUploadComponent = (res: FormFieldsObj, module: SurverBlockObj, rules: Rule[]) => {
    const {data: {render}} = (module as SurveyUploadObj);
    console.log('get upload component', res, render);
    // TODO: 上传的报错信息 场景不全
    if (render.num) {
        rules.push({
            validator: (rule, value, cb) => {
                if (value && value.length < render.num) {
                    cb(`最多上传${render.num}个文件`);
                } else {
                    cb();
                }
            },
        });
    }
    res.inputProps = {
        maxCount: Number(render.num) || 1,
        ...res.inputProps,
    };
    res.Component = UUpload;
    return res;
};

const getProductComponent = (res: FormFieldsObj, module: SurverBlockObj) => {
    res.inputProps = {
        ...module,
    };
    res.Component = (CMS_PRODUCT_COMPONENT_MAP as any)[module.type];
    return res;
};

const COMPONENT_CONFIG_MAP: {
    [props: string]: (res: FormFieldsObj, module: SurverBlockObj, rules?: Rule[]) => FormFieldsObj;
} = {
    'SummitInput': getInputComponent,
    'Input': getInputComponent,
    'CompanyInput': getCompanyInputComponent,
    'Dropdown': getDropdownComponent,
    'Radio': getRadioComponent,
    'Checkbox': getCheckboxComponent,
    'DropdownWap': getDropdownWapComponent,
    'Textarea': getTextareaComponent,
    'DatePicker': getDatePickerComponent,
    'Upload': getUploadComponent,
    'V5G002': getProductComponent,
    // 'V5G023': getProductComponent, // 仅在活动页面的提交成功后使用，不做表单渲染项，此处去除
    'CodePanel': getProductComponent,
};

export const useCmsSurvey = ({modules}: {modules: Block[]}) => {
    const [value, setValue] = useState<{[props: string]: string}>({});
    const hideList = useMemo<number[]>(() => {
        let res: number[] = [];
        let showList: number[] = [];
        modules.forEach(module => {
            if (module.data?.render?.linkage?.length) {
                const {render} = module.data;
                const linkage = render.linkage as SurveyLinkageObj[];
                const idx = (render.answer as string[])?.findIndex((i: string) => i === value[render.title]);
                linkage.forEach(item => {
                    if (item.checked === idx) {
                        showList = showList.concat(item.show);
                    } else {
                        res = res.concat(item.show);
                    }
                });
            }
        });
        return without(uniq(res), ...showList);
    }, [modules, value]);
    // 表单列表的临时字段
    const formFieldsTemp = useMemo<FormFieldsObj[]>(() => {
        const res = modules.map(module => {
            const {id, type, data: {render, html}, formItemProps, inputProps} = (module as SurverBlockObj);
            const rules: Rule[] = [];
            let res: FormFieldsObj = null;
            // 富文本CodePanel的情况下，不做表单渲染
            if (!render && html) {
                res = {
                    id,
                    type,
                    isProductComponent: true,
                    isActivitySuccessComponent: false,
                };
                return COMPONENT_CONFIG_MAP[type]?.(res, module, rules);
            }
            if (!render || render.show === false) {
                return null;
            }
            if (render.required || (render.tag === 'leads' && (!render.required || render.required !== false))) {
                if (type === 'Checkbox') {
                    rules.push({
                        validator: (rule, value, cb) => {
                            if (value && value.length < 1) {
                                cb('此项必填，请填写后再提交');
                            } else {
                                cb();
                            }
                        },
                    });
                } else {
                    rules.push({required: true, message: handleZh('此项必填，请填写后再提交')});
                }
            }
            res = {
                id,
                type,
                isProductComponent: CMS_PRODUCT_COMPONENT_MAP.hasOwnProperty(type),
                isActivitySuccessComponent: ACTIVITY_SUCCESS_COMPONENT_LIST.includes(type),
                formItemProps: {
                    label: render.displayName || render.title,
                    name: render.title,
                    extra: render.reminder,
                    rules,
                    ...formItemProps,
                },
                inputProps,
            };
            res = COMPONENT_CONFIG_MAP[type]?.(res, module, rules);
            return res;
        });
        return res;
    }, [modules]);
    // 表单列表
    const formFields = useMemo<FormFieldsObj[]>(() => {
        const newData = [...formFieldsTemp];
        newData.forEach((item, idx) => {
            if (item) {
                item.isHide = hideList.includes(idx);
            }
        });
        return newData;
    }, [formFieldsTemp, hideList]);

    const handleChange: (changedValues: any, values: any) => void = (_, values) => {
        setValue(values);
    };

    return {
        formFields,
        value,
        handleChange,
    };
};

export const useCmsSurveyApply = ({pageData}: {pageData: CmsPageDataObj}) => {
    const [api] = useState(() => {
        let api = urlConst.POST_SURVEY_NO_NEED_LOGIN;
        if (pageData.needLogin) {
            api = urlConst.POST_SURVEY_NEED_LOGIN;
        }
        if (pageData.path.includes('survey_summit')) {
            api = urlConst.POST_SURVEY_SUMMIT;
        }
        return api;
    });
    const [submitData, setSubmitData] = useState<CmsSurveyApplyObj>(null);
    const {tkDs, showMachine} = usePassMachineNew();
    const submit = async ({formData = null, headers = null, data = null}: {
        formData?: {[props: string]: string | string[]};
        headers?: any;
        data?: CmsSurveyApplyObj;
    }) => {
        let params = null;
        if (data) {
            params = data;
            setSubmitData(params);
        } else if (formData) {
            params = genCmsSurveySubmitData({formData, pageData});
            setSubmitData(params);
        } else {
            params = submitData;
        }
        if (!params) {
            throw new Error('参数错误, 至少需要传入formData/data中的一个参数');
        }
        const newHeaders = headers || {'Acs-Token': await getAntiParam()};
        return netService.post<CmsSurveyApplyObj, CmsSurveyResObj, any>(api, params, {}, {
            headers: newHeaders,
        }).then(res => {
            return Promise.resolve(res);
        }).catch(err => {
            if (err && err.code === 'yumenguan check exception') {
                showMachine();
            }
            return Promise.reject(err);
        });
    };
    useOnUpdate(() => {
        tkDs && submit({headers: tkDs});
    }, [tkDs]);
    return {
        submit,
    };
};
