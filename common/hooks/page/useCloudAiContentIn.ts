/**
 * @file 云智一体内容出现
 * <AUTHOR>
 */

import {MutableRefObject} from 'react';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {throttle} from 'lodash';
import {getElementTop, getScrollTop} from '@common/helper/page';

export const useCloudAiContentIn = ({
    ref,
}: {
    ref: MutableRefObject<HTMLElement>;
}) => {
    const [contentVisible, setContentVisible, contentVisibleRef] = useStateRef(false);
    const [wrapperVisible, setWrapperVisible, wrapperVisibleRef] = useStateRef(false);
    useOnMount(() => {
        const handleScoll = throttle(() => {
            const scrollTop = getScrollTop();
            const eleTop = getElementTop(ref.current);
            if (scrollTop + window.innerHeight <= eleTop || scrollTop > eleTop + ref.current.offsetHeight) {
                wrapperVisibleRef.current && setWrapperVisible(false);
            } else {
                !wrapperVisibleRef.current && setWrapperVisible(true);
            }
            if (scrollTop + window.innerHeight > eleTop + 200) {
                !contentVisibleRef.current && setContentVisible(true);
            } else {
                contentVisibleRef.current && setContentVisible(false);
            }
        }, 40);
        handleScoll();
        window.addEventListener('scroll', handleScoll);
        return () => {
            window.removeEventListener('scroll', handleScoll);
        };
    });
    return [contentVisible, wrapperVisible];
};
