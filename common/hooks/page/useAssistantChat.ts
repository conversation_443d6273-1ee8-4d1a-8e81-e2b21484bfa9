/* eslint-disable complexity */
/* eslint-disable max-statements */

/**
 * 调用千帆模型的对话助手
 */
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {v4 as uuidV4} from 'uuid';
import {MutableRefObject, useRef, useState} from 'react';
import {flushSync} from 'react-dom';
import {urlConst} from '@common/constant/urlConst';

// const ACCESS_TOKEN = '24.b9edd26e5b360d2605242723845f6a81.2592000.1701736711.282335-31581574'; // 1105
// const BASE_CHAT_URL = 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions';
// const CHAT_URL = BASE_CHAT_URL.concat(`?access_token=${ACCESS_TOKEN}`);
// const CHAT_URL_TEST = 'http://************:8666/api/qianfan/chat';
// console.log('🚀 ~ file: useAssistantChat.ts:16 ~ CHAT_URL:', CHAT_URL, CHAT_URL_TEST, urlConst.POST_QIANFAN_CHAT);

type MessageListType = {
    role: string;
    content: string;
    isTrialEnd?: boolean;
    isError?: boolean;
    isLoading?: boolean; // 智能生成中状态
};

interface RequestOptionsObj {
    model: string;
    system?: string;
    onSend?: () => void;
    onopen?: () => void;
    onmessage?: (data: string) => void;
    onclose?: () => void;
    onerror?: () => void;
}

export type StreamStatusType = 'PENDING' | 'RUNNING' | 'DONE' | 'ERROR' | 'LOADING';

const TOKENS_LIMIT_ERROR_TEXT = '您今天的使用上限已达5次，可前往千帆大模型控制台体验完整版，新用户限时送100000+tokens';

const parsePack = (str: string) => {
    // 定义正则表达式匹配模式
    const pattern = /({.*?})\s*\n/g; // 旧正则：/data:\s*({.*?})\s*\n/g，后端流数据可能data：丢失，故修改
    // 定义一个数组来存储所有匹 配到的 JSON 对象
    const result = [];
    // 使用正则表达式匹配完整的 JSON 对象并解析它们
    let match = null;
    while ((match = pattern.exec(str)) !== null) {
        const jsonStr = match[1];
        try {
            const json = JSON.parse(jsonStr);
            result.push(json);
        } catch (e) {
            console.error(e);
        }
    }
    // 如果是接口报错，如token次数用完了,返回形式特殊处理
    if (result.length < 1) {
        try {
            const json = JSON.parse(str);
            result.push(json);
        } catch (e) {
            console.error(e);
        }
    }
    // 输出所有解析出的 JSON 对象
    return result;
};

export const useAssistantChat: ({
    chatBodyRef,
    requestOptions,
}: {
    chatBodyRef: MutableRefObject<HTMLDivElement>;
    requestOptions?: RequestOptionsObj;
}) => [
    MessageListType[],
    StreamStatusType,
    (question: string, options?: RequestOptionsObj, isRetry?: boolean) => void, // isRetry必须包含question
    (model: string, system: string) => void,
    () => void,
    () => void,
] = ({chatBodyRef, requestOptions}) => {
    const [messageList, setMessageList, messageListRef] = useStateRef<MessageListType[]>([]);
    const sessionIdRef = useRef<string>(uuidV4()); // 会话id
    const shouldAbortRequestRef = useRef<boolean>(false); // 是否终止请求
    const [streamStatus, setStreamStatus] = useState<StreamStatusType>('PENDING'); // 流状态 PENDING | RUNNING | DONE ｜ ERROR
    const [isTrialEnd, setIsTrialEnd] = useState<boolean>(false); // 是否试用结束
    const isAutoScrollableRef = useRef<boolean>(true); // 是否可自动滚动

    // 自动滚动到底部
    const setAutoScrollToBottom = () => {
        if (isAutoScrollableRef.current) {
            chatBodyRef.current?.scrollTo({
                top: chatBodyRef.current.scrollHeight,
                behavior: 'smooth',
            });
        }
    };

    // 处理错误: 流状态置为ERROR，强制塞入错误信息，增加isError字段
    const handleError = (errorMsg?: string) => {
        setStreamStatus('ERROR');
        // 强制塞入错误信息
        setMessageList(pre => {
            return [
                ...pre.slice(0, -2),
                {
                    ...pre[pre.length - 2],
                    isError: true,
                },
                {
                    role: 'assistant',
                    content: errorMsg || '网络异常，请稍后重试',
                    isError: true,
                },
            ];
        });
        setAutoScrollToBottom();
    };

    // 试用结束：强制塞入试用结束信息，增加isTrialEnd字段
    const handleTrialEnd = () => {
        // 强制塞入token次数用完信息(由于预置了assistant字段，需要替换最后两个值)
        setMessageList(pre => [
            ...pre.slice(0, -2),
            {
                ...pre[pre.length - 2],
                isTrialEnd: true,
            },
            {
                role: 'assistant',
                content: TOKENS_LIMIT_ERROR_TEXT,
                isTrialEnd: true,
            },
        ]);
    };

    // 定义一个通用的模拟方法
    const fetchEventSource = async (question?: string, options = requestOptions, isRetry = false) => {
        const {onSend = null, onopen = null, onmessage = null, onclose = null, onerror = null} = options || {};
        setStreamStatus('LOADING');
        onSend && onSend(); // 发送消息前的回调
        // 试用结束，为最后一个用户消息添加试用结束状态，追加提示信息，不再发送请求
        if (isTrialEnd) {
            setMessageList(pre => [...pre.slice(0, -1), {
                role: 'user',
                content: question,
                isTrialEnd: true,
            }, {
                role: 'assistant',
                content: TOKENS_LIMIT_ERROR_TEXT,
                isTrialEnd: true,
            }]);
            isAutoScrollableRef.current = true; // 发送新消息后，允许自动滚动
            setAutoScrollToBottom(); // 发送消息后，自动滚动到底部
            return;
        }

        // 发送消息
        // 1. 重新生成时，去除最近的一条answer，重新发送
        // 2. 过滤试用结束状态和error状态的消息
        let filterMessageList = null;
        if (isRetry) {
            // 重新生成，去除最后一对问答，传值必须有question
            filterMessageList = messageListRef.current.slice(0, -2)?.filter(item => !item.isTrialEnd && !item.isError);
        } else {
            filterMessageList = messageListRef.current.filter(item => !item.isTrialEnd && !item.isError);
        }
        const newMessageList = [...filterMessageList, {
            role: 'user',
            content: question,
        }];
        flushSync(() => {
            if (isRetry) {
                setMessageList(pre => [...pre.slice(0, -2), {
                    role: 'user',
                    content: question,
                }, {
                    role: 'assistant',
                    content: '正在智能生成中，请稍后...',
                    isLoading: true,
                }]);
            } else {
                setMessageList(pre => [...pre, {
                    role: 'user',
                    content: question,
                }, {
                    role: 'assistant',
                    content: '正在智能生成中，请稍后...',
                    isLoading: true,
                }]);
            }
        });

        try {
            const controller = new AbortController(); // 每次请求创建新的controller
            const signal = controller.signal;
            shouldAbortRequestRef.current = false; // 每次新请求，关闭上次中断请求
            const response = await fetch(urlConst.POST_QIANFAN_CHAT, { // CHAT_URL_TEST || urlConst.POST_QIANFAN_CHAT
                method: 'POST',
                cache: 'no-cache',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    // stream: true, // 文心一言接口必填
                    model: options?.model || '',
                    modelRole: options?.system?.trim() || '',
                    messages: newMessageList?.map(item => ({role: item.role, content: item.content.trim()})), // 过滤isLoading状态
                    sessionId: sessionIdRef.current,
                }),
                signal,
            });

            // 在此处检查 signal.aborted 属性，以确定是否应处理响应
            if (signal.aborted) {
                // eslint-disable-next-line no-console
                console.log('Request was aborted');
                return;
            }

            if (response.status !== 200) {
                handleError();
                onerror && onerror();
                return;
            }

            onopen && onopen();
            const reader = response.body?.getReader();

            isAutoScrollableRef.current = true; // 发送新消息后，允许自动滚动

            // eslint-disable-next-line
            while (true) {
                setStreamStatus('RUNNING');
                setAutoScrollToBottom(); // 发送消息后，自动滚动到底部
                const {value, done} = await reader.read();
                // 在处理数据的过程中检查终止信号
                if (shouldAbortRequestRef.current) {
                    // 触发终止请求的逻辑
                    setStreamStatus('DONE');
                    setAutoScrollToBottom(); // DONE状态，用户增加DOM，允许自动滚动到底部
                    controller.abort();
                    break;
                }
                if (done) {
                    setStreamStatus('DONE');
                    setAutoScrollToBottom(); // DONE状态，用户增加DOM，允许自动滚动到底部
                    onclose && onclose();
                    break;
                }
                // 解析流数据
                const data = new TextDecoder().decode(value);
                // 监听异常data
                const parseData = parsePack(data);
                // console.log({data, parseData}); // 用于打印流数据与解析值
                parseData.forEach((json: any) => {
                    // 增加非数据流处理
                    if (!json.result || json.result.length === 0) {
                        // 千帆报错
                        if (json.error_msg) {
                            handleError(json.error_msg);
                            onerror && onerror();
                        }
                        if (json.errmsg) {
                            handleError(json.errmsg);
                            onerror && onerror();
                        }
                        if (json.error && json.code && json.message) {
                            if (json.code === 'QIANFAN_TRIAL_BCEEXCEPTION') {
                                // 试用结束
                                setIsTrialEnd(true);
                                handleTrialEnd();
                            } else {
                                // 其他错误
                                handleError(json.message?.global);
                            }
                            onclose && onclose();
                        }
                        return;
                    }
                    setMessageList(pre => {
                        const lastMessage = pre[pre.length - 1];
                        // 最后一个肯定是assistant，如果状态是isLoading，表示第一次; 否则，直接追加信息
                        return [
                            ...pre.slice(0, -1),
                            {
                                role: 'assistant',
                                content: lastMessage.isLoading ? json.result : pre[pre.length - 1].content.concat(json.result),
                            },
                        ];
                    });
                    onmessage && onmessage(json.result);
                });
            }
        } catch (e) {
            // 中断没有进来（被第二次请求拿到）
            setStreamStatus('ERROR');
            // 请求终止
            if (e.name === 'AbortError') {
                // TODO: 请求终止会不会改messageList
                console.error('请求终止');
            } else {
                // 强制塞入错误信息
                setMessageList(pre => {
                    return [
                        ...pre.slice(0, -2),
                        {
                            ...pre[pre.length - 2],
                            isError: true,
                        },
                        {
                            role: 'assistant',
                            content: '网络异常，请稍后重试',
                            isError: true,
                        },
                    ];
                });
                onerror && onerror();
                console.error(e);
            }
            setAutoScrollToBottom();
        }
    };

    // 中断流
    const handleInterrupt = () => {
        shouldAbortRequestRef.current = true;
    };

    // 重新生成
    const handleRetry = (model: string, system: string) => {
        // 取最后的用户question，追加再发送
        const lastMessage = messageListRef.current.filter(item => item.role === 'user')?.pop();
        if (lastMessage.content) {
            isAutoScrollableRef.current = true;
            fetchEventSource(lastMessage.content, {model, system}, true);
        }
    };

    // 监听chatBodyRef的scroll事件，如果有向上滚动，则不再自动滚动
    useOnMount(() => {
        const chatBody = chatBodyRef.current;
        const handleScroll = () => {
            if (chatBodyRef.current?.scrollTop < chatBodyRef.current?.scrollHeight - chatBodyRef.current?.clientHeight) {
                isAutoScrollableRef.current = false;
            } else {
                isAutoScrollableRef.current = true;
            }
        };
        chatBody?.addEventListener('scroll', handleScroll);
        return () => {
            chatBody?.removeEventListener('scroll', handleScroll);
        };
    });

    return [messageList, streamStatus, fetchEventSource, handleRetry, handleInterrupt, setAutoScrollToBottom];
};
