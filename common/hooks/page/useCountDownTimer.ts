import {useCallback, useEffect, useState} from 'react';

interface TimeCountObj {
    'days': string;
    'hours': string;
    'minutes': string;
    'seconds': string;
}
interface TimerDataObj {
    timeLeft: TimeCountObj;
    difference: number;
}

export const useCountDownTimer = ({deadline}: {deadline: string}) => {
    const calculateTimeLeft = useCallback(() => {
        const difference = +new Date(deadline) - +new Date();
        let timeLeft = {
            days: '--',
            hours: '--',
            minutes: '--',
            seconds: '--',
        };

        if (difference > 0) {
            timeLeft = {
                days: Math.floor(difference / (1000 * 60 * 60 * 24)).toString().padStart(2, '0'),
                hours: Math.floor((difference / (1000 * 60 * 60)) % 24).toString().padStart(2, '0'),
                minutes: Math.floor((difference / 1000 / 60) % 60).toString().padStart(2, '0'),
                seconds: Math.floor((difference / 1000) % 60).toString().padStart(2, '0'),
            };
        }

        return {timeLeft, difference};
    }, [deadline]);

    const [timeData, setTimeData] = useState<TimerDataObj>(calculateTimeLeft());
    const [isTimeUp, setIsTimeUp] = useState(null);

    useEffect(() => {
        const timer = setInterval(() => {
            const newTimeLeft: TimerDataObj = calculateTimeLeft();
            setTimeData(newTimeLeft);
            if (newTimeLeft.difference <= 0) {
                setIsTimeUp(true);
                clearInterval(timer);
            } else {
                setIsTimeUp(false);
            }
        }, 1000);

        return () => clearInterval(timer);
    }, [calculateTimeLeft, deadline]);

    return {timeLeft: timeData.timeLeft, isTimeUp};
};
