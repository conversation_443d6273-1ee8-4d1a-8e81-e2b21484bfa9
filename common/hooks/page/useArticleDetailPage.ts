import {replaceOfString} from '@baidu/bce-helper';
import {urlConst} from '@common/constant/urlConst';
import {useMemo, useState} from 'react';
import {useOnMount} from '@baidu/bce-hooks';
import {netService} from '@baidu/bce-services';
import {TagItemObj} from '@common/interface/common';
import {sendMonitor} from '@common/helper/page';
import {
    ArticleBeginningMarketPosObj,
    ArticlePopoverMarketingPosObj,
    ArticleProductCardMarketPosObj,
} from '@common/interface/article';
import {useCheckActivateUserInfo} from './useCheckActivateUser';

interface ArticleMarketDataObj {
    headData: ArticleBeginningMarketPosObj;
    popData: ArticlePopoverMarketingPosObj;
    cardData: ArticleProductCardMarketPosObj;
}

const DEFAULT_POS_ID_MAP: {[key: string]: string} = {
    'H': 'H00001',
    'P': 'P00001',
    'C': 'C00001',
};

export const useArticleDetailPage = ({id, env, articleTags}: {
    id: number;
    env: string;
    articleTags: TagItemObj[];
}) => {
    const [userinfo, , verifyHandler] = useCheckActivateUserInfo({});
    const [posData, setPosData] = useState<string[]>(null); // 运营位ID列表
    const [beginningMarketPosData, setBeginningMarketPosData] = useState<ArticleBeginningMarketPosObj[]>(null); // 文章顶部运营位数据
    const [popoverMarketPosData, setPopoverMarketPosData] = useState<ArticlePopoverMarketingPosObj[]>(null); // 文章弹窗运营位数据
    const [productCardMarketPosData, setProductCardMarketPosData] = useState<ArticleProductCardMarketPosObj[]>(null); // 文章产品卡片运营位数据
    const [rightMarketPosData, setRightMarketPosData] = useState<{img: string, link: string}>(null); // 文章右侧运营位数据
    const [aiBottomMarketPosData, setAiBottomMarketPosData] = useState<{img: string}>(null);

    // 营销位ID列表（补全默认营销位）：[文章头部运营位 H，弹窗运营位 P，卡片运营位 C]
    const marketPosIdList: string[] = useMemo(() => {
        // 运营位接口数据未返回
        if (!posData) {
            return null;
        }
        const regData: string[] = [];
        const hasProductCard = posData.some(i => i.startsWith('C'));
        Object.keys(DEFAULT_POS_ID_MAP).forEach(key => {
            // 若同时存在H和C，仅保存C
            const res = posData.filter(i => i.startsWith(key));
            if (res.length === 0) {
                key !== 'C' && regData.push(DEFAULT_POS_ID_MAP[key]);
            } else if (!(key === 'H' && hasProductCard)) {
                regData.push(res[0]);
            }
        });
        return regData;
    }, [posData]);

    // 所有根据文章ID获取的营销位数据
    const marketPosDataMap = useMemo(() => {
        const res: ArticleMarketDataObj = {
            'headData': null,
            'popData': null,
            'cardData': null,
        };
        if ([beginningMarketPosData, popoverMarketPosData, productCardMarketPosData].some(i => !i)) { // 强制运营位数据一起返回
            return res;
        }
        // 切割id，命中对应的营销位数据
        marketPosIdList?.forEach(item => {
            item.startsWith('H') && !res.headData && (res.headData = beginningMarketPosData?.filter(i => i.posId === item)[0] || null);
            item.startsWith('P') && !res.popData && (res.popData = popoverMarketPosData?.filter(i => i.posId === item)[0] || null);
            item.startsWith('C') && !res.cardData && (res.cardData = productCardMarketPosData?.filter(i => i.posId === item)[0] || null);
        });
        return res;
    }, [marketPosIdList, beginningMarketPosData, popoverMarketPosData, productCardMarketPosData]);

    // 点击运营位跳转(判断是否需要登录)
    const clickJumpLinkHandler = (url: string, isNeedLogin: boolean, trackData?: {name: string, value: string}) => {
        if (isNeedLogin && !userinfo?.hasLogin) {
            verifyHandler().then(() => {
                window.open(url, '_blank');
                sendMonitor({
                    category: '文章详情页',
                    name: trackData?.name || '跳转登录',
                    value: trackData?.value || url,
                });
            });
        } else {
            window.open(url, '_blank');
            sendMonitor({
                category: '文章详情页',
                name: trackData?.name || '运营位跳转',
                value: trackData?.value || url,
            });
        }
    };

    // 通过文章id获取运营位配置ID,再根据运营位ID从bos读取配置数据（「文章顶部T、弹窗P、产品卡片C、文章底部 & 右侧运营位」）
    const replaceUrl = (url: string) => {
        return replaceOfString(
            url,
            {':env/': env === 'online' ? '' : 'pre_'}
        );
    };
    useOnMount(async () => {
        Promise.allSettled([
            netService.get<{env: string}, string[]>(replaceOfString(urlConst.GET_ARTICLE_MARKETING_POS, {':id': id}), {
                env: env,
            }),
            netService.get(replaceUrl(urlConst.GET_ARTICLE_BEGINNING_MARKET_POS)),
            netService.get(replaceUrl(urlConst.GET_ARTICLE_POPOVER_MARKET_POS)),
            netService.get<null, ArticleProductCardMarketPosObj[]>(replaceUrl(urlConst.GET_ARTICLE_PRODUCT_CARD_MARKET_POS)),
            netService.get<null, {img: string, link: string}>(replaceUrl(urlConst.GET_ARTICLE_RIGHT_MARKET_POS)),
        ]).then(res => {
            const [posDataRes, beginningMarketPosRes, popoverMarketPosRes, productCardMarketPosRes, rightMarketPosRes] = res;
            if (posDataRes.status === 'fulfilled') {
                setPosData(posDataRes.value.result);
            }
            if (beginningMarketPosRes.status === 'fulfilled') {
                setBeginningMarketPosData(beginningMarketPosRes.value as unknown as ArticleBeginningMarketPosObj[]);
            }
            if (popoverMarketPosRes.status === 'fulfilled') {
                setPopoverMarketPosData(popoverMarketPosRes.value as unknown as ArticlePopoverMarketingPosObj[]);
            }
            if (productCardMarketPosRes.status === 'fulfilled') {
                setProductCardMarketPosData(productCardMarketPosRes.value as unknown as ArticleProductCardMarketPosObj[]);
            }
            if (rightMarketPosRes.status === 'fulfilled') {
                setRightMarketPosData(rightMarketPosRes.value as unknown as {img: string, link: string});
            }
            const errorMsgList = [
                '获取文章运营位ID数据失败:',
                '获取文章顶部运营位数据失败:',
                '获取文章弹窗运营位数据失败:',
                '获取文章产品卡片运营位数据失败:',
                '获取文章右侧运营位数据失败:',
            ];
            res.forEach((i, index) => {
                if (i.status === 'rejected') {
                    if (index === 0) {
                        setPosData(['H00001', 'P00001']);
                    }
                    console.error(errorMsgList[index], i.reason);
                }
            });
        });
        // 如果是 AI 生成的文章(当 tag 为 "更多内容", 即 id 为 325 时)请求底部图片数据
        if (articleTags.find(i => i.id === 325 || i.id === 274)) {
            netService.get(replaceUrl(urlConst.GET_ARTICLE_AI_BOTTOM_MARKET_POS)).then(res => {
                setAiBottomMarketPosData(res as unknown as {img: string});
            });
        }
    });

    return {marketPosDataMap, rightMarketPosData, aiBottomMarketPosData, clickJumpLinkHandler};
};
