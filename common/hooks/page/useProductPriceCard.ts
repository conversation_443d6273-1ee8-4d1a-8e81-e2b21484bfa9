import {useState, useEffect} from 'react';
import {message} from 'antd';
import {utc} from 'moment';
import {cloneDeep} from 'lodash';
import sha256 from 'crypto-js/sha256';
import Base64 from 'crypto-js/enc-base64';
import {Ioc} from '@baidu/bce-decorators';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {netService, UDynamicService} from '@baidu/bce-services';
import {UUserService} from '@common/services/userinfo';
import {V5G015ItemObj, PriceTableObj} from '@common/interface/blocks';
import {CampaignInfoItemObj, ProductPriceDetailObj, ProductCampaignInfoObj, ProductPriceParamsObj, ProductPriceSkuObj} from '@common/interface/page';
import {ProductUserType} from '@common/interface/common';
import {urlConst} from '@common/constant/urlConst';
import {ULoginModal} from '@common/components/ULoginModal/ULoginModal';
import {UActivateModal} from '@common/components/UActivateModal/UActivateModal';
import {pushProduct2Crm, sendMonitor} from '@common/helper/page';
import {URealNameModal} from '@common/components/URealNameModal/URealNameModal';

type LoginCallbackFn = (userinfo?: UserinfoObj) => void;
type ActiveCallbackFn = (activeType?: any) => void;
type CheckUserTypeCallbackFn = (uerType?: ProductUserType) => void;
type SelectChangeCbFn = (name: keyof PriceTableObj, value: string, isFirst?: boolean) => void;

const PRODUCT_PRICE_SHA_KEY = '28545491-f8f6-48b3-8634-330060bd5abc';

interface TimeUnitMapObj {
    [key: string]: string;
  }
const TIME_UNIT_MAP: TimeUnitMapObj = {
    'second': '秒',
    'minute': '分钟',
    'hour': '小时',
    'day': '天',
    'month': '月',
    'year': '年',
};
// 一维：userLimit，二维：realNameLimit
const CAMPAIGN_TITLE_TAG = [
    ['', '企业用户专享', '个人用户专享'],
    ['限产品首购', '限企业产品首购', '限个人产品首购'],
    ['新用户专享', '企业新用户专享', '个人新用户专享'],
];

const getCampaignTitleTag = (userLimit: number, realNameLimit: number) => {
    if (typeof userLimit === 'number' && typeof realNameLimit === 'number') {
        return CAMPAIGN_TITLE_TAG[userLimit] ? (CAMPAIGN_TITLE_TAG[userLimit][realNameLimit] || '') : '';
    }
    return '';
};

const BUY_URL: {
    [key: string]: string;
} = {
    BCC: '#/bcc/activity~product=activitybcc&config=',
    EIP: '#/eip/activity~product=activityeip&config=',
    CDN: '#/cdn/activity~product=activitycdn&campaignId=',
    CDS: '#/cds/activity~product=activitycds&config=',
    BOS: '#/bos/activity~product=activitybos&config=',
    FACE: '#/aiDay/activity~product=activityface&config=',
    IMAGE_RECOGNITION: '#/aiDay/activity~product=activityimage&config=',
    LSS: '#/lss/activity~product=activitylss&config=',
    OCR: '#/aiDay/activity~product=activityocr&config=',
    SPEECH: '#/speech/activity~product=activityspeech&config=',
};
// 生成订单页链接
const genJumpLink = (serviceType: string, campaignId: string) => {
    if (!Object.keys(BUY_URL).includes(serviceType)) {
        return [null, null];
    }
    // 活动价订单链接映射关系
    const prefixUrl = `/campaign/order.html${typeof window === 'undefined' ? '' : location.search}`;
    const prefixWapUrl = `/campaign/app/order/index.html${typeof window === 'undefined' ? '' : location.search}`;
    return [prefixUrl + BUY_URL[serviceType] + campaignId, prefixWapUrl + BUY_URL[serviceType] + campaignId];
};

// 活动价以.00结尾，自动省略掉小数后面00
const getRevisePrice = (price: string) => {
    return price === 'null' ? '' : price.replace(/\.00$/, '');
};

/**
 * 产品页组件获取活动价信息，并支持判断用户状态（登录、实名与激活）
 * 返回：当前参数，当前商品，活动展示信息，价格加载状态、登录与激活处理，判断用户状态函数、下拉框筛选事件处理
 * @param isCheckUserType 是否需要校验用户类型
 * @param isImmediately 是否立即执行,默认true
 * @param item 价格卡片数据
 * @param loginCallback 登录回调
 * @param activeCallback 激活回调
 * @param checkUserTypeCallback 校验完用户实名与否、用户类型之后的回调
 * @param genLoginUrl 生成登录Url
 * @returns [curParameters, chooseData, productCampaignInfo, priceLoading, loginHandler, activeHandler, checkUserTypeHandler, handleSelectChange]
 */

const uUserService = Ioc(UUserService);

export const useProductPriceCard = ({
    isCheckUserType = true, isImmediately = true, item, loginCallback, activeCallback, checkUserTypeCallback, genLoginUrl,
}: {
    isCheckUserType?: boolean;
    isImmediately?: boolean;
    item?: V5G015ItemObj;
    loginCallback?: LoginCallbackFn;
    activeCallback?: ActiveCallbackFn;
    checkUserTypeCallback?: CheckUserTypeCallbackFn;
    genLoginUrl?: () => string;
}): [
    UserinfoObj,
    V5G015ItemObj['parameters'],
    PriceTableObj,
    ProductCampaignInfoObj,
    boolean,
    (cb: LoginCallbackFn) => void,
    (cb: ActiveCallbackFn) => void,
    (cb: CheckUserTypeCallbackFn) => void,
    SelectChangeCbFn
] => {
    const [userinfo, setUserinfo] = useState(uUserService.userinfo);
    const [curParamsTable, setCurParamsTable, curParamsTableRef] = useStateRef(item?.paramsTable || [{}]); // 当前的所有商品参数
    const [curParameters, setCurParameters, curParametersRef] = useStateRef(item?.parameters || [{}]); // 当前参数
    const [chooseData, setChooseData] = useState(curParamsTable[0] || item?.paramsTable[0] || {}); // 当前选中的商品
    // 初始化展示活动信息默认值：若初始产品需要询价，会出现角标闪烁
    const [productCampaignInfo, setProductCampaignInfo] = useState<ProductCampaignInfoObj>({
        price: null,
        prePrice: null, // 登录前展示的价格
        delPrice: null,
        titleTag: item?.titleTag,
        hotTags: item.hotTags,
        unit: null,
        href: null,
        mobileHref: null,
    });
    const [priceLoading, setPriceLoading] = useState(false);

    // 校验用户实名与否和用户类型
    const checkUserTypeHandler = (cb: CheckUserTypeCallbackFn = checkUserTypeCallback) => {
        netService.get<null, {code: number, message: string}>(urlConst.GET_ACCOUNT_STATUS).then(response => {
            let status: ProductUserType = null;
            if (response.success && response.result) {
                const {code} = response.result;
                if (code === 0) {
                    status = 'READY';
                } else if (code === 200) {
                    status = 'NO_LOGIN';
                } else if (code === 201) {
                    status = 'NO_ACTIVE';
                } else if (code === 204) {
                    status = 'NO_REALNAME';
                }
            } else {
                console.info(response);
                message.error('接口异常，请重试');
            }
            cb && cb(status);
        }).catch(e => {
            console.info(e);
            message.error('接口异常，请重试');
        });
    };

    // 活动信息有活动价显示活动价+划线，无活动价显示原价
    const getProductCampaignInfo = (price: string, unit: string, delPrice?: string, campaignInfo?: CampaignInfoItemObj, serviceType?: string) => {
        if (campaignInfo) {
            const {buyCountLimit, newUserLimit, realNameLimit, catalogPrice, campaignPrice} = campaignInfo;
            const titleTag = getCampaignTitleTag(newUserLimit, realNameLimit) || '';
            const hotTags = [];
            // 优惠保留一位小数，如2.8折
            const discountRatio = campaignPrice && (catalogPrice > campaignPrice) ? (Math.ceil(campaignPrice / catalogPrice * 100) / 10) : null;
            discountRatio && hotTags.push({text: `限时${discountRatio}折起`, isRed: true});
            buyCountLimit !== 0 && hotTags.push({text: `限购${buyCountLimit}件`, isRed: false});
            const [pcLink, wapLink] = campaignInfo?.campaignId ? genJumpLink(serviceType, campaignInfo.campaignId) : [null, null];
            setProductCampaignInfo({
                titleTag,
                hotTags,
                price,
                prePrice: delPrice,
                delPrice: `¥${delPrice}`,
                unit,
                href: pcLink,
                mobileHref: wapLink,
            });
        } else {
            setProductCampaignInfo({
                titleTag: '',
                hotTags: [],
                price: price || curParamsTableRef.current?.[0]?.price, // 使用当前商品价格进行兜底
                prePrice: null,
                delPrice: delPrice ? `¥${delPrice}` : '',
                unit,
                href: null,
                mobileHref: null,
            });
        }
    };
    // 生成活动价sign
    const genProductPriceSign = (item: ProductPriceSkuObj, flavor?: string) => {
        const sign: string = `${item.serviceType}${item.region}${item.duration}${item.timeUnit}${flavor || ''}`;
        const message = Base64.stringify(sha256(sign + PRODUCT_PRICE_SHA_KEY));
        return message;
    };
    const assembleFlavor = (flavor: ProductPriceSkuObj['flavor'], flavorDisplayName: ProductPriceSkuObj['flavorDisplayName']) => {
        if (!flavorDisplayName || !flavor) {
            return flavor;
        }
        const resFlavor = [];
        for (const item of flavor) {
            // 剔除 subServiceType = default
            if (item.name === 'subServiceType' && item.value === 'default') {
                continue;
            }
            // 拼装单位
            const flavorDisplayNameTarget = flavorDisplayName.find(i => i.flavor === item.name);
            if (flavorDisplayNameTarget?.unitPrefix) {
                item.value = `${item.value}${flavorDisplayNameTarget.unitPrefix.toLowerCase()}`;
            }
            resFlavor.push(item);
        }
        return resFlavor;
    };
    // 生成活动价请求参数
    const genPriceParams = (item: ProductPriceSkuObj) => {
        const hasTime = Array.isArray(item.dimension) ? item.dimension.includes('time') : item.packageTime;
        const newFlavor = assembleFlavor(item.flavor, item.flavorDisplayName);
        const params = {
            'accountId': '',
            'agentAccountId': '',
            'serviceType': item.serviceType,
            'subServiceType': item.subServiceType,
            'chargeItemName': item.chargeItem,
            'region': item.region,
            'scene': 'NEW',
            'flavor': {
                'flavorItems': newFlavor,
            },
            'time': {
                'startTime': utc(new Date()),
                'period': `P${item.duration}${item.timeUnit[0].toUpperCase()}`,
            },
            'count': 1,
            'signAuth': genProductPriceSign(item, JSON.stringify(newFlavor)),
        };
        if (hasTime) {
            return params;
        } else {
            const {time, ...newParams} = params;
            return newParams;
        }
    };

    // 选择活动信息: 过滤未过期活动>cms侧选择的活动Id>cms侧活动最低价>原价
    const checkTargetCampaign = (campaignInfoList: CampaignInfoItemObj[], targetCampaignId?: string) => {
        let maxDiscountIndex = -1;
        let maxDiscount = -1;
        const now = new Date();
        // original: CMS侧活动展示原价逻辑
        if (targetCampaignId === 'original') {
            return -2;
        }
        const targetCampaignIndex = targetCampaignId
            ? campaignInfoList.findIndex(item => (
                (item.campaignId === targetCampaignId) && (new Date(item.expirationTime) > now)
            )) : -1;

        if (targetCampaignIndex >= 0) {
            return targetCampaignIndex;
        } else {
            campaignInfoList.forEach((item, index) => {
                if ((item.catalogPrice - item.campaignPrice > maxDiscount) && (new Date(item.expirationTime) > now)) {
                    maxDiscount = item.catalogPrice - item.campaignPrice;
                    maxDiscountIndex = index;
                }
            });
        }
        return maxDiscountIndex;
    };

    // 用户侧询价
    const getPrice = (priceConfig: ProductPriceSkuObj[] | ProductPriceSkuObj, targetCampaignId?: string) => {
        setPriceLoading(true);
        netService.post<{data: ProductPriceParamsObj[]}, ProductPriceDetailObj[]>(urlConst.POST_PRODUCT_PRICE,
            Array.isArray(priceConfig)
                ? {data: priceConfig.map(item => genPriceParams(item))}
                : {data: [genPriceParams(priceConfig)]}
        ).then(res => {
            if (res && res.success && res.result) {
                const result = res.result;
                const conf = Array.isArray(priceConfig) ? priceConfig[0] : priceConfig;
                const unit = `/${conf.duration === 1 ? '' : `${conf.duration}个`}${TIME_UNIT_MAP[conf.timeUnit]}`;
                // 判断长度
                const isMergeProduct = result.length > 1;
                if (isMergeProduct) {
                    let allDelPrice = 0;
                    let allPrice = 0;
                    result.forEach(item => {
                        allDelPrice = allDelPrice + item.catalogPrice;
                        allPrice = allPrice + item.price;
                    });
                    // 无活动信息，不展示划线价
                    getProductCampaignInfo(getRevisePrice(allPrice.toString()), unit);
                } else {
                    const data = result[0];
                    if (data?.campaignInfoList) {
                        const {campaignInfoList}: {campaignInfoList?: CampaignInfoItemObj[]} = data;
                        // 获取需要展示的活动Index
                        const maxDiscountIndex = checkTargetCampaign(campaignInfoList, targetCampaignId);
                        // eslint-disable-next-line max-depth
                        if (maxDiscountIndex >= 0) {
                            const maxDiscountItem = campaignInfoList[maxDiscountIndex];
                            getProductCampaignInfo(
                                getRevisePrice(`${maxDiscountItem.campaignPrice}`),
                                unit,
                                getRevisePrice(`${maxDiscountItem.catalogPrice}`),
                                maxDiscountItem,
                                conf.serviceType
                            );
                        } else {
                            // 有活动价但期望展示原价
                            const price = maxDiscountIndex === -2 ? campaignInfoList[0]?.catalogPrice : data.price;
                            // 有活动信息，但活动均过期，不展示划线价
                            getProductCampaignInfo(getRevisePrice(`${price}`), unit);
                        }
                    } else {
                        // 无活动信息，不展示划线价
                        getProductCampaignInfo(getRevisePrice(`${data.price}`), unit);
                    }
                }
            }
            setPriceLoading(false);
        }).catch(() => {
            setPriceLoading(false);
        });
    };

    const handleSelectChange = (name: keyof PriceTableObj, value: string, isFirst: boolean = false) => {
        // 如果没有paramsTable 表示只有一个产品且不需要询价
        const itemTemp = cloneDeep(item);
        if (itemTemp?.paramsTable) {
            const selectItem = itemTemp.paramsTable?.filter(i => i[name] === value); // 从商品池过滤出满足当前条件的商品
            if (selectItem && selectItem.length > 0) {
                setCurParamsTable(isFirst ? itemTemp.paramsTable : selectItem);
                let chooseDataList = [...selectItem];
                const curParams = curParametersRef.current.map(param => {
                    // 选中的name，除了改值，其他不处理
                    if (param.name === name) {
                        param.value = value;
                    } else {
                        // 其他name: 从当前商品池selectItem过滤出当前name的可选值，组成selectList
                        // 如果当前的param value存在selectList中，则不修改
                        const selectListTemp: Array<PriceTableObj[keyof PriceTableObj]> = [];
                        selectItem.forEach(i => {
                            if (i.hasOwnProperty(param.name)) {
                                selectListTemp.push(i[param.name]);
                            }
                        });
                        param.selectList = Array.from(new Set(selectListTemp));
                        if (!selectListTemp.includes(param.value)) {
                            param.value = param.selectList[0];
                        }
                    }
                    chooseDataList = chooseDataList.filter(data => {
                        return data[param.name].toString() === param.value.toString();
                    });
                    return param;
                });
                const newChooseData = chooseDataList?.[0];
                if (chooseDataList.length > 0 && newChooseData?.connectSku) {
                    // 用户侧询价
                    getPrice(JSON.parse(chooseDataList[0]?.connectSku), chooseDataList[0]?.targetCampaignId);
                } else {
                    setProductCampaignInfo({
                        titleTag: itemTemp?.titleTag,
                        hotTags: itemTemp?.hotTags,
                        price: newChooseData?.price,
                        prePrice: newChooseData?.deletePrice?.split('/')[0].slice(1).trim() || '',
                        delPrice: newChooseData?.deletePrice,
                        unit: newChooseData?.unit,
                        href: null,
                        mobileHref: null,
                    });
                }
                if (isFirst) {
                    setCurParameters(itemTemp.parameters);
                } else {
                    setCurParameters(curParams);
                    setChooseData(chooseDataList[0] || {});
                }
            }
        } else {
            setCurParameters(itemTemp.parameters);
        }
    };

    // 处理登录情况
    const loginHandler = (cb: LoginCallbackFn = loginCallback) => {
        if (!userinfo) {
            // 登录中，必须要登录完才能执行下边逻辑，直接返回
            return;
        }
        if (userinfo.hasLogin) {
            cb && cb(userinfo);
        } else {
            const dy: UDynamicService = Ioc(UDynamicService);
            dy.open({
                component: ULoginModal,
                props: {
                    maskClosable: true,
                    hideCloseIcon: false,
                    loginUrl: genLoginUrl && genLoginUrl(),
                    onOk: ({regSuccess}) => {
                        /**
                         * userinfoInit会触发portalCommon中的登录事件，会触发UUserService.dispatch
                         * 从而导致userinfo更新，下边的useEffect会监听到更新，所以如果立即执行就不需要在这里执行回调函数了
                         */
                        if (window.require) {
                            (window.require as any)(['portalCommon'], (portalCommon: {
                                userinfoInit: (cb: (info?: UserinfoObj) => void, isImmediately?: boolean) => void;
                            }) => {
                                portalCommon.userinfoInit(info => {
                                    regSuccess && pushProduct2Crm();
                                    !isImmediately && cb && cb(info);
                                });
                            });
                        } else {
                            window.portalCommon.userinfoInit(info => {
                                !isImmediately && cb && cb(info);
                                regSuccess && pushProduct2Crm();
                            });
                        }
                    },
                },
            });
        }
    };

    // 处理激活情况
    const activeHandler = (cb: ActiveCallbackFn = activeCallback) => {
        const dy: UDynamicService = Ioc(UDynamicService);
        dy.open({
            component: UActivateModal,
            props: {
                maskClosable: true,
                hideCloseIcon: false,
                onOk: () => {
                    // 激活成功 打开实名验证弹窗，无论是否成功均执行cb
                    sendMonitor({
                        category: 'iframe',
                        name: '实名',
                        value: '产品页-激活后实名-打开',
                        action: 'popup',
                    });
                    Ioc(UDynamicService).open({
                        component: URealNameModal,
                        props: {
                            maskClosable: true,
                            hideCloseIcon: false,
                            onOk: () => {
                                sendMonitor({
                                    category: 'iframe',
                                    name: '实名',
                                    value: '产品页-激活后实名-成功',
                                    action: 'popup',
                                });
                                cb && cb();
                            },
                            onCancel: () => {
                                sendMonitor({
                                    category: 'iframe',
                                    name: '实名',
                                    value: '产品页-激活后实名-退出',
                                    action: 'popup',
                                });
                                cb && cb();
                            },
                        },
                    });
                },
            },
        });
    };
    useOnMount(() => {
        uUserService.userinfo && setUserinfo({...uUserService.userinfo});
        uUserService.subscribe(info => {
            setUserinfo({...info});
        });
    });
    useEffect(() => {
        if (userinfo && isImmediately) {
            loginHandler();
            userinfo.hasLogin && isCheckUserType && checkUserTypeHandler();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userinfo]);

    return [userinfo, curParameters, chooseData, productCampaignInfo, priceLoading,
        loginHandler, activeHandler, checkUserTypeHandler, handleSelectChange];
};
