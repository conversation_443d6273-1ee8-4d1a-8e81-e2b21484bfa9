import {isIE} from '@common/helper/page';
import {useEffect} from 'react';

export function useTypeHistory(fn: (str: string) => void): (str: string) => void {
    const setType = (type: string) => {
        // ie中会报错，暂时先排除IE浏览器
        if (!type || (type !== window.location.pathname.split('/').slice(-1)[0]) && !isIE()) {
            // 拼接新的url
            const url = `${window.location.origin}/news/${type}`;
            // 把新的路径塞到history中，从而更换地址，而不会刷新页面
            history.pushState(null, null, url);
        }
    };

    // 初始化监听浏览器回退事件
    useEffect(() => {
        window.onpopstate = () => {
            fn(window.location.pathname.split('/').slice(-1)[0]);
        };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return setType;
}
