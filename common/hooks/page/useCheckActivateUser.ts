import {message} from 'antd';
import {debounce} from 'lodash';
import {Ioc} from '@baidu/bce-decorators';
import {urlConst} from '@common/constant/urlConst';
import {netService, UDynamicService} from '@baidu/bce-services';
import {UActivateModal} from '@common/components/UActivateModal/UActivateModal';
import {URealNameModal} from '@common/components/URealNameModal/URealNameModal';
import {useCheckUserinfo} from '../useCheckUserinfo';

type LoginCallbackFn = (userinfo: UserinfoObj) => void;
type VerifyWithRealNameProps = (cb?: {
    regSuccessCb?: any;
    activateSuccessCb?: any;
    realNameSuccessCb?: any;
}) => Promise<number | void>;

/**
 * useCheckActivateUserInfo: 二次封装useCheckUserInfo，用于PC端使用iframe支持判断用户是否登录和激活，并进行成功回调
 * @param genLoginUrl 生成登录Url（不配置则使用默认登录Url）
 * @returns [userinfo, loginHandler, verifyHandler]
 */
export const useCheckActivateUserInfo = ({
    genLoginUrl,
    mobile = false,
}: {
    genLoginUrl?: () => string;
    mobile?: boolean;
}): [
    UserinfoObj,
    (cb?: LoginCallbackFn) => void,
    () => Promise<number | void>,
    VerifyWithRealNameProps,
] => {
    const [userinfo, , loginHandler] = useCheckUserinfo({
        isCheckUserType: false,
        maskClosable: true,
        isImmediately: false,
        genLoginUrl: genLoginUrl || null,
    });
    // 验证用户是否登录 & 激活，成功后执行回调
    // 历史原因：激活弹窗（无需实名）成功后返回状态码201
    const verifyHandler = () => {
        return new Promise<number | void>((resolve, reject) => debounce(() => {
            loginHandler(info => {
                if (info.hasLogin) {
                    netService.get<null, {code: number, message: string}>(urlConst.GET_ACCOUNT_STATUS).then(res => {
                        if (res.success && res.result) {
                            const {code} = res.result;
                            if (code === 200) {
                                // 200: 进入未登录死链循环
                                reject(res);
                                message.error('接口异常，请重试');
                            } else if (code === 201) {
                                // 未激活-打开激活弹窗
                                if (mobile) {
                                    // 移动端自定义处理
                                    resolve(code);
                                } else {
                                    const dy: UDynamicService = Ioc(UDynamicService);
                                    dy.open({
                                        component: UActivateModal,
                                        props: {
                                            maskClosable: true,
                                            onOk: () => {
                                                // 激活成功
                                                resolve(code);
                                            },
                                        },
                                    });
                                }
                            } else {
                                // 其他情况（如未实名）：默认成功
                                resolve(code);
                            }
                        } else {
                            reject(res);
                            message.error('登录异常，请重试');
                        }
                    }).catch(e => {
                        reject(e);
                        console.error(e);
                        message.error('接口异常，请重试');
                    });
                } else {
                    reject(info);
                    message.error('登录异常，请重试');
                }
            });
        }, 1000, {leading: true, trailing: false})());
    };

    // 验证用户是否登录 & 激活 & 实名，成功后执行回调
    // 移动端：resolve的code为当前用户账户的初始态，业务需要根据当前的code自定义处理
    // 非移动端：resolve的code为当前弹窗处理后关闭的最终态
    // code: 0 - 均正常 204 - 实名失败 201 - 激活失败, 2040（新增 前端自定义）- 实名弹窗成功后返回
    const verifyHandlerWithRealName: VerifyWithRealNameProps = ({
        regSuccessCb, // 注册成功回调
        activateSuccessCb, // 激活弹窗成功触发
        realNameSuccessCb, // 实名弹窗成功触发
    }) => {
        // 激活弹窗(成功后自动触发实名弹窗)
        function handleActivate(resolve: (code: number) => void) {
            const dy: UDynamicService = Ioc(UDynamicService);
            dy.open({
                component: UActivateModal,
                props: {
                    maskClosable: true,
                    onOk: () => {
                        // 激活成功，若需要实名打开实名弹窗
                        activateSuccessCb && activateSuccessCb();
                        handleRealName(resolve);
                    },
                    onCancel: () => resolve(201),
                },
            });
        }

        // 实名弹窗
        function handleRealName(resolve: (code: number) => void) {
            const dy: UDynamicService = Ioc(UDynamicService);
            dy.open({
                component: URealNameModal,
                props: {
                    maskClosable: false,
                    hideCloseIcon: false,
                    onOk: () => {
                        // 实名成功默认code为0，如果有回调则在回调中触发登录成功后的处理
                        realNameSuccessCb && realNameSuccessCb();
                        resolve(2040);
                    },
                    onCancel: () => resolve(204),
                },
            });
        }

        return new Promise<number | void>((resolve, reject) => debounce(() => {
            loginHandler((info, isRegSuccess) => {
                if (isRegSuccess) {
                    // 新注册成功，触发注册成功回调
                    regSuccessCb && regSuccessCb();
                }
                if (info.hasLogin) {
                    netService.get<null, {code: number, message: string}>(urlConst.GET_ACCOUNT_STATUS).then(res => {
                        if (res.success && res.result) {
                            const {code} = res.result;
                            if (code === 200) {
                                // 200: 进入未登录死链循环
                                reject(res);
                                message.error('接口异常，请重试');
                            } else if (mobile) {
                                // 移动端根据code自定义处理
                                resolve(code);
                            } else {
                                // 非移动端 弹窗处理
                                switch (code) {
                                    case 201:
                                        handleActivate(resolve);
                                        break;
                                    case 204:
                                        // 实名成功后，更新登录态
                                        handleRealName(resolve);
                                        break;
                                    default:
                                        resolve(code);
                                }
                            }
                        } else {
                            reject(res);
                            message.error('登录异常，请重试');
                        }
                    }).catch(e => {
                        reject(e);
                        console.error(e);
                        message.error('接口异常，请重试');
                    });
                } else {
                    reject(info);
                    message.error('登录异常，请重试');
                }
            });
        }, 1000, {leading: true, trailing: false})());
    };

    return [userinfo, loginHandler, verifyHandler, verifyHandlerWithRealName];
};
