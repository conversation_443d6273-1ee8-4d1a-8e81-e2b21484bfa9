/**
 * @file 首页组合组合购模块common hook
 */

import {abtestPromise} from '@common/helper/page';
import {useOnMount} from '@baidu/bce-hooks';
import {IndexMergePurchaceModelObj, indexMergePurchaceModel, indexMoreRecommend} from '@common/components/page/IndexPurchaceItem/model';
import {useState} from 'react';
import {IS_PROD_ONLINE} from '@common/constant/variableConst';

indexMergePurchaceModel.modules.push(indexMoreRecommend);

export const useIndexMergePurchace = (groupBuyData: IndexMergePurchaceModelObj) => {
    const [data, setData] = useState(indexMergePurchaceModel);
    useOnMount(async () => {
        const resVersion = await abtestPromise({
            id: IS_PROD_ONLINE ? '97f37845688340c88287adf22966d673' : 'b5ef86129c6b432186d081087527c6c1',
            defaultVersion: 1,
            env: IS_PROD_ONLINE ? 'online' : 'sandbox',
        });
        if (resVersion === 2) {
            setData(groupBuyData);
        }
    });
    return [data];
};
