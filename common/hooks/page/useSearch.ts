import crypto from 'crypto';
import {useState} from 'react';
import {reduce} from 'lodash';
import {netService, ResponseObj} from '@baidu/bce-services';
import {searchState2url, getSearchParams, getTopSearchParams, uploadUserAction} from '@components/search/utils';
import {urlConst} from '@common/constant/urlConst';
import {
    PageQuery,
    RecommendData,
    SearchData,
    SearchParams,
    SearchState,
    SearchType,
    TopSearchData,
    TopSearchFullData,
    TopSearchParams,
    TypeCount,
    TypeCountResObj,
} from '@common/interface/search';
import {getAntiParam} from '@common/helper/page';
import {useOnMount, useOnUpdate, useStateRef} from '@baidu/bce-hooks';
import {Base64} from 'js-base64';
import {usePassMachineNew} from '../usePassMachineNew';

function createUid(query: string) {
    const md5 = crypto.createHash('md5');
    md5.update(query + String(new Date().getTime()));
    return md5.digest('hex');
}

function resolvePageParam(url: string): PageQuery {
    const urlObj = new URL(url);
    const pathList = urlObj.pathname.split('/');

    const res: PageQuery = {
        query: pathList?.[2] ? Base64.decode(pathList?.[2]) : '',
        type: pathList[3]?.split('-')?.[0] as SearchType || SearchType.ALL,
        pageNo: pathList[3]?.split('-')?.[1] || '1',
        from: urlObj.searchParams.get('from') || '',
        status: urlObj.searchParams.get('status') || 'ONLINE',
    };

    return res;
}

function getHistoryArr(): string[] {
    return JSON.parse(localStorage.getItem('searchHistory')) || [];
}
// 修改H5历史栈
function pushH5History(state: SearchState) {
    const newUrl = searchState2url(state);

    const supportedPushState = (typeof window.history.pushState === 'function');
    if (supportedPushState && location.href !== newUrl) {
        // 如果是因为 popstate 触发的 change，此时就不要进行 pushState 了
        // 否则历史记录就不太符合预期了，永远保持在同一个，进行不下去了
        // FIXME 好像有时候会被触发两次，貌似是 BCse.Search 的问题.
        window.history.pushState(state, '', newUrl);
        if (typeof window._hmt === 'object') {
            // @ts-ignore
            window._hmt.push(['_trackPageview', new URL(newUrl).pathname]);
        }
    }
}

// 记录搜索历史
function recordSearchHis(query: string) {
    const preHistory = getHistoryArr().filter(item => item !== query.trim());
    preHistory.unshift(query.trim());
    localStorage.setItem('searchHistory', JSON.stringify(preHistory.splice(0, 5)));
}

// 当前搜索范围为1-75，越界可能无result属性或dataList和searchInfo属性为null
// 因当前组件逻辑没有对void属性提供支持，故进行数据填充
function searchDataFill(data: SearchData): SearchData {
    if (data && data.message !== 'QUERY_WORDS_INVALID') {
        return {
            dataList: data.dataList || [],
            searchInfo: data.searchInfo || {
                curPage: 1,
                totalNum: 0,
            },
        };
    }
    else {
        return {
            message: data.message,
            dataList: [],
            searchInfo: {
                curPage: 1,
                totalNum: 0,
            },
        };
    }
}

function topDataFill(data: TopSearchFullData) {
    const res: TopSearchFullData = {
        cmsRelInfo: {
            cards: [],
            textLinks: [],
            recommendCampaigns: [],
            recommendDocs: [],
            recommendProducts: [],
        },
        searchTopInfo: [],
    };
    if (data) {
        res.cmsRelInfo = data.cmsRelInfo || {
            cards: [],
            textLinks: [],
            recommendCampaigns: [],
            recommendDocs: [],
            recommendProducts: [],
        };
        res.searchTopInfo = data.searchTopInfo || [];
    }
    return res;
}

// cms配置推荐数据缺失
function isRecomDataMiss(data: TopSearchFullData) {
    return (
        !data.cmsRelInfo
        || !data.cmsRelInfo.recommendProducts
        || !data.cmsRelInfo.recommendProducts.length
        || !data.cmsRelInfo.recommendDocs
        || !data.cmsRelInfo.recommendDocs.length
        || !data.cmsRelInfo.recommendCampaigns
        || !data.cmsRelInfo.recommendCampaigns.length
    );
}

// 接入玉门关的 POST 请求
async function yumenguanPost<P, R>(
    url: string,
    params: P,
    passMachine?: () => void,
    headers?: Record<string, string>
) {
    let response: ResponseObj<R & {message?: any}, null> = null;
    let message: SearchData['message'] = null;
    try {
        response = await netService.post<P, R>(
            url,
            params,
            null as never,
            {
                headers: typeof window === 'undefined' ? {
                    'Content-Type': 'application/json',
                    ...headers,
                } : {
                    'Content-Type': 'application/json',
                    'Acs-Token': await getAntiParam(),
                    ...headers,
                },
            }
        );
    } catch (err: any) {
        if (passMachine && err && err.code === 'yumenguan check exception') {
            passMachine();
        }
        if (err.error && err.error.message === 'QUERY_WORDS_INVALID') {
            message = 'QUERY_WORDS_INVALID';
        }
    }
    return {
        ...response?.result,
        message,
    };
}

// 获取搜索结果条目数据
async function getSearchData(state: SearchState, passMachine?: () => void, headers?: Record<string, string>) {
    const result = await yumenguanPost<SearchParams, SearchData>(
        urlConst.PORTAL_SEARCH,
        getSearchParams(state),
        passMachine,
        headers
    );
    return searchDataFill(result);
}

// 获取搜索置顶信息
async function getTopFullData(searchState: SearchState, passMachine?: () => void, headers?: Record<string, string>) {
    // 只有第一页才会显示置顶卡片和文字链
    if (searchState.pageNo !== '1') {
        return {
            cmsRelInfo: {
                cards: [],
                textLinks: [],
                recommendDocs: [],
                recommendCampaigns: [],
                recommendProducts: [],
            },
            searchTopInfo: [],
        };
    }

    const result = await yumenguanPost<TopSearchParams, TopSearchFullData>(
        urlConst.GET_SEARCH_TOP,
        getTopSearchParams(searchState),
        passMachine,
        headers
    );

    if (result?.cmsRelInfo) {
        result.cmsRelInfo = reduce(result.cmsRelInfo, (result, value, key) => {
            result[key] = JSON.parse(value as any);
            return result;
        }, {} as any);
    }

    return topDataFill(result);
}

// 获取搜索结果条目数量
async function getTypeCountData(state: SearchState, passMachine?: () => void, headers?: Record<string, string>) {
    const result = await yumenguanPost<{query: string, fromPage?: string}, TypeCountResObj>(
        urlConst.GET_SEARCH_TYPECOUNT,
        {query: state.query, fromPage: state.fromPage},
        passMachine,
        headers
    );
    return result?.searchInfo?.type || [];
}

async function getRecommendData(topSearchFullData: TopSearchFullData) {
    // cms推荐数据没配全，获取默认数据
    if (isRecomDataMiss(topSearchFullData)) {
        const response: any = await netService.get(urlConst.GET_DEFAULT_SEARCH_TOP);
        return response;
    }
    else {
        return topSearchFullData.cmsRelInfo;
    }
}

export async function searchSSR(context: any) {
    let searchData: SearchData = {
        dataList: [],
        searchInfo: {
            totalNum: 0,
            curPage: 1,
        },
    };
    let typeCount: TypeCount = [];
    let recommendData: RecommendData = {
        recommendCampaigns: [],
        recommendDocs: [],
        recommendProducts: [],
    };
    let topData: TopSearchData = {
        cmsRelInfo: {
            cards: [],
            textLinks: [],
        },
        searchTopInfo: [],
    };

    const fromPage = context.req.url;

    let searchState: SearchState = {
        query: '', // query为''代表还未进行过搜索
        pageNo: '1',
        type: SearchType.ALL,
        from: '',
        searchReferer: '',
        status: 'ONLINE',
        uid: '',
    };

    searchState = {
        ...searchState,
        ...resolvePageParam(`http://${context.req.headers.host}${context.req.url}`),
    };

    if (searchState.query) {
        searchState.uid = createUid(searchState.query);
        [searchData, topData, typeCount] = await Promise.all([
            getSearchData({...searchState, fromPage}, null),
            getTopFullData({...searchState, fromPage}, null),
            getTypeCountData({...searchState, fromPage}, null),
        ]);
        recommendData = await getRecommendData(topData as TopSearchFullData);
    }
    return {
        searchState,
        searchData,
        typeCount,
        topData,
        recommendData,
    };
}

type verifyCode = {
    tk: string;
    ds: string;
};

type VerifyHeaderOptions = {
    topTkDs?: verifyCode;
    typeTkDs?: verifyCode;
    searchTkDs?: verifyCode;
};

// 参数为ssr渲染后的数据，用以进行初始化
export function useSearch(
    PsearchState: SearchState,
    PsearchData: SearchData,
    PtypeCount: TypeCount,
    PtopData: TopSearchData,
    PrecommendData: RecommendData
) {
    const [loading, setLoading] = useState<boolean>(false);
    const [searchData, setSearchData] = useState<SearchData>(PsearchData);
    const [typeCount, setTypeCount] = useState<TypeCount>(PtypeCount);
    const [recommendData, setRecommendData] = useState<RecommendData>(PrecommendData);
    const [topData, setTopData] = useState<TopSearchData>(PtopData);
    const [searchState, setSearchState, searchStateRef] = useStateRef(PsearchState);

    // 搜索相关的接口接入玉门关:
    // 1. /api/search/top 获取搜索置顶数据
    // 2. /api/search/type 获取搜索条目数量
    // 3. /api/portalsearch 获取搜索条目列表
    const {tkDs, showMachine} = usePassMachineNew();

    // 因为异步更新，searchState作为旧状态，newState参数为新状态
    const getData = async (
        newState: SearchState,
        isPopState: boolean,
        headerOptions: VerifyHeaderOptions = {}
    ) => {
        if (!newState.query) {
            return;
        }
        const oldState = {...searchStateRef.current};
        // 当uid为空的时候是一次新的搜索，需要生成一次uid
        // 这种情况是用户从地址栏进入搜索页，或者点击导航的搜索按钮
        if (!newState.uid) {
            newState.uid = createUid(newState.query);
        }

        setLoading(true);
        try {
            const searchData = await getSearchData(newState, showMachine, {...headerOptions.searchTkDs});
            setSearchData(searchData);
            uploadUserAction(newState, String(searchData.searchInfo.totalNum), '-', '-', '-');
        }
        catch (e) {
            setSearchData({
                dataList: [],
                searchInfo: {curPage: 1, totalNum: 0},
            });
            setTypeCount([]);
        }

        if (oldState.query === '' || newState.pageNo === '1') {
            const topData = await getTopFullData(newState, showMachine, {...headerOptions.topTkDs});
            setTopData(topData);
            setRecommendData(await getRecommendData(topData));
        }
        else {
            setTopData({
                cmsRelInfo: {
                    cards: [],
                    textLinks: [],
                },
                searchTopInfo: [],
            });
        }
        setLoading(false);

        // 若上次请求被玉门关拦截了, 即使 query 没变化也需要重新请求
        if (newState.query !== oldState.query || !typeCount.length) {
            setTypeCount(await getTypeCountData(newState, showMachine, {...headerOptions.typeTkDs}));
        }
        if (isPopState !== true) {
            pushH5History(newState);
        }
        setSearchState({
            ...newState,
            searchReferer: location.href,
        });
    };

    const handlePageChange = (page: number) => {
        const newSearchState = {
            ...searchState,
            pageNo: String(page),
        };
        getData(newSearchState, false);
    };

    const handleNavClick = (type: SearchType) => {
        const newSearchState = {
            ...searchState,
            type,
            pageNo: '1',
        };
        getData(newSearchState, false);
    };

    const handleSearch = (val: string, headerOptions: VerifyHeaderOptions = {}) => {
        const query = val.trim();
        if (!query) {
            return;
        }
        recordSearchHis(query);
        // 当用户点击搜索按钮时需要生成新的uid
        const newSearchState = {
            ...searchState,
            query,
            type: searchState?.from === 'doc' ? SearchType.DOC : SearchType.ALL,
            pageNo: '1',
            uid: createUid(query),
        };
        getData(newSearchState, false, headerOptions);
    };

    useOnMount(() => {
        if (searchState.query) {
            uploadUserAction(searchState, String(searchData.searchInfo.totalNum), '-', '-', '-');
            setSearchState({
                ...searchState,
                searchReferer: location.href,
            });
        }

        const handlePopState = () => {
            const query = resolvePageParam(window.location.href);
            const newSearchState = {
                ...searchStateRef.current,
                ...query,
            };
            getData(newSearchState, true);
        };

        window.addEventListener('popstate', handlePopState);
        return () => {
            window.removeEventListener('popstate', handlePopState);
        };
    });

    useOnUpdate(() => {
        handleSearch(searchStateRef.current.query, {searchTkDs: tkDs});
    }, [tkDs]);

    return {
        handleNavClick,
        handlePageChange,
        handleSearch,
        loading,
        recommendData,
        searchData,
        searchState,
        topData,
        typeCount,
    };
}
