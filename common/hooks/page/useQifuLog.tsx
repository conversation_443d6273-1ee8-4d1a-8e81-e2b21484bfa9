import {isSandbox} from '@components/Products/Tms/defaultModel';
import {useCallback, useEffect, useState} from 'react';

const sendLog = (url: string) => {
    const img = new Image();
    const timer = setTimeout(() => {
        img.src = url;
    }, 0);
    const clearTimer = () => {
        if (timer) {
            clearTimeout(timer);
        }
    };
    img.onload = clearTimer;
    img.onerror = clearTimer;
    img.onabort = clearTimer;
};

const service
        = isSandbox()
            ? 'http://xuper-fe-qilinlin.bcc-szth.baidu.com:8811'
            : 'https://xuper.baidu.com';
const target = isSandbox() ? '/xpid/info/checkargs' : '/n/cm/v4';

const url = isSandbox()
    ? 'https://xuper-fe-assets.bj.bcebos.com/static/hyperthymesia/sandbox/h.min.js'
    : 'https://xuper-fe-assets.cdn.bcebos.com/static/hyperthymesia/h.min.js';

declare global {
    interface Window {
        Hyperthymesia: any;
    }
}

export type LogObject = {
    log: (lid: number, option?: Record<string, any>) => void;
};

function useQifuLog(pid: number, lid?: number) {
    const [instance, setInstance] = useState<LogObject>({log: (lid: number) => lid});

    const initLog = useCallback((pid, lid) => {
        if (window.Hyperthymesia) {
            const h = new window.Hyperthymesia({
                pid,
                service,
                target,
                sendfunc: sendLog,
            });
            setInstance(h);
            if (lid) {
                h.log(lid);
            }
        }

    }, []);

    useEffect(() => {
        if (window.Hyperthymesia) {
            initLog(pid, lid);
        } else {
            const script = document.createElement('script');
            script.src = url;
            document.body.appendChild(script);
            script.onload = () => {
                if (window.Hyperthymesia) {
                    initLog(pid, lid);
                }
            };
        }

        return () => {
            setInstance(null);
        };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return [instance];
}

export default useQifuLog;
