import {useEffect, useRef} from 'react';

export const useInterval = (callback: () => any, delay: number) => {
    const savedCallback = useRef(null);

    // 保存新回调
    useEffect(() => {
        savedCallback.current = callback;
    });

    // 建立 interval
    useEffect(() => {
        let id: NodeJS.Timer = null;
        if (delay) {
            id = setInterval(() => {
                savedCallback.current();
            }, delay);
        }

        return () => clearInterval(id);
    }, [delay]);
};
