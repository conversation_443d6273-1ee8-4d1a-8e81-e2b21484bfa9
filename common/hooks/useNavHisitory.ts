/**
 * @file 设置标签和url中query数据联动，支持历史回退功能
 */

import {getAllParamsFromUrl} from '@baidu/bce-helper';
import {useEffect} from 'react';


const isIE = () => navigator.userAgent.toLowerCase().includes('trident');
/**
 * 获取和设置页面滚动的位置，并且绑定动画
 * @param option 第一个参数为URL的参数如?type=aaa中的type, 第二个参数为浏览器回退触发事件传参为type值
 * @returns current setType 设置type值
 */
export const useNavHistory: (type: string, fn: (str: string) => void) => (str: string) => void = (type, fn) => {

    const setType = (str: string) => {
        const query = getAllParamsFromUrl();
        if (!query[type]) {
            query[type] = null;
        }
        // ie中会报错，暂时先排除IE浏览器
        if ((!query[type] || query[type] !== str) && !isIE()) {
            // 拼接新的url
            const url = Object.keys(query).reduce((result: string, currentItem: string, currentIndex: number) => {
                return `${result}${currentIndex === 0 ? '?' : '&'}${currentItem}=${currentItem === type ? str : query[currentItem]}`;
            }, `${window.location.origin}${window.location.pathname}`);
            // 把新的路径塞到history中，从而更换地址，而不会刷新页面
            history.pushState(null, null, url);
        }
    };

    // 初始化监听浏览器回退事件
    useEffect(() => {
        window.onpopstate = () => {
            const query = getAllParamsFromUrl();
            fn(query[type]);
        };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return setType;
};
