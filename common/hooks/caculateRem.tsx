import type {ImageLoader} from 'next/dist/client/image';

const ROOT_VALUE = 10;

// 计算rem
export function pxToRem(px: number) {
    return [px / ROOT_VALUE, 'rem'].join('');
}

// 计算实际像素
export function pxToRealPx(px: number) {
    const rem = px / ROOT_VALUE;
    let rootFontSize = 12;
    if (typeof window !== 'undefined') {
        rootFontSize = parseFloat(
            getComputedStyle(document.documentElement).fontSize
        );
    }
    return rem * rootFontSize;
}

export const customImageLoader: ImageLoader = ({src}) => {
    return src;
};
