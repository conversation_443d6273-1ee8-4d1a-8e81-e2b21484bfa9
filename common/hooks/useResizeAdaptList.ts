/**
 * @file useResizeAdaptList.ts
 * @description 自适应列表的hook，主要用于处理媒体查询下的列表元素显示和隐藏
 */

import {useEffect, useRef, useState} from 'react';
import {debounce} from 'lodash';

interface ResizeAdaptListProps<T> {
    dataSource: Array<T & {opacity: number}>; // 原始数据，在原基础加上opacity
    isVisibleView: boolean; // 媒体查询条件
    resizeBoxRef: React.RefObject<HTMLElement>; // 列表容器的ref
}

function useResizeAdaptList<T>({
    dataSource,
    isVisibleView,
    resizeBoxRef,
}: ResizeAdaptListProps<T>) {
    const resizeHandlerRef = useRef<() => void>(() => {});
    const [dataList, setDataList] = useState<Array<T & {opacity?: number}>>(dataSource);

    useEffect(() => {
        if (dataSource && isVisibleView) {
            // 计算所有 benefit-item 的总宽度，并与父容器 benefit-box 的宽度进行比较。如果总宽度超过了父容器的宽度，则需要移除超出部分的元素。
            resizeHandlerRef.current = debounce(() => {
                const boxParentWidth = resizeBoxRef.current?.parentElement.offsetWidth;
                const infoItems = Array.from(resizeBoxRef.current?.children || []) as HTMLElement[];
                const itemWidths = [];
                for (const item of infoItems) {
                    const width = item.offsetWidth;
                    itemWidths.push(width);
                }
                // 计算需要保留的元素
                let accumulatedWidth = 0;
                const newDataList = [];
                for (let i = 0; i < infoItems.length; i++) {
                    accumulatedWidth += itemWidths[i];
                    // 如果加上当前元素后仍然不超过父容器宽度，则保留
                    if (accumulatedWidth <= boxParentWidth) {
                        dataSource[i].opacity = 1;
                    } else {
                        dataSource[i].opacity = 0;
                    }
                    newDataList.push(dataSource[i]);
                }
                // 更新 数据列表
                setDataList(newDataList as any);
            }, 300);
            resizeHandlerRef.current();
            window.addEventListener('resize', resizeHandlerRef.current);
        } else {
            resizeHandlerRef.current && window.removeEventListener('resize', resizeHandlerRef.current);
        }
        return () => {
            resizeHandlerRef.current && window.removeEventListener('resize', resizeHandlerRef.current);
        };
    }, [isVisibleView, resizeBoxRef, dataSource]);

    return {
        dataList,
    };
}

export default useResizeAdaptList;
