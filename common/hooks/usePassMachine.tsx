/**
 * @file 滑块校验 (已废弃，不要使用！！)
 * <AUTHOR>
 */

import {useState, useRef, useCallback} from 'react';
import {useOnMount} from '@baidu/bce-hooks';

export interface VerifyCode {
    tk: string;
    ds: string;
}

/**
 * 滑块校验
 * @returns [tkDs, initVcode] 弹出滑块校验弹窗
 */
export function usePassMachine(): [VerifyCode, () => void] {
    const [tkDs, setTkDs] = useState<VerifyCode>(null);
    const ref = useRef(null);
    // 初始化
    const captchaInit = () => {
        const PassMkd = (window as any).PassMachine.mkd;
        ref.current = new PassMkd({
            // **自定义验证码类型**
            type: 'slide', // spin:旋转（语意）验证, slide: 滑块验证
            // 产品线接入ak
            ak: '133c8cd028874abf21b81beaa441d699',
            // 开启弹窗模式
            maskModule: true,
            // 验证结果的回调函数
            verifySuccessFn: (data: {tk: string, ds: string}) => {
                ref.current.removeMask();
                window.passMachine = {
                    showing: false,
                    tkDs: data,
                };
                setTkDs(data);
                captchaInit();
            },
        });
    };
    // 弹出滑块校验弹窗
    const initVcode = useCallback(() => {
        // 避免滑块弹窗重复展示
        if (window?.passMachine?.showing) {
            return;
        }
        window.passMachine.showing = true;
        ref.current?.initVcode();
    }, [ref]);

    useOnMount(() => {
        if (!window.passMachine) {
            window.passMachine = {
                showing: false,
            };
        }
        if ((window as any).PassMachine && (window as any).PassMachine.mkd) {
            captchaInit();
        } else {
            const script = document.createElement('script');
            const s = document.getElementsByTagName('script')[0];
            script.defer = true;
            script.src = 'https://wappass.baidu.com/static/machine/js/api/mkd.js';
            script.addEventListener('load', () => {
                captchaInit();
            });
            setTimeout(() => {
                s.parentNode.insertBefore(script, s);
            });
        }
    });

    return [tkDs, initVcode];
}
