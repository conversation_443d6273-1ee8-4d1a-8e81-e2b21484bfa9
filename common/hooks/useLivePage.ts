import {useStateRef, useOnMount} from '@baidu/bce-hooks';
import {netService} from '@baidu/bce-services';
import {message} from 'antd';
import {LiveDetailObj, LiveStatus} from '@common/interface/page';
import moment from 'moment';
import {useState} from 'react';
import {LiveChatListItem} from '@components/live/LiveChat/LiveChat';
import {urlConst} from '@common/constant/urlConst';
import {DeveloperRolesType} from '@common/interface/common';
import {wxShare} from '@common/helper/page';
import {isMobile} from '@baidu/bce-helper';

async function getNewChatList(liveId: string, lastCommentId: string): Promise<LiveChatListItem[]> {
    const res = await netService.get<{liveId: string, lastCommentId: string}, LiveChatListItem[]>(
        urlConst.LIVE_COMMENT,
        {
            liveId,
            lastCommentId,
        }
    );
    return res.result;
}

async function delComment(id: string, liveId: string) {
    const res = await netService.post<{id: string, liveId: string}, boolean>(
        urlConst.DELETE_LIVE_COMMENT,
        {
            id,
            liveId,
        }
    );
    return res.result;
}

async function publishComment(content: string, liveId: string) {
    const res = await netService.post<{content: string, liveId: string}, boolean>(
        urlConst.PUBLISH_LIVE_COMMENT,
        {
            content,
            liveId,
        }
    );
    return res.result;
}

async function getDelCommentList(liveId: string) {
    const res = await netService.get<null, LiveChatListItem[], {liveId: string}>(
        urlConst.LIVE_COMMENT_DEL,
        null,
        {
            liveId,
        }
    );
    return res.result;
}


export function useLivePage({
    id,
    detailOrigin,
}: {
    id: string;
    detailOrigin: LiveDetailObj;
}) {
    const [detail, setDetail, detailRef] = useStateRef<Partial<LiveDetailObj>>(detailOrigin || {});
    const [liveTimeStartObj, setLiveTimeStartObj] = useState<{m: number, d: number, h: string, min: string}>({
        m: 0,
        d: 0,
        h: '00',
        min: '00',
    });
    // eslint-disable-next-line max-len
    const [cyberplayerFile, setCyberplayerFile, cyberplayerFileRef] = useStateRef<string>('');
    const [userService, setUserService] = useState<{isManager: boolean}>({isManager: false});

    const setCyberplayerFileFun = (detail: LiveDetailObj) => {
        switch (detail.status) {
            case LiveStatus.LIVING:
                detail.pullStreamUrl && setCyberplayerFile(detail.pullStreamUrl);
                return;
            case LiveStatus.LIVE_END:
                detail.replayUrl && setCyberplayerFile(detail.replayUrl);
        }
    };
    useOnMount(async () => {
        try {
            if (!detailRef.current.id) {
                const detailTem = (await netService.get<null, LiveDetailObj, {id: string}>(
                    urlConst.GET_LIVE_DETAIL,
                    null,
                    {id}
                )).result;
                document.title = `${detailTem.title}-百度开发者中心`;
                setDetail({
                    ...detailTem,
                    guests: detailTem.guests ? JSON.parse(detailTem.guests as any) : [],
                    relatedArticles: detailTem.relatedArticles ? JSON.parse(detailTem.relatedArticles as any) : [],
                });
            }
            const detail = detailRef.current;
            setCyberplayerFileFun(detail as any);
            const time = moment(detail.liveTimeStart);
            setLiveTimeStartObj({
                m: time.month() + 1,
                d: time.date(),
                h: time.hour() < 10 ? `0${time.hour()}` : `${time.hour()}`,
                min: time.minute() < 10 ? `0${time.minute()}` : `${time.minute()}`,
            });
            const userInfo = (await netService.get<unknown, {roles: DeveloperRolesType[]}>(urlConst.GET_DEVELOPER_USER_CURRENT)).result;
            if (userInfo?.roles?.includes('MANAGER')) {
                setUserService({isManager: true});
            }
            isMobile() && wxShare({
                desc: detailOrigin.wxDesc || detail.wxDesc,
            });
        } catch (e) {
            // eslint-disable-next-line no-console
            console.error('获取直播详情出错', e);
            message.error('获取直播详情出错');
        }
    });
    return {
        getNewChatList,
        delComment,
        getDelCommentList,
        publishComment,
        detail,
        detailRef,
        cyberplayerFileRef,
        cyberplayerFile,
        liveTimeStartObj,
        userService,
    };
}
