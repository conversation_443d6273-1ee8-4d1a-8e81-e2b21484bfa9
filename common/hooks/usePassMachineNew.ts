/**
 * @file 滑块校验-新版
 * <AUTHOR>
 */

import {useOnMount} from '@baidu/bce-hooks';
import {IS_PROD_SANDBOX} from '@common/constant/variableConst';
import {useRef, useState} from 'react';

interface VerifyCode {
    tk: string;
    ds: string;
}

interface MachinSDKObj {
    init: () => void; // init方法，调用后验证组件会展示出来
    hide: () => void; // hide方法，调用后验证组件会隐藏起来(用于验证成功后)
    close: () => void; // close方法，销毁人机验证控件,销毁组件后再次使用验证码需要重新new 组件
}

export const usePassMachineNew = () => {
    const isSdkLoadedRef = useRef(false);
    const [tkDs, setTkDs] = useState<VerifyCode>(null);
    const needInitWhenLoadSdk = useRef(false);
    const machineSDKRef = useRef<MachinSDKObj>(null);

    const sdkInit = () => {
        if (window.passMachine?.inited) {
            return;
        }
        machineSDKRef.current = new (window as any).sdkMachine({
            container: document.body, // 弹窗渲染的容器，默认在body
            title: '', // 组件弹窗标题
            ak: 'jwoLb9xguX8HmBtWwtSObqedLxbOzwO1',
            deviceType: 'pc', // 接入SDK的设备类型，支持：pc \ wap。前端主要用该字段获取反馈地址，如果pc、wap配置feedbackurl一致，默认pc即可，如果不同需要区分。
            type: 'spin', // 希望出现的验证方式（非特殊情况建议不填随机），目前支持 puzzle(滑块)、click(点选)、spin(转图)
            testUrl: '', // 需要测试时，咨询zhenglili或caixiaoyu
            zIndex: 10000,
            initApiSuccessFn() {
                machineSDKRef.current.init(); // SDK初始化完成后立即展示人机控件
            },
            errorFn(err: any) {
                // 此处回调除验证失败之外的所有失败回调，包括（接口请求失败，组件实例化失败等）；
                console.info('errfn___' + err.message || '');
            },
            verifySuccessFn(result: VerifyCode) {
                // 验证成功的回调（如果有多个验证组件，全部成功后回调）
                // 验证成功后，改回调会返回最新的ds、tk参数值，业务需将此处获取的值传回业务
                const data = {tk: result.tk, ds: result.ds};
                setTkDs(data);
                machineSDKRef.current.hide();
                window.passMachine = {
                    ...window.passMachine,
                    showing: false,
                    tkDs: data,
                };
            },
            verifyFailFn() {
                // 单个验证组件验证失败的回调
            },
            feedbackEvent() {
                // feedback中会返回组件中默认的意见反馈链接
                // return true 会中断组件中默认的意见反馈跳转；不return则回调函数执行后，继续跳转意见反馈链接
                return true;
            },
        });
        window.passMachine = {...window.passMachine, inited: true};
    };

    const showMachine = () => {
        if (window.passMachine?.showing) {
            return;
        }
        window.passMachine = {...window.passMachine, showing: true};
        if (window.passMachine?.inited) {
            // 已经初始化sdk了
            machineSDKRef.current.init();
        } else if ((window as any).sdkMachine) {
            // sdk未初始化，但已经加载了
            sdkInit();
        } else {
            // sdk未加载
            needInitWhenLoadSdk.current = true;
        }
    };
    useOnMount(() => {
        if (!(window as any).sdkMachine && !window.passMachine?.inited) {
            const script = document.createElement('script');
            const s = document.getElementsByTagName('script')[0];
            script.defer = true;
            const qatest = IS_PROD_SANDBOX ? '.qatest' : '';
            script.src = `https://passport${qatest}.baidu.com/static/sdk-machine/js/mkd_v2.js?_=${(new Date()).getTime()}`;
            script.addEventListener('load', () => {
                isSdkLoadedRef.current = true;
                needInitWhenLoadSdk.current && sdkInit();
            });
            setTimeout(() => {
                s.parentNode.insertBefore(script, s);
            });
        }
    });
    return {
        tkDs, // 人机验证组件返回的tk和ds
        showMachine, // 展示人机验证组件
        machineSDKRef, // 人机验证组件实例
    };
};
