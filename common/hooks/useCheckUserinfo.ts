import {LOGIN_URL} from '@common/constant/variableConst';
import {isMobile} from '@baidu/bce-helper';
import {Ioc} from '@baidu/bce-decorators';
import {useOnMount, useStateRef} from '@baidu/bce-hooks';
import {netService, UDynamicService} from '@baidu/bce-services';
import {UUserService} from '@common/services/userinfo';
import {useState, useEffect, MutableRefObject} from 'react';
import {UEnvService} from '@common/services/env';
import {pushProduct2Crm, pushQifuAndTmsCrm} from '@common/helper/page';
import {ULoginModal} from './../components/ULoginModal/ULoginModal';
import {urlConst} from './../constant/urlConst';
import {UserType} from './../interface/common';

type LoginCallbackFn = (userinfo: UserinfoObj, isRegSuccess?: boolean) => void;
type CheckUserTypeCallbackFn = (uerType: UserType) => void;

const uUserService = Ioc(UUserService);

/**
 * 校验用户信息，支持判断是否登录和判断是个人用户还是企业用户
 * @param isCheckUserType 是否需要校验用户类型, 默认true
 * @param isImmediately 是否立即执行,默认true
 * @param loginCallback 登录回调
 * @param maskClosable mask是否允许关闭, 默认false
 * @param hideCloseIcon 是否隐藏关闭按钮, 默认false
 * @param checkUserTypeCallback 校验完用户实名与否、用户类型之后的回调
 * @param genLoginUrl 生成登录Url
 * @returns [hasLogin, userType, loginHandler, checkUserTypeHandler, userinfoRef]
 */

export const useCheckUserinfo = ({
    isCheckUserType = true, isImmediately = true, maskClosable, hideCloseIcon, loginCallback, checkUserTypeCallback, genLoginUrl,
}: {
    isCheckUserType?: boolean;
    isImmediately?: boolean;
    maskClosable?: boolean;
    hideCloseIcon?: boolean;
    loginCallback?: LoginCallbackFn;
    checkUserTypeCallback?: CheckUserTypeCallbackFn;
    genLoginUrl?: () => string;
}): [UserinfoObj, UserType, (cb?: LoginCallbackFn) => void, (cb?: CheckUserTypeCallbackFn) => void, MutableRefObject<UserinfoObj>] => {
    const [userinfo, setUserinfo, userinfoRef] = useStateRef(Ioc(UUserService).userinfo);
    const [userType, setUserType] = useState<UserType>(null);
    // 校验用户实名与否和用户类型
    const checkUserTypeHandler = (cb: CheckUserTypeCallbackFn = checkUserTypeCallback) => {
        netService.post<null, UserType>(urlConst.ACCOUNT_REAL_NAME_TYPE).then(res => {
            setUserType(res.result);
            cb && cb(res.result);
        });
    };
    // 处理登录情况
    const loginHandler = (cb: LoginCallbackFn = loginCallback) => {
        if (!userinfo) {
            // 登录中，必须要登录完才能执行下边逻辑，直接返回
            return;
        }
        if (userinfo.hasLogin) {
            cb && cb(userinfo);
        } else {
            if (isMobile()) {
                let url = '';
                if (genLoginUrl) {
                    url = genLoginUrl();
                } else {
                    const encodeCurrentUrl = encodeURIComponent(location.href.replace(location.hash, '')) + location.hash;
                    const env = Ioc(UEnvService).isProdSandbox ? 'sandbox' : 'online';
                    url = `${LOGIN_URL[env]}?redirect=${encodeCurrentUrl}`;
                }
                window.open(url, '_self');
                return;
            }
            const dy: UDynamicService = Ioc(UDynamicService);
            dy.open({
                component: ULoginModal,
                props: {
                    maskClosable,
                    hideCloseIcon,
                    loginUrl: genLoginUrl && genLoginUrl(),
                    onOk: ({regSuccess}) => {
                        /**
                         * userinfoInit会触发portalCommon中的登录事件，会触发UUserService.dispatch
                         * 从而导致userinfo更新，下边的useEffect会监听到更新，所以如果立即执行就不需要在这里执行回调函数了
                         */
                        if (window.require) {
                            (window.require as any)(['portalCommon'], (portalCommon: {
                                userinfoInit: (cb: (info?: UserinfoObj) => void, isImmediately?: boolean) => void;
                            }) => {
                                portalCommon.userinfoInit(info => {
                                    setTimeout(() => {
                                        !isImmediately && cb && cb(info, regSuccess);
                                    }, 300);
                                    if (/(qifu)|(qifu-sandbox)|(tms)/.test(location.host)) {
                                        regSuccess && pushQifuAndTmsCrm();
                                    } else {
                                        regSuccess && pushProduct2Crm();
                                    }
                                });
                            });
                        } else {
                            window.portalCommon.userinfoInit(info => {
                                setTimeout(() => {
                                    !isImmediately && cb && cb(info, regSuccess);
                                }, 300);
                                if (/(qifu)|(qifu-sandbox)|(tms)/.test(location.host)) {
                                    regSuccess && pushQifuAndTmsCrm();
                                } else {
                                    regSuccess && pushProduct2Crm();
                                }
                            });
                        }
                    },
                },
            });
        }
    };
    useOnMount(() => {
        uUserService.userinfo && setUserinfo({...uUserService.userinfo});
        uUserService.subscribe(info => {
            setUserinfo({...info});
        });
    });
    useEffect(() => {
        if (userinfo && isImmediately) {
            loginHandler();
            userinfo.hasLogin && isCheckUserType && checkUserTypeHandler();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userinfo]);
    return [userinfo, userType, loginHandler, checkUserTypeHandler, userinfoRef];
};
