import {Injectable} from '@baidu/bce-decorators';
import {UEnvService} from '../services/env';
import {BOS_ORIGIN_URL, BOS_CDN_ORIGIN_URL} from '../constant/variableConst';

@Injectable()
export class UHttpConfig {
    constructor(
        private env: UEnvService
    ) {}

    timeout = 10000;
    urlBosProdSandbox = `${BOS_ORIGIN_URL}/portal-cms/sandbox`;
    urlBosProdOnline = `${BOS_ORIGIN_URL}/portal-cms/online`;
    urlBosCDNProdSandbox = `${BOS_CDN_ORIGIN_URL}/portal-cms/sandbox`;
    urlBosCDNProdOnline = `${BOS_CDN_ORIGIN_URL}/portal-cms/online`;
    // portal-server
    urlPortalServerSandbox = 'http://127.0.0.1:9888';
    urlPortalServerProduct = 'http://portal-server.bce-portal.sdns.baidu.com';
    // portal
    urlPortalSandbox = 'http://127.0.0.1:8666';
    urlPortalProduct = 'http://portal.bce-portal.sdns.baidu.com';
    // yapi (dev) 换成iapi
    urlYapi = 'https://iapi.baidu-int.com/m1/353069-0-default';
    // cloud
    urlCloudSandbox = 'https://cloudtest.baidu.com';
    urlCloudProduct = 'https://cloud.baidu.com';
    urlCloudDev = 'http://localhost:9889';
    // developer
    urlDeveloperSandbox = 'http://gzbh-sandbox144-store-5117.gzbh.baidu.com:8616/api/bce_developer';
    urlDeveloperOnline = 'http://developer.sdns.baidu.com/api/bce_developer';
    urlDeveloperDev = 'https://yapi.baidu-int.com/mock/11827/api/bce_developer';
    urlDeveloperClientOnline = 'https://developer.baidu.com/api/bce_developer';
    // developer bos
    urlBosDeveloperProdSandbox = 'https://bce-cdn.bj.bcebos.com/developer-cms/sandbox';
    urlBosDeveloperProdOnline = 'https://bce-cdn.bj.bcebos.com/developer-cms/online';
    // console-hub
    urlConsoleHubProduct = 'http://console-hub.sdns.baidu.com';
    urlConsoleHubSandbox = 'http://qasandbox.bcetest.baidu.com';
    // doc bos
    urlDocBosProdSandbox = `${BOS_ORIGIN_URL}/p3m/bce-doc-sandbox/staging`;
    urlDocBosProdOnline = `${BOS_ORIGIN_URL}/p3m/bce-doc/online`;
    urlDocBosCDNProdSandbox = `${BOS_CDN_ORIGIN_URL}/p3m/bce-doc-sandbox/staging`;
    urlDocBosCDNProdOnline = `${BOS_CDN_ORIGIN_URL}/p3m/bce-doc/online`;
    // doc en bos
    urlDocEnBosProdSandbox = `${BOS_ORIGIN_URL}/p3m/I18N/staging`;
    urlDocEnBosProdOnline = `${BOS_ORIGIN_URL}/p3m/I18N/online`;
    urlDocEnBosCDNProdSandbox = `${BOS_CDN_ORIGIN_URL}/p3m/I18N/staging`;
    urlDocEnBosCDNProdOnline = `${BOS_CDN_ORIGIN_URL}/p3m/I18N/online`;

    get cloudBosUrl() {
        // 本地 - 拿cms线上的部署沙盒
        // 沙盒 - 拿cms线上部署沙盒
        // 线上 - 拿cms线上部署线上
        // 非线上 + source=cms_sandbox - 拿cms沙盒的部署沙盒
        // 用户侧走cdn地址
        if (!this.env.isProd) {
            return this.urlBosProdOnline;
        }
        if (this.env.isClient) {
            return this.env.isNotProductOnlineAndGetSourceFromCmsSandbox ? this.urlBosCDNProdSandbox : this.urlBosCDNProdOnline;
        }
        return this.env.isNotProductOnlineAndGetSourceFromCmsSandbox ? this.urlBosProdSandbox : this.urlBosProdOnline;
    }

    get developerBosUrl() {
        // 本地 - 拿cms沙盒的部署沙盒
        if (!this.env.isProd) {
            return this.urlBosDeveloperProdSandbox;
        }
        return this.env.isNotProductOnlineAndGetSourceFromCmsSandbox ? this.urlBosDeveloperProdSandbox : this.urlBosDeveloperProdOnline;
    }

    get developerBosUrlByEnv() {
        // 本地 - 拿cms沙盒的部署沙盒
        if (!this.env.isProd) {
            return this.urlBosDeveloperProdSandbox;
        }
        // 沙盒 - 拿cms沙盒的部署线上; 线上 - 拿cms线上部署线上
        return this.env.isProdSandbox ? this.urlBosDeveloperProdSandbox : this.urlBosDeveloperProdOnline;
    }

    // 线上环境不带前缀 _pre
    get bosUrlPrefix() {
        return this.env.isProdOnline ? '' : 'pre_';
    }

    // 从portal-server获取数据
    get portalServerUrl() {
        // return this.urlCloudSandbox;
        if (this.env.isDev) {
            return this.urlYapi;
        }

        // 本地访问沙盒数据
        if (this.env.isProdSandbox && !this.env.isProd) {
            return this.urlCloudSandbox;
        }

        // 用户侧访问
        if (this.env.isClient) {
            return '';
        }

        return this.env.isProdOnline ? this.urlPortalServerProduct : this.urlPortalServerSandbox;
    }

    // 从portal获取数据
    get portalUrl() {
        if (this.env.isDev) {
            return this.urlYapi;
        }
        // 用户侧访问
        if (this.env.isClient) {
            return '';
        }
        // 本地访问沙盒数据
        if (!this.env.isProd) {
            return this.urlCloudSandbox;
        }
        return this.env.isProdOnline ? this.urlPortalProduct : this.urlPortalSandbox;
    }

    // 请求开发者中心的接口
    get developerUrl() {
        if (this.env.isDev) {
            return this.urlDeveloperDev;
        }
        // 本地访问沙盒数据
        if (this.env.isProdSandbox && !this.env.isProd) {
            return this.urlDeveloperSandbox;
        }
        // 线上用户侧访问
        if (this.env.isProdOnline && this.env.isClient) {
            return this.urlDeveloperClientOnline;
        }
        return this.env.isProdOnline ? this.urlDeveloperOnline : this.urlDeveloperSandbox;
    }

    // 请求console-hub
    get consoleHubUrl() {
        if (this.env.isDev) {
            return this.urlYapi;
        }
        // 用户侧访问
        if (this.env.isClient) {
            return '';
        }
        return this.env.isProdOnline ? this.urlConsoleHubProduct : this.urlConsoleHubSandbox;
    }

    // 获取产品价格的bos地址
    get productPriceBosUrl() {
        if (this.env.isClient) {
            return this.env.isProdOnline ? this.urlBosCDNProdOnline : this.urlBosCDNProdSandbox;
        }
        return this.env.isProdOnline ? this.urlBosProdOnline : this.urlBosProdSandbox;
    }

    // 获取文档index页side数据bos地址
    get docSubIndexBosUrl() {
        if (this.env.isClient) {
            return this.env.isProdOnline ? this.urlDocBosCDNProdOnline : this.urlDocBosCDNProdSandbox;
        }
        return this.env.isProdOnline ? this.urlDocBosProdOnline : this.urlDocBosProdSandbox;
    }
    // 获取文档index页英文bos地址
    get docSubIndexEnBosUrl() {
        if (this.env.isClient) {
            return this.env.isProdOnline ? this.urlDocEnBosCDNProdOnline : this.urlDocEnBosCDNProdSandbox;
        }
        return this.env.isProdOnline ? this.urlDocEnBosProdOnline : this.urlDocEnBosProdSandbox;
    }

    // 获取cloud路径
    get cloudUrl() {
        if (this.env.isDev) {
            return this.urlYapi;
        }
        // 用户侧访问
        if (this.env.isClient) {
            return '';
        }
        // 本地访问沙盒数据
        if (!this.env.isProd) {
            return this.urlCloudSandbox;
        }
        return this.env.isProdOnline ? this.urlCloudProduct : this.urlCloudSandbox;
    }

}
