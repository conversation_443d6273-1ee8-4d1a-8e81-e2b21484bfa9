import optionCommon from '../../weirwood.json';

export const weirwoodOptions = {
    common: {
        // 只支持如下字符：英文字母(大小写)，数字(0-9)，下划线(_)，中划线(-)，点(.)，@符号
        buildid: optionCommon.buildid,
        token: optionCommon.token,
        ignoreUrls: [
            // 本地开发屏蔽错误发送
            'localhost',
            '127.0.0.1',
        ],
    },
    error: {
        collectWindowErrors: true,
        collectUnhandledRejections: true,
        // 静态资源加载异常
        collectResourceLoadErrors: true,
    },
    perf: {
        // 性能数据PV日志会比较大，可以输入 sampleRate 进行采样，控制在 50 W左右
        sampleRate: 1,
        spa: true,
        history: true,
    },
};
