export interface Title {
    zh: string;
    en: string;
    color: string;
}

export interface DocFixPageData {
    background: string;
    banner: {
        title: {
            cloud: string;
            colorText: string;
            normalText: string;
        };
        tip: string;
        date: string;
        user: string;
        dateIcon: string;
        userIcon: string;
        bg: string;
        products: Array<Array<{
            name: string;
            key: string;
            url: string;
        }>>;
    };
    award: {
        title: Title;
        bg: string;
        largeBg: string;
        cards: Array<{
            name: string;
            num: string;
            price: number;
            priceSuffix: string;
            desc: string;
        }>;
    };
    registration: {
        title: Title;
        process: {
            name: string;
            list: Array<{
                title: string;
                desc: string;
            }>;
        };
        cards: Array<{
            name: string;
            list: Array<{
                prefix: string;
                content: string;
            }>;
            desc: string;
        }>;
    };
    announcement: {
        title: Title;
        list: string[];
    };
}
