import {BCCImagesObj} from './groupBuy';

export enum EnumProductDurationUnit {
    // second = 'second',
    // minute = 'minute',
    // hour = 'hour',
    // day = 'day',
    month = 'month',
    year = 'year',
}

export enum EnumProductServiceType {
    BCC = 'BCC',
    BOS = 'BOS',
    EIP = 'EIP',
    CDS = 'CDS',
}

// 获取组合购场景的打标产品 请求参数
export interface GetGroupPurchaseProductsReqParams {
    serviceType: keyof typeof EnumProductServiceType;
    scene: 'mergePurchase' | 'mobileProduct';
}

// 产品Flavor
export interface GroupPurchaseProductFlavorObj {
    name: string;
    value: string | number| null;
    scale: string | number;
}

// 产品FlavorDisplayName
export interface GroupPurchaseProductFlavorDisplayNameObj {
    flavor: string;
    flavorDisplayName: string;
    value: string | number;
    valueDisplayName: string;
    scale: string | number;
    unitPrefix: null | string;
}

export interface GroupPurchaseProductObj {
    serviceType: keyof typeof EnumProductServiceType;
    region: string;
    status: 'online';
    packageTime: string | null;
    flavor: GroupPurchaseProductFlavorObj[];
    pricePrimaryKeyId: string;
    serviceTypeUpdatedAt: string;
    chargeItem: string;
    subServiceType: string;
    dimension: ['time'] | null | string[];
    flavorDisplayName: GroupPurchaseProductFlavorDisplayNameObj[];
    duration?: number;
    timeUnit?: string;
}

export interface GetPriceParams {
    accountId: string;
    agentAccountId: string;
    serviceType: keyof typeof EnumProductServiceType;
    subServiceType: string;
    chargeItemName: string;
    region: string;
    scene: string;
    flavor: {
        flavorItems: GroupPurchaseProductFlavorObj[];
    };
    time?: {
        startTime: string;
        period: string;
    };
    count: number;
    signAuth?: any;
    channel: string;
}

// 移动端 BCC 选配页向订单页传递的数据
export interface BCCOrderTransferData {
    params: {
        bcc: GetPriceParams;
        cds: GetPriceParams;
        eip: GetPriceParams;
    };
    productConfig: Array<{label: string, value: string | number}>;
    imageData: BCCImagesObj;
    formValues: MobilePurchaseFormValues;
}

export interface MobilePurchaseFooterProps {
    priceLoading: boolean;
    totalPrice: number;
    pricePrefix?: string;
    prices: Array<{
        productName: string;
        price: number;
        discountPrice?: number;
        serviceType?: string;
    }>;
    confirmText: string;
    onConfirm: () => void;
}

export interface MobilePurchaseFormValues {
    region: string;
    spec: [string, string];
    image: [string, string];
    diskName: string;
    diskSize: number;
    bandWidth: number;
    time: string;
    agreement: boolean;
    count: number;
}
