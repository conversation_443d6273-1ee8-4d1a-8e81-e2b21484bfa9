import {PageRequestObj} from '@baidu/bce-services';

export interface NewsListItemType {
    abstract: string;
    content: string;
    image: string;
    newsId: string;
    relatedArticles: string;
    relatedProducts: string;
    status: string;
    time: string;
    title: string;
    type: string;
    typeName: string;
}

interface HotNewsDataItem {
    name: string;
    list: Array<{
        title: string;
        link: string;
        time: string;
    }>;
}
export interface HotNewsDataType {
    campaign: Array<{img: string, link: string, title: string}>;
    news: HotNewsDataItem;
    productNews: HotNewsDataItem;
}
export interface NewsListQueryType extends PageRequestObj {
    status?: string;
    type?: 'campaign' | 'news' | 'productNews' | 'notice';
}


export interface NewsDetailDataType {
    newsId: string;
    type: string;
    typeName: string;
    keywords?: string;
    title: string;
    time: string;
    status: string;
    image: string;
    content: string;
    contentEtl: string;
    relatedArticles: string;
    relatedProducts: string;
    abstract: string;
}

export interface NewsDetailQueryType {
    newsId: string | string[];
    status: string | string[];
}

export interface NewsDataType extends Omit<NewsDetailDataType, 'relatedProducts'> {
    relatedProducts: Array<{
        link: string;
        name: string;
        image: string;
        desc: string;
    }>;
}
