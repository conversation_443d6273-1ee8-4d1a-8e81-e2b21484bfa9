import {FormInstance} from 'antd';
import {USelectProps} from '@common/components/USelect/USelect';
import {EnumProductServiceType} from './common';
import {ProductPriceDetailObj} from './page';

export interface PhoneVerifyValue {
    phone: string;
    code: string;
}

// 组合购产品组件统一的props
export interface GroupProductComponentProps {
    value?: any;
    onChange?: (e: {
        // 价格信息
        priceList: ProductPriceDetailObj[];
        // 右侧展示信息
        configList: Array<{
            name: string;
            value: string;
        }>;
        loading: boolean;
        // todo: 下单参数
        orderParams?: any;
    }) => void;
    onInited?: <T = any>(form: FormInstance<T>) => void;
}

// 获取组合购场景的打标产品 请求参数
export interface GetGroupPurchaseProductsReqParams {
    serviceType: keyof typeof EnumProductServiceType;
    scene: 'mergePurchase';
}

// 产品Flavor
export interface GroupPurchaseProductFlavorObj {
    name: string;
    value: string | number| null;
    scale: string | number;
}

// 产品FlavorDisplayName
export interface GroupPurchaseProductFlavorDisplayNameObj {
    flavor: string;
    flavorDisplayName: string;
    value: string | number;
    valueDisplayName: string;
    scale: string | number;
    unitPrefix: null | string;
}

// 组合购产品
export interface GroupPurchaseProductObj {
    serviceType: keyof typeof EnumProductServiceType;
    region: string;
    status: string;
    packageTime: string | null;
    flavor: GroupPurchaseProductFlavorObj[];
    pricePrimaryKeyId: string;
    serviceTypeUpdatedAt: string;
    chargeItem: string;
    subServiceType: string;
    dimension: null | string[];
    flavorDisplayName: GroupPurchaseProductFlavorDisplayNameObj[];
}

// 组合购询价参数
export type GroupPurchaseProductPriceParams = Omit<
    GroupPurchaseProductObj,
    'status' | 'pricePrimaryKeyId' | 'serviceTypeUpdatedAt' | 'chargeItem' | 'dimension' | 'flavorDisplayName' | 'flavor' | 'packageTime'
> & {
    accountId: string;
    agentAccountId: string;
    chargeItemName: string;
    count: number;
    scene: string;
    flavor: {
        flavorItems: GroupPurchaseProductObj['flavor'];
    };
    time?: {
        period: string;
        startTime: string;
    };
    signAuth?: string;
    duration?: number;
    timeUnit?: string;
};

export type GroupPurchaseProductObjIndex = keyof GroupPurchaseProductObj;

export type GroupPurchaseTabSelectPropsData = GroupPurchaseTabSelectProps['data'];

// tabselect 组件props
export interface GroupPurchaseTabSelectProps {
    data: Array<{
        label: string;
        value: string | number;
        desc?: string;
        extr?: any;
    }>;
    onChange?: (value: GroupPurchaseTabSelectPropsData[0]['value'], opt?: GroupPurchaseTabSelectPropsData[0]) => void;
    defaultValue?: string;
    value?: string;
    className?: string;
    label?: string;
    isOrder?: boolean;
}

// 代替 GroupPurchaseTabSelectProps
export interface GroupPurchaseRadioProps {
    options: Array<{
        label: string;
        value: string | number;
        desc?: string;
        extr?: any;
    }>;
    isOrder?: boolean;
    value?: string | number;
    onChange?: (
        value: string | number,
        option?: GroupPurchaseRadioProps['options'][number]
    ) => void;
}

// 时长选择 组价props 下拉形式
export interface GroupPurchaseDurationSelectProps {
    options: string[];
    value?: string;
    onChange?: USelectProps['onChange'];
    defaultValue?: string;
}

export type GroupPurchaseFormValue<T = any> = {
    duration?: string;
    count?: number;
} & T;
