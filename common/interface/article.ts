// 文章home页参数解析
export interface Param {
  order: ArticleParamOrder;
  pageNo: number;
  tagId: string;
}

export enum ArticleParamOrder {
  hot = 'hot',
  new = 'new',
  hotRank = 'hotRank'
}

// 文章状态
export type ArticleStatusType = 'PUBLISHED' | 'REVIEWING' | 'REJECTED' | 'DRAFT' | 'DELETED' | 'OFFLINE';

// 文章信息
export interface ArticleListItemObj {
  title: string;
  introduction: string;
  updatedAt: string;
  status: ArticleStatusType;
  id: number;
  isCollected?: boolean;
  isLiked?: boolean;
  likesNum?: number;
  collectionsNum?: number;
  coverImageURL?: string;
  readNum?: number;
  author?: string;
  authorId?: string;
  htmlContent?: string; // 搜索结果返回
}

// 文章详情
export interface ArticleDetailObj extends ArticleListItemObj{
    htmlContent: string;
    mdContent: string;
    rejectText: string;
    rejectImage: string;
}

// 最热文章单元
export interface HotArticleItemObj {
  title: string;
  desc: string;
  link: string;
  id: string;
}

// 通用状态
export type GeneralStatus = 'REJECTED' | 'REVIEWING' | 'PUBLISHED' | 'OFFLINE';

// 评论接口返回数据格式
export interface CommentReturnObj {
    id: number;
    content: string;
    createdAt: string;
    status: GeneralStatus;
    likeNum: number;
    isLiked: boolean;
    userId: number;
    nickname: string;
    avatar?: string;
    parentCommentId?: number;
}
// 评论列表项
export interface CommentItemObj extends CommentReturnObj {
    replyInfos: CommentItemObj[];
}

export type CommentType = 'comment' | 'reply';

interface baseMarketingPosObj {
    posId: string;
    posName?: string;
    relatedArticles: string;
    isNeedLogin?: boolean;
}

export interface ArticleBeginningMarketPosObj extends baseMarketingPosObj {
    img: string;
    title: string;
    desc: string;
    link: string;
    btnText: string;
}

export interface ArticlePopoverMarketingPosObj extends baseMarketingPosObj {
    img: string;
    link: string;
}

export interface ArticleProductItemObj {
    title: string;
    subTitle: string;
    titleTag: string;
    backgroundImage?: string;
    parameters: Array<{
        name: string;
        value: string;
    }>;
    priceFlag: string;
    price: string;
    unit: string;
    button: {
        text: string;
        href: string;
        disabled: boolean;
    };
}

export interface ArticleProductCardMarketPosObj extends baseMarketingPosObj {
    dataRender: ArticleProductItemObj[];
    dataConfig: {
        row: number;
        column: number;
    };
}
