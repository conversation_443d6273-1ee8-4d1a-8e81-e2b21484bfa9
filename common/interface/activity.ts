import {PageRequestObj} from '@baidu/bce-services';
import {TagItemObj} from './common';

export interface ActivityParam {
    category: string;
    status: ACTIVITY_STATUS_PARAM;
    pageNo: string;
}
// 活动状态
export enum ACTIVITY_STATUS {
    NOT_PUBLISHED = 'NOT_PUBLISHED',
    APPLYING = 'APPLYING',
    IN_PROGRESS = 'IN_PROGRESS',
    ENDED = 'ENDED',
}

// 活动状态(页面参数版)
export enum ACTIVITY_STATUS_PARAM {
    ALL = 'all',
    APPLYING = 'apply',
    IN_PROGRESS = 'progress',
    ENDED = 'end',
}


export type ActivityFilterItemsProps = {
    label: string;
    activeVal?: any;
    data: Array<{
        title: string;
        val: any;
    }>;
    onChange?: (e: any) => void;
};

export type ActivityFilterObj = PageRequestObj & Pick<ActivityItemObj, 'campaignStatus'> & {tag: string};

// 活动类型
export enum ACTIVITY_TYPE {
    LIVE = 'LIVE',
    OTHERS = 'OTHERS',
}

// 活动单元
export interface ActivityItemObj {
    id: number;
    url: string;
    title: string;
    tags: TagItemObj[];
    campaignStartTime: string;
    campaignEndTime: string;
    campaignStatus: ACTIVITY_STATUS;
    liveId?: string;
    cover: string;
    showInCalendar: number;
    type: ACTIVITY_TYPE;
}

