import {CyberplayerVersion} from './common';

interface BaseSummitModuleBlock {
    title: string;
    titleEn?: string;
    hoverbg?: string;
    hide?: boolean;
    desc?: string;
    descM?: string;
    descpc?: string;
    childtitleleft?: string;
    childtitright?: string;
}
export interface SummitConfigDataObj {
    title: string;
    desc: string;
    keyword: string;
    imgPageLogo?: string;
    themeBg?: string;
    themeBgM?: string;
}

export interface SummitHeaderDataObj {
    navTitle: string;
    id: string;
    link: string;
}

export interface SummitBtnObj {
    link: string;
    text: string;
    isFormBtn?: boolean;
}

export interface SummitBannerDataObj {
    content: {
        timeicon?: string;
        addressicon?: string;
        titleImg: string;
        titleImgM?: string;
        titleImgHeight?: string; // 支持外部控制标题高度
        title: string;
        titleM?: string;
        desc?: string;
        descM?: string;
        contentBg?: string; // 内容区域背景图
        contentBgM?: string;
        titlebgImg?: string;
        button?: {
            title: string;
            link: string;
        };
        contentList?: Array<{
            label: string;
            desc: string;
            icon?: string;
            extract?: string;
            link?: string;
        }>;
        time?: string;
        location?: string;
        swiperImgList?: string[]; // banner区域用循环图
        btnList: SummitBtnObj[];
        btnListM?: SummitBtnObj[];
        isLive?: boolean; // banner区域是否展示视频
        liveData?: {
            title: string;
            videoSrc: string; // 视频地址
            videoCover: string; // 视频封面
            videoCoverM: string; // 视频封面 - 移动端
        };
    };
    background: {
        logo?: string;
        bannerBg?: string;
        bannerBgs?: string;
        bannerBgM: string;
        isVideo?: boolean;
        contentBg?: string;
        isVideoM?: boolean;
        bannerVideo?: string;
        bannerVideoM?: string;
        bannerBg2?: string;
        bannerVideo2?: string;
    };
}

export type SummitLiveStatusType = 'NOT_READY' | 'ONGOING' | 'END' | 'OFFLINE';

// 峰会直播流接口返回
export interface SummitLiveStreamStatusObj {
    stream: string;
    status: SummitLiveStatusType;
}
// 峰会直播推荐卡片
export interface SummitRecommendCardItemObj {
    icon: string;
    link: string;
    subTitle: string;
    title: string;
    visible: boolean;
}
export interface SummitRecommendCardObj {
    list: SummitRecommendCardItemObj[];
}

export interface SummitLiveStreamObj {
    id: string; // 直播流id - 用于区分直播流
    status: SummitLiveStatusType; // 直播流初始化状态（当整体直播流isEnd：true时，不再发送请求，此处需要设置为END）
    live: string; // PC直播流地址
    liveM: string; // 移动端直播流地址
    videoWarmUp: string; // 预热视频地址
    videoPlayBack?: string; // 回放视频地址
    videoCover: string; // 视频封面
    videoCoverM: string; // 视频封面 - 移动端
    videoEndCover?: string; // 视频结束封面
    videoEndCoverM?: string; // 视频结束封面 - 移动端
    appName?: string; // 用于生成回放视频：appName /api/playback/${appName}/${streamName}
    streamName?: string; // 用于生成回放视频：直播流名称
    playOptions?: {
        playRate?: boolean; // 调整倍速
        autostart?: boolean; // 自动播放
        controls?: boolean; // 是否展示控制条
        muted?: boolean; // 是否静音
        loop?: boolean; // 是否循环播放
    };
}
export interface SummitLiveDataObj extends BaseSummitModuleBlock {
    isLiveEnd?: boolean;
    videoIcon?: string; // 视频图标
    videoIconM?: string; // 视频图标 - 移动端
    playerVersion?: CyberplayerVersion; // 播放器版本
    background?: {
        isVideo?: boolean;
        bannerBg: string;
        bannerVideo?: string;
        bannerBgM: string;
        titleImg: string;
        titleImgM: string;
    };
    list: Array<{
        title: string; // 展示的标题名称
        titleM?: string;
        titleBg?: string; // 标题背景图
        titleBgM?: string; // 标题背景图 - 移动端
        startTime: string;
        endTime: string;
        date?: string;
        timeTable?: Array<{ // 大会议程时间表
            startTime: string;
            endTime: string;
            title: string;
        }>;
        recStartIndex?: number; // 推荐卡片开始index位置
        recEndIndex?: number; // 若不存在number，则取值到最后
        liveStream: SummitLiveStreamObj;
    }>;
    config: {
        startTime: string; // 直播流开始监听时间
        endTime: string; // 直播流结束监听时间
        isEnd: boolean; // 直播流是否整体结束，若结束则不再监听（需将每个直播流初始状态设置为END）
        forumStartTime?: string; // 分论坛开始监听时间
        forumEndTime?: string; // 分论坛结束监听时间
        forumStartIndex?: string; // 从list中划分分论坛开始的index
        noticeMap?: { // 直播流状态提示语
            NOT_READY: string;
            ONGOING: string;
            OFFLINE: string;
            REVIEW_GENERATING: string;
            END: string;
        };
        streamStatusTipsMap?: { // 直播流角标提示语（文字或图片）
            showImg: boolean; // 展示文字或图片
            NOT_READY: string;
            ONGOING: string;
            END: string;
            OFFLINE: string;
        };
    };
}

export interface SummitHighlightDataObj extends BaseSummitModuleBlock {
    list: Array<{
        titlePre?: string;
        title: string;
        iconpc?: string;
        content: string;
        contentM?: string;
        icon?: string;
        img?: string;
        bg?: string;
        bgActive?: string; // pc亮点hover背景图
        bgActiveVideo?: string; // pc亮点hover背景视频
        bgM?: string;
    }>;
}

export interface SummitAgendaDataItemObj {
    subTitle?: string;
    timeRange?: string;
    date?: string; // 日期
    startTime: string;
    endTime: string;
    theme: string;
    themeM?: string;
    color?: string;
    logTitle?: string;
    itemBg?: string; // 主题背景图
    itemBgM?: string; // 主题背景图 - 移动端
    desc?: string; // 主题描述
    videoTimePoint?: string; // 视频时间点
    details: Array<{
        speaker: string;
        speakerM?: string;
        speakerDetail?: string;
    }>;
}

export interface SummitAgendaDataObj extends BaseSummitModuleBlock {
    location?: string; // 演讲地
    timeRange?: string; // 时间范围
    image?: string; // 分论坛配图
    buttonLink?: string;
    buttonTitle?: string;
    date?: string;
    agendaLabelData: Array<{
        label: string;
        value?: string;
    }>;
    list: Array<{
        subTitle: string;
        subTitleEn?: string; // 英文副标题
        timeRange?: string; // 主题时间
        content: SummitAgendaDataItemObj[];
        tabTitle?: string;
        desc?: string;
    }>;
}

export interface SummitAgendaListDataObj extends BaseSummitModuleBlock {
    list: Array<{
        title: string;
        list: SummitAgendaDataObj[];
    }>;
}

// 峰会嘉宾
export interface SummitSpeakerDataObj extends BaseSummitModuleBlock {
    list: Array<{
        isHover?: boolean;
        img: string;
        imgBig?: string;
        imgM?: string;
        name: string;
        desc: string;
        descM?: string;
    }>;
}

// 分论坛数据
export interface SummitSubjectDataObj extends BaseSummitModuleBlock {
    subjectList: Array<{
        title: string;
        subTitle?: string;
        desc?: string;
        date: string;
        background: string;
        backgroundMini?: string;
        backgroundM?: string;
        link?: string;
    }>;
}

export interface SummitVenueDataObj extends BaseSummitModuleBlock {
    name: string;
    detail: string;
    routes?: string;
    list?: Array<{
        desc?: string;
        icon?: string;
        info?: string;
        descPc?: string;
    }>;
    buttonLink?: string;
    buttonTitle?: string;
    button?: SummitBtnObj;
    venueMap?: string;
    venueMapM?: string; // 移动端地图
    isImageVenue: boolean; // 地图区域是否仅展示图片（宽高比22.5%）
    venueMapBg?: string; // pc端自适应背景图片（可选）
    routeData?: Array<{ // 路线详细信息
        title: string;
        desc: string[];
        icon?: string;
        info?: string;
    }>;
}

// /summit/AICloudSummit2024/venue/index.html
export interface SummitVenueItemDataObj {
    title: string;
    location: string;
    locationM: string;
    list: Array<{
        subTitle: string;
        desc: string;
        date: string;
        startTime: string;
        endTime: string;
        content: SummitAgendaDataItemObj[];
    }>;
}

export interface SummitVenueListDataObj extends BaseSummitModuleBlock {
    list: Array<{
        title: string;
        venueMap: string;
        venueMapM: string;
        list: SummitVenueItemDataObj[];
    }>;
}
export interface SummitContactDataObj extends BaseSummitModuleBlock {
    bg: string;
    bgWap: string;
    bgFixedWap: string;
    btn: SummitBtnObj;
}

// 峰会视频展示
export interface SummitVideoItemObj {
    title: string; // 视频标题
    titleM?: string; // 视频标题 - 移动端
    desc?: string; // 视频描述
    videoSrc: string; // 视频地址
    videoCover: string; // 视频封面
    videoCoverM?: string; // 视频封面 - 移动端
}

export interface SummitCaseDataObj extends BaseSummitModuleBlock {
    subTitle: string; // 副标题
    list: SummitVideoItemObj[];
}

// 新闻资讯
export interface SummitNewsDataObj extends BaseSummitModuleBlock {
    list: Array<{
        title: string;
        desc: string;
        descM?: string;
        btn: string;
        link: string;
        bg?: string;
        bgM?: string;
        content?: Array<{
            title: string;
            link: string;
            bg: string;
            bgM: string;
        }>;
    }>;
}

// 合作伙伴
export interface SummitPartnerDataObj extends BaseSummitModuleBlock {
    list: Array<{
        label: string;
        labelM?: string;
        list: Array<{
            iconImg: string;
            iconImgM?: string;
            link?: string;
            text: string;
        }>;
    }>;
}

// 往期回顾
export interface SummitReviewDataObj extends BaseSummitModuleBlock {
    list: Array<{
        title: string;
        img?: string;
        desctitle?: string;
        descinfo?: string;
        titleM?: string;
        desc: string;
        descM?: string;
        bg: string;
        bgM?: string;
        link: string;
    }>;
}

// 相关产品
export interface SummitProductDataObj extends BaseSummitModuleBlock {
    list: Array<{
        title: string;
        desc: string;
        bg?: string;
        bgM?: string;
        descDetail?: {
            [key: string]: string[];
        };
        btn: SummitBtnObj;
    }>;
}

export interface SummitClassDataItemObj extends SummitAgendaDataItemObj {
    date?: string;
    details: Array<{
        speakerImg?: string;
        speaker: string;
        speakerDetail?: string;
    }>;
}
export interface SummitClassDataObj extends BaseSummitModuleBlock {
    listtwo: any;
    list: Array<{
        subTitle: string;
        content: SummitClassDataItemObj[];
    }>;
}

export interface SummitFloatBarDataObj {
    hidden?: boolean;
    btn?: {
        text: string;
        link: string;
        isFormBtn?: boolean;
    };
    whiteBook?: {
        title: string;
        content: Array<{
            title: string;
            desc: string;
            link: string;
        }>;
    };
    customBtn?: Array<{
        hidden?: boolean;
        text: string;
        link: string;
        isFormBtn?: boolean;
        bg?: string;
        bgActive?: string;
        bgM?: string;
    }>;
    assistantBtn?: {
        hidden?: boolean;
        title: string;
        bg: string;
        bgActive?: string;
        bgM: string;
        link: string;
        linkM?: string;
    };
}

export interface SummitFormItemDataObj {
    title: string; // 表单label名称（弹窗表单支持自定义）
    type: string; // 表单类型
    name: string; // 表单的标识符
    fieldName: string; // 提交表单的真实字段
    hide?: boolean; // 渲染时隐藏
    render?: {
        required?: boolean; // 必选
        placeholder?: string; // 自定义placeholder
        maxLength?: number; // 最大长度
        validation?: 'mobile' | 'email' | 'password' | 'number' | 'url' | 'letter'; // 验证类型
        answer?: Array<{
            value: string;
            text: string;
            link?: string;
        }>; // 下拉框字段，link为带跳转的dropdown特殊字段
        links?: Array<{
            value: string; // answer中的value
            show: string[]; // 对应展示的表单name
        }>; // 一般为逻辑设置，如下拉框选择其他，新增输入框
    };
}

export interface SummitFormDataObj {
    title: string;
    formBg: string;
    btnText: string;
    btnTextM?: string;
    data?: SummitFormItemDataObj[];
}
export type SummitFormDataType = {[props: string]: string | string[]};
export type SummitResultStatusType = 'success' | 'warning' | 'error' | 'info';
export interface SummitResultDataObj {
    type: SummitResultStatusType; // 用于展示icon
    title: string; // 标题
    desc: string; // 描述
    useInterval?: boolean; // 是否展示倒计时
    useDropdownLink?: boolean; // 是否需要支持自定义跳转
    jumpUrl?: string; // 跳转链接
    btnText?: string; // 跳转链接文案
    jumpTarget?: '_blank' | '_self' | '_top' | '_parent';
}

export interface BaseSummitDataObj {
    butbanner: any;
    newagenda: any;
    isBtn?: {
        title: string;
        time: string;
    };
    config: SummitConfigDataObj;
    header: SummitHeaderDataObj[];
    headerm: SummitHeaderDataObj[];
    banner: SummitBannerDataObj;
    live?: SummitLiveDataObj; // 直播数据
    highlights: SummitHighlightDataObj;
    agenda: SummitAgendaDataObj;
    agendaList: SummitAgendaListDataObj;
    salon?: SummitAgendaDataObj; // 开发者沙龙（2023智算峰会）数据结构同agenda
    speaker?: SummitSpeakerDataObj; // 峰会嘉宾
    news?: SummitNewsDataObj; // 新闻资讯
    partner?: SummitPartnerDataObj; // 合作伙伴
    review?: SummitReviewDataObj; // 往期回顾
    case?: SummitCaseDataObj; // 峰会视频展示 - 一般为客户案例展示
    subject?: SummitSubjectDataObj; // 分论坛
    product?: SummitProductDataObj; // 相关产品
    venue?: SummitVenueDataObj;
    venueList?: SummitVenueListDataObj;
    contact?: SummitContactDataObj;
    publicClass?: SummitClassDataObj; // 公开课
    floatbar?: SummitFloatBarDataObj;
    formData?: SummitFormDataObj; // 自定义表单数据，后续将全部转移CMS会议表单中
    surveyPath?: string; // 若使用峰会表单弹窗则需要配置
    surveyPathSandbox?: string; // 峰会表单沙盒地址，若不存在使用surveyPath
    address?: SummitVenueDataObj;
    navigation?: SummitNavigationDataObj;
    specialOffer?: SummitspecialOfferDataObj;
}
// 峰会2025
export interface Summit2025BannerDataObj {
    banner?: {
        background?: {
            title?: string;
            bannerBg?: string;
            bannerBgs?: string;
            bannerBgM: string;
            isVideo?: boolean;
            isVideoM?: boolean;
            bannerVideo?: string;
            bannerVideoM?: string;
            bannerBg2?: string;
            bannerVideo2?: string;
        };
        content?: {
            titleImg: string;
            titleImgM?: string;
            titleImgHeight?: string; // 支持外部控制标题高度
            titlebgImgM?: string;
            title: string;
            titleM?: string;
            desc?: string;
            descM?: string;
            contentBg?: string; // 内容区域背景图
            contentBgM?: string;
            contentList?: Array<{
                label: string;
                desc: string;
                icon?: string;
                extract?: string;
                link?: string;
            }>;
            time?: string;
            location?: string;
            swiperImgList?: string[]; // banner区域用循环图
            btnList: SummitBtnObj[];
            btnListM?: SummitBtnObj[];
            isLive?: boolean; // banner区域是否展示视频
            liveData?: {
                title: string;
                videoSrc: string; // 视频地址
                videoCover: string; // 视频封面
                videoCoverM: string; // 视频封面 - 移动端
            };
        };
    };
}
export interface SummitNavigationDataObj {
    logopc?: string;
    logom?: string;
    logopcB?: string;
}


export interface SummitspecialOfferDataObj {
    bg?: string;
    list?: Array<{
        title?: string;
        desc?: string;
    }>;
    button?: {
        title?: string;
        link?: string;
    };
}
