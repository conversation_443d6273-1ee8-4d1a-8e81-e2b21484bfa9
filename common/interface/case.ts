export interface CaseListDataType {
    keywords: string;
    scene: string;
    industry: string;
    pageSize: number;
    pageNo: number;
}
export interface CaseReturnData {
    keywords: string;
    description: string;
    industry: string;
    logoImgSrc: string;
    mainImgSrc: string;
    pageId: string;
    path: string;
    scene: string;
    title: string;
    type: string;
}
export interface SearchData {
    keywords?: string;
    type: string;
}
export interface CaseIndustryType {
    EdpResultResponse: string;
    success: boolean;
    status: number;
}
export interface CaseSceneType {
    name: string;
}
