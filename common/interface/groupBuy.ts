import Big from 'big.js';
import {
    DurationBccEnum,
    CpuBccEnmu,
    MemoryBccEnmu,
    BandwidthBccEnum,
    SizeBccEnum,
    RegionBccEnum,
    SpecBccEnum,
} from '@components/groupBuy/GroupItemBcc/model';
import {
    CapacityBosEnum,
    DisplayNameBosEumn,
    DurationBosEnum,
    RegionBosEnum,
} from '@components/groupBuy/GroupItemBos/model';
import {CapacityCdnEnum, displayNameCdnEumn, DurationCdnEnum} from '@components/groupBuy/GroupItemCDN/model';
import {CapacityAipageEnum, displayNameAipageEumn, DurationAipageEnum} from '@components/groupBuy/GroupItemAipage/model';
import {RegionEipEnum, displayNameEipEumn, DurationEipEnum} from '@components/groupBuy/GroupItemEip/model';
import {CapacityLssEnum, displayNameLssEumn, DurationLssEnum} from '@components/groupBuy/GroupItemLSS/model';
import {RegionNatEnum, displayNameNatEumn, DurationNatEnum} from '@components/groupBuy/GroupItemNat/model';
import {
    PersonRoleCbsEumn,
    CouponTypeEnum,
    DurationCbsEnum,
    displayNameCbsEumn,
    ComTypeCbsEumn,
    SubServiceTypeCbsEnum,
} from '@components/groupBuy/GroupItemCbs/model';
import {DisplayNameTmsEumn, DurationTmsEnum} from '@components/groupBuy/GroupItemTms/model';
import {GroupItemEnum} from '@components/groupBuy/GroupItemContainer/GroupItemContainer';
import {ProductPriceDetailObj} from './page';
import {GroupPurchaseProductObj} from './groupPurchase';

export interface GroupBuyTabSelectProps {
    data: Array<{
        text: string;
        value: string;
        desc?: string;
    }>;
    onChange?: (value: string) => void;
    defaultValue?: string;
    value?: string;
    className?: string;
    label?: string;
}

export type GroupBuyBccItemObj = {
    region: RegionBccEnum;
    duration: DurationBccEnum;
    cpu: CpuBccEnmu;
    memory: MemoryBccEnmu;
    serviceType: 'BCC';
    displayName: '云服务器BCC';
    timeUnit: 'MONTH';
    // EIP
    bandwidth: BandwidthBccEnum;
    // CDS
    size: SizeBccEnum;
    spec: string;
    specName: SpecBccEnum;
    // imageId: '2d33bd41-fbdc-41ae-a372-e0204ae61387';
    // imageType: 'common';
    // osType: 'linux';
    // osVersion: 1;
};

export type GroupBuyBosItemObj = {
    region: RegionBosEnum;
    duration: DurationBosEnum;
    capacity: CapacityBosEnum;
    serviceType: 'BOS';
    displayName: DisplayNameBosEumn;
    timeUnit: 'MONTH';
};

export type GroupBuyCdnItemObj = {
    serviceType: 'CDN';
    displayName: displayNameCdnEumn;
    duration: DurationCdnEnum;
    capacity: CapacityCdnEnum;
    timeUnit: 'MONTH';
};

export type GroupBuyAipageItemObj = {
    serviceType: 'AIPAGE';
    capacity: CapacityAipageEnum;
    displayName: displayNameAipageEumn;
    duration: DurationAipageEnum;
    timeUnit: 'YEAR';
};

export type GroupBuyEipItemObj = {
    serviceType: 'EIP';
    region: RegionEipEnum;
    displayName: displayNameEipEumn;
    duration: DurationEipEnum;
    timeUnit: 'MONTH';
};

export type GroupBuyLssItemObj = {
    serviceType: 'LSS';
    capacity: CapacityLssEnum;
    displayName: displayNameLssEumn;
    duration: DurationLssEnum;
    timeUnit: 'MONTH';
};

export type GroupBuyNatItemObj = {
    serviceType: 'NAT';
    region: RegionNatEnum;
    displayName: displayNameNatEumn;
    duration: DurationNatEnum;
    timeUnit: 'MONTH';
};

export type GroupBuyCbsItemObj = {
    serviceType: 'CBS';
    displayName: displayNameCbsEumn;
    personRole: PersonRoleCbsEumn;
    comType?: ComTypeCbsEumn;
    couponType?: CouponTypeEnum;
    subServiceType?: SubServiceTypeCbsEnum;
    duration: DurationCbsEnum;
    timeUnit: 'YEAR' | 'DAY';
};

export type GroupBuyTmsItemObj = {
    serviceType: 'TMS';
    personRole: DisplayNameTmsEumn;
    duration: DurationTmsEnum;
    timeUnit: 'DAY';
};

type TemObj = Pick<GroupBuyBccItemObj, 'specName' | 'cpu' | 'memory' | 'size' | 'bandwidth' | 'duration'>;
export type GroupBuyBccTabArrObj = {
    [p in keyof TemObj]: GroupBuyTabSelectProps['data']
};
export interface BCCImagesObj {
    'id': string;
    'imageId': string;
    'name': string;
    'imageType': string;
    'snapshotId': string;
    'cpu': number;
    'memory': number;
    'osType': string;
    'osVersion': string;
    'osName': string;
    'osBuild': string;
    'osLang': string;
    'diskSize': number;
    'createTime': string;
    'status': string;
    'minMem': number;
    'minCpu': number;
    'minDiskGb': number;
    'desc': string;
    'osArch': string;
    'ephemeralSize': number;
    'imageDescription': string;
    'shareToUserNumLimit': number;
    'sharedToUserNum': number;
    'fpgaType': string;
    'name_fri': string;
}
export interface BCCSysObj {
    images: BCCImagesObj[];
    visible: {
        [props: string]: string[];
    };
}

export interface ProductPriceItem {
    configId: string;
    configGroupId: string;
    originPrice: number;
    currentPrice: number;
    serviceType: keyof typeof GroupItemEnum;
    parentServiceType: keyof typeof GroupItemEnum;
    disPlayName: string;
    priceDetail?: ProductPriceItem[];
}

export interface ShowConfigData {
    label: string;
    value: string | number;
}
export interface ShowConfigItem {
    id: number;
    serviceType: keyof typeof GroupItemEnum;
    data: ShowConfigData[];
}

export interface Product {
    serviceType: string;
    region: string;
    flavor: Array<{
        name: string;
        value: string;
        scale: number;
    }>;
    flavorDisplayName: Array<{
        flavor: string;
        flavorDisplayName: string;
        value: string;
        valueDisplayName: string;
    }>;
}

export interface Price {
    number: Big;
    serviceType?: string;
}

export interface ProductFromPortal {
    // serviceType: 可以用来做 title map 的 Key
    serviceType: string;
    selected: boolean;
    product: GroupPurchaseProductObj[];
}

export interface ProductCard extends ProductFromPortal {
    price?: ProductPriceDetailObj[];
    isExpand: boolean;
    priceLoading: boolean;
}

export interface ConfigInfo {
    name: string;
    value: string;
}

export interface ProductConfigItem {
    serviceType: string;
    selected: boolean;
    price?: ProductPriceDetailObj[];
    config: ConfigInfo[];
}
