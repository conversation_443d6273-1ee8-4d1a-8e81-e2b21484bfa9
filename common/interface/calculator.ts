/**
 * @file 价格计算器
 */

import {CmsBtnObj} from './common';

export interface PriceCalculatorNavItem {
    title: string;
    serviceType: PriceCalculatorServiceType;
    /** 购买指南 */
    buyingGuide: string;
    /** 产品详情 */
    productLink?: string;
    /** 免费试用 */
    tryLink: string;
    /** 产品描述 */
    desc: string;
}

export interface PriceCalculatorNavObj {
    category: string;
    list?: PriceCalculatorNavItem[];
    children: PriceCalculatorNavItem[];
}

export type PriceCalculatorServiceType =
    | 'bcc'
    | 'test'
    | 'qianfan'
    | 'eip'
    | 'rds'
    | 'vpn'
    | 'eipgroup'
    | 'blb'
    | 'nat'
    | 'bec'
    | 'dwz'
    | 'bos'
    | 'cds'
    | 'bes'
    | 'palo'
    | 'face'
    | 'imagerecognition'
    | 'nlp'
    | 'antiporn'
    | 'intelligentwriting'
    | 'machinetranslation'
    | 'imagesearch'
    | 'imageprocess'
    | 'body'
    | 'bcc_test'
    | 'cds_test'
    | 'scs'
    | 'bmr'
    | 'ls'
    | 'speech'
    | 'ocr'
    | 'cfs';

export type PriceCalculatorMapObj = {
    [key in PriceCalculatorServiceType]: {
        priceCalculator: {
            comp: () => JSX.Element;// 计算器组件
        };
        priceDetail?: { // 详情
            comp?: () => JSX.Element;
            buyHref?: string; // 立即购买，跳转到console购买页面
        };
        priceList?: any; // 清单，待定
        priceEstimateDetail?: any; // 估算详情
    };
};

export interface PriceAllItemObj {
    category: string;
    key: string;
    children: PriceAllItemObj[];
    name?: string;
    desc?: string;
    btn1?: CmsBtnObj;
    btn2?: CmsBtnObj;
}
