import {CmsBtnObj} from './common';

export enum SearchType {
  ALL = 'all',
  PRODUCT = 'product',
  SOLUTION = 'solution',
  PRO_SOL = 'pro_sol',
  DOC = 'doc',
  MARKET = 'market',
  SITE = 'site',
  FORUM = 'forum',
}

export interface SearchDataItem {
    content: string;
    description: string; // 没有content时使用description
    url: string;
    setTop?: any; // 旧版置顶数据接口
    title: string;
    type: SearchType;
    searchButtonList?: Array<{
        buttonName: string;
        url: string;
    }>;
    searchPointList?: Array<{
        pointName: string;
        url: string;
    }>;
}

export interface PageQuery {
    query?: string;
    type?: SearchType;
    pageNo?: string;
    from?: string;
    status?: string;
}
export interface SearchParams {
    uid: string;
    query: string;
    type: string;
    pageNo: string;
    status: string;
}

export interface SearchState {
    uid: string;
    query: string;
    type: SearchType;
    pageNo: string;
    status: string;
    from: string;
    searchReferer: string;
    fromPage?: string;
}

export interface SearchData {
    message?: 'QUERY_WORDS_INVALID';
    dataList: SearchDataItem[];
    searchInfo: {
        curPage: number;
        totalNum: number;
    };
}

export type TypeCount = Array<{
    count: number;
    value: SearchType;
}>;

export interface TypeCountResObj {
    searchInfo: {
        curPage: number;
        totalNum: number;
        type: TypeCount;
    };
}

export interface TopSearchParams {
    query: string;
    status: string;
    fromPage?: string;
}

// ts似乎没有接口mixin的方法
export interface TopSearchFullData {
    message?: 'QUERY_WORDS_INVALID';
    cmsRelInfo?: {
        cards: TopDataItem[];
        textLinks: Array<{
            link: string;
            title: string;
            tag: string;
            desc: string;
        }>;
        recommendDocs: RecommendDataItem[];
        recommendCampaigns: RecommendDataItem[];
        recommendProducts: RecommendDataItem[];
    };
    searchTopInfo: Array<{
        title: string;
        topList: SetTopDataItem[];
    }>;

}

export interface TopSearchData {
    cmsRelInfo?: {
        cards: TopDataItem[];
        textLinks: Array<{
            link: string;
            title: string;
            tag: string;
            desc: string;
        }>;
    };
    searchTopInfo: Array<{
        title: string;
        topList: SetTopDataItem[];
    }>;
}

export interface TopDataItem {
    bgUrl: string;
    darkTheme?: boolean;
    btnGroup: CmsBtnObj[];
    title: string;
    desc: string;
    id: number;
    link: string;
    textLinks: Array<{
        title: string;
        disabled: boolean;
        linkGroup: CmsBtnObj[];
    }>;
    type: 'product' | 'campaign';
}

export interface SetTopDataItem {
    productName: string; // 标题
    desc: string;
    apply: Array<{ // 按钮
        className: string; // 没用到过
        disabled: boolean;
        link: string;
        name: string;
    }>;
    url: string;
    promotionV2: {
        items: Array<{ // 对应textLinks
            icon: string;
            title: string;
            units: Array<{ // 对应textLinks.linkGroup
                link: CmsBtnObj;
            }>;
        }>;
    };
    event: Array<{
        content: string;
    }>;
    promotion: {
        items: Array<{
            disabled: boolean;
            link: {
                href: string;
                text: string;
            };
            content: string;
        }>;
    };
}

export interface RecommendData {
    recommendDocs: RecommendDataItem[];
    recommendCampaigns: RecommendDataItem[];
    recommendProducts: RecommendDataItem[];
}

export interface RecommendDataItem {
    text?: string;
    url: string;
    link?: string;
}

// 用于兼容cms配置卡片和联想卡片的中间数据
export interface MiddleCardData {
    type?: 'campaign' | 'product' | 'relevance';
    btnGroup: CmsBtnObj[];
    title: string;
    desc: string;
    bgUrl?: string;
    darkTheme?: boolean;
    link: string;
    textLinks?: Array<{
        title: string;
        href?: string;
        disabled: boolean;
        startTime?: string;
        endTime?: string;
        linkGroup: Array<{
            startTime?: string;
            endTime?: string;
        } & CmsBtnObj>;
    }>;
    promotionV2?: {
        items: Array<{
            icon: string;
            title: string;
            units: Array<{
                link: CmsBtnObj;
            }>;
        }>;
    };
    event?: Array<{
        content: string;
    }>;
    promotion?: {
        items: Array<{
            disabled: boolean;
            link: {
                href: string;
                text: string;
            };
            content: string;
        }>;
    };

}
