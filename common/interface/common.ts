/**
 * @file 整个项目的公共类型
 */

import {CSSProperties} from 'react';
import {AutoCompleteProps, SelectProps, InputNumberProps} from 'antd';
import {SliderBaseProps, SliderSingleProps} from 'antd/lib/slider';
import {CheckboxGroupProps} from 'antd/lib/checkbox';
import {Block} from './blocks';

// 页面基本的props
export interface PagePropsObj {
    portalCommonOption?: PortalCommonOptionsObj;
    headOption?: {
        title?: string;
        description?: string;
        keywords?: string;
        favicon?: string;
    };
    isMobile?: boolean;
    isEn?: boolean;
    hidePortalCommon?: boolean;
}

export interface CmsPageDataObj {
    pageId: string;
    title: string;
    description: string;
    keywords: string;
    category?: string;
    path: string;
    serviceType: string;
    serviceName: string;
    department: string;
    modules: Block[];
    secondaryNavConfigId?: string;
    needLogin?: 0 | 1;
}

export type CmsBtnObj = {
    buttonColor?: 'black' | 'blue' | null;
    disabled?: boolean;
    text?: string;
    href?: string;
    mobileHref?: string;
    login?: boolean;
    realName?: boolean;
    type?: 'primary' | 'default' | 'link';
};

/** 开发者用户角色 */
export type DeveloperRolesType = 'CHILD' | 'MAIN' | 'MANAGER';

/** 官网通用按钮props */
export interface UButtonProps {
    type?: 'default' | 'primary' | 'link'; // 类型，默认default,非必须
    link?: string; // 跳转链接
    className?: string; // className
    style?: CSSProperties; // style
    onClick?: React.MouseEventHandler<HTMLElement>; // 点击事件
    target?: '_self' | '_blank'; // 跳转方式，默认_blank
    children: JSX.Element | string;
    track?: {category: string, name: string, value: string, action?: string};
}

// 搜索优化核心关键词
export interface CoreKeywordsObj {name: string, link: string}

/**
 * 将一个interface中的一个或多个属性变成可选，多个属性的话第二个参数用|分隔
 */
export type PartialOneProperty<T, K extends keyof T> = Omit<T, K> & {
    [P in K]?: T[P];
};

// 开发者中心标签
export interface TagItemObj {
    name: string;
    id?: number;
    key?: string;
    desc?: string;
    parent?: number;
    parentName?: string;
    children?: TagItemObj[];
    picture?: string;
    level?: number;
    // 20是一级 30是二级
    type?: 20 | 30;
}

/**
 * 用户类型 NONE:未实名 PERSONAL:个人用户 ENTERPRISE: 企业用户
 */
export type UserType = 'NONE' | 'PERSONAL' | 'ENTERPRISE';

export type ProductUserType = 'READY' | 'NO_LOGIN' | 'NO_ACTIVE' | 'NO_REALNAME' | 'UNKNOWN_ERROR';
export interface CyberplayerConfig {
    width?: string; // 宽度，也可以支持百分比(不过父元素宽度要有)
    height?: string; // 高度，也可以支持百分比
    title?: string; // 标题
    file?: string; // 播放地址
    errorTip?: string; // 发生错误时的提示
    image?: string; // 预览图
    autostart?: boolean; // 是否自动播放
    isLive?: boolean; // 是否是直播
    stretching?: 'uniform' | 'none' | 'exactfit' | 'fill'; // 拉伸设置
    repeat?: boolean; // 是否重复播放
    volume?: number; // 音量 0 -100
    primary?: 'flash' | null;
    controls?: 'none' | 'over' | boolean; // controlbar是否显示
    starttime?: number; // 视频开始播放时间点(单位s)，如果不设置，则可以从上次播放时间点续播
    playRate?: boolean; // 是否支持倍速播放
    logo?: { // logo设置
        linktarget?: string;
        margin?: number;
        hide?: boolean;
        position?: string; // 位置
        file?: string; // 图片地址
    };
    controlbar?: {
        barLogo?: boolean;
    };
    rtmp?: {
        reconnecttime: number;
    };
    hls?: {
        reconnecttime: number;
    };
    flv?: {
        reconnecttime: number;
    };
    ak?: string;
    RtcPlugin?: {
        serverUrl: string; // 播放链接
        usedatachannel?: boolean; // 是否开启datachannel传输数据，默认为false
        reConnectTimeOut?: number; // 断线重连超时时间，默认30s
        reConnectTime?: number; // 断线重连次数，默认6次
        usehttps?: boolean; // 是否使用https，默认false
    };
    maxBufferLength?: number; // 设置最大缓存长度
}
export type CyberplayerVersion = '3.5.2' | '4.4.2.1' | '4.3.2';

type cb = (event: any) => void;
export interface CyberplayerApi {
    play: () => void;
    pause: () => void;
    stop: () => void;
    getPosition: () => number;
    getWidth: () => number;
    getHeight: () => number;
    setVolume: (num: number) => void;
    setMute: (bool: boolean) => void;
    getState: () => 'playing' | 'paused' | 'idle' | 'buffering';
    onConnection: (e: cb) => void;
    onNoLiveStream: (e: cb) => void;
    onAlive: (e: cb) => void;
    onPlay: (e: cb) => void;
    onIdle: (e: cb) => void;
    onPause: (e: cb) => void;
    onError: (e: any) => void;
    remove: () => void;
    setControls: (e: boolean) => void;
    getControls: () => boolean;
    on: (eventName: 'ready' | 'error' | 'play' | 'pause', cb: cb) => void;
    setup: (config: CyberplayerConfig) => void;
    seek: (num: number) => void;
    onTime: (cb: cb) => void; // 视频播放时间点实时变换的监听函数
}

/**
 * 获取函数参数的类型
 */
export type GetFuncType<T> = T extends (arg: infer P) => void ? P : string;

export type UMultipleSelectProps = SelectProps & {maxLength?: number};
export type UCompanyInputProps = AutoCompleteProps & {campaignId?: string};
export interface USliderInputProps {
    value?: InputNumberProps['value'];
    defaultValue?: InputNumberProps['value'];
    onChange?: SliderSingleProps['onChange'];
    min: SliderBaseProps['min'];
    max: SliderBaseProps['max'];
    unit: string;
    sliderProps?: SliderBaseProps;
    inputNumberProps?: InputNumberProps;
}
export type USurveyCheckBoxProps = CheckboxGroupProps & {
    answerRule?: Array<{
        show?: boolean;
        placeholder?: string;
    }>;
};
type ProductApiUnit = 'minute' | 'hour' | 'character';

export interface ProductApiInfo {
    'p3m_name': string;
    'api_id'?: string;
    'api_name': string | Array<Record<keyof Omit<ProductApiInfo, 'p3m_name'>, string>>;
    'parent_api_id'?: string;
    'parent_api_name'?: string;
    'p3m_unit'?: ProductApiUnit;
    'console_unit'?: ProductApiUnit;
}

export enum EnumProductServiceType {
    AI_TE = 'AI_TE',
    INTELLIGENTWRITING = 'INTELLIGENTWRITING',
    AIPAGE = 'AIPAGE',
    BCC = 'BCC',
    BCN_OCR = 'BCN_OCR',
    BOS = 'BOS',
    CBS = 'CBS',
    CDN = 'CDN',
    CDS = 'CDS',
    EIP = 'EIP',
    FACE = 'FACE',
    GENERAL_OCR = 'GENERAL_OCR',
    ICR = 'ICR',
    ID_OCR = 'ID_OCR',
    IMAGE_RECOGNITION = 'IMAGE_RECOGNITION',
    LOCATION_OCR = 'LOCATION_OCR',
    LSS = 'LSS',
    NAT = 'NAT',
    OCR = 'OCR',
    SPEECH = 'SPEECH',
    TMS = 'TMS',
    WEBIMAGE_OCR = 'WEBIMAGE_OCR',
}

export interface SurveyIframePostMessageObj {
    surveySubmitSuccess: boolean;
    url: string;
}
