/**
 * @file 区块数据类型
*/
import {CloudLiveObj} from './page';
import {CmsBtnObj} from './common';
import {CmsSurveyPageDataObj} from './cmsSurvey';

/* eslint-disable max-lines */

export interface Block<T = any, P = any> {
    type?: T;
    id?: number;
    data: P;
    displayName?: string;
    index?: number;
    isMobile?: boolean;
}

type V6ConfigBgTheme = 'default' | 'white' | 'gray'; // 背景色主题

export type V1RS001Obj = Block<'V1RS001', {
    render: {
        title: string;
        desc: string;
        bg: string;
        newsTicker?: string;
        cards: Array<{
            name: string;
            blackText: string;
            redText: string;
            bg?: string;
        }>;
    };
}>;

export type V1RG001Obj = Block<'V1RG001', {
    render: {
        title: string;
        desc: string;
        bg?: string;
        rules?: {
            title: string;
            content: string;
        };
        card: {
            name: string;
            blackText: string;
            redText: string;
            bg?: string;
        };
        productList: Array<Array<{
            title: string;
            desc: string;
            hotValue?: string;
            parameters: Array<{
                name: string;
                value: string;
            }>;
            price: string;
            priceUnit: string;
            priceTag?: string;
            detailBtn: {
                text: string;
                href: string;
            };
            buyBtn: {
                text: string;
                href: string;
            };
        }>>;
    };
    config: {
        row: number;
    };
}>;

export type V1CS001Obj = Block<'V1CS001', {
    render: {
        title: string;
        desc: string;
        logoImgSrc: string;
        isImage: boolean;
        mainImgSrc: string;
        videoSrc: string;
        showProductLinksTestimony: boolean;
        showTestimony: boolean;
        testimony: string;
        testimonyCustomer: string;
        content: {
            htmlContent: string;
            mdContent: string;
        };
        productLinks: Array<{
            title: string;
            links: Array<{
                title: string;
                link: string;
            }>;
        }>;
    };
    config: {
        type: 'new' | 'old';
    };
}>;

export type V1CS001NewObj = Block<'V1CS001', {
    render: {
        title: string;
        desc: string;
        logoImgSrc: string;
        isImage: boolean;
        mainImgSrc: string;
        videoSrc: string;
        showTestimony: boolean;
        testimony: string;
        content: {
            htmlContent: string;
            mdContent: string;
        };
        productLinks: Array<{
            title: string;
            links: Array<{
                title: string;
                link: string;
            }>;
        }>;
        benefits: Array<{
            title: string;
            desc: string;
            opacity?: number;
        }>;
        relatedProduct: Array<{
            name: string;
            link: string;
            img: string;
            type: 'product' | 'case' | 'none';
        }>;
        industry: string;
        scene: string;
        avatar: string;
        customer: string;
        customerName: string;
        customerTitle: string;
        customerDesc: string;
        bannerCaseLogo: string;
        relatedCase: Array<{
            title: string;
            desc: string;
            logo: string;
            bg: string;
            tag: string[];
            link: string;
        }>;
    };
    config: {
        type: 'new' | 'old';
    };
}>;

export type V5GItemObj = {
    title?: string;
    image?: string;
    desc?: string;
    button?: CmsBtnObj;
};

// V5G001 V5G002 V5G002_en V5G003 共用
export type V5G001Obj = Block<'V5G001' | 'V5G002' | 'V5G002_en' | 'V5G003', {
    render: {
        title?: string;
        content?: string;
        image?: string;
        button?: CmsBtnObj;
        video?: string;
        subTitle?: SubTitleObj;
    };
    config: {
        background?: string;
    };
}>;

// V5G004A V5G004B V5G004C V5G006A V5G006B V5G009B共用
export type V5G004Obj = Block<'V5G004A' | 'V5G004B' | 'V5G004C' | 'V5G006A' | 'V5G006B' | 'V5G009B', {
    render: {
        title: string;
        items: V5GItemObj[];
    };
    config: {
        background?: string;
        columns?: number;
    };
}>;

export type V5G005Obj = Block<'V5G005A' | 'V5G009A' | 'V1SG002', {
    render: {
        title: string;
        items: V5GItemObj[][];
    };
    config: {
        background?: string;
        columns?: number;
        rows?: number;
    };
}>;
export type V5G005BObj = Block<'V5G005B', {
    render: {
        title: string;
        items: V5GItemObj[];
    };
    config: {
        background?: string;
        columns?: number;
        rows?: number;
    };
}>;
export type V5G007AObj = Block<'V5G007A', {
    render: {
        title: string;
        items: Array<{
            icon: string;
            image: string;
            title: string;
            units: V5GItemObj[];
        }>;
    };
    config: {
        background?: string;
        columns: number;
    };
}>;
export type V5G008Obj = Block<'V5G007B' | 'V5G008A' | 'V5G008B' | 'V1SG001', {
    render: {
        title: string;
        subTitle?: string;
        items: Array<V5GItemObj & {tags: Array<{text: string, icon: string}>}>;
    };
    config: {
        background: string;
        columns: number;
    };
}>;
export type V5G009AObj = Block<'V5G009A', {
    render: {
        title: string;
        items: Array<Array<{
            desc?: string;
            href?: string;
            image?: string;
            title?: string;
            button?: {
                href: string;
            };
        }>>;
    };
    config: {
        background?: string;
        columns?: number;
        rows?: number;
    };
}>;

export type V5G010ItemObj = {
    title: string;
    units: Array<{
        link?: CmsBtnObj;
    }>;
};
export type V5G010Obj = Block<'V5G010', {
    render: {
        title: string;
        items: V5G010ItemObj[];
    };
    config: {
        background?: string;
        columns: number;
    };
}>;

export type V5S001Obj = Block<'V5S001' | 'V5S001_en', {
    render: {
        banner: {
            title: string;
            content: string;
            background?: {
                color?: string;
                image?: string;
                type?: string;
                wapImg?: string;
                video?: string;
                isDark?: boolean;
            };
            calculatorLink?: {
                href?: string;
                disabled?: boolean;
            };
            primaryLink?: CmsBtnObj;
            secondaryLink?: CmsBtnObj;
            tertiaryLink?: CmsBtnObj;
            rankLink?: CmsBtnObj & {
                rankIcon: string;
            };
            similarProduct?: {
                purchasePercent: string;
                path: string;
                serviceName: string;
            };
            activityLink?: {
                text: string;
                href: string;
                icon: string;
                wapText: string;
                btnText: string;
            };
            textLink?: CmsBtnObj[];
            video?: {
                title: string;
                url: string;
                desc: string;
            };
            cloudLive?: CloudLiveObj;
        };
        productList?: BannerProductList;
        promotion?: {
            items?: Array<{
                content?: string;
                link?: CmsBtnObj;
                title?: string;
            }>;
        };
        promotionV2?: {
            items: Array<{
                icon: string;
                title: string;
                units: Array<{
                    tag: {
                        text: string;
                        type: string;
                    };
                    link: CmsBtnObj;
                }>;
            }>;
        };
        promotionV3?: {
            items: Array<{
                title?: string;
                tag?: string;
                tagColor?: string;
                desc?: string;
                link?: string;
                textRows?: Array<{content: CmsBtnObj, tag: string}>;
                disabled?: boolean;
                tagText?: string;
                priceUnit?: string;
                price?: string;
                bottomText?: string;
            }>;
            rows: number;
            cover?: {
                title: string;
                desc: string;
                activityBtn: CmsBtnObj;
                getBtn: {
                    couponId: string;
                    text: string;
                };
            };
            promotion?: {
                link: string;
                title: string;
                tagColor: string;
                tag: string;
                desc: string;
            };
        };
        marketingPositon?: string;
        nav?: CmsProAndSolSubNavObj;
        hasProductList?: string;
    };
    config: {
        customBannerHeight?: string;
        isNewBanner2202?: boolean;
        isNewBanner?: boolean;
        promotionColumns?: number;
        promotionV2Columns?: number;
        withCalculatorLink?: boolean;
        withPromotion?: boolean;
        withRankLink?: boolean;
        withSecondaryLink?: boolean;
        withTertiaryLink?: boolean;
        hideProductList?: boolean;
        withBannerMarket?: 'productRecommend' | 'activityRecommend';
        promotionMode?: 'promotionVideo' | 'introVideo' | 'coupon3' | 'coupon2' | 'promotion' | 'introVideo' | 'textLink';
        productListMode?: 'split' | 'tied';
        withVideo?: boolean;
    };
}>;

export type V5B001Obj = Block<'V5B001', {
    render: {
        title: string;
        colNames: string[];
        rows?: Array<{
            icon?: string;
            name?: string;
            cellOne?: string;
            cellTwo?: string;
        }>;
    };
    config: {
        background?: string;
    };
}>;

export type V5B001BObj = Block<'V5B001B', {
    render: {
        title: string;
        colNames: string[];
        rows?: Array<{
            icon?: string;
            cells: string[];
        }>;
    };
    config: {
        background?: string;
        ratio: number[];
        ratioCount: number;
    };
}>;

export type V5B002Obj = Block<'V5B002' | 'V5B004' | 'V5B003A', {
    render: {
        title?: string;
        tabs?: Array<{
            tab?: {
                name: string;
                icon?: string;
                activeIcon?: string;
            };
            items?: Array<{
                image?: string;
                units?: V5GItemObj[];
                button?: CmsBtnObj;
            }>;
        }>;
    };
    config: {
        background?: string;
        columns?: number[];
        tabs?: number;
    };
}>;

export type V5B003BObj = Block<'V5B003B' | 'V5B007', {
    render: {
        title?: string;
        tabs?: Array<{
            tab?: {
                name: string;
                icon?: string;
                activeIcon?: string;
            };
            items?: Array<{
                title?: string;
                image?: string;
                desc?: string[] | string;
                button?: CmsBtnObj;
            }>;
        }>;
    };
    config: {
        background?: string;
        columns?: number[];
        tabs?: number;
    };
}>;

export type V5B005Obj = Block<'V5B005' | 'V5B006', {
    render: {
        title?: string;
        subTitle?: SubTitleObj;
        tabs?: Array<{
            tab?: {
                name: string;
                icon?: string;
                activeIcon?: string;
            };
            items?: Array<{
                desc?: string;
                title?: string;
            }>;
        }>;
    };
    config: {
        background?: string;
        columns?: number[];
        tabs?: number;
    };
}>;

export type V5DescItemObj = {
    title: string;
    desc: string;
    image: string;
};
export type V5B008Obj = Block<'V5B008', {
    render: {
        title: string;
        items: Array<V5DescItemObj & {
            units?: V5DescItemObj[];
        }>;
    };
    config: {
        background?: string;
        columns: number;
    };
}>;
export type V5B009Obj = Block<'V5B009', {
    render: {
        title: string;
        items: Array<V5DescItemObj & {
            button?: CmsBtnObj;
        }>;
    };
    config: {
        background?: string;
        columns: number;
    };
}>;
export type V5B010Obj = Block<'V5B010A' | 'V5B010B', {
    render: {
        title: string;
        items: Array<{
            title: string;
            subtitle: string;
            price: string;
            unit: string;
            units: Array<{
                name: string;
                value: string;
            }>;
            button?: CmsBtnObj;
        }>;
    };
    config: {
        background?: string;
        columns: number;
    };
}>;
export type V5B011Obj = Block<'V5B011', {
    render: {
        title: string;
        items: Array<{
            image: string;
            activeImage: string;
        }>;
    };
    config: {
        background?: string;
        columns: number;
    };
}>;
export type V5B016Obj = Block<'V5B016', {
    render: {
        title?: string;
        tabs?: Array<{
            tab: {
                name: string;
            };
            items: Array<{
                title: string;
                desc: string;
                price: string;
                originalPrice: string;
                button?: CmsBtnObj;
            }>;
        }>;
    };
    config: {
        background?: string;
        columns: number[];
        tabs: number;
    };
}>;
export type V5B017Obj = Block<'V5B017', {
    render: {
        title?: string;
        subTitle?: SubTitleObj;
        items: Array<{
            title: string;
            image: string;
            units: Array<{
                button: CmsBtnObj;
            }>;
        }>;
    };
    config: {
        background?: string;
        columns: number;
    };
}>;

export interface SubTitleObj {
    content?: string;
    link?: CmsBtnObj;
}

export interface PriceTableObj{
    key?: number;
    sd?: string;
    priceFlag?: string;
    price?: string;
    unit?: string;
    deletePrice?: string;
    href?: string;
    mobileHref?: string;
    connectSku?: string;
    targetCampaignId?: string;
    hotTags?: any;
}

interface V5G015ItemBtnObj {
    disabled?: boolean;
    text?: string;
    href?: string;
    mobileHref?: string;
    login?: boolean;
    realName?: boolean;
}
export interface V5G015ItemObj {
    id?: number;
    title?: string;
    subTitle?: string;
    titleTag?: string;
    backgroundImage?: string;
    parameters?: Array<{
        name?: keyof PriceTableObj;
        value?: PriceTableObj[keyof PriceTableObj];
        selectList?: Array<PriceTableObj[keyof PriceTableObj]>;
    }>;
    priceFlag?: string;
    price?: string;
    unit?: string;
    deletePrice?: string;
    hotTags?: Array<{
        text?: string;
        isRed?: boolean;
    }>;
    button?: V5G015ItemBtnObj;
    secondaryButton?: V5G015ItemBtnObj; // V5G015E底部副按钮
    rankLink?: {
        text?: string;
        href?: string;
    };
    paramsTable?: PriceTableObj[];
    subProductMessage?: string; // V5G015E
    subProductTitle?: string; // V5G015E
}

export interface V5G015CItemObj extends V5G015ItemObj {
    id?: number;
    features?: Array<{
        name?: string;
        value?: string;
    }>;
    sences?: Array<{
        first?: string;
        second?: string;
    }>;
}

interface V5G015Config {
    background?: string;
    columns?: number[];
    rows?: number[];
    withRankLink?: boolean;
    tabs?: number;
}

export type V5G015Obj<T = 'V5G015', Item = V5G015ItemObj> = Block<T, {
    render: {
        title?: string;
        subTitle?: SubTitleObj;
        tabs?: Array<{
            tab?: {
                name?: string;
            };
            items?: Item[];
        }>;
        items?: Item[];
    };
    config: V5G015Config;
}>;

export type V5B014BObj = Block<'V5B014B', {
    render: {
        title: string;
        subTitle?: string | SubTitleObj;
        items: Array<{
            time: string;
            items: Array<{
                button: CmsBtnObj;
                units: Array<{name: string}>;
            }>;
        }>;
    };
    config: any;
}>;

export interface CmsProAndSolSubNavObj {
    apply: {
        type: string;
        className: string;
        text: string;
        href: string;
        disabled: boolean;
    };
    content: Array<{
        control: {
            type: string;
        };
        data: {
            name: string;
        };
    }>;
    dropDownAble: boolean;
    fixed: boolean;
    name: string;
}

export interface CmsBanerNewsObj {
    items: Array<{
        title: string;
        textRows: Array<{
            tag: string;
            content: CmsBtnObj;
        }>;
    }>;
    rows: number;
}

export type BannerProductList = {
    type: {
        type: 'IMAGE' | 'TXT';
        text?: string;
        image?: string;
        disabled: boolean;
        href: string;
    };
    name: CmsBtnObj & {active: boolean};
    units: Array<{
        link: CmsBtnObj & {active: boolean};
    }>;
};

export type V1SS001Obj = Block<'V1SS001', {
    render: {
        banner: {
            title: string;
            content: string;
            primaryLink: CmsBtnObj;
            secondaryLink?: CmsBtnObj;
            video?: {
                title: string;
                desc: string;
                url: string;
            };
            background: {
                image: string;
                color: string;
                wapImg?: string;
            };
        };
        newsList?: CmsBanerNewsObj;
        advantageTitle?: string;
        advantageList?: Array<{
            title: string;
            image: string;
            desc: string;
            button: CmsBtnObj;
        }>;
        productList: BannerProductList;
        nav: CmsProAndSolSubNavObj;
    };
    config: {
        productListMode: 'split' | 'tied';
        hideProductList: boolean;
        withVideo: boolean;
        withSecondaryLink: boolean;
        withAdvantage: boolean;
        withNews: boolean;
        txtColor: 'white' | 'black';
        advantageColumn?: number;
    };
}>;

export type V5G024Obj = Block<'V5G024', {
    render: {
        title: string;
        subTitle: string | SubTitleObj;
        news: Array<{
            date: string;
            title: string;
            desc: string;
            link: CmsBtnObj;
        }>;
    };
    config: any;
}>;

export type V5G025Obj = Block<'V5G025', {
    render: {
        title: string;
        subTitle: string | SubTitleObj;
        items: Array<{
            id: string;
            icon: string;
            title: string;
            desc: string;
            link: CmsBtnObj;
        }>;
    };
    config: {
        counts: number;
    };
}>;

export type V5G011Obj = Block<'V5G011', {
    render: {
        title: string;
        items: Array<V5GItemObj & {
            video: {
                src: string;
                image: string;
            };
        }>;
    };
    config: {
        background: string;
        columns: number;
    };
}>;

export type V5G012Obj = Block<'V5G012', {
    render: {
        title: string;
        items: V5GItemObj[];
    };
    config: {
        minCount: string;
        maxCount: number;
    };
}>;

export type V5G013Obj = Block<'V5G013', {
    render: {
        title: string;
        subTitle: string;
        image: string;
        items: Array<{
            title: string;
            desc: string[];
        }>;
    };
    config: {
        background: string;
    };
}>;

export type V5G014Obj = Block<'V5G014', {
    render: {
        title: string;
        subTitle: string;
        items: Array<{
            image: string;
            title: string;
            link: string;
        }>;
    };
    config: {
        background: string;
    };
}>;

export type V1DS001Obj = Block<'V1DS001', {
    render: {
        title: string;
        productLink: string;
        desc: string;
        repoName?: string;
        aside: {
            disabled: boolean;
            isImage: boolean;
            imageSource: string;
            videoSource: string;
            videoCover?: string;
        };
        btnGroup: Array<{
            disabled: boolean;
            theme: string;
            text: string;
            href: string;
        }>;
    };
    config: any;
}>;

export type V1DS002Obj = Block<'V1DS002', {
    render: {
        title: string;
        subTitle: string;
        items: Array<{
            id: string;
            title: string;
            desc: string;
            linkList: CmsBtnObj[];
        }>;
    };
    config: any;
}>;

export type V1DG002Obj = Block<'V1DG002', {
    render: {
        title: string;
        subTitle: string;
        list: Array<{
            title: string;
            columns: number;
            linkList: CmsBtnObj[];
        }>;
    };
    config: any;
}>;

export type V1DG003Obj = Block<'V1DG003', {
    render: {
        title: string;
        linkList: Array<{
            disabled?: boolean;
            text?: string;
            href?: string;
            icon?: string;
        }>;
    };
    config: any;
}>;

export type V1DG004Obj = Block<'V1DG004', {
    render: {
        title: string;
        subTitle: string;
        videoList: Array<{
            title: string;
            tag: string;
            link: string;
            desc: string;
            cover?: string;
        }>;
    };
    config: any;
}>;

export type V5G016Obj = Block<'V5G016', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        items: Array<{
            image: string;
            title: string;
            desc: string;
            button: CmsBtnObj;
        }>;
    };
    config: {
        columns: number;
    };
}>;

export type V5G017Obj = Block<'V5G017', {
    render: {
        title: string;
        items: Array<{
            title: string;
            desc: string;
            link: string;
        }>;
    };
    config: {
        background: string;
    };
}>;

export type V5G018AObj = Block<'V5G018A', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        items: Array<{
            title: string;
            desc: string;
            image: string;
            imageHover: string;
            button?: CmsBtnObj;
        }>;
    };
    config: {
        counts: number;
    };
}>;

export type V5G018BObj = Block<'V5G018B', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        image: string;
        items: Array<{
            title: string;
            desc: string;
            href: string;
        }>;
    };
    config: any;
}>;

export type V5G019Obj = Block<'V5G019', {
    render: {
        mobile: {
            title: string;
            desc: string;
            button: CmsBtnObj;
        };
        pc: {
            title: string;
            desc: string;
            button: CmsBtnObj;
        };
    };
    config: any;
}>;

export type V5G020Obj = Block<'V5G020', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        tabs: Array<{
            tab: {
                name: string;
            };
            items: Array<{
                image: string;
                sence: {
                    title: string;
                    desc: string;
                };
                sences: Array<{
                    title: string;
                    desc: string;
                    detail: CmsBtnObj;
                }>;
                whatName: string;
                whats: Array<{
                    title: string;
                    descs: string[];
                }>;
                relatedProductsName: string;
                relatedProducts: Array<{
                    title: string;
                    href: string;
                    icon: string;
                }>;
                recommendName: string;
                recommend: Array<{
                    title: string;
                    img: string;
                    desc: string;
                    href: string;
                }>;
            }>;
        }>;
    };
    config: any;
}>;

export type V5G020BObj = Block<'V5G020B', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        tabs: Array<{
            tab: {
                name: string;
            };
            items: Array<{
                image: string;
                sence: {
                    title: string;
                    desc: string;
                };
                listName: string;
                lists: Array<{
                    title: string;
                    descs: string[];
                }>;
                relatedProductsName: string;
                relatedProducts: Array<{
                    title: string;
                    href: string;
                    icon: string;
                }>;
            }>;
        }>;
    };
    config: any;
}>;
export type V5G021Obj = Block<'V5G021', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        items: Array<{
            title: string;
            enhance: string;
            bg?: string;
            list: Array<{
                title: string;
                icon?: string;
                desc: string;
                link?: CmsBtnObj;
            }>;
        }>;
    };
    config: {
        columns: number;
    };
}>;

export type V5G022AObj = Block<'V5G022A', {
    render: {
        title: string;
        cardList: Array<{
            config: {
                // 注意这个类型里的 counts 都是数字字符串
                counts: string;
            };
            headerCard: {
                title: string;
                desc: string;
                image: string;
                link: string;
                tags: Array<{
                    icon: string;
                    text: string;
                }>;
            };
            items: Array<{
                title: string;
                desc: string;
                link: string;
            }>;
        }>;
    };
    config: {
        counts: string;
    };
}>;

export type V5G022BObj = Block<'V5G022B', {
    render: {
        title: string;
        items: [
            {
                title: string;
                desc: string;
                button: CmsBtnObj;
                background?: string;
                tags: Array<{
                    icon?: string;
                    text: string;
                }>;
            },
            ...Array<{
                title: string;
                desc: string;
                link: string;
                background: string;
            }>
        ];
    };
    config: {
        counts: string;
    };
}>;

export type V5G023Obj = Block<'V5G023', {
    render: {
        title: string;
        items: Array<{
            icon: string;
            desc: string;
            button: CmsBtnObj;
        }>;
    };
    config: {
        background: string;
        counts: number;
    };
}>;

export type V5G026Obj = Block<'V5G026', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        items: Array<{
            id: string;
            title: string;
            desc: string;
            bg: string;
            btn: CmsBtnObj;
            characters: string[];
        }>;
    };
    config: any;
}>;

export type V5G027Obj = Block<'V5G027A' | 'V5G027B', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        items: Array<{
            id: string;
            list: Array<{
                link: string;
                video?: string;
                previewImg?: string;
                title: string;
                desc: string;
            }>;
        }>;
    };
    config: any;
}>;

export type V5G028Obj = Block<'V5G028', {
    render: {
        title?: string;
        subTitle?: SubTitleObj;
        table: Array<Array<{
            type: string;
            content: string;
            note: string;
            rowspan: number;
        }>>;
        lastRow: Array<{
            type: string;
            priceUnit: string;
            price: string;
            priceSuffix: string;
            btn: {
                text: string;
                href: string;
                disabled: boolean;
            };
            note: string;
            content: string;
        }>;
    };
    config: {
        hasParentRow: boolean;
        disableLastRow: boolean;
    };
}>;

export type V5G029Obj = Block<'V5G029', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        rows: Array<Array<{
            title: string;
            tag: string;
            desc: string;
            link: string;
            cover?: string;
        }>>;
    };
    config: any;
}>;

export type V5G030Obj = Block<'V5G030', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        steps: Array<{
            icon: string;
            title: string;
            desc: string;
            value?: string;
            link: CmsBtnObj;
        }>;
    };
    config: any;
}>;

export type V5B018Obj = Block<'V5B018', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        items: Array<Array<{
            image: string;
            title: string;
            desc: string;
            button: CmsBtnObj;
        }>>;
    };
    config: {
        rows: number;
        columns: number;
    };
}>;

export type V5S004Obj = Block<'V5S004', {
    render: {
        image: string;
        src: string;
    };
    config: any;
}>;

export type V5C002Obj = Block<'V5C002', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        items: Array<{
            icon?: string;
            title: string;
            button: CmsBtnObj;
            units: Array<{
                content: string;
            }>;
        }>;
    };
}>;
export type MarkdownObj = Block<'Markdown', {
    mdContent: string;
    htmlContent: string;
}>;

export type CodePanelObj = Block<'CodePanel', {html: string}>;

export type V1SG003Obj = Block<'V1SG003', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        items: Array<Array<{
            image: string;
            title: string;
            desc: string;
            units: Array<{
                title: string;
            }>;
        }>>;
    };
    config: {
        rows: number;
        columns: number;
    };
}>;

export type V1EG001Obj = Block<'V1EG001', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        items: Array<Array<{
            image: string;
            title: string;
            desc: string;
            button?: CmsBtnObj;
        }>>;
    };
    config: {
        rows: number;
        columns: number;
    };
}>;

export type V1EG002Obj = Block<'V1EG002A' | 'V1EG002B', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        items: Array<{
            image: string;
            title: string;
            desc: string;
            price: {
                unit: string;
                text: string;
                after: string;
            };
            button: CmsBtnObj;
            list: Array<{
                title: string;
                desc: string;
            }>;
        }>;
    };
    config: {
        columns: number;
    };
}>;

export type V1EG003Obj = Block<'V1EG003', {
    render: {
        content: string;
        background: string;
        button?: CmsBtnObj;
    };
    config: any;
}>;

export type V1EG004Obj = Block<'V1EG004', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        tabs: Array<{
            tab: {
                name: string;
            };
            items: Array<{
                title: string;
                desc: string;
                imgTitle: string;
                image: string;
            }>;
        }>;
    };
    config: {
        tabs: number;
        columns: number[];
    };
}>;

export type V5G031Obj = Block<'V5G031', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
    };
    config: {
        background?: string;
    };
}>;

export type V5G032Obj = Block<'V5G032', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        videoSrc: string;
        videoCover: string;
        descList: Array<{
            title: string;
            desc?: string;
        }>;
    };
    config: {
        background?: string;
    };
}>;

export type V5G033Obj = Block<'V5G033', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        tabs: Array<{
            tab: {
                name: string;
                icon: string;
            };
            items: Array<{
                name: string;
                query: string;
            }>;
        }>;
    };
    config: {
        background?: string;
    };
}>;

export type V5G034Obj = Block<'V5G034', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        surveyId: string;
        showNotice?: boolean;
    };
}>;

export type V5G035Obj = Block<'V5G035', {
    render: {
        title: string;
        subTitle?: SubTitleObj;
        tabs: Array<{
            tab: {
                name: string;
                icon: string;
            };
            items: Array<{
                title: string;
                tag?: string;
                desc: string;
                tags: string;
                link?: string;
            }>;
        }>;
    };
    config: {
        columns: number[];
        rows: number[];
        tabs: number;
    };
}>;

export type V6S001Obj = Block<'V6S001A' | 'V6S001B', {
    render: {
        tabs: Array<{
            title: string;
            subTitle?: string; // V6S001B版本不存在此字段
            desc: string;
            buttons: CmsBtnObj[];
            video?: string; // 若video不存在或lottie解析失败 使用image 补位
            image: string; // banner配图（暂时PC & wap 共用同一张图）
            infosWithUnit: boolean; // V6S001A版本不存在此字段，默认false，两种版式不一样。 存在标签纯文本类型「https://cloudtest.baidu.com/product-s/keyue_communication」
            infos?: Array<{ // V6S001A版本不存在此字段
                label: string; // 标签 90%
                unit?: string; // 单位 + （若infosWithUnit 为true，则不需要取这个字段）
                text: string; // 文本 回复可用度
            }>;
        }>;
    };
    config: {
        background: string; // 背景图片
    };
}>;

// V6G001：此组件结构与V5B002保持了一致
export type V6G001Obj = Block<'V6G001', {
    render: {
        title: string;
        titleM?: string; // 可能的移动端标题支持br换行
        tabs: Array<{
            tab?: { // 没用字段，可忽略
                name?: string;
                icon?: string;
            };
            items: Array<{
                title: string;
                icon?: string;
                desc: string;
                button: CmsBtnObj;
                tags: Array<{
                    icon?: string;
                    text: string;
                }>;
                img: string;
            }>;
        }>;
    };
    config: {
        bgTheme: V6ConfigBgTheme;
    };
}>;

export type V6G002Obj = Block<'V6G002', {
    render: {
        title: string;
        titleM?: string; // 可能的移动端标题支持br换行
        subTitle?: SubTitleObj;
        items: Array<{
            icon: string;
            title: string;
            key: string;
            collapsedTitle: string;
            collapsedDes: string;
            intros: Array<{
                subTitle: string;
                des: string;
            }>;
            img: string;
        }>;
        button: CmsBtnObj;
    };
    config: {
        bgTheme: V6ConfigBgTheme;
        columns: number;
    };
}>;

export type V6G003Obj = Block<'V6G003', {
    render: {
        title: string;
        titleM?: string; // 可能的移动端标题支持br换行
        subTitle?: SubTitleObj;
        isZigzag: boolean; // 为true 展示一大一小的版式，如果个数为3个 则忽略这个配置（cms会加 用户侧也加个判断）
        isSingleRow: boolean; // 为true 展示一行版式，如果个数为3个 则忽略这个配置（cms会加 用户侧也加个判断）
        items: Array<{
            title: string;
            tag: string[];
            intro: string; // 描述文本
            button: CmsBtnObj;
            img: string;
        }>;
    };
    config: {
        bgTheme: V6ConfigBgTheme;
        columns: number;
    };
}>;

export type V6G004Obj = Block<'V6G004', {
    render: {
        title: string;
        titleM?: string; // 可能的移动端标题支持br换行
        subTitle?: SubTitleObj;
        tabs: Array<{
            tab: {
                name: string;
                icon?: string;
            };
            items: Array<{
                buttonColor?: 'black' | 'blue' | null;
                img: string;
                title: string;
                subTitle: string;
                desc: string;
                link: string; // 链接地址，支持整卡跳转
                infos: Array<{
                    label: string; // 若配置侧无unit字段，则使用纯文本样式
                    unit?: string;
                    desc: string;
                }>;
            }>;
        }>;
        logoWall: {
            disabled: boolean;
            title: string;
            list: Array<Array<{
                id: string;
                image: string;
            }>>;
        };
        attestation: {
            disabled: boolean; // 是否展示认证
            title: string;
            list: Array<{
                image: string;
                title: string;
                id: string;
            }>;
        };
    };
    config: {
        bgTheme: V6ConfigBgTheme;
        columns: number;
    };
}>;

export type V6G005Obj = Block<'V6G005', {
    render: {
        title: string;
        titleM?: string; // 可能的移动端标题支持br换行
        subTitle?: SubTitleObj;
        items: Array<{
            icon: string;
            title: string;
            intros: string[]; // 描述文本
            link?: string; // 链接, 用户侧建议加上trim判断，为空则无hover跳转
        }>;
    };
    config: {
        bgTheme: V6ConfigBgTheme;
    };
}>;

export type V6G006Obj = Block<'V6G006', {
    render: {
        title: string;
        titleM?: string; // 可能的移动端标题支持br换行
        subTitle?: SubTitleObj;
        items: Array<{
            title: string;
            desc?: string;
            buttons: CmsBtnObj[];
        }>;
        isBgVideo: boolean; // 是否使用视频背景
        videoSrc?: string; // 视频地址
        videoCover?: string; // 视频封面
        bgSrc?: string; // 背景图片地址 - 支持背景纯图
        button: CmsBtnObj; // 底部按钮
        secondaryButton: CmsBtnObj;
    };
    config: {
        bgTheme: V6ConfigBgTheme;
    };
}>;
export type V6G010Obj = Block< 'V6G010', {
    render: {
        title?: string;
        titleM?: string; // 新增
        subTitle?: SubTitleObj;
        items: Array<{
            title: string;
            image?: string;
            units: Array<{
                button: CmsBtnObj;
            }>;
        }>;
        button?: CmsBtnObj;
    };
    config: {
        bgTheme: V6ConfigBgTheme;
        columns: number;
    };
}>;
export type V6G014Obj = Block<'V6G014', {
    render: {
        title?: string;
        titleM?: string; // 可能的移动端标题支持br换行
        subTitle?: SubTitleObj;
        content?: {
            title?: string;
            desc?: string; // 描述文本
            img?: string; // 右侧图片
            bgImg?: string; // 整体背景图
            stepsTitle?: string; // 部署步骤标题
            stepsList?: Array<{
                    // icon: string;
                    id?: string;
                    name?: string; // 描述文本
            }>;
            relatedProductsTitle?: string; // 相关产品标题
            relatedProductsList?: Array<{
                    id?: string;
                    name?: string; // 描述文本
                    link?: string;// 链接
            }>;
            button?: CmsBtnObj; // 查看详情
        };
    };
    config: {
        bgTheme: V6ConfigBgTheme;
        columns: number;
    };
}>;
export type V6G014ImgObj = Block<'V6G014Img', {
images: Array<{
    img: string;
    id: string;
}>;
}>;
export interface V6G007ItemObj {
    id?: number;
    title?: string;
    subTitle?: string;
    titleTag?: string;
    backgroundImage?: string;
    parameters?: Array<{
        name?: keyof PriceTableObj;
        value?: PriceTableObj[keyof PriceTableObj];
        selectList?: Array<PriceTableObj[keyof PriceTableObj]>;
    }>;
    priceFlag?: string;
    price?: string;
    unit?: string;
    deletePrice?: string;
    hotTags?: Array<{
        text?: string;
        isRed?: boolean;
    }>;
    button?: V5G015ItemBtnObj;
    secondaryButton?: V5G015ItemBtnObj; // V5G015E底部副按钮
    rankLink?: {
        text?: string;
        href?: string;
    };
    paramsTable?: PriceTableObj[];
    subProductMessage?: string; // V5G015E
    subProductTitle?: string; // V5G015E
}

interface V6G007Config {
    background?: string;
    column?: number;
    rows?: number[];
    withRankLink?: boolean;
    tabs?: number;
    bgTheme: V6ConfigBgTheme; // 背景色主题
}

export type V6G007Obj<T = 'V6G007', Item = V6G007ItemObj> = Block<T, {
    render: {
        title?: string;
        subTitle?: SubTitleObj;
        tabs?: Array<{
            tab?: {
                name?: string;
                icon?: string;
            };
            items?: Item[];
        }>;
        items?: Item[];
    };
    config: V6G007Config;
}>;

export interface V6G008RecursionTabObj {
    tabName: string; // tab名称
    id: string; // 唯一id
    content?: {
        title: string; // 型号标题
        desc: string; // 型号描述
        sceneTitle: string; // 适用场景标题
        sceneList: string; // 多个适用场景,用半角逗号分隔
        specTitle: string; // 规格参数标题
        specList: Array<{ // 规格参数列表
            name: string;
            value: string;
            id: string;
        }>;
        priceUnit: string; // 价格单位¥
        price: string; // 价格
        priceAfter: string; // 价格补充说明，如：起
        linePrice: string; // 原价、划线价
        priceTip: string; // 价格备注，如：此参考价格为实例包年价格
        btn: CmsBtnObj; // 按钮
        bg: string; // 背景图
    };
    list?: V6G008RecursionTabObj[]; // 子tab列表
}

export type V6G008Obj = Block<'V6G008', {
    render: {
        title?: string;
        subTitle?: SubTitleObj;
        titleM?: string; // 可能的移动端标题支持br换行
        level1Title: string; // 第一层tab标题
        level2Title: string; // 第一层tab标题
        level3Title: string; // 第一层tab标题
        tabs: V6G008RecursionTabObj[]; // 2-3层tab列表
    };
    config: {
        tabCounts: number; // tab的层级，2或者3
        bgTheme: V6ConfigBgTheme;
    };
}>;

export type V6G012Obj = Block<'V6G012', {
    render: {
        title?: string;
        titleM?: string; // 可能的移动端标题支持br换行
        subTitle?: SubTitleObj;
        items: Array<{
            title?: string;
            icon?: string; // 右侧图片
            desc?: string; // 描述文本
            button?: CmsBtnObj; // 查看详情
        }>;
    };
    config: {
        bgTheme: V6ConfigBgTheme;
        columns: number;
    };
}>;
export type V6S002Obj = Block<'V6S002', {
    render: {
        title: string;
        desc: string;
        activityLink: CmsBtnObj; // 活动链接
        btnTheme: 'gradient' | 'default';
        buttons: CmsBtnObj[]; // default primary link文本链
        bg: string; // 背景图片
        bgM: string; // 背景图片（移动端）
        layoutType: 'image' | 'survey' | 'none';
        image?: string; // banner配图 组件2
        surveyPath?: string; // 问卷id 展示表单
        surveyModel?: CmsSurveyPageDataObj;
        // infosWithUnitTheme: 'gradient' | 'default';
        // isLongInfos: boolean; // 是否是长版本
        infos?: Array<{
            label: string; // 标签 90%
            labelM: string; // 移动端标签
            // secondaryLabel?: string; // 渐变文本
            // unit?: string; // 单位 + infosWithUnitTheme为渐变 则使用渐变
            desc: string; // 文本 回复可用度
            descM?: string; // 移动端文本
            opacity?: number; // 透明度
        }>;
    };
    config: any;
}>;

interface V6G017ItemObj {
    title: string;
    desc: string;
    button: CmsBtnObj;
    image: string;
}

export type V6G017Obj = Block<'V6G017', {
    render: {
        title: string;
        titleM?: string; // 新增
        subTitle?: SubTitleObj;
        items: V6G017ItemObj[][]; // 二维数组
    };
    config: {
        bgTheme: V6ConfigBgTheme;
        columnList: number[]; // 通过此字段渲染用户侧每行个数 如[2,3,3]，表示第一行2个，第二行3个，第三行3个排列
    };
}>;

export type V6G009Obj = Block<'V6G009', {
    render: {
        title: string;
        bg: string;
        items: Array<Array<V6G009TableDefault | V6G009TablePrimary | V6G009TableButton>>;
        subTitle: SubTitleObj;
    };
    config: {
        'rows': number;
        'columns': number;
    };
}>;

export type V6G009TablePrimary = {
    image: string;
    title: string;
    desc: string;
    'tags': Array<{
        text: string;
    }>;
};

export type V6G009TableDefault = {
    title: string;
    desc: string;
};

export type V6G009TableButton = {
    'priceUnit': string;
    'price': string;
    'priceAfter': string;
    'btn': {
        'text': string;
        'href': string;
        'disabled': boolean;
        'setTimer': boolean;
    };
};

interface V6G015ItemObj {
    title: string;
    subTitle: string;
    desc: string;
    button: CmsBtnObj;
    image: string;
}

export type V6G015Obj = Block<'V6G015', {
    render: {
        title: string;
        titleM?: string;
        subTitle?: SubTitleObj;
        items: V6G015ItemObj[][]; // 二维数组
        button: CmsBtnObj;
    };
    config: {
        bgTheme: V6ConfigBgTheme;
        columnList: number[]; // 通过此字段渲染用户侧每行个数 如[3,3]，表示第一行3个，第二行3个
    };
}>;
export type V6G016Obj = Block<'V6G016', {
    render: {
        titleList: string[];
        btnList: CmsBtnObj[];
        bg: string;
    };
    config: any;
}>;

export type V6S003Obj = Block<'V6S003', {
    render: {
        content: {
            title: string;
            keywords: string;
            bg: string;
            bgM: string;
            mainBtn: CmsBtnObj;
            subBtn: CmsBtnObj;
        };
        cards: V6G007ItemObj[];
    };
    config: {
        priceCardDisabled: boolean; // 是否禁用价格卡片
    };
}>;

export type V6G013Obj = Block<'V6G013', {
    render: {
        title: string;
        titleM?: string;
        subTitle?: SubTitleObj;
        rows: Array<Array<{
            title: string; // 第一行
            subTitle: string; // 第二行
            desc: string;
            link: string;
        }>>;
        button: CmsBtnObj;
    };
    config: {
        bgTheme: V6ConfigBgTheme;
    };
}>;

export type V6G018Obj = Block<'V6G018', {
    render: {
        title: string;
        titleM?: string;
        subTitle?: SubTitleObj;
        content: {
            name: string;
            query: string;
        };
    };
    config: {
        bgTheme: V6ConfigBgTheme;
    };
}>;
