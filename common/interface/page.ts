/**
 * @file page.ts 个别页面中的公共interface
 */

/**
 * 首页企业服务中心数据
 */

// TODO: 已经有点杂了，是否可以理一下分模块
import {TagItemObj} from './common';

export interface IndexEnterpriseDataObj {
    titleData: {
        title: string;
        subTitle: string;
        btnText: string;
        btnHref: string;
    };
    productData: Array<{
        category: string;
        products: Array<{
            name: string;
            desc: string;
            link: string;
            tags: Array<{text: string, type: 'grey' | 'blue'}>;
            price: Array<{
                text: string;
                type: 'black' | 'red';
                size: number;
            }>;
            promotionLabel?: string;
        }>;
    }>;
}

export interface IndexMarketingPositionObj {
    title: string;
    tag: string;
    desc: string;
    btn?: string;
    href?: string;
    discount?: {
        pre: string;
        after: string;
    };
}

export interface IndexProductsObj {
    title: string;
    subTitle: string;
    btnText: string;
    btnHref: string;
    modules: Array<{
        category: string;
        wapIcon?: string;
        tabProducts?: Array<{
            text: string;
            href: string;
            disabled?: boolean;
        }>;
        marketList?: Array<{
            name: string;
            desc: string;
            btn: string;
            href: string;
            bg: string;
            recommendType?: 'static' | 'dynamic'; // 推荐展示类型
            recommendKey?: string; // 推荐方案的关联产品，以英文逗号,进行分割
        }>;
        productList: Array<{
            name: string;
            desc: string;
            wapDesc?: string;
            button?: string;
            link: string;
            tagTxt?: string;
            price: Array<{
                text: string;
                type: 'black' | 'red';
                size: number;
            }>;
            tags?: Array<{
                text: string;
                type: 'blue' | 'grey';
            }>;
        }>;
        contrastMarketList?: Array<{
            name: string;
            desc: string;
            btn: string;
            href: string;
            bg: string;
        }>;
    }>;
}

export interface IndexDeveloperObj {
    title: string;
    desc: string;
    btn: {
        text: string;
        href: string;
    };
    modules: Array<{
        title: string;
        icon: string;
        data: Array<{
            text: string;
            href: string;
        }>;
    }>;
}

export interface IndexActivityObj {
    show: boolean;
    bg: string;
    main: {
        href: string;
        text: string;
        bg: string;
    };
    list: Array<{
        href: string;
        text: string;
    }>;
}

export interface ExperienceTypeObj {
    key: string;
    name: string;
    child: Array<{
        key: string;
        name: string;
        href: string;
        desc: string;
        previewType: 'img-static' | 'video' | 'img-left-to-right' | 'img-rotate' | 'img-scale';
        fileList: string;
    }>;
}
export interface ProductPriceNavObj {
    category: string;
    products: Array<{
        name: string;
        link: string;
    }>;
}

export interface SdkPropsObj {
    server: Array<{
        name: string;
        latestVersion: string;
        updateTime: string;
        downloadUrl: string;
        md5: string;
        image: string;
        productList: Array<{
            name: string;
            updateContent: string;
            latestVersion: string;
            updateDate: string;
            docLink: string;
        }>;
        key: string;
    }>;
    client: Array<{
        category: string;
        key: string;
        sdkList: Array<{
            docLink: string;
            downloadUrl: string;
            latestVersion: string;
            name: string;
            updateContent: string;
            updateDate: string;
        }>;
    }>;
}

// 分页器props
export interface UPagerPropsObj {
    totalCount: number; // 数据总数
    pageSize: number; // 页面展示的数据条数
    currentPage?: number; // 当前页
    className?: string; // class
    onChange?: (pageNum: number, pageSize: number) => void; // onChange事件
    isHide?: boolean; // 是否隐藏分页器
    size?: 'default' | 'small'; // 分页尺寸
    showTotal?: boolean; // 是否显示总数
    showQuickJumper?: boolean; // 是否显示快速跳转
    showSizeChanger?: boolean; // 是否显示pageSize切换器
    pageSizeOptions?: number[]; // 指定每页显示多少条
    ellipsisJump?: boolean; // 省略号悬浮可变成快速跳转按钮
    pageNumRender?: (num: number | '…') => React.ReactNode; // 自定义页码结构
}

// 最佳实践中心
export interface PracticeObj {
    id: number;
    title: string;
    desc: string;
    tags: string[];
    category: string;
    updatedAt?: string;
    mdContent?: string;
    url?: string;
    relatedProducts?: Array<{
        title: string;
        desc: string;
        link: string;
    }>;
}

export interface PracticeQueryObj {
    category?: string;
    pageNo?: number;
    pageSize?: number;
    orderBy?: string;
}

export type DocIndexListType = Array<{
    category: string;
    icon: string;
    products: Array<{
        name: string;
        link: string;
        expanded?: boolean;
    }>;
}>;

/** 直播状态 */
export enum LiveStatus {
    LIVE_BEFORE = 'LIVE_BEFORE',
    LIVING = 'LIVING',
    LIVE_END = 'LIVE_END',
}
export interface LiveRelatedArticlesItemObj {
    title: string;
    url: string;
    id: any;
}
export interface LiveDetailObj {
    id: string;
    title: string;
    mdDesc: string;
    htmlDesc: string;
    wxDesc: string;
    guests: Array<{
        desc: string;
        name: string;
        img: any;
    }>;
    status: LiveStatus;
    liveTimeEnd: string;
    liveTimeStart: string;
    pullStreamUrl: string;
    replayUrl: string;
    qrCode: any;
    keyVision: any;
    qrDesc: string;
    tags?: string | TagItemObj[] | number[];
    relatedArticles: LiveRelatedArticlesItemObj[];
    source: 'CLOUD' | 'DEVELOPER';
    qrCodeTalk1: string;
    qrDescTalk1: string;
    qrCodeTalk2: string;
    qrDescTalk2: string;
    applyLink: string;
}

export interface LiveJoinFormVal {
    name: string;
    mobile: string;
    email: string;
    company: string;
    job: string;
    industry: string;
}

export interface LiveJoinPostParams extends LiveJoinFormVal {
    liveId: string;
    track: string;
}
export interface LongTailKeywordObj {
    id: number;
    initials: null;
    coreKeyword: string;
    longTailKeyword: string;
    url: string;
}


// 通用状态
export type GeneralStatusDeveloper = 'REJECTED' | 'REVIEWING' | 'PUBLISHED' | 'OFFLINE';

// 问题列表项
export interface QuestionListItemObj {
    id: string;
    title: string;
    stick: number;
    tags: any[];
    answerNum: number;
    answer: AnswerItemObj;
    isFollow: boolean;
    status: GeneralStatusDeveloper;
    createdAt: string;
    htmlContent?: string; // 搜索结果返回
    viewCount: number;
}

/** 问题详情 */
export interface QuestionDetailObj extends QuestionListItemObj {
    nickname: string;
    avatar: string;
    rejectReason: string;
    userId: number;
    htmlContent: string;
    mdContent: string;
}

// 右侧热门活动
export type AsideActivityDataArr = Array<{name: string, link: string, desc: string}>;

export enum AskParamType {
    hot = 'hot',
    new = 'new',
    unanswered = 'unanswered'
}

// 回答
export interface AnswerItemObj {
    id: string;
    mdContent: string;
    htmlContent: string;
    isCollected: boolean;
    collectionNum: number;
    commentNum: number;
    nickname: string;
    userId: number;
    avatar: string;
    createdAt: string;
    likeNum: number;
    isLiked: boolean;
    isDisliked: boolean;
    isExcellent: boolean;
    status: GeneralStatusDeveloper;
}

export enum PanelKey {
    Home = 'home',
    Framework1 = 'framework1',
    DigitalBase = 'digital-base',
    IntelEngine = 'intel-engine',
    FullSceneApp = 'full-scene-app',
    Technology = 'technology',
    ContactUs = 'contact-us',
}

// 解决方案聚合页数据-重构
export interface SolutionIndexDataObj {
    category: string;
    list: Array<{
        name: string;
        link: string;
        desc: string;
        icon: string;
        bgImg: string;
        list: Array<{
            name: string;
            link: string;
            desc: string;
        }>;
    }>;
}

export interface CloudAiProductObj {
    title: string;
    subTitle: string;
    list: Array<{
        category: string;
        desc: string;
        btn: {
            text: string;
            link: string;
        };
        subBtn?: {
            text: string;
            link: string;
        };
        bg?: string;
        bgPreview?: string;
        bgTransfer?: string;
        characteristicsIndustryApp?: Array<{
            tit: string;
            items: Array<{
                text: string;
                link: string;
            }>;
        }>;
        characteristicsPaaS?: Array<{
            num: number | string;
            unit: string;
            desc: string;
        }>;
        characteristicsIaas?: {
            tit: string;
            items: Array<{
                text: string;
                link: string;
            }>;
        };
    }>;
}

// 视频中心：类型
export interface VideoCenterTypeItemObj {
    name?: string;
    value?: string;
    content?: Array<{
        name?: string;
        value?: string;
    }>;
}
// 视频中心：类型接口数据格式
export interface VideoCenterTypeObj {
    types?: VideoCenterTypeItemObj;
    classification?: {
        name?: string;
        value?: string;
        content?: VideoCenterTypeItemObj[];
    };
}
// 视频中心：视频列表请求数据格式
export interface VideoCenterQueryObj {
    pageNo?: number;
    pageSize?: number;
    firstClassValue?: string;
    secondClassValue?: string;
    type?: string;
    orderBy?: string;
    name?: string; // 搜索新增字段
}

export interface VideoCenterListObj {
    orderBy?: string;
    order?: string;
    pageNo?: number;
    pageSize?: number;
    totalCount?: number;
    result?: VideoCenterDetailObj[];
}

// 视频中心：视频页数据格式
export interface VideoCenterDetailObj {
    id: number;
    name: string;
    videoDesc: string;
    src: string;
    imgUrl: string;
    guest?: string;
    views?: number;
    likes?: number;
    createTime?: number;
    modifyTime?: number;
    priority?: number;
    firstClassName: string;
    firstClassValue: string;
    secondClassName: string;
    secondClassValue: string;
    typeName: string;
    typeValue: string;
}

/** ------活动价:START------- */
// 用户侧询价返回
export interface CampaignInfoItemObj {
    campaignName: string;
    catalogPrice: number;
    campaignPrice: number;
    policyId: string;
    campaignId: string;
    expirationTime: string;
    buyCountLimit?: number;
    newUserLimit?: number;
    realNameLimit?: number;
    promotionType?: number;
}
export interface ProductPriceDetailObj {
    serviceType: string;
    catalogPrice?: number;
    price?: number;
    campaignInfoList?: CampaignInfoItemObj[];
}
export interface ProductCampaignInfoObj {
    titleTag: string;
    hotTags: Array<{
        text?: string;
        isRed?: boolean;
    }>;
    price: string;
    prePrice: string; // 登录前展示的价格
    delPrice: string;
    unit: string;
    href: string;
    mobileHref: string;
}

// 用户侧询价请求参数
export interface ProductPriceParamsObj {
    accountId?: string;
    agentAccountId?: string;
    serviceType?: any;
    subServiceType?: any;
    chargeItemName?: any;
    region?: any;
    scene?: string;
    flavor?: {
        flavorItems?: any;
    };
    count?: number;
    signAuth?: string;
}
// 活动价connectSku值
export interface ProductPriceSkuObj {
    serviceType?: string;
    region?: string;
    flavor?: Array<{
        name?: string;
        value?: string;
        scale?: number;
    }>;
    pricePrimaryKeyId?: string;
    chargeItem?: string;
    subServiceType?: string;
    usages?: any;
    dimension?: string[];
    postPayUnitPrefix?: string;
    flavorDisplayName?: Array<{
        flavor?: string;
        flavorDisplayName?: string;
        value?: string;
        valueDisplayName?: string;
        scale?: string;
        unitPrefix?: string;
    }>;
    packageTime?: string;
    mergePurchaseLabel?: number;
    productPageLabel?: number;
    singleProductLabel?: number;
    duration?: number;
    timeUnit?: string;
}
/** ------活动价:END------- */

export interface CloudLiveProducts {
    title: string;
    desc: string;
    unit?: string;
    price: string;
    priceAfter?: string;
    link: string;
    linkWap?: string;
    tag?: string;
    key: string;
}

/**
 * 官网直播详情
 */
export interface CloudLiveObj {
    liveId: string;
    liveTitle: string;
    liveDesc: string;
    liveKeywords: string;
    detailLink: string;
    liveCover: string;
    startTime: string;
    endTime: string;
    wxShareConfig: string;
    pullStreamUrl: string;
    reviewUrl: string;
    recommendProducts: string | CloudLiveProducts[];
    recommendLive: string | Array<{title: string, liveId: string}>;
    returnProductText: string;
    liveEntryText: string;
    imRoomId: number;
}

// 产品券
export interface ProductCouponObj {
    id: number;
    name: string;
    couponType: 'DISCOUNT_COUPON' | 'CASH_COUPON';
    productType: string;
    productTypes: string[];
    productRuleDescription: string;
    totalAmount: number;
    balance: number;
    amountOrDiscount: string;
    usedAmountToShow: string;
    balanceToShow: string;
    beginTime: string;
    endTime: string;
    couponStatus: 'NORMAL';
    status: 'UNUSED';
    region: string;
    conditionArgsMap: {
        price?: string;
        productTotalPrice?: string;
    };
    effectArgsMap: {
        discountRate: string;
        upperBoundAmount: string;
    };
    conditionEffectDescription: string;
    orderType: null;
    disabled?: boolean;
}

// 产品Flavor
export interface ProductFlavorObj {
    name: string;
    value: string | number| null;
    scale: string | number;
}

// 产品FlavorDisplayName
export interface ProductFlavorDisplayNameObj {
    flavor: string;
    flavorDisplayName: string;
    value: string | number;
    valueDisplayName: string;
    scale: string | number;
    unitPrefix: null | string;
}

// 组合购产品
export interface ProductInquiryObj {
    serviceType: string;
    region: string;
    status: 'online';
    packageTime: string | null;
    flavor: ProductFlavorObj[];
    pricePrimaryKeyId: string;
    serviceTypeUpdatedAt: string;
    chargeItem: string;
    subServiceType: string;
    dimension: ['time'] | null | string[];
    flavorDisplayName: ProductFlavorDisplayNameObj[];
}

/**
 * 产品页券组配置信息
 */
export interface ProductCouponGroupObj {
    title: string;
    urls: string;
    desc: string;
    ruleBtnText: string;
    ruleBtnUrl: string;
    coupons: Array<{
        couponGroupId: string;
        available: boolean;
        couponGroupName: string;
        coupons: Array<{couponInfo: string}>;
    }>;
}

/** ------文档产品index页:START ------ */
export interface DocIndexSideBarItemObj {
    id: string;
    documentId: string;
    name: string;
    repoName: string;
    filePath: string;
    disabled: boolean;
    path: string;
    children?: DocIndexSideBarItemObj[];
}

export interface DocSubIndexSideBarObj {
    name: string;
    description: string;
    items: DocIndexSideBarItemObj[];
    repoName: string;
    seo: {
        title: string;
        description: string;
        keywords: string;
    };
}

export interface DocSubIndexHamburgerObj {
    category: string;
    icon: string;
    products: Array<{
        link: string;
        name: string;
    }>;
}
/** ------文档产品index页:END ------ */

// 考试/课程的前置课程校验接口返回参数
export interface ExamCoursePreCheckObj {
    checkStatus: boolean;
    unfinishedCourses: Array<{
        courseName: string;
        courseId: string;
    }>;
}

// 千人千面：配置侧参数类型
export interface RecommendConfigDataObj {
    [key: string]: any;
    recommendType?: 'static' | 'dynamic';
    recommendKey?: string;
}

export interface AiDemoItem {
    title: string;
    resultShow: boolean;
    collapseActiveName: string;
    demoType: string;
    location: {
        path: string;
        keyName: string;
    };
    imgs: string[];
    req: {
        options: Array<{
            type: string;
            requestKey: string;
            title: string;
            value: string;
            items: Array<{
                name: string;
                value: string;
            }>;
        }>;
        params: string;
        post: string;
        header: string;
        link: string;
        cloudLink: string;
    };
    res: {
        path: string;
        key: string;
        value: string;
        showKey?: string;
        showKeys?: Array<{
            name: string;
            value: string;
            valueMap: Array<{
                valueKey: string;
                valueResult: string;
            }>;
        }>;
        showLocation: boolean;
        type: string;
    };
    errConfig: {
        defaultMsg: string;
        msgMap: Array<{
            code: string;
            msg: string;
        }>;
    };
}

export interface AidemoOption {
    linkNav: boolean;
    title: string;
    type: string;
    items: AiDemoItem[];
    feedBack: boolean;
    newItems: AiDemoItem[];
    hideUpload?: boolean;
    path?: string;
    images?: string[];
}

// aidemo 页面数据
export interface AiDemoObj {
    name: string;
    description: string;
    options: AidemoOption;
}

