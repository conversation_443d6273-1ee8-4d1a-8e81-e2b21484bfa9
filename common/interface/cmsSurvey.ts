/**
 * @file cms表单组件的类型
 */

import {FormItemProps, InputProps, RadioProps, CheckboxProps, DatePickerProps} from 'antd';
import {Block} from './blocks';
import {CmsPageDataObj, UCompanyInputProps, UMultipleSelectProps} from './common';

type SurveyBaseObj = {
    title: string;
    displayName?: string;
    required: boolean;
    reminder: string;
    show?: boolean;
    tag?: string; // leads
    placeholder?: string; // 自定义placeholder
};

// CMS表单数据类型
export interface CmsSurveyPageDataObj extends Omit<CmsPageDataObj, 'modules'> {
    modules: SurverBlockObj[];
    title: string;
    surveyType: 'BASE' | 'ACTIVITY'; // BASE || ACTIVITY
    surveyDesc: string;
    surveyBodyBg: string;
    surveyBodyBgImg: string;
    isShowSurveyBanner: 0 | 1;
    surveyBanner: string;
    surveyBannerWap: string;
    isHideSurveyTitleAndDesc: 0 | 1;
    surveyAgreement: string;
    needLogin?: 0 | 1;
    needVerify?: 0 | 1;
    prompt: string;
    link: string;
    formStyle: string;
    recipient: string;
    forumId: string;
    forumName: string;
    forumLink: string;
    contrast: 0 | 1;
    weight: number;
    startTime: string;
    endTime: string;
    stay: number;
    isShowSuccessImg: 0 | 1;
    successImg: string;
    bannerLink: string;
    multiplyCommit: 0 | 1;
    abtestId: string;
    alreadySubmitInfo: string;
    customLoginUrl: string;
    needEnterpriseVerify: 0 | 1;
    allowChildAccountCommit: 0 | 1;
}

export type SurveyLinkageObj = {
    checked: number;
    show: number[];
};

export interface SurveyBlock<T = any, P = any, I = any> extends Block<T, any> {
    data: {
        render: P & SurveyBaseObj;
        html?: string; // 富文本 CodePanel
    };
    formItemProps?: FormItemProps;
    inputProps?: I;
}

export type SurverBlockObj =
    SurveyInputObj | SurveyCompanyInputObj | SurveyTextareaObj | SurveyDropdownObj
    | SurveyRadioObj | SurveyCheckboxObj | SurveyDatePickerObj | SurveyUploadObj
    | SurveyBlock<'V5G002' | 'V5G023' | 'CodePanel'>;

export type SurveyInputObj = SurveyBlock<'SummitInput' | 'Input', {
    validation: 'none' | 'mobile' | 'email' | 'number' | 'id' | 'url' | 'letter' | 'invite';
    length: string;
    campaignId?: string; // 强制将input类型转CompanyInput时需要
    info?: string; // 如 otherInfo
}, InputProps>;

export type SurveyCompanyInputObj = SurveyBlock<'CompanyInput', {
    tag?: string;
    info?: string;
    campaignId?: string; // 需要外部注入
}, UCompanyInputProps>;

export type SurveyTextareaObj = SurveyBlock<'Textarea', {
    length: string;
    minLength?: string;
}, InputProps>;

export type SurveyDropdownObj = SurveyBlock<'Dropdown' | 'DropdownWap', {
    answer: string[];
    linkage: SurveyLinkageObj[];
}, UMultipleSelectProps>;

export type SurveyRadioObj = SurveyBlock<'Radio', {
    answer: string[];
    linkage: SurveyLinkageObj[];
}, RadioProps>;

export type SurveyCheckboxObj = SurveyBlock<'Checkbox', {
    answer: string[];
    linkage: SurveyLinkageObj[];
    multiLineStyle?: boolean;
    answerRule?: Array<{show?: boolean, placeholder?: string}>; // 选中项展示input框
}, CheckboxProps>;

export type SurveyDatePickerObj = SurveyBlock<'DatePicker', {
    placeholder?: string;
}, DatePickerProps>;

export type SurveyUploadObj = SurveyBlock<'Upload', {
    num?: number;
}, DatePickerProps>;

// 表单字段
export interface FormFieldsObj {
    id: number;
    type: string;
    formItemProps?: FormItemProps;
    inputProps?: any;
    Component?: React.ComponentType<any>;
    isHide?: boolean;
    isProductComponent?: boolean; // 是否是产品组件
    isActivitySuccessComponent?: boolean; // 是否是活动类型提交成功后的产品组件
}

// 表单提交数据
export interface CmsSurveyApplyObj {
    campaignId: string;
    questions: Array<{
        title: string;
        answers: Array<{answer: string}>;
    }>;
    pageId?: string;
    needLogin?: boolean;
}

export interface CmsSurveyResObj {
    code: number;
    message: string;
    configId: string;
    redirectUrl: string;
}
